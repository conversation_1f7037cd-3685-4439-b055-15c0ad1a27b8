# Differential Updates Implementation

This document describes the differential update system implemented for the Electron app, which significantly reduces update download sizes and improves user experience.

## Overview

Differential updates only download the parts of the application that have changed between versions, rather than downloading the entire application. This can reduce update sizes by 70-90% in typical scenarios.

## Implementation Details

### 1. AutoUpdater Configuration

The `electron/AutoUpdater.js` file has been enhanced with:

```javascript
// Enable differential downloads
autoUpdater.disableDifferentialDownload = false;

// Configure differential update settings
autoUpdater.differentialDownloadOptions = {
  useMultipleRangeRequest: true,
  splittingThreshold: 1024 * 1024, // 1MB threshold
};
```

### 2. Build Configuration

#### App Switching Integration
The differential update configuration is automatically applied through `initApp.js` when switching between apps. This ensures consistent configuration across all apps in the multi-app system.

#### Package.json Scripts (Auto-generated)
- `yarn release:differential` - Creates optimized differential releases
- `yarn release:full` - Creates complete package rebuilds

#### NSIS Configuration (Auto-applied)
```json
{
  "nsis": {
    "differentialPackage": true,
    "packElevateHelper": false
  },
  "compression": "maximum"
}
```

#### initApp.js Integration
The `initApp.js` file has been enhanced to automatically include differential update settings when configuring any app:

```javascript
"nsis": {
  // ... other settings
  "differentialPackage": true,
  "packElevateHelper": false
},
"compression": "maximum"
```

### 3. Release Management

#### Differential Release Script
The `scripts/release-differential.js` provides:
- Automated file hash generation for change detection
- Optimized build process with differential support
- Validation of differential update setup

#### Usage
```bash
# Create differential release
node scripts/release-differential.js

# Validate setup
node scripts/release-differential.js --validate
```

### 4. GitHub Workflow Integration

The `.github/workflows/release.yml` has been updated to use differential releases for Windows builds:

```yaml
- name: Build Electron app with differential updates (Windows)
  if: matrix.os == 'windows-latest'
  run: |
    node scripts/release-differential.js
```

## Benefits

1. **Reduced Download Sizes**: 70-90% smaller updates
2. **Faster Updates**: Users get updates much faster
3. **Bandwidth Savings**: Significant reduction in bandwidth usage
4. **Better User Experience**: Less waiting time for updates
5. **Backward Compatible**: Works with existing auto-updater setup

## Validation

Run the validation script to ensure everything is configured correctly:

```bash
node scripts/release-differential.js --validate
```

Expected output:
```
🔍 Validating differential update setup...
   ✅ AutoUpdater differential config
   ✅ Package.json differential scripts
   ✅ NSIS differential package config
✅ All differential update checks passed!
```

## Usage Examples

### Development
```bash
# Switch to any app (differential config auto-applied)
node initApp.js prosy

# Test differential release locally
yarn release:differential

# Validate configuration
node scripts/release-differential.js --validate
```

### Production
The GitHub workflow automatically uses differential releases for all Windows builds when tags are pushed.

## Technical Details

### File Change Detection
The system uses SHA-256 hashes to detect file changes between versions:
- Generates hashes for all build files
- Compares with previous version hashes
- Only downloads changed files

### Download Optimization
- Uses HTTP range requests for partial downloads
- Splits large files into chunks for parallel downloading
- Implements retry logic for failed downloads

### Security
- Maintains all existing security features
- Verifies file integrity using checksums
- Prevents downgrade attacks

## Troubleshooting

### Common Issues

1. **Large Initial Download**: First update after enabling differential updates will be full size
2. **Network Issues**: Differential downloads may fall back to full downloads on network errors
3. **File Corruption**: System automatically falls back to full download if differential fails

### Debugging

Enable verbose logging in AutoUpdater:
```javascript
autoUpdater.logger = log;
autoUpdater.logger.transports.file.level = "debug";
```

## Monitoring

Monitor update performance by checking:
- Download sizes in logs
- Update completion times
- User feedback on update experience

## Future Enhancements

1. **Cross-platform Support**: Extend to macOS and Linux
2. **Advanced Compression**: Implement additional compression algorithms
3. **Update Scheduling**: Smart update timing based on usage patterns
4. **Rollback Support**: Quick rollback to previous versions
