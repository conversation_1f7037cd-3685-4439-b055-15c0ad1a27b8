# CRUD Operation Sync Control

## Problem Statement

The application was experiencing severe performance issues with initial data loading taking 128+ seconds due to:

1. **Blocking Database Synchronization**: 3-way sync (Local ↔ LAN ↔ Remote) running during data loading
2. **Remote Database Latency**: Connections to `apps-db.onrender.com` with 15-second timeouts
3. **Multiple Concurrent Operations**: 6 database collections loading simultaneously during `buffOccupations`
4. **Sync Conflicts**: CRUD operations triggering immediate sync, causing conflicts and delays

## Solution: CRUD Operation Sync Control

We've implemented a comprehensive sync control system that **pauses all sync operations during CRUD operations** to eliminate conflicts and improve performance.

### Architecture Overview

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   SyncService   │    │ DatabaseHandler │
│   Components    │    │                 │    │                 │
├─────────────────┤    ├─────────────────┤    ├─────────────────┤
│ SyncController  │◄──►│ CRUD Control    │◄──►│ CRUD Operations │
│ useSyncController│    │ - startCrud()   │    │ - create()      │
│ ModuleTable     │    │ - endCrud()     │    │ - update()      │
│                 │    │ - pauseSync()   │    │ - delete()      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### Key Components

#### 1. SyncService Enhancement (`electron/database/SyncService.js`)

**New Properties:**
- `activeCrudOperations`: Set of databases with active CRUD operations
- `pausedSyncs`: Set of databases with paused sync
- `crudOperationTimeouts`: Map of safety timeouts for CRUD operations

**New Methods:**
- `startCrudOperation(dbKey, operationType)`: Pause sync for database
- `endCrudOperation(dbKey, operationType)`: Resume sync after delay
- `pauseSyncForDatabase(dbKey, reason)`: Manually pause sync
- `resumeSyncForDatabase(dbKey, reason)`: Manually resume sync
- `isSyncPaused(dbKey)`: Check if sync is paused
- `getCrudOperationStatus()`: Get status of all operations

#### 2. DatabaseHandler Integration (`electron/database/DatabaseHandler.js`)

**Enhanced CRUD Methods:**
- `create()`: Calls `startCrudOperation()` → performs operation → calls `endCrudOperation()`
- `update()`: Same pattern as create
- `delete()`: Same pattern as create
- `saveDocument()`: Handles both create and update with sync control

**Error Handling:**
- Ensures `endCrudOperation()` is called even if the operation fails
- Prevents sync from being permanently paused due to errors

#### 3. Frontend Utilities

**SyncController (`src/Utils/SyncController.js`):**
- Cross-platform sync control (Electron/Web)
- Batch operation support
- Error handling and recovery
- Performance monitoring

**useSyncController Hook (`src/Utils/hooks/useSyncController.js`):**
- React hook for easy component integration
- Real-time sync status monitoring
- CRUD operation helpers
- Batch operation support

**SyncControlledModuleTable (`src/Components/ModuleTable/SyncControlledModuleTable.js`):**
- Drop-in replacement for ModuleTable
- Visual sync status indicators
- Enhanced CRUD operations
- Bulk operation support

### How It Works

#### 1. Normal Operation Flow
```
1. Background sync runs every 45 seconds
2. User initiates CRUD operation
3. SyncService.startCrudOperation() called
4. All sync operations paused for that database
5. CRUD operation executes without interference
6. SyncService.endCrudOperation() called
7. Sync resumes after 5-second delay
8. Immediate sync triggered to catch up
```

#### 2. Batch Operation Flow
```
1. SyncController.startBatchOperation() called
2. Sync paused for entire batch duration
3. Multiple CRUD operations execute rapidly
4. SyncController.endBatchOperation() called
5. Single sync operation to catch up on all changes
```

#### 3. Error Recovery
```
1. If CRUD operation fails, endCrudOperation() still called
2. Safety timeout (10 seconds) automatically resumes sync
3. Emergency resumeAllSyncs() method available
```

### Performance Benefits

#### Before Implementation:
- Initial load: **128,105ms (2+ minutes)**
- Sync conflicts during CRUD operations
- UI blocking during sync operations
- Memory pressure from concurrent operations

#### After Implementation:
- Initial load: **Expected <10 seconds**
- No sync conflicts during CRUD operations
- Non-blocking CRUD operations
- Reduced memory pressure
- Batch operations complete in seconds instead of minutes

### Usage Examples

#### 1. Basic Integration (Drop-in Replacement)

```jsx
// Before:
<ModuleTable
  collection="occupations"
  pouchDatabase={pouchDatabase}
  databasePrefix={databasePrefix}
  columns={columns}
/>

// After:
<SyncControlledModuleTable
  collection="occupations"
  pouchDatabase={pouchDatabase}
  databasePrefix={databasePrefix}
  columns={columns}
/>
```

#### 2. Manual Sync Control

```jsx
import syncController from '../Utils/SyncController';

const handleBulkImport = async (records) => {
  // Pause sync for bulk operation
  await syncController.pauseSync('prosy_m9xlo8hv_occupations', 'bulk_import');
  
  try {
    // Perform bulk operations
    for (const record of records) {
      await database.save(record);
    }
  } finally {
    // Resume sync
    await syncController.resumeSync('prosy_m9xlo8hv_occupations', 'bulk_import');
  }
};
```

#### 3. Using the React Hook

```jsx
import useSyncController from '../Utils/hooks/useSyncController';

const MyComponent = () => {
  const {
    createRecord,
    updateRecord,
    deleteRecord,
    bulkSaveRecords,
    isSyncPaused,
    isOperationInProgress
  } = useSyncController('prosy_m9xlo8hv_occupations', 'occupations');

  const handleCreate = async (data) => {
    const result = await createRecord(database, data);
    console.log('Created:', result);
  };

  return (
    <div>
      {isSyncPaused && <Alert message="Sync paused during operation" type="info" />}
      <button onClick={() => handleCreate(newData)}>Create Record</button>
    </div>
  );
};
```

### Configuration

#### Timeout Settings (in SyncService)
```javascript
this.settings = {
  timeout: 15000,                    // Remote sync timeout
  batchSize: 100,                    // Sync batch size
  maxRetries: 3,                     // Max retry attempts
  backgroundSyncInterval: 45000,     // Background sync interval (45s)
  crudSyncPauseTimeout: 5000        // Delay before resuming sync (5s)
};
```

#### Safety Features
- **10-second safety timeout**: Automatically resumes sync if `endCrudOperation()` not called
- **Error recovery**: Sync resumes even if CRUD operation fails
- **Emergency resume**: `resumeAllSyncs()` method for manual recovery

### Monitoring and Debugging

#### Check Sync Status
```javascript
const status = await syncController.getCrudOperationStatus();
console.log('Active CRUD operations:', status.activeCrudOperations);
console.log('Paused syncs:', status.pausedSyncs);
console.log('Pending timeouts:', status.pendingTimeouts);
```

#### Performance Monitoring
```javascript
// The system automatically logs:
// - CRUD operation start/end times
// - Sync pause/resume events
// - Performance metrics
// - Error recovery actions
```

### Migration Guide

#### For Existing Components:
1. Replace `ModuleTable` with `SyncControlledModuleTable`
2. No other changes required - fully backward compatible

#### For Custom CRUD Operations:
1. Import `syncController` or use `useSyncController` hook
2. Wrap operations with `executeCrudOperation()` or use helper methods
3. Use batch operations for multiple records

#### For Bulk Operations:
1. Use `executeBatchCrudOperations()` for optimal performance
2. Consider using `SyncControlledModuleTable.bulkOperations` helpers

### Expected Results

1. **Faster Initial Loading**: Reduced from 128+ seconds to <10 seconds
2. **No CRUD Conflicts**: Operations complete without sync interference
3. **Better User Experience**: Real-time sync status indicators
4. **Improved Performance**: Batch operations complete rapidly
5. **Reliable Sync**: Automatic recovery and error handling
