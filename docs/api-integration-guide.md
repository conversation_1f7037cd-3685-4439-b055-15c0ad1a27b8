# Free REST API Integration Guide

This guide provides recommendations for free REST APIs that can enhance your business management applications.

## 🚀 Quick Start APIs (Ready to Use)

### 1. Weather Integration
**Service**: OpenWeatherMap  
**Free Tier**: 60 calls/minute, 1,000 calls/day  
**Use Cases**: Hotel weather displays, garage location weather, property climate data

```javascript
// Already implemented in src/Services/WeatherService.js
import WeatherService from '../Services/WeatherService';

const weather = await WeatherService.getCurrentWeather('New York');
console.log(`Temperature: ${weather.temperature}°C`);
```

### 2. Currency Exchange
**Service**: ExchangeRate-API  
**Free Tier**: 1,500 requests/month  
**Use Cases**: Multi-currency pricing, loan calculations, property valuations

```javascript
// Already implemented in src/Services/CurrencyService.js
import CurrencyService from '../Services/CurrencyService';

const conversion = await CurrencyService.convertCurrency(100, 'USD', 'EUR');
console.log(`$100 USD = €${conversion.convertedAmount} EUR`);
```

### 3. QR Code Generation
**Service**: QR Server API  
**Free Tier**: Unlimited  
**Use Cases**: Digital receipts, contact cards, payment codes, WiFi sharing

```javascript
// Already implemented in src/Services/QRCodeService.js
import QRCodeService from '../Services/QRCodeService';

const qrUrl = QRCodeService.generateBusinessCardQR({
  firstName: 'John',
  lastName: 'Doe',
  email: '<EMAIL>',
  phone: '+1234567890'
});
```

## 📊 Business Enhancement APIs

### Finance & Economics
| API | Free Tier | Use Case | Apps |
|-----|-----------|----------|------|
| **Fixer.io** | 100 req/month | Currency rates | All apps |
| **Alpha Vantage** | 5 req/minute | Stock prices | Lenkit, Abacus |
| **CoinGecko** | Unlimited | Crypto prices | All apps |

### Location & Maps
| API | Free Tier | Use Case | Apps |
|-----|-----------|----------|------|
| **OpenWeatherMap** | 1K req/day | Weather data | Homz, ZenWrench |
| **Nominatim** | Unlimited | Geocoding | Prosy, Homz |
| **IP Geolocation** | 1K req/month | User location | All apps |

### Communication
| API | Free Tier | Use Case | Apps |
|-----|-----------|----------|------|
| **EmailJS** | 200 emails/month | Email sending | All apps |
| **Twilio** | Trial credits | SMS notifications | All apps |
| **Telegram Bot** | Unlimited | Notifications | Mission Control |

### Business Data
| API | Free Tier | Use Case | Apps |
|-----|-----------|----------|------|
| **Clearbit Logo** | 50 req/month | Company logos | Kyeyo, Mission Control |
| **Hunter.io** | 25 searches/month | Email finder | Kyeyo |
| **Company Enrichment** | 100 req/month | Business data | All apps |

## 🏢 Industry-Specific APIs

### Restaurant Management (Evia)
```javascript
// Food & Recipe APIs
const apis = {
  spoonacular: 'https://api.spoonacular.com', // 150 req/day
  edamam: 'https://api.edamam.com', // 5 req/minute
  openFoodFacts: 'https://world.openfoodfacts.org/api/v0' // Unlimited
};

// Menu QR codes for contactless dining
const menuQR = QRCodeService.generateURLQR('https://yourmenu.com/table/5');
```

### Hotel Management (Homz/InnControl)
```javascript
// Travel & Hospitality APIs
const apis = {
  amadeus: 'https://api.amadeus.com', // 2K req/month
  booking: 'https://distribution-xml.booking.com', // Limited
  tripadvisor: 'https://api.tripadvisor.com' // 500 req/day
};

// Weather widget for guests
const hotelWeather = await WeatherService.getCurrentWeather('Miami');
```

### Garage Management (ZenWrench/Kanify)
```javascript
// Automotive APIs
const apis = {
  nhtsa: 'https://vpic.nhtsa.dot.gov/api', // Unlimited
  kbb: 'https://api.kbb.com', // Limited
  vinDecoder: 'https://api.vindecoder.eu' // 50 req/month
};

// Vehicle information lookup
const vehicleInfo = await fetch(`https://vpic.nhtsa.dot.gov/api/vehicles/DecodeVin/${vin}?format=json`);
```

### Recruitment (Kyeyo)
```javascript
// Professional APIs
const apis = {
  github: 'https://api.github.com', // 5K req/hour
  linkedin: 'https://api.linkedin.com', // OAuth required
  indeed: 'https://api.indeed.com' // Partner access
};

// GitHub profile integration
const githubProfile = await fetch(`https://api.github.com/users/${username}`);
```

### Loan Management (Lenkit)
```javascript
// Financial calculation APIs
const apis = {
  exchangeRate: 'https://api.exchangerate-api.com', // 1.5K req/month
  bankHolidays: 'https://date.nager.at/api/v3', // Unlimited
  creditScore: 'https://api.credit.com' // Limited
};

// Interest rate calculations with currency conversion
const loanAmount = await CurrencyService.convertCurrency(10000, 'USD', 'EUR');
```

## 🔧 Implementation Examples

### Adding Weather to Hotel Dashboard
```javascript
// In your hotel dashboard component
import WeatherService from '../../Services/WeatherService';

const HotelDashboard = () => {
  const [weather, setWeather] = useState(null);
  
  useEffect(() => {
    const fetchWeather = async () => {
      const hotelLocation = 'Miami, FL';
      const weatherData = await WeatherService.getCurrentWeather(hotelLocation);
      setWeather(weatherData);
    };
    
    fetchWeather();
  }, []);

  return (
    <div className="weather-widget">
      {weather && (
        <div>
          <h3>Current Weather</h3>
          <p>{weather.temperature}°C - {weather.description}</p>
        </div>
      )}
    </div>
  );
};
```

### Multi-Currency Pricing
```javascript
// In your pricing component
import CurrencyService from '../../Services/CurrencyService';

const PriceDisplay = ({ amount, baseCurrency = 'USD' }) => {
  const [prices, setPrices] = useState({});
  
  useEffect(() => {
    const convertPrices = async () => {
      const currencies = ['EUR', 'GBP', 'CAD'];
      const conversions = {};
      
      for (const currency of currencies) {
        const result = await CurrencyService.convertCurrency(amount, baseCurrency, currency);
        conversions[currency] = result.convertedAmount;
      }
      
      setPrices(conversions);
    };
    
    convertPrices();
  }, [amount, baseCurrency]);

  return (
    <div className="price-display">
      <div className="base-price">${amount} {baseCurrency}</div>
      {Object.entries(prices).map(([currency, price]) => (
        <div key={currency} className="converted-price">
          {price} {currency}
        </div>
      ))}
    </div>
  );
};
```

## 🔑 Environment Variables Setup

Add these to your `.env` file:

```bash
# Weather API
REACT_APP_WEATHER_API_KEY=your_openweather_api_key

# Currency API
REACT_APP_CURRENCY_API_KEY=your_currency_api_key

# Email API (already configured)
REACT_APP_EMAILJS_SERVICE_ID=service_9k6dx3e
REACT_APP_EMAILJS_TEMPLATE_ID=template_35bswmr
REACT_APP_EMAILJS_PUBLIC_KEY=O_O7cqgzyd54Gm0gd

# Business APIs
REACT_APP_CLEARBIT_API_KEY=your_clearbit_key
REACT_APP_ABSTRACT_EMAIL_API_KEY=your_abstract_key
```

## 📈 Next Steps

1. **Sign up for API keys** for the services you want to use
2. **Test the provided services** with the implemented code
3. **Integrate into your specific apps** based on business needs
4. **Monitor usage** to stay within free tier limits
5. **Implement caching** to reduce API calls
6. **Add error handling** for API failures

## 🎯 Recommended Priority

1. **High Priority**: Weather, Currency, QR Codes (already implemented)
2. **Medium Priority**: Email validation, Business data APIs
3. **Low Priority**: Industry-specific APIs based on app needs

## 💡 Pro Tips

- **Cache API responses** to reduce calls
- **Implement fallbacks** for when APIs are down
- **Use environment variables** for API keys
- **Monitor rate limits** to avoid service interruption
- **Consider upgrading** to paid tiers for production use
