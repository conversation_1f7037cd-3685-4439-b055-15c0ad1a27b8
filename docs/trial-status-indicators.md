# Trial Status Indicators & Subscription Phases

This document outlines the trial status indicator system and explains the clear separation between trial and subscription periods.

## Overview

The trial status indicator system provides multiple placement options to show users:
- ✅ Current phase (trial vs subscription)
- ✅ Days remaining in current phase
- ✅ Clear separation between trial and subscription periods
- ✅ Quick access to subscription details
- ✅ Upgrade/subscription options

## Trial vs Subscription Period Separation

### **Important: Clear Phase Separation**

The system enforces a clear separation between trial and subscription periods:

#### **Trial Period** (Phase 1)
- **Duration**: Days 1 to `trial_period_days` (e.g., Days 1-30 if trial_period_days = 30)
- **Access**: Full access to ALL features regardless of plan restrictions
- **Status**: `trial`
- **Available Modules**: `['*']` (wildcard = all modules)

#### **Subscription Period** (Phase 2)
- **Duration**: Starts AFTER trial ends (e.g., Day 31 onwards)
- **Access**: Limited to features included in the subscription plan
- **Status**: `active`, `expired`, etc.
- **Available Modules**: Based on plan's `available_modules`

#### **No Overlap**
- Trial and subscription periods are **mutually exclusive**
- Subscription billing period starts AFTER trial period ends
- Clear transition from full access (trial) to plan-based access (subscription)

## Implementation Options

### 1. Header Status Badge (Recommended - Currently Implemented)

**Location**: Top header actions area, next to connection status and dark mode toggle
**File**: `src/Components/TrialStatusIndicator/index.js`

**Features**:
- ✅ Always visible in header
- ✅ Compact design (shows "15d" for 15 days remaining)
- ✅ Color-coded: Blue (normal), Orange (7 days or less), Red (expired)
- ✅ Click to view detailed modal
- ✅ Mobile and desktop responsive
- ✅ Non-intrusive but easily accessible

**Visual Examples**:
- Trial active: `🕐 15d` (blue tag)
- Trial ending: `⚠️ 3d` (orange tag)
- Expired: `❌ Exp` (red tag)
- No subscription: `⚠️ R/O` (orange tag)

### 2. Footer Progress Indicator (Alternative Option)

**Location**: Sidebar footer area
**File**: `src/Components/TrialStatusFooter/index.js`

**Features**:
- ✅ Shows trial progress bar
- ✅ Displays days remaining
- ✅ Adapts to collapsed/expanded sidebar
- ✅ Minimal when collapsed, detailed when expanded
- ✅ Visual progress representation

**Visual Examples**:
- Expanded: Progress bar with "Trial Period - 15 days left"
- Collapsed: Small icon with "15d"

### 3. Breadcrumb Area Indicator (Alternative Option)

**Location**: Page header/breadcrumb area
**File**: `src/Components/TrialStatusBreadcrumb/index.js`

**Features**:
- ✅ Appears in page content area
- ✅ Pulsing animation for urgent statuses
- ✅ Detailed text descriptions
- ✅ Click for modal details

**Visual Examples**:
- `🕐 Trial: 15 days left` (blue tag)
- `⚠️ Trial Ending Soon` (orange tag, pulsing)
- `❌ Subscription Expired` (red tag)

## Current Implementation

The **Header Status Badge** is currently implemented and active. It appears in:

### Desktop View
- Top header actions area
- Between connection status and dark mode toggle
- Compact tag format

### Mobile View
- Header actions (responsive)
- Maintains compact design for mobile screens

## Usage

The trial indicator automatically:

1. **Detects trial status** from subscription data
2. **Shows appropriate indicator** based on:
   - Trial days remaining
   - Subscription status
   - Urgency level (7 days or less = urgent)
3. **Provides click interaction** for detailed information
4. **Hides automatically** when:
   - No trial period exists
   - Subscription is fully active (non-trial)
   - User is in Mission Control (admin area)

## Customization Options

### Color Coding
- **Blue (#1890ff)**: Normal trial period (8+ days)
- **Orange (#faad14)**: Trial ending soon (≤7 days) or no subscription
- **Red (#ff4d4f)**: Expired subscription

### Display Modes
- **Compact**: Shows minimal text (e.g., "15d")
- **Full**: Shows descriptive text (e.g., "Trial: 15 days left")

### Responsive Behavior
- **Desktop**: Full feature set with tooltips
- **Mobile**: Optimized for touch interaction
- **Collapsed sidebar**: Minimal display

## Integration Points

### Layout Integration
```javascript
// In src/Components/Layout/index.js
import TrialStatusIndicator from "../TrialStatusIndicator";

// Added to desktop actions
<TrialStatusIndicator
  key="trial-status"
  pouchDatabase={pouchDatabase}
  databasePrefix={databasePrefix}
  organizationId={organizationId}
  compact={true}
/>

// Added to mobile actions
<TrialStatusIndicator
  key="trial-status-mobile"
  pouchDatabase={pouchDatabase}
  databasePrefix={databasePrefix}
  organizationId={organizationId}
  compact={true}
/>
```

### Subscription Service Integration
The indicators use the existing `SubscriptionService` to:
- Validate subscription status
- Check trial period status
- Calculate days remaining
- Determine access levels

## User Experience

### Trial Period Active
- User sees subtle blue indicator showing days remaining
- Full access to all features
- Gentle reminder without interruption

### Trial Ending Soon (≤7 days)
- Indicator changes to orange
- More prominent but not alarming
- Encourages subscription action

### Trial Expired/No Subscription
- Red indicator for expired, orange for no subscription
- Clear indication of limited access
- Direct path to subscription options

## Benefits

1. **Always Visible**: Users always know their trial status
2. **Non-Intrusive**: Doesn't interfere with normal workflow
3. **Informative**: Clear indication of time remaining and access level
4. **Actionable**: Direct access to subscription management
5. **Responsive**: Works across all device types
6. **Consistent**: Follows existing UI patterns and design language

## Future Enhancements

Potential improvements could include:
- Animated countdown for last 24 hours
- Integration with notification system
- Customizable reminder thresholds
- A/B testing for different indicator styles
- Analytics tracking for conversion optimization
