# Centralized Pricing and Subscription Management System

## Overview

This document describes the implementation of a centralized pricing and subscription management system that provides modular pricing enforcement across all applications in the platform.

## Architecture

### Core Components

1. **SubscriptionService** (`src/Services/SubscriptionService.js`)
   - Centralized subscription validation logic
   - Trial period management
   - Subscription status tracking
   - Module access validation

2. **ModuleAccessMiddleware** (`src/Middleware/ModuleAccessMiddleware.js`)
   - Subscription-based module filtering
   - Access control enforcement
   - Upgrade prompt generation
   - Graceful degradation for expired subscriptions

3. **FeatureRegistry** (`src/Services/FeatureRegistry.js`)
   - Central registry of all features across applications
   - Tier-based feature mapping
   - Application-specific feature definitions
   - Module-to-feature mapping

4. **Enhanced Data Models**
   - Updated Plans module with feature flags and tier support
   - Enhanced Subscriptions module with trial period tracking
   - New Installation Packages module for setup fee management

## Features

### Subscription Tiers

- **Bronze/Basic**: Core features and basic reporting
- **Silver/Professional**: Advanced features, API access, bulk operations
- **Gold/Enterprise**: AI insights, custom workflows, white labeling
- **Lifetime**: All features with permanent access

### Key Capabilities

- ✅ **Modular Access Control**: Filter modules based on subscription tier
- ✅ **Trial Period Management**: Automatic trial period tracking and validation
- ✅ **Graceful Degradation**: Limited access for expired subscriptions
- ✅ **Feature Registry**: Centralized feature definitions and mappings
- ✅ **Installation Packages**: Tiered installation fee management
- ✅ **Real-time Validation**: Dynamic subscription status checking
- ✅ **Upgrade Prompts**: Contextual upgrade suggestions

## Usage

### Basic Subscription Validation

```javascript
import SubscriptionService from '../Services/SubscriptionService';

const subscriptionService = new SubscriptionService(pouchDatabase, databasePrefix);

// Validate organization subscription
const validation = await subscriptionService.validateSubscription('org123');

if (validation.isValid) {
  console.log('Subscription is active');
  console.log('Available modules:', validation.availableModules);
  console.log('Days remaining:', validation.daysRemaining);
} else {
  console.log('Subscription issue:', validation.message);
}
```

### Module Access Control

```javascript
import ModuleAccessMiddleware from '../Middleware/ModuleAccessMiddleware';

const middleware = new ModuleAccessMiddleware(pouchDatabase, databasePrefix);

// Filter modules by subscription
const filteredModules = await middleware.filterModulesBySubscription(
  allModules,
  organizationId,
  { appName: 'prosy' }
);

// Check specific module access
const accessResult = await middleware.checkModuleAccess('analytics', organizationId);
if (!accessResult.hasAccess) {
  const upgradePrompt = await middleware.getUpgradePrompt('analytics', organizationId);
  // Show upgrade prompt to user
}
```

### Feature Registry Usage

```javascript
import FeatureRegistry from '../Services/FeatureRegistry';

// Get features for a specific tier and application
const features = FeatureRegistry.getFeaturesForApplication('prosy', 'silver');

// Get modules allowed for a tier
const allowedModules = FeatureRegistry.getModulesForTier('gold', 'abacus');

// Check feature availability
const hasFeature = FeatureRegistry.isFeatureAvailableForTier('ai_insights', 'bronze');
```

## Data Models

### Plans Schema

```javascript
{
  title: "Professional Plan",
  software: { value: "software_id", label: "Software Name" },
  type: "months", // days, weeks, months, years, one_time
  price: 99.99,
  users: 10,
  installation_fee: 199.99,
  tier: "silver", // bronze, silver, gold, lifetime
  available_modules: "invoices,reports,analytics" // comma-separated
}
```

### Subscriptions Schema

```javascript
{
  organization: { value: "org_id", label: "Organization Name" },
  plan: { value: "plan_id", label: "Plan Title" },
  quantity: 12, // billing periods
  start: "2024-01-01T00:00:00.000Z",
  end: "2024-12-31T23:59:59.999Z", // optional explicit end date
  trial_period_days: 30,
  payment_status: "paid", // pending, paid, failed, cancelled
  status: "active" // trial, active, expired, cancelled
}
```

### Installation Packages Schema

```javascript
{
  name: "Premium Setup",
  software: { value: "software_id", label: "Software Name" },
  package_type: "premium", // basic, standard, premium
  installation_fee: 499.99,
  setup_time_hours: 8,
  included_services: "data_migration,training,customization",
  training_hours: 4,
  support_period_days: 90,
  is_active: true
}
```

## Integration Points

### AppLoader Integration

The AppLoader component automatically applies subscription filtering:

```javascript
// Subscription-aware modules with access control
const [subscriptionFilteredModules, setSubscriptionFilteredModules] = useState(null);

useEffect(() => {
  const applySubscriptionFiltering = async () => {
    const middleware = new ModuleAccessMiddleware(DB, DB_PREFIX);
    const filteredModules = await middleware.filterModulesBySubscription(
      memoizedModulesWithConfigs,
      userOrganization._id,
      { appName: APP_PATH_NAME }
    );
    setSubscriptionFilteredModules(filteredModules);
  };

  applySubscriptionFiltering();
}, [memoizedModulesWithConfigs, userOrganization, DB_PREFIX, APP_PATH_NAME]);
```

### Mission Control Management

The Mission Control app provides subscription management through:

- **Subscription Manager UI**: Create, edit, and monitor subscriptions
- **Plans Management**: Configure pricing tiers and features
- **Installation Packages**: Manage setup fees and services
- **Analytics Dashboard**: Track subscription metrics

## Testing

### Running Tests

```bash
# Run all pricing system tests
npm run test:pricing

# Run tests in watch mode
npm run test:pricing:watch

# Run specific test suites
npm test -- --testPathPattern=SubscriptionService
npm test -- --testPathPattern=ModuleAccessMiddleware
npm test -- --testPathPattern=FeatureRegistry
```

### Test Coverage

The system includes comprehensive tests for:

- ✅ Subscription validation logic
- ✅ Trial period calculations
- ✅ Module access control
- ✅ Feature registry operations
- ✅ Tier-based access rules
- ✅ Error handling scenarios
- ✅ Integration workflows

## Configuration

### Environment Variables

```bash
# Optional: Override default trial period
REACT_APP_DEFAULT_TRIAL_DAYS=30

# Optional: Enable debug logging
REACT_APP_PRICING_DEBUG=true
```

### Feature Registry Configuration

Features are automatically initialized but can be customized:

```javascript
// Register custom feature
FeatureRegistry.registerFeature('custom_feature', {
  name: 'Custom Feature',
  description: 'Application-specific feature',
  category: 'custom',
  tier: 'silver',
  applications: ['my_app']
});

// Export/Import feature definitions
const exportData = FeatureRegistry.export();
FeatureRegistry.import(exportData);
```

## Migration Guide

### For Existing Applications

1. **Update AppLoader**: The AppLoader automatically applies subscription filtering
2. **Add Module Properties**: Ensure modules have proper tier mappings
3. **Configure Plans**: Update existing plans with tier and module information
4. **Test Access Control**: Verify module filtering works correctly

### For New Applications

1. **Define Features**: Register application-specific features in FeatureRegistry
2. **Configure Tiers**: Map features to appropriate subscription tiers
3. **Set Module Mappings**: Define which modules are enabled by each feature
4. **Test Integration**: Verify subscription filtering and access control

## Monitoring and Analytics

### Subscription Metrics

The system tracks:

- Active subscriptions by tier
- Trial conversion rates
- Feature usage by tier
- Upgrade opportunities
- Subscription renewals

### Performance Monitoring

- Module filtering performance
- Database query optimization
- Cache hit rates for subscription validation
- Error rates and fallback scenarios

## Security Considerations

- **Client-side Validation**: UI filtering only - server-side validation required for security
- **Data Encryption**: Subscription data should be encrypted in transit and at rest
- **Access Logging**: Log subscription access attempts for audit trails
- **Rate Limiting**: Implement rate limiting for subscription validation calls

## Troubleshooting

### Common Issues

1. **Modules Not Filtering**: Check organization ID and subscription status
2. **Trial Period Issues**: Verify trial_period_days field in subscription
3. **Feature Registry Errors**: Ensure features are properly registered
4. **Performance Issues**: Check subscription validation caching

### Debug Mode

Enable debug logging:

```javascript
localStorage.setItem('PRICING_DEBUG', 'true');
```

This will log detailed information about:
- Subscription validation results
- Module filtering decisions
- Feature registry lookups
- Access control decisions

## Future Enhancements

### Planned Features

- **Usage-based Billing**: Track feature usage for metered billing
- **Custom Plans**: Allow organizations to create custom subscription plans
- **API Rate Limiting**: Implement tier-based API rate limits
- **Advanced Analytics**: Detailed subscription and usage analytics
- **Automated Billing**: Integration with payment processors
- **Multi-tenant Support**: Enhanced organization isolation

### Extensibility

The system is designed for easy extension:

- **New Tiers**: Add additional subscription tiers
- **Custom Features**: Register application-specific features
- **Integration Points**: Add new middleware hooks
- **Validation Rules**: Extend subscription validation logic

## Support

For questions or issues with the pricing system:

1. Check the troubleshooting section above
2. Review test cases for usage examples
3. Examine the source code documentation
4. Contact the development team for assistance

## Changelog

### Version 1.0.0 (Initial Release)

- ✅ Core subscription validation service
- ✅ Module access middleware
- ✅ Feature registry system
- ✅ Enhanced data models
- ✅ Mission Control integration
- ✅ Comprehensive test suite
- ✅ Documentation and examples
