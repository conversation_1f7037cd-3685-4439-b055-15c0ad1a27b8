const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

function initAndroidApp(appName, packageId) {
    try {
        // Create android-apps directory if it doesn't exist
        if (!fs.existsSync('android-apps')) {
            fs.mkdirSync('android-apps');
        }

        // Initialize Tauri Android project
        console.log('Initializing Tauri Android project...');
        execSync('cargo tauri android init', { stdio: 'inherit' });

        // Create the target directory
        const targetDir = path.join('android-apps', appName);
        if (!fs.existsSync(targetDir)) {
            fs.mkdirSync(targetDir, { recursive: true });
        }

        // Move files from src-tauri/gen/android to target directory
        console.log(`Moving files to ${targetDir}...`);
        if (fs.existsSync('src-tauri/gen/android')) {
            fs.cpSync('src-tauri/gen/android', targetDir, { recursive: true });
            fs.rmSync('src-tauri/gen/android', { recursive: true, force: true });
        }

        // Update build.gradle.kts
        const buildGradlePath = path.join(targetDir, 'app/build.gradle.kts');
        if (fs.existsSync(buildGradlePath)) {
            let buildGradle = fs.readFileSync(buildGradlePath, 'utf8');
            buildGradle = buildGradle.replace(
                /namespace = ".*"/,
                `namespace = "${packageId}"`
            ).replace(
                /applicationId = ".*"/,
                `applicationId = "${packageId}"`
            );
            fs.writeFileSync(buildGradlePath, buildGradle);
            console.log('Updated build.gradle.kts');
        }

        // Update strings.xml
        const stringsXmlPath = path.join(targetDir, 'app/src/main/res/values/strings.xml');
        if (fs.existsSync(stringsXmlPath)) {
            let stringsXml = `<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string name="app_name">${appName}</string>
    <string name="custom_url_scheme">${packageId}</string>
</resources>`;
            fs.writeFileSync(stringsXmlPath, stringsXml);
            console.log('Updated strings.xml');
        }

        console.log(`\nAndroid project initialized successfully in android-apps/${appName}`);
        console.log(`Package ID: ${packageId}`);
        console.log('\nYou can now run:');
        console.log('cargo tauri android dev    # for development');
        console.log('cargo tauri android build  # for production build');

    } catch (error) {
        console.error('Error initializing Android project:', error);
        process.exit(1);
    }
}

// Get command line arguments
const args = process.argv.slice(2);
if (args.length < 2) {
    console.log('Usage: node init-android.js <app-name> <package-id>');
    console.log('Example: node init-android.js abacus net.haclab.abacus');
    process.exit(1);
}

const [appName, packageId] = args;
initAndroidApp(appName, packageId);