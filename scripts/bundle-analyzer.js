// PERFORMANCE FIX: Bundle analyzer for detailed optimization insights
const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

class BundleAnalyzer {
  constructor() {
    this.buildDir = path.join(__dirname, '../build');
    this.staticDir = path.join(this.buildDir, 'static');
    this.reportDir = path.join(this.buildDir, 'reports');
  }

  // PERFORMANCE FIX: Comprehensive bundle analysis
  async analyze() {
    console.log('🔍 Starting comprehensive bundle analysis...');
    
    if (!fs.existsSync(this.staticDir)) {
      console.error('❌ Build directory not found. Run build first.');
      return;
    }

    // Create reports directory
    if (!fs.existsSync(this.reportDir)) {
      fs.mkdirSync(this.reportDir, { recursive: true });
    }

    const analysis = {
      timestamp: new Date().toISOString(),
      summary: await this.generateSummary(),
      chunks: await this.analyzeChunks(),
      dependencies: await this.analyzeDependencies(),
      recommendations: await this.generateRecommendations(),
    };

    // Save detailed report
    fs.writeFileSync(
      path.join(this.reportDir, 'bundle-analysis.json'),
      JSON.stringify(analysis, null, 2)
    );

    // Generate HTML report
    await this.generateHTMLReport(analysis);

    console.log('📊 Bundle analysis completed!');
    console.log(`📋 Reports saved to: ${this.reportDir}`);
    
    return analysis;
  }

  // PERFORMANCE FIX: Generate bundle summary
  async generateSummary() {
    const jsDir = path.join(this.staticDir, 'js');
    const cssDir = path.join(this.staticDir, 'css');
    
    let totalJS = 0;
    let totalCSS = 0;
    let chunkCount = 0;

    if (fs.existsSync(jsDir)) {
      const jsFiles = fs.readdirSync(jsDir).filter(f => f.endsWith('.js'));
      chunkCount = jsFiles.length;
      totalJS = jsFiles.reduce((sum, file) => {
        return sum + fs.statSync(path.join(jsDir, file)).size;
      }, 0);
    }

    if (fs.existsSync(cssDir)) {
      const cssFiles = fs.readdirSync(cssDir).filter(f => f.endsWith('.css'));
      totalCSS = cssFiles.reduce((sum, file) => {
        return sum + fs.statSync(path.join(cssDir, file)).size;
      }, 0);
    }

    return {
      totalSize: totalJS + totalCSS,
      totalSizeMB: ((totalJS + totalCSS) / 1024 / 1024).toFixed(2),
      jsSize: totalJS,
      jsSizeMB: (totalJS / 1024 / 1024).toFixed(2),
      cssSize: totalCSS,
      cssSizeKB: (totalCSS / 1024).toFixed(2),
      chunkCount,
    };
  }

  // PERFORMANCE FIX: Analyze individual chunks
  async analyzeChunks() {
    const jsDir = path.join(this.staticDir, 'js');
    const chunks = [];

    if (!fs.existsSync(jsDir)) return chunks;

    const jsFiles = fs.readdirSync(jsDir).filter(f => f.endsWith('.js'));
    
    jsFiles.forEach(file => {
      const filePath = path.join(jsDir, file);
      const stats = fs.statSync(filePath);
      
      chunks.push({
        name: file,
        size: stats.size,
        sizeKB: (stats.size / 1024).toFixed(2),
        sizeMB: (stats.size / 1024 / 1024).toFixed(2),
        type: this.getChunkType(file),
        isLarge: stats.size > 1024 * 1024, // > 1MB
        isMain: file.includes('main'),
      });
    });

    return chunks.sort((a, b) => b.size - a.size);
  }

  // PERFORMANCE FIX: Analyze dependencies
  async analyzeDependencies() {
    const packageJsonPath = path.join(__dirname, '../package.json');
    
    if (!fs.existsSync(packageJsonPath)) {
      return { error: 'package.json not found' };
    }

    const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
    const dependencies = packageJson.dependencies || {};
    
    // Categorize dependencies by likely impact on bundle size
    const heavyDeps = [];
    const mediumDeps = [];
    const lightDeps = [];

    const heavyLibraries = [
      'antd', '@ant-design', 'recharts', 'puppeteer', 'canvas',
      'jspdf', 'pdf-lib', '@react-pdf', 'pouchdb', 'moment',
      'lodash', 'axios', 'react-beautiful-dnd'
    ];

    const mediumLibraries = [
      'react', 'react-dom', 'react-router', 'redux', 'zustand',
      'date-fns', 'numeral', 'papaparse', 'xlsx'
    ];

    Object.keys(dependencies).forEach(dep => {
      const version = dependencies[dep];
      
      if (heavyLibraries.some(heavy => dep.includes(heavy))) {
        heavyDeps.push({ name: dep, version });
      } else if (mediumLibraries.some(medium => dep.includes(medium))) {
        mediumDeps.push({ name: dep, version });
      } else {
        lightDeps.push({ name: dep, version });
      }
    });

    return {
      total: Object.keys(dependencies).length,
      heavy: heavyDeps,
      medium: mediumDeps,
      light: lightDeps,
    };
  }

  // PERFORMANCE FIX: Generate optimization recommendations
  async generateRecommendations() {
    const summary = await this.generateSummary();
    const chunks = await this.analyzeChunks();
    const deps = await this.analyzeDependencies();
    
    const recommendations = [];

    // Bundle size recommendations
    if (parseFloat(summary.totalSizeMB) > 10) {
      recommendations.push({
        type: 'critical',
        category: 'Bundle Size',
        issue: 'Very large bundle size (>10MB)',
        solution: 'Implement aggressive code splitting and lazy loading',
        impact: 'high'
      });
    } else if (parseFloat(summary.totalSizeMB) > 5) {
      recommendations.push({
        type: 'warning',
        category: 'Bundle Size',
        issue: 'Large bundle size (>5MB)',
        solution: 'Consider code splitting for non-critical features',
        impact: 'medium'
      });
    }

    // Large chunk recommendations
    const largeChunks = chunks.filter(chunk => chunk.isLarge);
    if (largeChunks.length > 0) {
      recommendations.push({
        type: 'warning',
        category: 'Code Splitting',
        issue: `${largeChunks.length} chunks are larger than 1MB`,
        solution: 'Split large chunks using dynamic imports',
        impact: 'medium',
        details: largeChunks.map(c => `${c.name}: ${c.sizeMB}MB`)
      });
    }

    // Dependency recommendations
    if (deps.heavy && deps.heavy.length > 10) {
      recommendations.push({
        type: 'info',
        category: 'Dependencies',
        issue: 'Many heavy dependencies detected',
        solution: 'Consider lighter alternatives or lazy loading',
        impact: 'medium'
      });
    }

    // Main chunk recommendations
    const mainChunk = chunks.find(chunk => chunk.isMain);
    if (mainChunk && parseFloat(mainChunk.sizeMB) > 5) {
      recommendations.push({
        type: 'critical',
        category: 'Main Chunk',
        issue: `Main chunk is very large (${mainChunk.sizeMB}MB)`,
        solution: 'Move vendor libraries to separate chunks',
        impact: 'high'
      });
    }

    return recommendations;
  }

  // PERFORMANCE FIX: Generate HTML report
  async generateHTMLReport(analysis) {
    const htmlContent = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bundle Analysis Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .summary { background: #f5f5f5; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
        .chunk { background: white; border: 1px solid #ddd; padding: 15px; margin: 10px 0; border-radius: 4px; }
        .large { border-left: 4px solid #ff4d4f; }
        .medium { border-left: 4px solid #faad14; }
        .small { border-left: 4px solid #52c41a; }
        .recommendation { padding: 10px; margin: 5px 0; border-radius: 4px; }
        .critical { background: #fff2f0; border-left: 4px solid #ff4d4f; }
        .warning { background: #fffbe6; border-left: 4px solid #faad14; }
        .info { background: #f6ffed; border-left: 4px solid #52c41a; }
        table { width: 100%; border-collapse: collapse; }
        th, td { padding: 8px; text-align: left; border-bottom: 1px solid #ddd; }
    </style>
</head>
<body>
    <h1>Bundle Analysis Report</h1>
    <p>Generated: ${analysis.timestamp}</p>
    
    <div class="summary">
        <h2>Summary</h2>
        <p><strong>Total Bundle Size:</strong> ${analysis.summary.totalSizeMB} MB</p>
        <p><strong>JavaScript:</strong> ${analysis.summary.jsSizeMB} MB</p>
        <p><strong>CSS:</strong> ${analysis.summary.cssSizeKB} KB</p>
        <p><strong>Total Chunks:</strong> ${analysis.summary.chunkCount}</p>
    </div>

    <h2>Chunks</h2>
    ${analysis.chunks.map(chunk => `
        <div class="chunk ${chunk.isLarge ? 'large' : chunk.size > 500000 ? 'medium' : 'small'}">
            <strong>${chunk.name}</strong> (${chunk.type})
            <br>Size: ${chunk.sizeMB} MB
        </div>
    `).join('')}

    <h2>Recommendations</h2>
    ${analysis.recommendations.map(rec => `
        <div class="recommendation ${rec.type}">
            <strong>${rec.category}:</strong> ${rec.issue}
            <br><strong>Solution:</strong> ${rec.solution}
            <br><strong>Impact:</strong> ${rec.impact}
            ${rec.details ? `<br><strong>Details:</strong> ${rec.details.join(', ')}` : ''}
        </div>
    `).join('')}
</body>
</html>`;

    fs.writeFileSync(path.join(this.reportDir, 'bundle-report.html'), htmlContent);
  }

  getChunkType(filename) {
    if (filename.includes('main')) return 'Main';
    if (filename.includes('vendor')) return 'Vendor';
    if (filename.includes('runtime')) return 'Runtime';
    if (filename.includes('antd')) return 'Antd';
    if (filename.includes('react')) return 'React';
    if (filename.includes('charts')) return 'Charts';
    if (filename.includes('documents')) return 'Documents';
    if (filename.includes('database')) return 'Database';
    if (filename.includes('utils')) return 'Utils';
    return 'Feature';
  }
}

// Run if called directly
if (require.main === module) {
  const analyzer = new BundleAnalyzer();
  analyzer.analyze().catch(console.error);
}

module.exports = BundleAnalyzer;
