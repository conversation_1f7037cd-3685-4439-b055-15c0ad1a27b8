const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

class DifferentialReleaseManager {
  constructor() {
    this.buildDir = 'build';
    this.distDir = 'dist';
  }

  async createDifferentialRelease() {
    console.log('🚀 Creating differential release...');
    
    // Step 1: Build the app
    console.log('📦 Building application...');
    execSync('yarn build', { stdio: 'inherit' });
    
    // Step 2: Generate file hashes for change detection
    console.log('🔍 Generating file hashes...');
    await this.generateFileHashes();
    
    // Step 3: Create the release with differential support
    console.log('🏗️  Building Electron app with differential support...');
    execSync('electron-builder --windows --publish always --config.nsis.differentialPackage=true', {
      stdio: 'inherit',
      env: {
        ...process.env,
        GH_TOKEN: process.env.GH_TOKEN || process.env.GITHUB_TOKEN
      }
    });
    
    console.log('✅ Differential release created successfully!');
  }

  async generateFileHashes() {
    const crypto = require('crypto');
    const hashes = {};
    
    const generateHash = (filePath) => {
      const content = fs.readFileSync(filePath);
      return crypto.createHash('sha256').update(content).digest('hex');
    };
    
    const walkDir = (dir) => {
      const files = fs.readdirSync(dir);
      files.forEach(file => {
        const filePath = path.join(dir, file);
        const stat = fs.statSync(filePath);
        
        if (stat.isDirectory()) {
          walkDir(filePath);
        } else {
          const relativePath = path.relative(this.buildDir, filePath);
          hashes[relativePath] = generateHash(filePath);
        }
      });
    };
    
    if (fs.existsSync(this.buildDir)) {
      walkDir(this.buildDir);
    }
    
    // Ensure dist directory exists
    if (!fs.existsSync(this.distDir)) {
      fs.mkdirSync(this.distDir, { recursive: true });
    }
    
    // Save hashes for future comparison
    fs.writeFileSync(
      path.join(this.distDir, 'file-hashes.json'),
      JSON.stringify(hashes, null, 2)
    );
    
    console.log(`📋 Generated hashes for ${Object.keys(hashes).length} files`);
  }

  async validateDifferentialSetup() {
    console.log('🔍 Validating differential update setup...');
    
    const checks = [
      {
        name: 'AutoUpdater differential config',
        check: () => {
          const autoUpdaterPath = path.join('electron', 'AutoUpdater.js');
          if (!fs.existsSync(autoUpdaterPath)) return false;
          const content = fs.readFileSync(autoUpdaterPath, 'utf8');
          return content.includes('disableDifferentialDownload = false') &&
                 content.includes('differentialDownloadOptions');
        }
      },
      {
        name: 'Package.json differential scripts',
        check: () => {
          const packagePath = 'package.json';
          if (!fs.existsSync(packagePath)) return false;
          const content = fs.readFileSync(packagePath, 'utf8');
          return content.includes('release:differential');
        }
      },
      {
        name: 'NSIS differential package config',
        check: () => {
          const packagePath = 'package.json';
          if (!fs.existsSync(packagePath)) return false;
          const pkg = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
          return pkg.build?.nsis?.differentialPackage === true;
        }
      }
    ];

    let allPassed = true;
    checks.forEach(({ name, check }) => {
      const passed = check();
      console.log(`   ${passed ? '✅' : '❌'} ${name}`);
      if (!passed) allPassed = false;
    });

    if (allPassed) {
      console.log('✅ All differential update checks passed!');
    } else {
      console.log('❌ Some differential update checks failed. Please review the setup.');
    }

    return allPassed;
  }
}

// Run if called directly
if (require.main === module) {
  const manager = new DifferentialReleaseManager();
  
  // Check if we should validate or create release
  const args = process.argv.slice(2);
  if (args.includes('--validate')) {
    manager.validateDifferentialSetup().catch(console.error);
  } else {
    manager.createDifferentialRelease().catch(console.error);
  }
}

module.exports = DifferentialReleaseManager;
