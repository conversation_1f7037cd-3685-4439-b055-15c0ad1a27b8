/**
 * Enhanced Development Server with Multi-App Support
 * Provides hot app switching, parallel development, and enhanced debugging
 */

const express = require('express');
const { createProxyMiddleware } = require('http-proxy-middleware');
const chokidar = require('chokidar');
const WebSocket = require('ws');
const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

const { switchToApp, listApps, getCurrentAppName, appsConfig } = require('../initApp');

class MultiAppDevServer {
  constructor() {
    this.app = express();
    this.port = process.env.DEV_PORT || 3000;
    this.wsPort = process.env.WS_PORT || 3001;
    this.currentApp = getCurrentAppName();
    this.runningApps = new Map(); // Track running app instances
    this.watchers = new Map(); // File watchers for each app
    this.setupWebSocket();
    this.setupRoutes();
    this.setupFileWatchers();
  }

  setupWebSocket() {
    this.wss = new WebSocket.Server({ port: this.wsPort });
    console.log(`🔌 WebSocket server running on port ${this.wsPort}`);

    this.wss.on('connection', (ws) => {
      console.log('📱 Client connected to dev server');
      
      // Send current app info
      ws.send(JSON.stringify({
        type: 'APP_INFO',
        currentApp: this.currentApp,
        availableApps: Object.keys(appsConfig)
      }));

      ws.on('message', (message) => {
        try {
          const data = JSON.parse(message);
          this.handleWebSocketMessage(data, ws);
        } catch (error) {
          console.error('❌ WebSocket message error:', error);
        }
      });
    });
  }

  handleWebSocketMessage(data, ws) {
    switch (data.type) {
      case 'SWITCH_APP':
        this.switchApp(data.appName, ws);
        break;
      case 'GET_APP_STATUS':
        this.sendAppStatus(ws);
        break;
      case 'START_PARALLEL_DEV':
        this.startParallelDevelopment(data.apps, ws);
        break;
      case 'STOP_APP':
        this.stopApp(data.appName, ws);
        break;
    }
  }

  setupRoutes() {
    // API endpoint for app switching
    this.app.post('/api/switch-app', (req, res) => {
      const { appName } = req.body;
      if (switchToApp(appName)) {
        this.currentApp = appName;
        this.broadcastToClients({
          type: 'APP_SWITCHED',
          newApp: appName
        });
        res.json({ success: true, currentApp: appName });
      } else {
        res.status(400).json({ success: false, error: 'Failed to switch app' });
      }
    });

    // API endpoint for app list
    this.app.get('/api/apps', (req, res) => {
      res.json({
        current: this.currentApp,
        available: appsConfig,
        running: Array.from(this.runningApps.keys())
      });
    });

    // API endpoint for app-specific development tools
    this.app.get('/api/dev-tools/:appName', (req, res) => {
      const { appName } = req.params;
      const appPath = path.join(__dirname, '..', 'src', 'Apps', appName);
      
      if (!fs.existsSync(appPath)) {
        return res.status(404).json({ error: 'App not found' });
      }

      // Scan for development-specific files
      const devTools = this.scanDevTools(appPath);
      res.json(devTools);
    });

    // Proxy to React dev server
    this.app.use('/', createProxyMiddleware({
      target: 'http://localhost:3000',
      changeOrigin: true,
      ws: true
    }));
  }

  setupFileWatchers() {
    // Watch for changes in app-specific files
    Object.keys(appsConfig).forEach(appName => {
      const appPath = path.join(__dirname, '..', 'src', 'Apps', appName);
      
      if (fs.existsSync(appPath)) {
        const watcher = chokidar.watch(appPath, {
          ignored: /node_modules/,
          persistent: true
        });

        watcher.on('change', (filePath) => {
          this.handleFileChange(appName, filePath);
        });

        this.watchers.set(appName, watcher);
      }
    });
  }

  handleFileChange(appName, filePath) {
    console.log(`📝 File changed in ${appName}: ${path.basename(filePath)}`);
    
    this.broadcastToClients({
      type: 'FILE_CHANGED',
      appName,
      filePath: path.relative(process.cwd(), filePath),
      timestamp: Date.now()
    });

    // Auto-reload if it's the current app
    if (appName === this.currentApp) {
      this.broadcastToClients({
        type: 'RELOAD_REQUIRED',
        reason: 'File change in current app'
      });
    }
  }

  async switchApp(appName, ws) {
    try {
      console.log(`🔄 Switching to app: ${appName}`);
      
      if (switchToApp(appName)) {
        this.currentApp = appName;
        
        ws.send(JSON.stringify({
          type: 'APP_SWITCH_SUCCESS',
          newApp: appName,
          config: appsConfig[appName]
        }));

        this.broadcastToClients({
          type: 'APP_SWITCHED',
          newApp: appName
        });

        console.log(`✅ Successfully switched to ${appName}`);
      } else {
        ws.send(JSON.stringify({
          type: 'APP_SWITCH_ERROR',
          error: 'Failed to switch app'
        }));
      }
    } catch (error) {
      console.error('❌ App switch error:', error);
      ws.send(JSON.stringify({
        type: 'APP_SWITCH_ERROR',
        error: error.message
      }));
    }
  }

  startParallelDevelopment(apps, ws) {
    console.log(`🚀 Starting parallel development for: ${apps.join(', ')}`);
    
    apps.forEach(appName => {
      if (!this.runningApps.has(appName)) {
        this.startAppInstance(appName);
      }
    });

    ws.send(JSON.stringify({
      type: 'PARALLEL_DEV_STARTED',
      apps,
      runningApps: Array.from(this.runningApps.keys())
    }));
  }

  startAppInstance(appName) {
    const port = 3000 + Object.keys(appsConfig).indexOf(appName) + 1;
    
    console.log(`🚀 Starting ${appName} on port ${port}`);
    
    // This would start a separate React dev server for each app
    // Implementation depends on your build system
    const process = spawn('npm', ['start'], {
      env: { ...process.env, PORT: port, CURRENT_APP: appName },
      stdio: 'pipe'
    });

    this.runningApps.set(appName, { process, port });
    
    process.stdout.on('data', (data) => {
      console.log(`[${appName}] ${data}`);
    });

    process.on('exit', () => {
      this.runningApps.delete(appName);
      console.log(`🛑 ${appName} instance stopped`);
    });
  }

  stopApp(appName, ws) {
    if (this.runningApps.has(appName)) {
      const { process } = this.runningApps.get(appName);
      process.kill();
      this.runningApps.delete(appName);
      
      ws.send(JSON.stringify({
        type: 'APP_STOPPED',
        appName
      }));
    }
  }

  scanDevTools(appPath) {
    const tools = {
      hasTests: fs.existsSync(path.join(appPath, '__tests__')),
      hasStorybook: fs.existsSync(path.join(appPath, 'stories')),
      hasDocumentation: fs.existsSync(path.join(appPath, 'README.md')),
      hasConstants: fs.existsSync(path.join(appPath, 'constants')),
      hasUtils: fs.existsSync(path.join(appPath, 'utils')),
      moduleCount: 0,
      componentCount: 0
    };

    // Count modules and components
    const modulesPath = path.join(appPath, 'modules');
    if (fs.existsSync(modulesPath)) {
      tools.moduleCount = fs.readdirSync(modulesPath).length;
    }

    const componentsPath = path.join(appPath, 'CustomViews');
    if (fs.existsSync(componentsPath)) {
      tools.componentCount = fs.readdirSync(componentsPath).length;
    }

    return tools;
  }

  sendAppStatus(ws) {
    ws.send(JSON.stringify({
      type: 'APP_STATUS',
      currentApp: this.currentApp,
      runningApps: Array.from(this.runningApps.entries()).map(([name, info]) => ({
        name,
        port: info.port,
        status: 'running'
      })),
      availableApps: Object.keys(appsConfig)
    }));
  }

  broadcastToClients(message) {
    this.wss.clients.forEach(client => {
      if (client.readyState === WebSocket.OPEN) {
        client.send(JSON.stringify(message));
      }
    });
  }

  start() {
    this.app.listen(this.port, () => {
      console.log(`🚀 Multi-App Dev Server running on port ${this.port}`);
      console.log(`📱 Current app: ${this.currentApp}`);
      console.log(`🔧 Available commands:`);
      console.log(`   - POST /api/switch-app { "appName": "appName" }`);
      console.log(`   - GET /api/apps`);
      console.log(`   - WebSocket on port ${this.wsPort}`);
    });
  }

  stop() {
    // Clean up watchers
    this.watchers.forEach(watcher => watcher.close());
    
    // Stop running app instances
    this.runningApps.forEach(({ process }) => process.kill());
    
    // Close WebSocket server
    this.wss.close();
    
    console.log('🛑 Multi-App Dev Server stopped');
  }
}

// Export for use as module
module.exports = MultiAppDevServer;

// CLI usage
if (require.main === module) {
  const server = new MultiAppDevServer();
  server.start();

  // Graceful shutdown
  process.on('SIGINT', () => {
    console.log('\n🛑 Shutting down dev server...');
    server.stop();
    process.exit(0);
  });
}
