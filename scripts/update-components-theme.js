/**
 * Update Components to Use Dynamic Theme Configuration
 * This script updates existing components to use the centralized theme system
 */

const fs = require('fs');
const path = require('path');

class ComponentThemeUpdater {
  constructor() {
    this.componentsToUpdate = [
      'src/Components/AppLoader.js',
      'src/Components/Layout/index.js',
      'src/Components/Dashboard/OrganizationInformation.js',
      'src/Components/Stats/StatsGrid.js'
    ];
    
    this.appConstantsFiles = [
      'src/Apps/abacus/constants/index.js',
      'src/Apps/evia/constants/index.js',
      'src/Apps/homz/constants/index.js',
      'src/Apps/inncontrol/constants/index.js',
      'src/Apps/kanify/constants/index.js',
      'src/Apps/kyeyo/constants/index.js',
      'src/Apps/lenkit/constants/index.js',
      'src/Apps/mission-control/constants/index.js',
      'src/Apps/prosy/constants/index.js',
      'src/Apps/smart/constants/index.js',
      'src/Apps/zenwrench/constants/index.js'
    ];
  }

  updateAllComponents() {
    console.log('🔄 Updating components to use dynamic theme configuration...');
    
    // Update main components
    this.componentsToUpdate.forEach(componentPath => {
      this.updateComponent(componentPath);
    });
    
    // Update app constants files
    this.updateAppConstants();
    
    console.log('✅ All components updated successfully!');
  }

  updateComponent(componentPath) {
    const fullPath = path.join(__dirname, '..', componentPath);
    
    if (!fs.existsSync(fullPath)) {
      console.warn(`⚠️  Component not found: ${componentPath}`);
      return;
    }

    try {
      let content = fs.readFileSync(fullPath, 'utf8');
      
      // Add import for dynamic theme
      if (!content.includes('import appTheme')) {
        const importStatement = `import appTheme, { getCurrentAppColor, APP_COLORS, APP_THEME } from '../config/appTheme';\n`;
        
        // Find the last import statement
        const importRegex = /import.*from.*['"];?\n/g;
        const imports = content.match(importRegex);
        if (imports) {
          const lastImport = imports[imports.length - 1];
          content = content.replace(lastImport, lastImport + importStatement);
        } else {
          // If no imports found, add at the beginning
          content = importStatement + content;
        }
      }

      // Replace hardcoded color mappings
      const colorMappingRegex = /const colors = \{[\s\S]*?\};/g;
      if (colorMappingRegex.test(content)) {
        content = content.replace(colorMappingRegex, '// Colors now imported from appTheme.js\n  const colors = APP_COLORS;');
      }

      // Replace hardcoded color assignments
      content = content.replace(/let color = "#722ed1";/g, 'let color = getCurrentAppColor();');
      content = content.replace(/color = "#722ed1"/g, 'color = getCurrentAppColor()');

      // Replace direct color references
      content = content.replace(/#722ed1/g, 'getCurrentAppColor()');

      // Update CSS variable setting
      const cssVariableRegex = /document\.documentElement\.style\.setProperty\('--primary-color', color\);/g;
      if (cssVariableRegex.test(content)) {
        content = content.replace(cssVariableRegex, 'appTheme.applyCSSVariables();');
      }

      fs.writeFileSync(fullPath, content);
      console.log(`📝 Updated: ${componentPath}`);
    } catch (error) {
      console.error(`❌ Error updating ${componentPath}:`, error.message);
    }
  }

  updateAppConstants() {
    console.log('🔄 Updating app constants files...');
    
    this.appConstantsFiles.forEach(constantsPath => {
      const fullPath = path.join(__dirname, '..', constantsPath);
      
      if (!fs.existsSync(fullPath)) {
        console.warn(`⚠️  Constants file not found: ${constantsPath}`);
        return;
      }

      try {
        const appName = path.basename(path.dirname(constantsPath));
        
        const dynamicConstants = `/**
 * App Constants - ${appName.toUpperCase()}
 * This file is automatically updated when switching apps
 * Import from appTheme.js for dynamic values
 */

import { getAppConstants } from '../../config/appTheme';

// Get dynamic constants for this app
const constants = getAppConstants('${appName}');

export const APP_NAME = constants?.APP_NAME || "${appName.charAt(0).toUpperCase() + appName.slice(1)}";
export const APP_PATH_NAME = constants?.APP_PATH_NAME || "${appName}";
export const APP_TAG = constants?.APP_TAG || "Business Management System";
export const DATABASE_PREFIX = constants?.DATABASE_PREFIX || "${appName}_";
export const APP_COLOR = constants?.APP_COLOR || "#722ed1";
export const APP_VERSION = constants?.APP_VERSION || "1.0.0";
export const APP_ICON_PATH = constants?.APP_ICON_PATH || "icons/${appName}";

// Legacy exports for backward compatibility
export const APP_ICON = \`
<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
  <rect width="24" height="24" rx="4" fill="\${APP_COLOR}"/>
  <text x="12" y="16" text-anchor="middle" fill="white" font-size="10" font-weight="bold">
    \${APP_NAME.charAt(0)}
  </text>
</svg>
\`;

export default {
  APP_NAME,
  APP_PATH_NAME,
  APP_TAG,
  DATABASE_PREFIX,
  APP_COLOR,
  APP_VERSION,
  APP_ICON_PATH,
  APP_ICON
};
`;

        fs.writeFileSync(fullPath, dynamicConstants);
        console.log(`📝 Updated constants: ${appName}`);
      } catch (error) {
        console.error(`❌ Error updating constants for ${constantsPath}:`, error.message);
      }
    });
  }

  createMigrationGuide() {
    const guidePath = path.join(__dirname, '..', 'THEME_MIGRATION_GUIDE.md');
    
    const guide = `# Theme Migration Guide

## Overview
Components have been updated to use a centralized, dynamic theme system that automatically adapts when switching between apps.

## Changes Made

### 1. Centralized Theme Configuration
- **New file:** \`src/config/appTheme.js\`
- Contains all app colors, theme variants, and configuration
- Automatically updates when switching apps using \`initApp.js\`

### 2. Updated Components
The following components now use dynamic theming:

- \`src/Components/AppLoader.js\`
- \`src/Components/Layout/index.js\`
- \`src/Components/Dashboard/OrganizationInformation.js\`
- \`src/Components/Stats/StatsGrid.js\`

### 3. Updated App Constants
All app-specific constants files now import from the centralized theme:

- \`src/Apps/*/constants/index.js\` files updated
- Dynamic values based on current app
- Backward compatibility maintained

## How to Use

### Import Theme Configuration
\`\`\`javascript
import appTheme, { 
  getCurrentAppColor, 
  APP_COLORS, 
  APP_THEME,
  FEATURE_FLAGS,
  hasFeature 
} from '../config/appTheme';
\`\`\`

### Get Current App Color
\`\`\`javascript
// Instead of hardcoded colors
const color = getCurrentAppColor();

// Or for specific apps
const abacusColor = getAppColor('abacus');
\`\`\`

### Use Feature Flags
\`\`\`javascript
// Check if current app has a feature
if (hasFeature('enablePOS')) {
  // Show POS-specific UI
}

// Or check directly
if (FEATURE_FLAGS.enableInventory) {
  // Show inventory features
}
\`\`\`

### Apply CSS Variables
\`\`\`javascript
// Apply all CSS variables at once
appTheme.applyCSSVariables();

// CSS variables available:
// --primary-color
// --app-name
// --app-type
// --database-prefix
\`\`\`

## Benefits

1. **Automatic Theme Switching**: Colors and themes update automatically when switching apps
2. **Centralized Configuration**: All theme-related settings in one place
3. **Type Safety**: Consistent color and theme usage across components
4. **Feature Flags**: App-specific features can be enabled/disabled dynamically
5. **Backward Compatibility**: Existing code continues to work

## Migration for New Components

When creating new components, use the dynamic theme system:

\`\`\`javascript
import { getCurrentAppColor, APP_THEME, hasFeature } from '../config/appTheme';

const MyComponent = () => {
  const primaryColor = getCurrentAppColor();
  const showAdvancedFeatures = hasFeature('enableAnalytics');
  
  return (
    <div style={{ color: primaryColor }}>
      <h1>{APP_THEME.displayName}</h1>
      {showAdvancedFeatures && <AdvancedPanel />}
    </div>
  );
};
\`\`\`

## Troubleshooting

### Colors Not Updating
1. Ensure you're importing from \`appTheme.js\`
2. Check that \`initApp.js\` was used to switch apps
3. Verify the component is using \`getCurrentAppColor()\`

### Feature Flags Not Working
1. Check that the feature is defined in \`FEATURE_FLAGS\`
2. Ensure you're using \`hasFeature()\` function
3. Verify the current app name is correct

### CSS Variables Not Applied
1. Call \`appTheme.applyCSSVariables()\` in your component
2. Check that CSS variables are defined in the theme config
3. Ensure the component is mounted after theme initialization

---

*This guide was generated automatically during theme migration.*
`;

    fs.writeFileSync(guidePath, guide);
    console.log('📚 Created migration guide: THEME_MIGRATION_GUIDE.md');
  }

  run() {
    console.log('🚀 Starting theme migration...');
    this.updateAllComponents();
    this.createMigrationGuide();
    console.log('🎉 Theme migration completed successfully!');
    console.log('📚 Check THEME_MIGRATION_GUIDE.md for details');
  }
}

// CLI interface
if (require.main === module) {
  const updater = new ComponentThemeUpdater();
  updater.run();
}

module.exports = ComponentThemeUpdater;
