/**
 * App-Specific Environment Setup
 * Configures environment variables and settings for each app
 */

const fs = require('fs');
const path = require('path');

// Load apps configuration from apps.json
const appsConfig = JSON.parse(fs.readFileSync(path.join(__dirname, '..', 'apps.json'), 'utf8'));

class AppEnvironmentManager {
  constructor() {
    this.envDir = path.join(__dirname, '..', 'env');
    this.setupEnvironmentDirectory();
  }

  setupEnvironmentDirectory() {
    if (!fs.existsSync(this.envDir)) {
      fs.mkdirSync(this.envDir, { recursive: true });
    }
  }

  generateAppEnvironments() {
    console.log('🔧 Generating app-specific environment files...');
    
    Object.entries(appsConfig).forEach(([appName, config]) => {
      this.createAppEnvironment(appName, config);
    });

    this.createMasterEnvironment();
    console.log('✅ Environment files generated successfully!');
  }

  createAppEnvironment(appName, config) {
    const envContent = this.generateEnvContent(appName, config);
    const envFile = path.join(this.envDir, `.env.${appName}`);
    
    fs.writeFileSync(envFile, envContent);
    console.log(`📝 Created: .env.${appName}`);
  }

  generateEnvContent(appName, config) {
    const basePort = 3000;
    const appIndex = Object.keys(appsConfig).indexOf(appName);
    const port = basePort + appIndex;

    return `# Environment configuration for ${config.name} (${appName})
# Generated automatically - modify with caution

# App Identity
REACT_APP_NAME=${appName}
REACT_APP_DISPLAY_NAME=${config.name}
REACT_APP_VERSION=${config.version}
REACT_APP_TYPE=${config.type}
REACT_APP_COLOR=${config.color}

# Vite Environment Variables (for Vite compatibility)
VITE_APP_NAME=${appName}
VITE_APP_DISPLAY_NAME=${config.name}
VITE_APP_VERSION=${config.version}
VITE_APP_TYPE=${config.type}
VITE_APP_COLOR=${config.color}

# Development
PORT=${port}
BROWSER=none
FAST_REFRESH=true
GENERATE_SOURCEMAP=true

# Database
REACT_APP_DATABASE_PREFIX=${appName}_
REACT_APP_DATABASE_NAME=${appName}_db
VITE_DATABASE_PREFIX=${appName}_
VITE_DATABASE_NAME=${appName}_db

# Build Configuration
BUILD_PATH=build-${appName}
PUBLIC_URL=.

# API Configuration
REACT_APP_API_BASE_URL=http://localhost:${port + 1000}
REACT_APP_WS_URL=ws://localhost:${port + 2000}

# Feature Flags
REACT_APP_ENABLE_DEV_TOOLS=true
REACT_APP_ENABLE_HOT_RELOAD=true
REACT_APP_ENABLE_ANALYTICS=${appName === 'mission-control' ? 'true' : 'false'}

# App-Specific Settings
${this.getAppSpecificSettings(appName, config)}

# Electron Configuration (if applicable)
ELECTRON_IS_DEV=true
ELECTRON_APP_NAME=${config.name}
ELECTRON_APP_ID=net.haclab.${appName}

# Security
REACT_APP_ENABLE_HTTPS=false
HTTPS=false

# Performance
REACT_APP_BUNDLE_ANALYZER=${process.env.ANALYZE_BUNDLE || 'false'}
REACT_APP_OPTIMIZE_IMAGES=true

# Logging
REACT_APP_LOG_LEVEL=debug
REACT_APP_ENABLE_CONSOLE_LOGS=true
`;
  }

  getAppSpecificSettings(appName, config) {
    const specificSettings = {
      'abacus': `# Inventory Management Specific
REACT_APP_ENABLE_BARCODE_SCANNER=true
REACT_APP_ENABLE_STOCK_ALERTS=true
REACT_APP_DEFAULT_CURRENCY=USD`,

      'evia': `# Restaurant Management Specific
REACT_APP_ENABLE_POS=true
REACT_APP_ENABLE_KITCHEN_DISPLAY=true
REACT_APP_DEFAULT_TAX_RATE=0.1`,

      'homz': `# Hotel Management Specific
REACT_APP_ENABLE_BOOKING_ENGINE=true
REACT_APP_ENABLE_HOUSEKEEPING=true
REACT_APP_DEFAULT_TIMEZONE=UTC`,

      'inncontrol': `# Hotel Management Specific
REACT_APP_ENABLE_CHANNEL_MANAGER=true
REACT_APP_ENABLE_REVENUE_MANAGEMENT=true
REACT_APP_DEFAULT_CURRENCY=USD`,

      'kanify': `# Garage Management Specific
REACT_APP_ENABLE_VEHICLE_TRACKING=true
REACT_APP_ENABLE_PARTS_INVENTORY=true
REACT_APP_DEFAULT_LABOR_RATE=50`,

      'kyeyo': `# Recruitment Management Specific
REACT_APP_ENABLE_CV_PARSING=true
REACT_APP_ENABLE_INTERVIEW_SCHEDULING=true
REACT_APP_DEFAULT_LANGUAGE=en`,

      'lenkit': `# Loan Management Specific
REACT_APP_ENABLE_CREDIT_SCORING=true
REACT_APP_ENABLE_PAYMENT_GATEWAY=true
REACT_APP_DEFAULT_INTEREST_RATE=0.05`,

      'mission-control': `# Management Software Specific
REACT_APP_ENABLE_MULTI_TENANT=true
REACT_APP_ENABLE_ADVANCED_ANALYTICS=true
REACT_APP_ENABLE_API_MANAGEMENT=true`,

      'prosy': `# Property Management Specific
REACT_APP_ENABLE_TENANT_PORTAL=true
REACT_APP_ENABLE_MAINTENANCE_REQUESTS=true
REACT_APP_DEFAULT_LEASE_TERM=12`,

      'smart': `# School Management Specific
REACT_APP_ENABLE_GRADEBOOK=true
REACT_APP_ENABLE_PARENT_PORTAL=true
REACT_APP_DEFAULT_ACADEMIC_YEAR=2024`,

      'zenwrench': `# Garage Management Specific
REACT_APP_ENABLE_DIAGNOSTIC_TOOLS=true
REACT_APP_ENABLE_CUSTOMER_PORTAL=true
REACT_APP_DEFAULT_WARRANTY_PERIOD=90`
    };

    return specificSettings[appName] || '# No app-specific settings';
  }

  createMasterEnvironment() {
    const masterEnv = `# Master Environment Configuration
# This file contains shared settings across all apps

# Development Server
DEV_SERVER_PORT=3001
WS_SERVER_PORT=3002

# Build Configuration
NODE_ENV=development
CI=false

# Shared Database Settings
DB_HOST=localhost
DB_PORT=5432

# Shared API Settings
API_TIMEOUT=30000
API_RETRY_ATTEMPTS=3

# Shared Feature Flags
ENABLE_MULTI_APP_DEV=true
ENABLE_HOT_SWITCHING=true
ENABLE_PARALLEL_DEV=true

# Monitoring
ENABLE_PERFORMANCE_MONITORING=true
ENABLE_ERROR_TRACKING=true

# Security
ENABLE_CORS=true
CORS_ORIGIN=http://localhost:3000
`;

    fs.writeFileSync(path.join(this.envDir, '.env.master'), masterEnv);
    console.log('📝 Created: .env.master');
  }

  loadAppEnvironment(appName) {
    const envFile = path.join(this.envDir, `.env.${appName}`);
    const masterEnvFile = path.join(this.envDir, '.env.master');
    
    // Load master environment first
    if (fs.existsSync(masterEnvFile)) {
      this.loadEnvFile(masterEnvFile);
    }
    
    // Load app-specific environment
    if (fs.existsSync(envFile)) {
      this.loadEnvFile(envFile);
      console.log(`🔧 Loaded environment for ${appName}`);
    } else {
      console.warn(`⚠️  Environment file not found for ${appName}`);
    }
  }

  loadEnvFile(filePath) {
    const content = fs.readFileSync(filePath, 'utf8');
    const lines = content.split('\n');
    
    lines.forEach(line => {
      line = line.trim();
      if (line && !line.startsWith('#')) {
        const [key, ...valueParts] = line.split('=');
        const value = valueParts.join('=');
        if (key && value !== undefined) {
          process.env[key] = value;
        }
      }
    });
  }

  switchEnvironment(appName) {
    if (!appsConfig[appName]) {
      throw new Error(`App ${appName} not found`);
    }

    console.log(`🔄 Switching environment to ${appName}...`);
    this.loadAppEnvironment(appName);
    
    // Update current app indicator
    process.env.CURRENT_APP = appName;
    process.env.REACT_APP_CURRENT_APP = appName;
    
    console.log(`✅ Environment switched to ${appName}`);
    return true;
  }

  listEnvironments() {
    console.log('\n🔧 Available Environment Files:');
    console.log('=' .repeat(50));
    
    const envFiles = fs.readdirSync(this.envDir)
      .filter(file => file.startsWith('.env.'))
      .sort();
    
    envFiles.forEach(file => {
      const appName = file.replace('.env.', '');
      const config = appsConfig[appName];
      const status = config ? '✅' : '❓';
      const name = config ? config.name : 'Unknown';
      
      console.log(`${status} ${file.padEnd(20)} - ${name}`);
    });
  }

  validateEnvironments() {
    console.log('🔍 Validating environment configurations...');
    
    const issues = [];
    
    Object.keys(appsConfig).forEach(appName => {
      const envFile = path.join(this.envDir, `.env.${appName}`);
      
      if (!fs.existsSync(envFile)) {
        issues.push(`Missing environment file for ${appName}`);
      } else {
        // Validate required variables
        const content = fs.readFileSync(envFile, 'utf8');
        const requiredVars = [
          'REACT_APP_NAME',
          'REACT_APP_VERSION',
          'PORT',
          'REACT_APP_DATABASE_PREFIX'
        ];
        
        requiredVars.forEach(varName => {
          if (!content.includes(varName)) {
            issues.push(`Missing ${varName} in ${appName} environment`);
          }
        });
      }
    });
    
    if (issues.length === 0) {
      console.log('✅ All environment configurations are valid');
    } else {
      console.log('❌ Environment validation issues:');
      issues.forEach(issue => console.log(`   - ${issue}`));
    }
    
    return issues.length === 0;
  }
}

// CLI interface
if (require.main === module) {
  const manager = new AppEnvironmentManager();
  const command = process.argv[2];
  const appName = process.argv[3];

  switch (command) {
    case 'generate':
      manager.generateAppEnvironments();
      break;
    case 'switch':
      if (!appName) {
        console.error('❌ Please specify an app name');
        process.exit(1);
      }
      manager.switchEnvironment(appName);
      break;
    case 'list':
      manager.listEnvironments();
      break;
    case 'validate':
      manager.validateEnvironments();
      break;
    default:
      console.log(`
🔧 App Environment Manager

Usage:
  node setup-app-env.js generate           - Generate all environment files
  node setup-app-env.js switch <appName>   - Switch to app environment
  node setup-app-env.js list               - List all environment files
  node setup-app-env.js validate           - Validate environment configurations

Examples:
  node setup-app-env.js generate
  node setup-app-env.js switch zenwrench
  node setup-app-env.js validate
      `);
  }
}

module.exports = AppEnvironmentManager;
