#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Load apps configuration
const appsPath = path.join(__dirname, '../src/config/apps.json');
const apps = JSON.parse(fs.readFileSync(appsPath, 'utf8'));

function showUsage() {
  console.log(`
🚀 App Release Manager

Usage:
  node scripts/release-apps.js <command> [options]

Commands:
  release <app> [bump-type]     - Release specific app
  release-all [bump-type]       - Release all apps
  build <app>                   - Build specific app locally
  build-all                     - Build all apps locally

Bump Types:
  patch (default)  - 0.0.1 -> 0.0.2
  minor           - 0.0.1 -> 0.1.0  
  major           - 0.0.1 -> 1.0.0

Examples:
  node scripts/release-apps.js release prosy patch
  node scripts/release-apps.js release-all minor
  node scripts/release-apps.js build prosy
  node scripts/release-apps.js build-all

GitHub Actions:
  This script can also trigger GitHub Actions workflows for automated releases.
`);
}

function execCommand(command, options = {}) {
  try {
    console.log(`🔧 Running: ${command}`);
    const result = execSync(command, { 
      stdio: 'inherit', 
      cwd: path.join(__dirname, '..'),
      ...options 
    });
    return true;
  } catch (error) {
    console.error(`❌ Command failed: ${command}`);
    console.error(error.message);
    return false;
  }
}

function buildApp(appName) {
  if (!apps[appName]) {
    console.error(`❌ App '${appName}' not found!`);
    console.log(`Available apps: ${Object.keys(apps).join(', ')}`);
    return false;
  }
  
  console.log(`\n🏗️  Building ${apps[appName].name} (${appName})...`);
  console.log('='.repeat(50));
  
  // Initialize app configuration
  if (!execCommand(`node initApp.js ${appName}`)) {
    return false;
  }
  
  // Build React app
  if (!execCommand('yarn build')) {
    return false;
  }
  
  // Build Electron app for current platform
  const platform = process.platform;
  let buildCommand;
  
  switch (platform) {
    case 'win32':
      buildCommand = 'yarn dist-win64';
      break;
    case 'darwin':
      buildCommand = 'yarn dist-mac';
      break;
    case 'linux':
      buildCommand = 'yarn dist-linux';
      break;
    default:
      console.error(`❌ Unsupported platform: ${platform}`);
      return false;
  }
  
  if (!execCommand(buildCommand)) {
    return false;
  }
  
  console.log(`✅ Successfully built ${apps[appName].name}!`);
  console.log(`📦 Output directory: dist/`);
  return true;
}

function buildAllApps() {
  console.log(`🏗️  Building all apps...`);
  console.log('='.repeat(50));
  
  let successful = 0;
  let failed = 0;
  
  Object.keys(apps).forEach(appName => {
    console.log(`\n📦 Building ${appName}...`);
    if (buildApp(appName)) {
      successful++;
    } else {
      failed++;
    }
  });
  
  console.log(`\n📊 Build Summary:`);
  console.log(`✅ Successful: ${successful}`);
  console.log(`❌ Failed: ${failed}`);
  
  return failed === 0;
}

function releaseApp(appName, bumpType = 'patch') {
  if (!apps[appName]) {
    console.error(`❌ App '${appName}' not found!`);
    console.log(`Available apps: ${Object.keys(apps).join(', ')}`);
    return false;
  }
  
  console.log(`\n🚀 Releasing ${apps[appName].name} (${appName}) with ${bumpType} bump...`);
  console.log('='.repeat(60));
  
  // Check if GitHub CLI is available
  try {
    execSync('gh --version', { stdio: 'ignore' });
  } catch (error) {
    console.error('❌ GitHub CLI (gh) is required for releases');
    console.error('Install it from: https://cli.github.com/');
    return false;
  }
  
  // Trigger GitHub Actions workflow
  const command = `gh workflow run release.yml -f app_name=${appName} -f version_bump=${bumpType}`;
  
  if (!execCommand(command)) {
    console.error('❌ Failed to trigger release workflow');
    return false;
  }
  
  console.log(`✅ Release workflow triggered for ${apps[appName].name}!`);
  console.log(`🔗 Check progress at: https://github.com/haclab-co/apps/actions`);
  return true;
}

function releaseAllApps(bumpType = 'patch') {
  console.log(`\n🚀 Releasing all apps with ${bumpType} bump...`);
  console.log('='.repeat(50));
  
  // Check if GitHub CLI is available
  try {
    execSync('gh --version', { stdio: 'ignore' });
  } catch (error) {
    console.error('❌ GitHub CLI (gh) is required for releases');
    console.error('Install it from: https://cli.github.com/');
    return false;
  }
  
  // Trigger GitHub Actions workflow for all apps
  const command = `gh workflow run release-all-apps.yml -f apps_to_release=all -f version_bump=${bumpType}`;
  
  if (!execCommand(command)) {
    console.error('❌ Failed to trigger release workflow');
    return false;
  }
  
  console.log(`✅ Release workflow triggered for all apps!`);
  console.log(`🔗 Check progress at: https://github.com/haclab-co/apps/actions`);
  return true;
}

function listApps() {
  console.log('\n📋 Available Apps:');
  console.log('==================');
  
  Object.entries(apps).forEach(([key, app]) => {
    console.log(`${app.name.padEnd(25)} v${app.version.padEnd(10)} (${key})`);
  });
  
  console.log(`\n📊 Total Apps: ${Object.keys(apps).length}`);
}

// Main execution
const [,, command, ...args] = process.argv;

switch (command) {
  case 'release':
    if (args.length < 1) {
      console.error('❌ Usage: release <app> [bump-type]');
      listApps();
      process.exit(1);
    }
    releaseApp(args[0], args[1] || 'patch');
    break;
    
  case 'release-all':
    releaseAllApps(args[0] || 'patch');
    break;
    
  case 'build':
    if (args.length !== 1) {
      console.error('❌ Usage: build <app>');
      listApps();
      process.exit(1);
    }
    buildApp(args[0]);
    break;
    
  case 'build-all':
    buildAllApps();
    break;
    
  case 'list':
    listApps();
    break;
    
  default:
    showUsage();
    if (command) {
      console.error(`❌ Unknown command: ${command}`);
      process.exit(1);
    }
}
