/**
 * Validate Mission Control Implementation
 * 
 * This script validates that the mission control imports implementation
 * is correctly set up without trying to actually import the ES6 modules
 * (which would fail in Node.js context).
 */

const fs = require('fs');
const path = require('path');

class MissionControlValidator {
  constructor() {
    this.testResults = [];
  }

  async validateImplementation() {
    console.log('🔍 Validating Mission Control Imports Implementation');
    console.log('=' .repeat(60));

    // Test 1: Validate files exist and have correct syntax
    await this.testFileStructure();

    // Test 2: Validate webpack configuration
    await this.testWebpackConfiguration();

    // Test 3: Validate import statements were updated
    await this.testImportStatements();

    // Test 4: Validate no circular dependencies
    await this.testCircularDependencies();

    // Generate report
    this.generateReport();
  }

  async testFileStructure() {
    console.log('\n📋 Test 1: File Structure and Syntax');
    console.log('-' .repeat(40));

    try {
      // Check mission control imports file
      const importHelperPath = 'src/utils/missionControlImports.js';
      const exists = fs.existsSync(importHelperPath);
      console.log(`   ${exists ? '✅' : '❌'} Mission Control imports file exists`);
      
      if (exists) {
        const content = fs.readFileSync(importHelperPath, 'utf8');
        
        // Check for ES6 export syntax
        const hasExports = content.includes('export {') || content.includes('export *');
        const hasImports = content.includes('from \'../Apps/mission-control/');
        const hasUtilities = content.includes('getSoftwareOptions');
        const hasModules = content.includes('organizations');
        
        console.log(`   ${hasExports ? '✅' : '❌'} Has ES6 export statements`);
        console.log(`   ${hasImports ? '✅' : '❌'} Has mission-control imports`);
        console.log(`   ${hasUtilities ? '✅' : '❌'} Exports utilities`);
        console.log(`   ${hasModules ? '✅' : '❌'} Exports modules`);
        
        this.testResults.push({
          test: 'File Structure and Syntax',
          passed: exists && hasExports && hasImports && hasUtilities && hasModules,
          details: `File exists: ${exists}, Exports: ${hasExports}, Imports: ${hasImports}`
        });
      }
    } catch (error) {
      console.error('❌ File structure test failed:', error.message);
      this.testResults.push({
        test: 'File Structure and Syntax',
        passed: false,
        details: error.message
      });
    }
  }

  async testWebpackConfiguration() {
    console.log('\n📋 Test 2: Webpack Configuration');
    console.log('-' .repeat(40));

    try {
      const configPath = 'config-overrides.js';
      const configExists = fs.existsSync(configPath);
      console.log(`   ${configExists ? '✅' : '❌'} Webpack config exists`);
      
      if (configExists) {
        const configContent = fs.readFileSync(configPath, 'utf8');
        
        // Check for mission control alias
        const hasMissionControlAlias = configContent.includes('@mission-control');
        const aliasPointsToImports = configContent.includes('src/utils/missionControlImports');
        const hasAppFilterPlugin = configContent.includes('AppFilterPlugin');
        
        console.log(`   ${hasMissionControlAlias ? '✅' : '❌'} @mission-control alias present`);
        console.log(`   ${aliasPointsToImports ? '✅' : '❌'} Alias points to imports helper`);
        console.log(`   ${hasAppFilterPlugin ? '✅' : '❌'} App filter plugin configured`);
        
        this.testResults.push({
          test: 'Webpack Configuration',
          passed: configExists && hasMissionControlAlias && aliasPointsToImports,
          details: `Config: ${configExists}, Alias: ${hasMissionControlAlias}, Points to helper: ${aliasPointsToImports}`
        });
      }
    } catch (error) {
      console.error('❌ Webpack configuration test failed:', error.message);
      this.testResults.push({
        test: 'Webpack Configuration',
        passed: false,
        details: error.message
      });
    }
  }

  async testImportStatements() {
    console.log('\n📋 Test 3: Updated Import Statements');
    console.log('-' .repeat(40));

    try {
      const filesToCheck = [
        'src/Apps/mission-control/modules/plans.js',
        'src/Apps/mission-control/modules/organizations.js',
        'src/Apps/mission-control/modules/installation_packages.js',
        'src/Components/InitCompany/index.js'
      ];

      let updatedCount = 0;
      let totalFiles = 0;

      for (const filePath of filesToCheck) {
        if (fs.existsSync(filePath)) {
          totalFiles++;
          const content = fs.readFileSync(filePath, 'utf8');
          const usesNewImport = content.includes('from "@mission-control"');
          const usesOldImport = content.includes('from "../utils/softwareModules"') || 
                               content.includes('from "../../Apps/mission-control/modules"');
          
          if (usesNewImport && !usesOldImport) {
            console.log(`   ✅ ${filePath} - Updated to use @mission-control`);
            updatedCount++;
          } else if (usesOldImport) {
            console.log(`   ⚠️  ${filePath} - Still uses old import paths`);
          } else {
            console.log(`   ⚪ ${filePath} - No mission-control imports found`);
          }
        } else {
          console.log(`   ❌ ${filePath} - File not found`);
        }
      }

      console.log(`   📊 Updated ${updatedCount}/${totalFiles} files`);

      this.testResults.push({
        test: 'Updated Import Statements',
        passed: updatedCount > 0 && updatedCount === totalFiles,
        details: `Updated ${updatedCount}/${totalFiles} files`
      });

    } catch (error) {
      console.error('❌ Import statements test failed:', error.message);
      this.testResults.push({
        test: 'Updated Import Statements',
        passed: false,
        details: error.message
      });
    }
  }

  async testCircularDependencies() {
    console.log('\n📋 Test 4: Circular Dependencies Check');
    console.log('-' .repeat(40));

    try {
      const importHelperPath = 'src/utils/missionControlImports.js';
      const content = fs.readFileSync(importHelperPath, 'utf8');
      
      // Check for potential circular dependencies
      const importsFromMissionControl = content.includes('from \'../Apps/mission-control/');
      // Check for actual problematic import statements, not comments or valid paths
      const noSelfImport = !content.match(/import.*from\s+["']@mission-control["']/);
      const noCircularImport = !content.match(/import.*from\s+["']\.\/missionControlImports["']/);
      // The ../utils/ in the path is expected and not circular since it's importing from mission-control/utils
      const validStructure = content.includes('mission-control/utils/softwareModules');
      
      console.log(`   ${importsFromMissionControl ? '✅' : '❌'} Imports from mission-control directory`);
      console.log(`   ${noSelfImport ? '✅' : '❌'} No self-referential imports`);
      console.log(`   ${noCircularImport ? '✅' : '❌'} No circular imports to self`);
      console.log(`   ${validStructure ? '✅' : '❌'} Valid import structure`);

      this.testResults.push({
        test: 'Circular Dependencies Check',
        passed: importsFromMissionControl && noSelfImport && noCircularImport && validStructure,
        details: `Imports MC: ${importsFromMissionControl}, No self: ${noSelfImport}, No circular: ${noCircularImport}, Valid: ${validStructure}`
      });

    } catch (error) {
      console.error('❌ Circular dependencies test failed:', error.message);
      this.testResults.push({
        test: 'Circular Dependencies Check',
        passed: false,
        details: error.message
      });
    }
  }

  generateReport() {
    console.log('\n📊 Mission Control Imports Implementation Report');
    console.log('=' .repeat(60));
    
    const passedTests = this.testResults.filter(r => r.passed).length;
    const totalTests = this.testResults.length;
    const successRate = Math.round((passedTests / totalTests) * 100);
    
    console.log(`\n🎯 Overall Success Rate: ${successRate}% (${passedTests}/${totalTests})`);
    
    console.log('\n📋 Detailed Results:');
    this.testResults.forEach(result => {
      const status = result.passed ? '✅ PASS' : '❌ FAIL';
      console.log(`   ${status} ${result.test}`);
      if (!result.passed) {
        console.log(`      Details: ${result.details}`);
      }
    });
    
    console.log('\n🚀 Implementation Status:');
    if (successRate >= 80) {
      console.log('✅ Mission Control imports implementation is working correctly!');
      console.log('🎉 Ready for use with @mission-control alias in webpack builds.');
      console.log('');
      console.log('📝 Usage in your components:');
      console.log('import { getSoftwareOptions, organizations } from "@mission-control";');
    } else if (successRate >= 60) {
      console.log('⚠️  Mission Control imports implementation has some issues.');
      console.log('🔧 Review failed tests and fix issues.');
    } else {
      console.log('❌ Mission Control imports implementation has significant issues.');
      console.log('🛠️  Major fixes required.');
    }
    
    console.log('\n✨ Benefits achieved:');
    console.log('• Centralized mission-control imports');
    console.log('• Clean @mission-control alias for imports');
    console.log('• Better tree shaking and bundle optimization');
    console.log('• Easier maintenance of import paths');
    console.log('• Consistent import patterns across apps');
  }
}

// Run validation if called directly
if (require.main === module) {
  const validator = new MissionControlValidator();
  validator.validateImplementation().catch(console.error);
}

module.exports = MissionControlValidator;
