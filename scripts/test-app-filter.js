/**
 * Test script for App Filter Plugin
 * Tests the filtering logic without running a full build
 */

const AppFilterPlugin = require('./webpack-app-filter-plugin');
const packageJson = require('../package.json');

// Get current app from package.json
const currentApp = packageJson.name || 'default';

console.log('🧪 Testing App Filter Plugin');
console.log('=' .repeat(50));
console.log(`Current app: ${currentApp}`);

// Create plugin instance
const plugin = new AppFilterPlugin(currentApp);

// Test cases - simulate webpack resolve requests
const testCases = [
  // Should be included (current app)
  `/src/Apps/${currentApp}/modules/SomeModule.js`,
  `/src/Apps/${currentApp}/components/SomeComponent.js`,
  
  // Should be included (mission-control)
  '/src/Apps/mission-control/modules/SomeModule.js',
  '/src/Apps/mission-control/utils/helper.js',
  
  // Should be included (universal)
  '/src/Apps/Universal/components/CommonComponent.js',
  '/src/Apps/universal/utils/helper.js',
  
  // Should be excluded (other apps)
  '/src/Apps/prosy/modules/PropertyModule.js',
  '/src/Apps/zenwrench/modules/GarageModule.js',
  '/src/Apps/abacus/modules/InventoryModule.js',
  '/src/Apps/evia/modules/RestaurantModule.js',
  
  // Should not be affected (non-app files)
  '/src/components/SomeComponent.js',
  '/src/utils/helper.js',
  '/node_modules/react/index.js',
];

console.log('\n🔍 Testing filter logic:');
console.log('-' .repeat(50));

testCases.forEach(testCase => {
  // Simulate the plugin's filtering logic
  if (!testCase.includes('/src/Apps/')) {
    console.log(`⚪ PASS-THROUGH: ${testCase}`);
    return;
  }
  
  const appMatch = testCase.match(/\/Apps\/([^\/]+)\//i);
  if (appMatch) {
    const appName = appMatch[1].toLowerCase();
    const isAllowed = plugin.allowedApps.map(a => a.toLowerCase()).includes(appName);
    
    if (isAllowed) {
      console.log(`✅ INCLUDE: ${testCase} (${appName})`);
    } else {
      console.log(`🚫 EXCLUDE: ${testCase} (${appName})`);
    }
  }
});

console.log('\n📊 Summary:');
console.log(`Current app: ${currentApp}`);
console.log(`Allowed apps: ${plugin.allowedApps.join(', ')}`);
console.log('\n✅ App Filter Plugin test completed!');
