#!/usr/bin/env node

// PERFORMANCE FIX: Setup script for build optimizations
const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🚀 Setting up build optimizations...');

// Check if we're in the right directory
const packageJsonPath = path.join(process.cwd(), 'package.json');
if (!fs.existsSync(packageJsonPath)) {
  console.error('❌ package.json not found. Please run this script from the project root.');
  process.exit(1);
}

// Install required dev dependencies
console.log('📦 Installing optimization dependencies...');
try {
  execSync('yarn add -D webpack-bundle-analyzer compression-webpack-plugin terser-webpack-plugin css-minimizer-webpack-plugin', {
    stdio: 'inherit'
  });
  console.log('✅ Dependencies installed successfully');
} catch (error) {
  console.error('❌ Failed to install dependencies:', error.message);
  process.exit(1);
}

// Create .env.production file if it doesn't exist
const envProdPath = path.join(process.cwd(), '.env.production');
if (!fs.existsSync(envProdPath)) {
  console.log('📝 Creating .env.production file...');
  const envContent = `# Production environment variables for optimized builds
GENERATE_SOURCEMAP=false
INLINE_RUNTIME_CHUNK=false
IMAGE_INLINE_SIZE_LIMIT=0
DISABLE_ESLINT_PLUGIN=true
REACT_APP_OPTIMIZE_BUNDLE=true
NODE_ENV=production
BABEL_ENV=production
`;
  fs.writeFileSync(envProdPath, envContent);
  console.log('✅ .env.production created');
}

// Create webpack cache directory
const cacheDir = path.join(process.cwd(), 'node_modules', '.cache', 'webpack');
if (!fs.existsSync(cacheDir)) {
  fs.mkdirSync(cacheDir, { recursive: true });
  console.log('✅ Webpack cache directory created');
}

// Create reports directory
const reportsDir = path.join(process.cwd(), 'build', 'reports');
if (!fs.existsSync(reportsDir)) {
  fs.mkdirSync(reportsDir, { recursive: true });
  console.log('✅ Reports directory created');
}

// Verify all optimization files are in place
const requiredFiles = [
  'config-overrides.js',
  'scripts/advanced-build.js',
  'scripts/bundle-analyzer.js',
  'docs/BUILD_OPTIMIZATION_GUIDE.md'
];

console.log('🔍 Verifying optimization files...');
let allFilesPresent = true;

requiredFiles.forEach(file => {
  const filePath = path.join(process.cwd(), file);
  if (fs.existsSync(filePath)) {
    console.log(`✅ ${file}`);
  } else {
    console.log(`❌ ${file} - Missing!`);
    allFilesPresent = false;
  }
});

if (!allFilesPresent) {
  console.log('\n⚠️  Some optimization files are missing. Please ensure all files are created.');
}

// Run initial analysis if build exists
const buildDir = path.join(process.cwd(), 'build');
if (fs.existsSync(buildDir)) {
  console.log('\n📊 Running initial bundle analysis...');
  try {
    execSync('node scripts/bundle-analyzer.js', { stdio: 'inherit' });
    console.log('✅ Initial analysis completed');
  } catch (error) {
    console.log('⚠️  Could not run initial analysis. Build the project first.');
  }
}

// Display next steps
console.log('\n🎉 Build optimization setup completed!');
console.log('\n📋 Next Steps:');
console.log('1. Run optimized build: yarn build:production');
console.log('2. Analyze bundle: yarn analyze');
console.log('3. Review reports in build/reports/');
console.log('4. Read the guide: docs/BUILD_OPTIMIZATION_GUIDE.md');
console.log('\n💡 Available Commands:');
console.log('   yarn build:advanced     - Advanced optimized build');
console.log('   yarn build:production   - Full production build');
console.log('   yarn analyze:build      - Build and analyze');
console.log('   yarn analyze            - Analyze existing build');

// Performance tips
console.log('\n⚡ Quick Performance Tips:');
console.log('• Use yarn build:production for releases');
console.log('• Run yarn analyze regularly to monitor bundle size');
console.log('• Check build/reports/ for detailed insights');
console.log('• Consider lazy loading for heavy components');
console.log('• Monitor the main chunk size (should be < 2MB)');

console.log('\n🚀 Happy optimizing!');
