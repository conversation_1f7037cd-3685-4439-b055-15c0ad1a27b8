/**
 * Build All Apps Script
 * Builds all apps in the multi-app system with optimizations and reporting
 */

const { execSync, spawn } = require('child_process');
const fs = require('fs');
const path = require('path');
const { switchToApp, appsConfig } = require('../initApp');

class MultiAppBuilder {
  constructor() {
    this.buildResults = {};
    this.startTime = Date.now();
    this.outputDir = path.join(__dirname, '..', 'dist-all');
    this.logFile = path.join(this.outputDir, 'build-log.json');
  }

  async buildAllApps() {
    console.log('🏗️  Starting Multi-App Build Process...');
    console.log('=' .repeat(60));

    // Create output directory
    if (!fs.existsSync(this.outputDir)) {
      fs.mkdirSync(this.outputDir, { recursive: true });
    }

    const apps = Object.keys(appsConfig);
    console.log(`📱 Found ${apps.length} apps to build:`);
    apps.forEach(app => console.log(`   - ${app} (${appsConfig[app].name})`));
    console.log('');

    // Build each app
    for (const appName of apps) {
      await this.buildSingleApp(appName);
    }

    // Generate build report
    this.generateBuildReport();
    
    console.log('\n🎉 Multi-App Build Complete!');
    console.log(`📊 Build report saved to: ${this.logFile}`);
  }

  async buildSingleApp(appName) {
    const startTime = Date.now();
    console.log(`\n🔨 Building ${appName}...`);
    console.log('-' .repeat(40));

    try {
      // Switch to the app
      if (!switchToApp(appName)) {
        throw new Error(`Failed to switch to app: ${appName}`);
      }

      // Run the build
      const buildOutput = execSync('yarn build', { 
        encoding: 'utf8',
        stdio: 'pipe',
        maxBuffer: 1024 * 1024 * 10 // 10MB buffer
      });

      const buildTime = Date.now() - startTime;
      
      // Move build output to app-specific directory
      const appBuildDir = path.join(this.outputDir, appName);
      const sourceBuildDir = path.join(__dirname, '..', 'build');
      
      if (fs.existsSync(sourceBuildDir)) {
        // Copy build to app-specific directory
        execSync(`xcopy "${sourceBuildDir}" "${appBuildDir}" /E /I /Y`, { stdio: 'pipe' });
        
        // Analyze build size
        const buildStats = this.analyzeBuildSize(appBuildDir);
        
        this.buildResults[appName] = {
          status: 'success',
          buildTime,
          buildStats,
          timestamp: new Date().toISOString(),
          output: buildOutput.slice(-1000) // Last 1000 chars
        };

        console.log(`✅ ${appName} built successfully in ${(buildTime / 1000).toFixed(2)}s`);
        console.log(`📦 Build size: ${(buildStats.totalSize / 1024 / 1024).toFixed(2)} MB`);
      } else {
        throw new Error('Build directory not found after build');
      }

    } catch (error) {
      const buildTime = Date.now() - startTime;
      
      this.buildResults[appName] = {
        status: 'failed',
        buildTime,
        error: error.message,
        timestamp: new Date().toISOString()
      };

      console.log(`❌ ${appName} build failed after ${(buildTime / 1000).toFixed(2)}s`);
      console.log(`   Error: ${error.message}`);
    }
  }

  analyzeBuildSize(buildDir) {
    const stats = {
      totalSize: 0,
      fileCount: 0,
      jsSize: 0,
      cssSize: 0,
      assetSize: 0,
      files: []
    };

    const analyzeDirectory = (dir) => {
      const items = fs.readdirSync(dir);
      
      items.forEach(item => {
        const itemPath = path.join(dir, item);
        const stat = fs.statSync(itemPath);
        
        if (stat.isDirectory()) {
          analyzeDirectory(itemPath);
        } else {
          const size = stat.size;
          const ext = path.extname(item).toLowerCase();
          
          stats.totalSize += size;
          stats.fileCount++;
          
          if (ext === '.js') {
            stats.jsSize += size;
          } else if (ext === '.css') {
            stats.cssSize += size;
          } else {
            stats.assetSize += size;
          }
          
          // Track large files
          if (size > 100 * 1024) { // Files > 100KB
            stats.files.push({
              name: path.relative(buildDir, itemPath),
              size,
              type: ext
            });
          }
        }
      });
    };

    if (fs.existsSync(buildDir)) {
      analyzeDirectory(buildDir);
    }

    return stats;
  }

  generateBuildReport() {
    const totalTime = Date.now() - this.startTime;
    const successCount = Object.values(this.buildResults).filter(r => r.status === 'success').length;
    const failCount = Object.values(this.buildResults).filter(r => r.status === 'failed').length;

    const report = {
      summary: {
        totalApps: Object.keys(appsConfig).length,
        successful: successCount,
        failed: failCount,
        totalBuildTime: totalTime,
        timestamp: new Date().toISOString()
      },
      results: this.buildResults,
      recommendations: this.generateRecommendations()
    };

    // Save detailed report
    fs.writeFileSync(this.logFile, JSON.stringify(report, null, 2));

    // Print summary
    console.log('\n📊 Build Summary:');
    console.log('=' .repeat(40));
    console.log(`✅ Successful: ${successCount}`);
    console.log(`❌ Failed: ${failCount}`);
    console.log(`⏱️  Total time: ${(totalTime / 1000).toFixed(2)}s`);
    
    if (successCount > 0) {
      const avgBuildTime = Object.values(this.buildResults)
        .filter(r => r.status === 'success')
        .reduce((sum, r) => sum + r.buildTime, 0) / successCount;
      
      console.log(`📈 Average build time: ${(avgBuildTime / 1000).toFixed(2)}s`);
    }

    // Show failed builds
    if (failCount > 0) {
      console.log('\n❌ Failed Builds:');
      Object.entries(this.buildResults)
        .filter(([_, result]) => result.status === 'failed')
        .forEach(([app, result]) => {
          console.log(`   - ${app}: ${result.error}`);
        });
    }
  }

  generateRecommendations() {
    const recommendations = [];
    
    // Analyze build times
    const buildTimes = Object.values(this.buildResults)
      .filter(r => r.status === 'success')
      .map(r => r.buildTime);
    
    if (buildTimes.length > 0) {
      const avgTime = buildTimes.reduce((a, b) => a + b, 0) / buildTimes.length;
      const slowBuilds = Object.entries(this.buildResults)
        .filter(([_, r]) => r.status === 'success' && r.buildTime > avgTime * 1.5);
      
      if (slowBuilds.length > 0) {
        recommendations.push({
          type: 'performance',
          message: `Consider optimizing slow builds: ${slowBuilds.map(([app]) => app).join(', ')}`,
          apps: slowBuilds.map(([app]) => app)
        });
      }
    }

    // Analyze build sizes
    const largeBuildApps = Object.entries(this.buildResults)
      .filter(([_, r]) => r.status === 'success' && r.buildStats && r.buildStats.totalSize > 5 * 1024 * 1024) // > 5MB
      .map(([app]) => app);
    
    if (largeBuildApps.length > 0) {
      recommendations.push({
        type: 'size',
        message: `Consider optimizing large builds: ${largeBuildApps.join(', ')}`,
        apps: largeBuildApps
      });
    }

    return recommendations;
  }

  async buildSpecificApps(appNames) {
    console.log(`🏗️  Building specific apps: ${appNames.join(', ')}`);
    
    for (const appName of appNames) {
      if (!appsConfig[appName]) {
        console.log(`❌ App not found: ${appName}`);
        continue;
      }
      await this.buildSingleApp(appName);
    }
    
    this.generateBuildReport();
  }
}

// CLI interface
if (require.main === module) {
  const builder = new MultiAppBuilder();
  const args = process.argv.slice(2);
  
  if (args.length > 0) {
    // Build specific apps
    builder.buildSpecificApps(args);
  } else {
    // Build all apps
    builder.buildAllApps();
  }
}

module.exports = MultiAppBuilder;
