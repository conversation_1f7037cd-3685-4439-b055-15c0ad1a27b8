class AppFilterPlugin {
  constructor(currentApp) {
    this.currentApp = currentApp;
    this.sharedApps = ['mission-control', 'universal'];
    this.allowedApps = [currentApp, ...this.sharedApps];
    this.excludedCount = 0;
    this.includedCount = 0;

    console.log(`🔧 AppFilterPlugin initialized for: ${currentApp}`);
    console.log(`✅ Allowed apps: ${this.allowedApps.join(', ')}`);
  }

  apply(compiler) {
    compiler.hooks.normalModuleFactory.tap('AppFilterPlugin', (factory) => {
      factory.hooks.beforeResolve.tap('AppFilterPlugin', (resolveData) => {
        if (!resolveData || !resolveData.request.includes('/src/Apps/')) return;

        const request = resolveData.request;
        const appMatch = request.match(/\/Apps\/([^\/]+)\//i);

        if (appMatch) {
          const appName = appMatch[1].toLowerCase();
          const isAllowed = this.allowedApps.map(a => a.toLowerCase()).includes(appName);

          if (!isAllowed) {
            this.excludedCount++;
            console.log(`🚫 Excluding: ${appName} code from ${this.currentApp} build (${request})`);
            return false;
          } else {
            this.includedCount++;
            if (this.includedCount <= 5) { // Only log first few to avoid spam
              console.log(`✅ Including: ${appName} code in ${this.currentApp} build`);
            }
          }
        }
      });
    });

    compiler.hooks.done.tap('AppFilterPlugin', () => {
      console.log(`📊 AppFilterPlugin Summary:`);
      console.log(`   ✅ Included modules: ${this.includedCount}`);
      console.log(`   🚫 Excluded modules: ${this.excludedCount}`);
    });
  }
}

module.exports = AppFilterPlugin;
