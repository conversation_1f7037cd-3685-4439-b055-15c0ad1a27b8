// PERFORMANCE FIX: Performance monitoring and alerting script
const fs = require('fs');
const path = require('path');

class PerformanceMonitor {
  constructor() {
    this.buildDir = path.join(__dirname, '../build');
    this.reportsDir = path.join(this.buildDir, 'reports');
    this.historyFile = path.join(this.reportsDir, 'performance-history.json');
    
    // Performance thresholds
    this.thresholds = {
      totalSizeMB: 8,      // Total bundle should be < 8MB
      mainChunkMB: 2,      // Main chunk should be < 2MB
      chunkCount: 50,      // Should have < 50 chunks
      largeChunksMB: 1,    // Individual chunks should be < 1MB
      buildTimeMinutes: 3, // Build should complete in < 3 minutes
    };
  }

  // PERFORMANCE FIX: Monitor current build performance
  async monitor() {
    console.log('📊 Monitoring build performance...');
    
    if (!fs.existsSync(this.buildDir)) {
      console.error('❌ No build found. Run a build first.');
      return;
    }

    const metrics = await this.collectMetrics();
    const alerts = this.checkThresholds(metrics);
    const trends = await this.analyzeTrends(metrics);
    
    // Save current metrics to history
    await this.saveToHistory(metrics);
    
    // Generate report
    const report = {
      timestamp: new Date().toISOString(),
      metrics,
      alerts,
      trends,
      recommendations: this.generateRecommendations(metrics, alerts, trends)
    };

    // Save performance report
    fs.writeFileSync(
      path.join(this.reportsDir, 'performance-report.json'),
      JSON.stringify(report, null, 2)
    );

    // Display results
    this.displayResults(report);
    
    return report;
  }

  // PERFORMANCE FIX: Collect performance metrics
  async collectMetrics() {
    const staticDir = path.join(this.buildDir, 'static');
    const jsDir = path.join(staticDir, 'js');
    const cssDir = path.join(staticDir, 'css');
    
    let totalJS = 0;
    let totalCSS = 0;
    let chunks = [];
    let mainChunkSize = 0;

    // Analyze JavaScript files
    if (fs.existsSync(jsDir)) {
      const jsFiles = fs.readdirSync(jsDir).filter(f => f.endsWith('.js'));
      
      jsFiles.forEach(file => {
        const filePath = path.join(jsDir, file);
        const stats = fs.statSync(filePath);
        const sizeMB = stats.size / 1024 / 1024;
        
        totalJS += stats.size;
        chunks.push({
          name: file,
          size: stats.size,
          sizeMB: sizeMB.toFixed(2),
          type: this.getChunkType(file)
        });

        if (file.includes('main')) {
          mainChunkSize = stats.size;
        }
      });
    }

    // Analyze CSS files
    if (fs.existsSync(cssDir)) {
      const cssFiles = fs.readdirSync(cssDir).filter(f => f.endsWith('.css'));
      cssFiles.forEach(file => {
        const filePath = path.join(cssDir, file);
        const stats = fs.statSync(filePath);
        totalCSS += stats.size;
      });
    }

    // Calculate derived metrics
    const totalSize = totalJS + totalCSS;
    const largeChunks = chunks.filter(chunk => chunk.size > 1024 * 1024); // > 1MB

    return {
      totalSizeMB: (totalSize / 1024 / 1024).toFixed(2),
      jsSizeMB: (totalJS / 1024 / 1024).toFixed(2),
      cssSizeKB: (totalCSS / 1024).toFixed(2),
      mainChunkMB: (mainChunkSize / 1024 / 1024).toFixed(2),
      chunkCount: chunks.length,
      largeChunksCount: largeChunks.length,
      chunks: chunks.sort((a, b) => b.size - a.size),
      largeChunks: largeChunks,
    };
  }

  // PERFORMANCE FIX: Check performance thresholds
  checkThresholds(metrics) {
    const alerts = [];

    if (parseFloat(metrics.totalSizeMB) > this.thresholds.totalSizeMB) {
      alerts.push({
        level: 'critical',
        metric: 'Total Bundle Size',
        current: `${metrics.totalSizeMB}MB`,
        threshold: `${this.thresholds.totalSizeMB}MB`,
        message: 'Bundle size exceeds recommended limit'
      });
    }

    if (parseFloat(metrics.mainChunkMB) > this.thresholds.mainChunkMB) {
      alerts.push({
        level: 'warning',
        metric: 'Main Chunk Size',
        current: `${metrics.mainChunkMB}MB`,
        threshold: `${this.thresholds.mainChunkMB}MB`,
        message: 'Main chunk is too large, consider code splitting'
      });
    }

    if (metrics.chunkCount > this.thresholds.chunkCount) {
      alerts.push({
        level: 'info',
        metric: 'Chunk Count',
        current: metrics.chunkCount,
        threshold: this.thresholds.chunkCount,
        message: 'High number of chunks may impact loading performance'
      });
    }

    if (metrics.largeChunksCount > 3) {
      alerts.push({
        level: 'warning',
        metric: 'Large Chunks',
        current: metrics.largeChunksCount,
        threshold: 3,
        message: 'Multiple large chunks detected'
      });
    }

    return alerts;
  }

  // PERFORMANCE FIX: Analyze performance trends
  async analyzeTrends(currentMetrics) {
    if (!fs.existsSync(this.historyFile)) {
      return { message: 'No historical data available' };
    }

    const history = JSON.parse(fs.readFileSync(this.historyFile, 'utf8'));
    
    if (history.length < 2) {
      return { message: 'Insufficient historical data for trend analysis' };
    }

    const previous = history[history.length - 1];
    const trends = {};

    // Calculate trends
    const sizeTrend = parseFloat(currentMetrics.totalSizeMB) - parseFloat(previous.totalSizeMB);
    const chunkTrend = currentMetrics.chunkCount - previous.chunkCount;
    const mainChunkTrend = parseFloat(currentMetrics.mainChunkMB) - parseFloat(previous.mainChunkMB);

    trends.bundleSize = {
      direction: sizeTrend > 0 ? 'increasing' : sizeTrend < 0 ? 'decreasing' : 'stable',
      change: `${sizeTrend > 0 ? '+' : ''}${sizeTrend.toFixed(2)}MB`,
      percentage: ((sizeTrend / parseFloat(previous.totalSizeMB)) * 100).toFixed(1)
    };

    trends.chunkCount = {
      direction: chunkTrend > 0 ? 'increasing' : chunkTrend < 0 ? 'decreasing' : 'stable',
      change: chunkTrend
    };

    trends.mainChunk = {
      direction: mainChunkTrend > 0 ? 'increasing' : mainChunkTrend < 0 ? 'decreasing' : 'stable',
      change: `${mainChunkTrend > 0 ? '+' : ''}${mainChunkTrend.toFixed(2)}MB`
    };

    return trends;
  }

  // PERFORMANCE FIX: Generate recommendations
  generateRecommendations(metrics, alerts, trends) {
    const recommendations = [];

    // Bundle size recommendations
    if (parseFloat(metrics.totalSizeMB) > 10) {
      recommendations.push({
        priority: 'high',
        action: 'Implement aggressive code splitting',
        reason: 'Bundle size is very large (>10MB)',
        impact: 'Significant loading time improvement'
      });
    }

    // Main chunk recommendations
    if (parseFloat(metrics.mainChunkMB) > 3) {
      recommendations.push({
        priority: 'high',
        action: 'Split vendor libraries from main chunk',
        reason: 'Main chunk is too large',
        impact: 'Better caching and faster initial load'
      });
    }

    // Large chunks recommendations
    if (metrics.largeChunksCount > 2) {
      recommendations.push({
        priority: 'medium',
        action: 'Break down large chunks using dynamic imports',
        reason: 'Multiple large chunks detected',
        impact: 'Improved loading performance'
      });
    }

    // Trend-based recommendations
    if (trends.bundleSize && trends.bundleSize.direction === 'increasing') {
      recommendations.push({
        priority: 'medium',
        action: 'Review recent dependency additions',
        reason: `Bundle size is increasing (${trends.bundleSize.change})`,
        impact: 'Prevent further size growth'
      });
    }

    return recommendations;
  }

  // PERFORMANCE FIX: Save metrics to history
  async saveToHistory(metrics) {
    if (!fs.existsSync(this.reportsDir)) {
      fs.mkdirSync(this.reportsDir, { recursive: true });
    }

    let history = [];
    if (fs.existsSync(this.historyFile)) {
      history = JSON.parse(fs.readFileSync(this.historyFile, 'utf8'));
    }

    history.push({
      timestamp: new Date().toISOString(),
      ...metrics
    });

    // Keep only last 30 entries
    if (history.length > 30) {
      history = history.slice(-30);
    }

    fs.writeFileSync(this.historyFile, JSON.stringify(history, null, 2));
  }

  // PERFORMANCE FIX: Display results
  displayResults(report) {
    console.log('\n📊 Performance Report');
    console.log('=====================');
    
    // Metrics
    console.log(`📦 Total Bundle Size: ${report.metrics.totalSizeMB}MB`);
    console.log(`🎯 Main Chunk: ${report.metrics.mainChunkMB}MB`);
    console.log(`📄 Chunks: ${report.metrics.chunkCount}`);
    console.log(`⚠️  Large Chunks: ${report.metrics.largeChunksCount}`);

    // Alerts
    if (report.alerts.length > 0) {
      console.log('\n🚨 Performance Alerts:');
      report.alerts.forEach(alert => {
        const icon = alert.level === 'critical' ? '🔴' : alert.level === 'warning' ? '🟡' : '🔵';
        console.log(`${icon} ${alert.metric}: ${alert.current} (threshold: ${alert.threshold})`);
        console.log(`   ${alert.message}`);
      });
    } else {
      console.log('\n✅ All performance metrics within thresholds');
    }

    // Trends
    if (report.trends.bundleSize) {
      console.log('\n📈 Trends:');
      console.log(`Bundle Size: ${report.trends.bundleSize.direction} (${report.trends.bundleSize.change})`);
      console.log(`Chunk Count: ${report.trends.chunkCount.direction} (${report.trends.chunkCount.change})`);
    }

    // Recommendations
    if (report.recommendations.length > 0) {
      console.log('\n💡 Recommendations:');
      report.recommendations.forEach((rec, index) => {
        console.log(`${index + 1}. [${rec.priority.toUpperCase()}] ${rec.action}`);
        console.log(`   Reason: ${rec.reason}`);
        console.log(`   Impact: ${rec.impact}`);
      });
    }

    console.log(`\n📋 Full report saved to: ${path.join(this.reportsDir, 'performance-report.json')}`);
  }

  getChunkType(filename) {
    if (filename.includes('main')) return 'Main';
    if (filename.includes('vendor')) return 'Vendor';
    if (filename.includes('runtime')) return 'Runtime';
    return 'Feature';
  }
}

// Run if called directly
if (require.main === module) {
  const monitor = new PerformanceMonitor();
  monitor.monitor().catch(console.error);
}

module.exports = PerformanceMonitor;
