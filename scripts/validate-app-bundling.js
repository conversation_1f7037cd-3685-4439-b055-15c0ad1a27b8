/**
 * App-Specific Bundling Validation Script
 * 
 * This script validates that the app-specific bundling implementation
 * is working correctly by testing different scenarios.
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

class BundlingValidator {
  constructor() {
    this.appsConfig = JSON.parse(fs.readFileSync('apps.json', 'utf8'));
    this.testResults = [];
  }

  async validateImplementation() {
    console.log('🔍 Validating App-Specific Bundling Implementation');
    console.log('=' .repeat(60));

    // Test 1: Validate webpack plugin logic
    await this.testPluginLogic();

    // Test 2: Validate app switching mechanism
    await this.testAppSwitching();

    // Test 3: Validate configuration integration
    await this.testConfigurationIntegration();

    // Test 4: Validate Mission Control imports
    await this.testMissionControlImports();

    // Test 5: Validate Differential Updates
    await this.testDifferentialUpdates();

    // Generate report
    this.generateReport();
  }

  async testPluginLogic() {
    console.log('\n📋 Test 1: App Filter Plugin Logic');
    console.log('-' .repeat(40));

    try {
      // Test with different apps
      const testApps = ['zenwrench', 'prosy', 'abacus'];
      
      for (const app of testApps) {
        console.log(`\n🧪 Testing plugin logic for: ${app}`);
        
        // Switch to app
        execSync(`node initApp.js ${app}`, { stdio: 'pipe' });
        
        // Run plugin test
        const output = execSync('node scripts/test-app-filter.js', { 
          encoding: 'utf8',
          stdio: 'pipe'
        });
        
        // Validate output contains expected exclusions
        const hasExclusions = output.includes('🚫 EXCLUDE:');
        const hasInclusions = output.includes('✅ INCLUDE:');
        const correctApp = output.includes(`Current app: ${app}`);
        
        this.testResults.push({
          test: `Plugin Logic - ${app}`,
          passed: hasExclusions && hasInclusions && correctApp,
          details: `Exclusions: ${hasExclusions}, Inclusions: ${hasInclusions}, Correct App: ${correctApp}`
        });
        
        console.log(`   ${hasExclusions && hasInclusions && correctApp ? '✅' : '❌'} Plugin logic test`);
      }
    } catch (error) {
      console.error('❌ Plugin logic test failed:', error.message);
      this.testResults.push({
        test: 'Plugin Logic',
        passed: false,
        details: error.message
      });
    }
  }

  async testAppSwitching() {
    console.log('\n📋 Test 2: App Switching Mechanism');
    console.log('-' .repeat(40));

    try {
      const testApps = ['zenwrench', 'prosy'];
      
      for (const app of testApps) {
        console.log(`\n🔄 Testing app switch to: ${app}`);
        
        // Switch app
        execSync(`node initApp.js ${app}`, { stdio: 'pipe' });
        
        // Verify package.json was updated
        const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
        const correctName = packageJson.name === app;
        const correctVersion = packageJson.version === this.appsConfig[app].version;
        
        // Verify other config files
        const capacitorConfig = JSON.parse(fs.readFileSync('capacitor.config.json', 'utf8'));
        const correctCapacitor = capacitorConfig.appName === this.appsConfig[app].name;
        
        this.testResults.push({
          test: `App Switching - ${app}`,
          passed: correctName && correctVersion && correctCapacitor,
          details: `Name: ${correctName}, Version: ${correctVersion}, Capacitor: ${correctCapacitor}`
        });
        
        console.log(`   ${correctName ? '✅' : '❌'} Package.json name updated`);
        console.log(`   ${correctVersion ? '✅' : '❌'} Package.json version updated`);
        console.log(`   ${correctCapacitor ? '✅' : '❌'} Capacitor config updated`);
      }
    } catch (error) {
      console.error('❌ App switching test failed:', error.message);
      this.testResults.push({
        test: 'App Switching',
        passed: false,
        details: error.message
      });
    }
  }

  async testConfigurationIntegration() {
    console.log('\n📋 Test 3: Configuration Integration');
    console.log('-' .repeat(40));

    try {
      // Test webpack config syntax
      execSync('node -c config-overrides.js', { stdio: 'pipe' });
      console.log('   ✅ Webpack config syntax valid');
      
      // Test plugin file syntax
      execSync('node -c scripts/webpack-app-filter-plugin.js', { stdio: 'pipe' });
      console.log('   ✅ App filter plugin syntax valid');
      
      // Test if config can be loaded
      const configOverrides = require('../config-overrides.js');
      const isFunction = typeof configOverrides === 'function';
      console.log(`   ${isFunction ? '✅' : '❌'} Config exports function`);
      
      this.testResults.push({
        test: 'Configuration Integration',
        passed: isFunction,
        details: `Config is function: ${isFunction}`
      });
      
    } catch (error) {
      console.error('❌ Configuration integration test failed:', error.message);
      this.testResults.push({
        test: 'Configuration Integration',
        passed: false,
        details: error.message
      });
    }
  }

  async testMissionControlImports() {
    console.log('\n📋 Test 4: Mission Control Imports');
    console.log('-' .repeat(40));

    try {
      // Test if mission control imports file exists and is valid
      const importsPath = 'src/utils/missionControlImports.js';
      const exists = fs.existsSync(importsPath);
      console.log(`   ${exists ? '✅' : '❌'} Mission Control imports file exists`);
      
      if (exists) {
        // Test syntax
        execSync(`node -c ${importsPath}`, { stdio: 'pipe' });
        console.log('   ✅ Mission Control imports syntax valid');
      }
      
      this.testResults.push({
        test: 'Mission Control Imports',
        passed: exists,
        details: `File exists: ${exists}`
      });
      
    } catch (error) {
      console.error('❌ Mission Control imports test failed:', error.message);
      this.testResults.push({
        test: 'Mission Control Imports',
        passed: false,
        details: error.message
      });
    }
  }

  async testDifferentialUpdates() {
    console.log('\n📋 Test 5: Differential Updates');
    console.log('-' .repeat(40));

    try {
      // Test if differential release script exists
      const scriptPath = 'scripts/release-differential.js';
      const exists = fs.existsSync(scriptPath);
      console.log(`   ${exists ? '✅' : '❌'} Differential release script exists`);

      if (exists) {
        // Test syntax
        execSync(`node -c ${scriptPath}`, { stdio: 'pipe' });
        console.log('   ✅ Differential release script syntax valid');
      }

      // Test AutoUpdater configuration
      const autoUpdaterPath = 'electron/AutoUpdater.js';
      const autoUpdaterExists = fs.existsSync(autoUpdaterPath);
      console.log(`   ${autoUpdaterExists ? '✅' : '❌'} AutoUpdater exists`);

      if (autoUpdaterExists) {
        const content = fs.readFileSync(autoUpdaterPath, 'utf8');
        const hasDifferentialConfig = content.includes('disableDifferentialDownload = false') &&
                                    content.includes('differentialDownloadOptions');
        console.log(`   ${hasDifferentialConfig ? '✅' : '❌'} Differential download configured`);
      }

      // Test package.json configuration
      const packagePath = 'package.json';
      const packageExists = fs.existsSync(packagePath);
      if (packageExists) {
        const pkg = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
        const hasScripts = pkg.scripts && pkg.scripts['release:differential'];
        const hasNsisConfig = pkg.build?.nsis?.differentialPackage === true;
        console.log(`   ${hasScripts ? '✅' : '❌'} Differential release scripts configured`);
        console.log(`   ${hasNsisConfig ? '✅' : '❌'} NSIS differential package enabled`);
      }

      this.testResults.push({
        test: 'Differential Updates',
        passed: exists && autoUpdaterExists,
        details: `Script exists: ${exists}, AutoUpdater exists: ${autoUpdaterExists}`
      });

    } catch (error) {
      console.error('❌ Differential updates test failed:', error.message);
      this.testResults.push({
        test: 'Differential Updates',
        passed: false,
        details: error.message
      });
    }
  }

  generateReport() {
    console.log('\n📊 Validation Report');
    console.log('=' .repeat(60));
    
    const passedTests = this.testResults.filter(r => r.passed).length;
    const totalTests = this.testResults.length;
    const successRate = Math.round((passedTests / totalTests) * 100);
    
    console.log(`\n🎯 Overall Success Rate: ${successRate}% (${passedTests}/${totalTests})`);
    
    console.log('\n📋 Detailed Results:');
    this.testResults.forEach(result => {
      const status = result.passed ? '✅ PASS' : '❌ FAIL';
      console.log(`   ${status} ${result.test}`);
      if (!result.passed) {
        console.log(`      Details: ${result.details}`);
      }
    });
    
    console.log('\n🚀 Implementation Status:');
    if (successRate >= 80) {
      console.log('✅ App-specific bundling implementation is working correctly!');
      console.log('🎉 Ready for production use.');
    } else if (successRate >= 60) {
      console.log('⚠️  App-specific bundling implementation has some issues.');
      console.log('🔧 Review failed tests and fix issues before production use.');
    } else {
      console.log('❌ App-specific bundling implementation has significant issues.');
      console.log('🛠️  Major fixes required before production use.');
    }

    console.log('\n📝 Next Steps:');
    console.log('1. Run actual builds to test bundle size reduction');
    console.log('2. Test app functionality after switching');
    console.log('3. Monitor build performance improvements');
    console.log('4. Update import statements to use Mission Control helper');
    console.log('5. Test differential updates with: yarn release:differential');
  }
}

// Run validation if called directly
if (require.main === module) {
  const validator = new BundlingValidator();
  validator.validateImplementation().catch(console.error);
}

module.exports = BundlingValidator;
