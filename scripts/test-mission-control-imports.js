/**
 * Test Mission Control Imports Implementation
 * 
 * This script tests that the centralized mission control imports
 * are working correctly and all imports resolve properly.
 */

const fs = require('fs');
const path = require('path');

class MissionControlImportsValidator {
  constructor() {
    this.testResults = [];
    this.importHelperPath = 'src/utils/missionControlImports.js';
  }

  async validateImplementation() {
    console.log('🔍 Validating Mission Control Imports Implementation');
    console.log('=' .repeat(60));

    // Test 1: Validate import helper syntax
    await this.testImportHelperSyntax();

    // Test 2: Validate webpack alias configuration
    await this.testWebpackAliasConfiguration();

    // Test 3: Validate updated import statements
    await this.testUpdatedImportStatements();

    // Test 4: Check for circular dependencies
    await this.testCircularDependencies();

    // Generate report
    this.generateReport();
  }

  async testImportHelperSyntax() {
    console.log('\n📋 Test 1: Import Helper Syntax');
    console.log('-' .repeat(40));

    try {
      // Test if import helper exists and has valid syntax
      const exists = fs.existsSync(this.importHelperPath);
      console.log(`   ${exists ? '✅' : '❌'} Mission Control imports file exists`);
      
      if (exists) {
        // Test syntax
        const { execSync } = require('child_process');
        execSync(`node -c ${this.importHelperPath}`, { stdio: 'pipe' });
        console.log('   ✅ Mission Control imports syntax valid');
        
        // Check file content
        const content = fs.readFileSync(this.importHelperPath, 'utf8');
        const hasUtilityExports = content.includes('getSoftwareOptions');
        const hasModuleExports = content.includes('organizations');
        const hasConstantsExports = content.includes('export * from');
        
        console.log(`   ${hasUtilityExports ? '✅' : '❌'} Utility exports present`);
        console.log(`   ${hasModuleExports ? '✅' : '❌'} Module exports present`);
        console.log(`   ${hasConstantsExports ? '✅' : '❌'} Constants exports present`);
        
        this.testResults.push({
          test: 'Import Helper Syntax',
          passed: exists && hasUtilityExports && hasModuleExports,
          details: `Exists: ${exists}, Utils: ${hasUtilityExports}, Modules: ${hasModuleExports}`
        });
      }
    } catch (error) {
      console.error('❌ Import helper syntax test failed:', error.message);
      this.testResults.push({
        test: 'Import Helper Syntax',
        passed: false,
        details: error.message
      });
    }
  }

  async testWebpackAliasConfiguration() {
    console.log('\n📋 Test 2: Webpack Alias Configuration');
    console.log('-' .repeat(40));

    try {
      // Check webpack config
      const configPath = 'config-overrides.js';
      const configExists = fs.existsSync(configPath);
      console.log(`   ${configExists ? '✅' : '❌'} Webpack config exists`);
      
      if (configExists) {
        const configContent = fs.readFileSync(configPath, 'utf8');
        const hasMissionControlAlias = configContent.includes('@mission-control');
        const aliasPointsToImports = configContent.includes('src/utils/missionControlImports');
        
        console.log(`   ${hasMissionControlAlias ? '✅' : '❌'} @mission-control alias present`);
        console.log(`   ${aliasPointsToImports ? '✅' : '❌'} Alias points to imports helper`);
        
        this.testResults.push({
          test: 'Webpack Alias Configuration',
          passed: configExists && hasMissionControlAlias && aliasPointsToImports,
          details: `Config: ${configExists}, Alias: ${hasMissionControlAlias}, Points to helper: ${aliasPointsToImports}`
        });
      }
    } catch (error) {
      console.error('❌ Webpack alias test failed:', error.message);
      this.testResults.push({
        test: 'Webpack Alias Configuration',
        passed: false,
        details: error.message
      });
    }
  }

  async testUpdatedImportStatements() {
    console.log('\n📋 Test 3: Updated Import Statements');
    console.log('-' .repeat(40));

    try {
      const filesToCheck = [
        'src/Apps/mission-control/modules/plans.js',
        'src/Apps/mission-control/modules/organizations.js',
        'src/Apps/mission-control/modules/installation_packages.js',
        'src/Components/InitCompany/index.js'
      ];

      let allUpdated = true;
      let updatedCount = 0;

      for (const filePath of filesToCheck) {
        if (fs.existsSync(filePath)) {
          const content = fs.readFileSync(filePath, 'utf8');
          const usesNewImport = content.includes('from "@mission-control"');
          const usesOldImport = content.includes('from "../utils/softwareModules"') || 
                               content.includes('from "../../Apps/mission-control/modules"');
          
          if (usesNewImport && !usesOldImport) {
            console.log(`   ✅ ${filePath} - Updated to use @mission-control`);
            updatedCount++;
          } else if (usesOldImport) {
            console.log(`   ⚠️  ${filePath} - Still uses old import paths`);
            allUpdated = false;
          } else {
            console.log(`   ⚪ ${filePath} - No mission-control imports found`);
          }
        } else {
          console.log(`   ❌ ${filePath} - File not found`);
          allUpdated = false;
        }
      }

      console.log(`   📊 Updated ${updatedCount}/${filesToCheck.length} files`);

      this.testResults.push({
        test: 'Updated Import Statements',
        passed: allUpdated && updatedCount > 0,
        details: `Updated ${updatedCount}/${filesToCheck.length} files`
      });

    } catch (error) {
      console.error('❌ Import statements test failed:', error.message);
      this.testResults.push({
        test: 'Updated Import Statements',
        passed: false,
        details: error.message
      });
    }
  }

  async testCircularDependencies() {
    console.log('\n📋 Test 4: Circular Dependencies Check');
    console.log('-' .repeat(40));

    try {
      // Basic check for potential circular dependencies
      const importHelperContent = fs.readFileSync(this.importHelperPath, 'utf8');
      const importsFromMissionControl = importHelperContent.includes('../Apps/mission-control/');
      const noSelfImport = !importHelperContent.includes('from "@mission-control"');
      
      console.log(`   ${importsFromMissionControl ? '✅' : '❌'} Imports from mission-control directory`);
      console.log(`   ${noSelfImport ? '✅' : '❌'} No self-referential imports`);
      
      this.testResults.push({
        test: 'Circular Dependencies Check',
        passed: importsFromMissionControl && noSelfImport,
        details: `Imports MC: ${importsFromMissionControl}, No self-import: ${noSelfImport}`
      });

    } catch (error) {
      console.error('❌ Circular dependencies test failed:', error.message);
      this.testResults.push({
        test: 'Circular Dependencies Check',
        passed: false,
        details: error.message
      });
    }
  }

  generateReport() {
    console.log('\n📊 Mission Control Imports Validation Report');
    console.log('=' .repeat(60));
    
    const passedTests = this.testResults.filter(r => r.passed).length;
    const totalTests = this.testResults.length;
    const successRate = Math.round((passedTests / totalTests) * 100);
    
    console.log(`\n🎯 Overall Success Rate: ${successRate}% (${passedTests}/${totalTests})`);
    
    console.log('\n📋 Detailed Results:');
    this.testResults.forEach(result => {
      const status = result.passed ? '✅ PASS' : '❌ FAIL';
      console.log(`   ${status} ${result.test}`);
      if (!result.passed) {
        console.log(`      Details: ${result.details}`);
      }
    });
    
    console.log('\n🚀 Implementation Status:');
    if (successRate >= 80) {
      console.log('✅ Mission Control imports implementation is working correctly!');
      console.log('🎉 Ready for use with @mission-control alias.');
    } else if (successRate >= 60) {
      console.log('⚠️  Mission Control imports implementation has some issues.');
      console.log('🔧 Review failed tests and fix issues.');
    } else {
      console.log('❌ Mission Control imports implementation has significant issues.');
      console.log('🛠️  Major fixes required.');
    }
    
    console.log('\n📝 Usage:');
    console.log('// Import mission control utilities and modules:');
    console.log('import { getSoftwareOptions, organizations } from "@mission-control";');
  }
}

// Run validation if called directly
if (require.main === module) {
  const validator = new MissionControlImportsValidator();
  validator.validateImplementation().catch(console.error);
}

module.exports = MissionControlImportsValidator;
