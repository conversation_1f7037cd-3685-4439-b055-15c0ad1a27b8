#!/usr/bin/env node

// Load environment variables from .env file
require('dotenv').config();

const { execSync } = require('child_process');
const path = require('path');

console.log('🚀 Starting release with environment variables...');
console.log('================================================');

// Check if GH_TOKEN is available
const token = process.env.GH_TOKEN || process.env.GITHUB_TOKEN;
if (!token) {
  console.error('❌ GitHub token not found!');
  console.error('Please ensure GH_TOKEN or GITHUB_TOKEN is set in your .env file');
  process.exit(1);
}

console.log(`✅ GitHub token found: ${token.substring(0, 10)}...`);

// Set environment variables for the build process
process.env.GH_TOKEN = token;
process.env.GITHUB_TOKEN = token;

try {
  console.log('🏗️  Building and releasing...');
  
  // Run the release command
  execSync('yarn build && electron-builder --windows --publish always', {
    stdio: 'inherit',
    cwd: path.join(__dirname, '..'),
    env: {
      ...process.env,
      GH_TOKEN: token,
      GITHUB_TOKEN: token
    }
  });
  
  console.log('✅ Release completed successfully!');
} catch (error) {
  console.error('❌ Release failed:', error.message);
  process.exit(1);
}
