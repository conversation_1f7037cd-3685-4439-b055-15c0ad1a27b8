#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Load apps configuration
const appsPath = path.join(__dirname, '../src/config/apps.json');
const apps = JSON.parse(fs.readFileSync(appsPath, 'utf8'));

function incrementVersion(version, type) {
  const parts = version.split('.').map(Number);
  switch(type) {
    case 'major': return `${parts[0] + 1}.0.0`;
    case 'minor': return `${parts[0]}.${parts[1] + 1}.0`;
    case 'patch': return `${parts[0]}.${parts[1]}.${parts[2] + 1}`;
    default: return version;
  }
}

function showUsage() {
  console.log(`
📦 App Version Manager

Usage:
  node scripts/manage-versions.js <command> [options]

Commands:
  list                          - List all apps and their versions
  bump <app> <type>            - Bump version for specific app
  bump-all <type>              - Bump version for all apps
  set <app> <version>          - Set specific version for app
  sync                         - Sync package.json with apps.json versions

Version Types:
  patch    - 0.0.1 -> 0.0.2
  minor    - 0.0.1 -> 0.1.0  
  major    - 0.0.1 -> 1.0.0

Examples:
  node scripts/manage-versions.js list
  node scripts/manage-versions.js bump prosy patch
  node scripts/manage-versions.js bump-all patch
  node scripts/manage-versions.js set prosy 1.0.0
  node scripts/manage-versions.js sync
`);
}

function listApps() {
  console.log('\n📋 Current App Versions:');
  console.log('========================');
  
  Object.entries(apps).forEach(([key, app]) => {
    console.log(`${app.name.padEnd(25)} v${app.version.padEnd(10)} (${key})`);
  });
  
  console.log(`\n📊 Total Apps: ${Object.keys(apps).length}`);
}

function bumpApp(appName, bumpType) {
  if (!apps[appName]) {
    console.error(`❌ App '${appName}' not found!`);
    console.log(`Available apps: ${Object.keys(apps).join(', ')}`);
    return false;
  }
  
  const oldVersion = apps[appName].version;
  const newVersion = incrementVersion(oldVersion, bumpType);
  
  apps[appName].version = newVersion;
  
  console.log(`✅ Updated ${apps[appName].name}: ${oldVersion} -> ${newVersion}`);
  return true;
}

function bumpAllApps(bumpType) {
  console.log(`🚀 Bumping all apps (${bumpType}):`);
  console.log('================================');
  
  let updated = 0;
  Object.keys(apps).forEach(appName => {
    if (bumpApp(appName, bumpType)) {
      updated++;
    }
  });
  
  console.log(`\n📊 Updated ${updated} apps`);
  return updated > 0;
}

function setAppVersion(appName, version) {
  if (!apps[appName]) {
    console.error(`❌ App '${appName}' not found!`);
    return false;
  }
  
  const oldVersion = apps[appName].version;
  apps[appName].version = version;
  
  console.log(`✅ Set ${apps[appName].name}: ${oldVersion} -> ${version}`);
  return true;
}

function saveApps() {
  fs.writeFileSync(appsPath, JSON.stringify(apps, null, 2));
  console.log(`💾 Saved changes to ${appsPath}`);
}

function syncPackageJson() {
  const packagePath = path.join(__dirname, '../package.json');
  const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
  
  // Find the current app from package.json name
  const currentApp = Object.keys(apps).find(key => key === packageJson.name);
  
  if (currentApp && apps[currentApp]) {
    const oldVersion = packageJson.version;
    const newVersion = apps[currentApp].version;
    
    if (oldVersion !== newVersion) {
      packageJson.version = newVersion;
      fs.writeFileSync(packagePath, JSON.stringify(packageJson, null, '\t'));
      console.log(`✅ Synced package.json: ${oldVersion} -> ${newVersion}`);
    } else {
      console.log(`✅ package.json already in sync (${newVersion})`);
    }
  } else {
    console.log(`ℹ️  No matching app found for package.json name: ${packageJson.name}`);
  }
}

// Main execution
const [,, command, ...args] = process.argv;

switch (command) {
  case 'list':
    listApps();
    break;
    
  case 'bump':
    if (args.length !== 2) {
      console.error('❌ Usage: bump <app> <type>');
      process.exit(1);
    }
    if (bumpApp(args[0], args[1])) {
      saveApps();
    }
    break;
    
  case 'bump-all':
    if (args.length !== 1) {
      console.error('❌ Usage: bump-all <type>');
      process.exit(1);
    }
    if (bumpAllApps(args[0])) {
      saveApps();
    }
    break;
    
  case 'set':
    if (args.length !== 2) {
      console.error('❌ Usage: set <app> <version>');
      process.exit(1);
    }
    if (setAppVersion(args[0], args[1])) {
      saveApps();
    }
    break;
    
  case 'sync':
    syncPackageJson();
    break;
    
  default:
    showUsage();
    if (command) {
      console.error(`❌ Unknown command: ${command}`);
      process.exit(1);
    }
}
