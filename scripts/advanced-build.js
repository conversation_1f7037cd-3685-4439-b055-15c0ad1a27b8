// PERFORMANCE FIX: Advanced build optimization script
const fs = require('fs');
const path = require('path');
const { spawn } = require('child_process');
const { promisify } = require('util');

// PERFORMANCE FIX: Enhanced environment setup
function setupEnvironment() {
  // Core optimizations
  process.env.GENERATE_SOURCEMAP = 'false';
  process.env.INLINE_RUNTIME_CHUNK = 'false';
  process.env.IMAGE_INLINE_SIZE_LIMIT = '0';
  process.env.ESLint_NO_DEV_ERRORS = 'true';
  process.env.TSC_COMPILE_ON_ERROR = 'true';
  process.env.DISABLE_ESLINT_PLUGIN = 'true';
  
  // React optimizations
  process.env.REACT_APP_OPTIMIZE_BUNDLE = 'true';
  process.env.NODE_ENV = 'production';
  process.env.BABEL_ENV = 'production';
  
  // Memory optimizations
  process.env.NODE_OPTIONS = '--max-old-space-size=8192 --optimize-for-size';
  
  // Bundle analysis
  process.env.WEBPACK_ANALYZE = process.env.ANALYZE_BUNDLE || 'false';
  
  console.log('🔧 Environment configured for optimized build');
}

// PERFORMANCE FIX: Pre-build cleanup
async function preBuildCleanup() {
  const buildDir = path.join(__dirname, '../build');
  
  if (fs.existsSync(buildDir)) {
    console.log('🧹 Cleaning previous build...');
    fs.rmSync(buildDir, { recursive: true, force: true });
  }
  
  // Clean webpack cache
  const cacheDir = path.join(__dirname, '../node_modules/.cache');
  if (fs.existsSync(cacheDir)) {
    console.log('🗑️  Clearing webpack cache...');
    fs.rmSync(cacheDir, { recursive: true, force: true });
  }
}

// PERFORMANCE FIX: Post-build optimizations
async function postBuildOptimizations() {
  console.log('⚡ Running post-build optimizations...');
  
  // Analyze and report bundle sizes
  await analyzeBundleSize();
  
  // Generate build report
  await generateBuildReport();
  
  // Optimize static assets
  await optimizeStaticAssets();
}

// PERFORMANCE FIX: Enhanced bundle analysis
async function analyzeBundleSize() {
  const buildDir = path.join(__dirname, '../build/static');
  
  if (!fs.existsSync(buildDir)) {
    console.log('📦 Build directory not found, skipping analysis');
    return;
  }
  
  console.log('\n📊 Detailed Bundle Analysis:');
  console.log('============================');
  
  let totalJSSize = 0;
  let totalCSSSize = 0;
  let chunkSizes = [];
  
  // Analyze JS files
  const jsDir = path.join(buildDir, 'js');
  if (fs.existsSync(jsDir)) {
    const jsFiles = fs.readdirSync(jsDir).filter(file => file.endsWith('.js'));
    
    jsFiles.forEach(file => {
      const filePath = path.join(jsDir, file);
      const stats = fs.statSync(filePath);
      const sizeKB = (stats.size / 1024).toFixed(2);
      totalJSSize += stats.size;
      
      const type = getChunkType(file);
      chunkSizes.push({ file, size: stats.size, sizeKB, type });
      
      console.log(`   ${type}: ${file} (${sizeKB} KB)`);
    });
    
    console.log(`   📊 Total JS: ${(totalJSSize / 1024 / 1024).toFixed(2)} MB`);
  }
  
  // Analyze CSS files
  const cssDir = path.join(buildDir, 'css');
  if (fs.existsSync(cssDir)) {
    const cssFiles = fs.readdirSync(cssDir).filter(file => file.endsWith('.css'));
    
    cssFiles.forEach(file => {
      const filePath = path.join(cssDir, file);
      const stats = fs.statSync(filePath);
      const sizeKB = (stats.size / 1024).toFixed(2);
      totalCSSSize += stats.size;
      console.log(`   CSS: ${file} (${sizeKB} KB)`);
    });
    
    console.log(`   🎨 Total CSS: ${(totalCSSSize / 1024).toFixed(2)} KB`);
  }
  
  // Performance recommendations
  console.log('\n💡 Performance Recommendations:');
  console.log('================================');
  
  const totalSizeMB = (totalJSSize + totalCSSSize) / 1024 / 1024;
  
  if (totalSizeMB > 10) {
    console.log('   ⚠️  Large bundle detected (>10MB)');
    console.log('   💡 Consider implementing lazy loading for routes');
    console.log('   💡 Split large vendor libraries into separate chunks');
  } else if (totalSizeMB > 5) {
    console.log('   ⚠️  Medium bundle size (>5MB)');
    console.log('   💡 Consider code splitting for non-critical features');
  } else {
    console.log('   ✅ Bundle size is reasonable');
  }
  
  // Find largest chunks
  const largestChunks = chunkSizes
    .filter(chunk => chunk.size > 1024 * 1024) // > 1MB
    .sort((a, b) => b.size - a.size)
    .slice(0, 5);
    
  if (largestChunks.length > 0) {
    console.log('\n🔍 Largest Chunks (>1MB):');
    largestChunks.forEach(chunk => {
      console.log(`   ${chunk.file}: ${chunk.sizeKB} KB`);
    });
  }
}

function getChunkType(filename) {
  if (filename.includes('main')) return 'Main';
  if (filename.includes('vendor')) return 'Vendor';
  if (filename.includes('runtime')) return 'Runtime';
  if (filename.includes('antd')) return 'Antd';
  if (filename.includes('react')) return 'React';
  if (filename.includes('charts')) return 'Charts';
  if (filename.includes('documents')) return 'Documents';
  if (filename.includes('database')) return 'Database';
  if (filename.includes('utils')) return 'Utils';
  return 'Chunk';
}

// PERFORMANCE FIX: Generate build report
async function generateBuildReport() {
  const buildTime = new Date().toISOString();
  const report = {
    buildTime,
    nodeVersion: process.version,
    environment: process.env.NODE_ENV,
    optimizations: {
      sourceMaps: process.env.GENERATE_SOURCEMAP === 'true',
      bundleAnalysis: process.env.ANALYZE_BUNDLE === 'true',
      codesplitting: true,
      minification: true,
    }
  };
  
  fs.writeFileSync(
    path.join(__dirname, '../build/build-report.json'),
    JSON.stringify(report, null, 2)
  );
  
  console.log('📋 Build report generated: build/build-report.json');
}

// PERFORMANCE FIX: Optimize static assets
async function optimizeStaticAssets() {
  console.log('🖼️  Optimizing static assets...');
  
  // Add any additional asset optimization here
  // For example, image compression, font optimization, etc.
  
  console.log('✅ Static assets optimized');
}

// Main build function
async function runAdvancedBuild() {
  console.log('🚀 Starting advanced optimized build...');
  
  try {
    setupEnvironment();
    await preBuildCleanup();
    
    console.log('📦 Building application...');
    
    const buildProcess = spawn('react-scripts-less', ['build'], {
      stdio: 'inherit',
      shell: true,
      env: process.env
    });
    
    buildProcess.on('close', async (code) => {
      if (code === 0) {
        console.log('✅ Build completed successfully!');
        await postBuildOptimizations();
        console.log('🎉 Advanced build process completed!');
      } else {
        console.error('❌ Build failed with code:', code);
        process.exit(code);
      }
    });
    
  } catch (error) {
    console.error('❌ Build process failed:', error);
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  runAdvancedBuild();
}

module.exports = { runAdvancedBuild, analyzeBundleSize };
