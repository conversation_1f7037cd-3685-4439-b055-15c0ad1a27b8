# Environment configuration for Mission Control (mission-control)
# Generated automatically - modify with caution

# App Identity
REACT_APP_NAME=mission-control
REACT_APP_DISPLAY_NAME=Mission Control
REACT_APP_VERSION=0.0.7
REACT_APP_TYPE=Haclab Management Software
REACT_APP_COLOR=#ff0000

# Vite Environment Variables (for Vite compatibility)
VITE_APP_NAME=mission-control
VITE_APP_DISPLAY_NAME=Mission Control
VITE_APP_VERSION=0.0.7
VITE_APP_TYPE=Haclab Management Software
VITE_APP_COLOR=#ff0000

# Development
PORT=3007
BROWSER=none
FAST_REFRESH=true
GENERATE_SOURCEMAP=true

# Database
REACT_APP_DATABASE_PREFIX=mission-control_
REACT_APP_DATABASE_NAME=mission-control_db
VITE_DATABASE_PREFIX=mission-control_
VITE_DATABASE_NAME=mission-control_db

# Build Configuration
BUILD_PATH=build-mission-control
PUBLIC_URL=.

# API Configuration
REACT_APP_API_BASE_URL=http://localhost:4007
REACT_APP_WS_URL=ws://localhost:5007

# Feature Flags
REACT_APP_ENABLE_DEV_TOOLS=true
REACT_APP_ENABLE_HOT_RELOAD=true
REACT_APP_ENABLE_ANALYTICS=true

# App-Specific Settings
# Management Software Specific
REACT_APP_ENABLE_MULTI_TENANT=true
REACT_APP_ENABLE_ADVANCED_ANALYTICS=true
REACT_APP_ENABLE_API_MANAGEMENT=true

# Electron Configuration (if applicable)
ELECTRON_IS_DEV=true
ELECTRON_APP_NAME=Mission Control
ELECTRON_APP_ID=net.haclab.mission-control

# Security
REACT_APP_ENABLE_HTTPS=false
HTTPS=false

# Performance
REACT_APP_BUNDLE_ANALYZER=false
REACT_APP_OPTIMIZE_IMAGES=true

# Logging
REACT_APP_LOG_LEVEL=debug
REACT_APP_ENABLE_CONSOLE_LOGS=true
