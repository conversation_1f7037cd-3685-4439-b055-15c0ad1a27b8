# Environment configuration for Lenkit (lenkit)
# Generated automatically - modify with caution

# App Identity
REACT_APP_NAME=lenkit
REACT_APP_DISPLAY_NAME=Lenkit
REACT_APP_VERSION=0.0.29
REACT_APP_TYPE=Loan and Savings Management System
REACT_APP_COLOR=#31c48d

# Vite Environment Variables (for Vite compatibility)
VITE_APP_NAME=lenkit
VITE_APP_DISPLAY_NAME=Lenkit
VITE_APP_VERSION=0.0.29
VITE_APP_TYPE=Loan and Savings Management System
VITE_APP_COLOR=#31c48d

# Development
PORT=3006
BROWSER=none
FAST_REFRESH=true
GENERATE_SOURCEMAP=true

# Database
REACT_APP_DATABASE_PREFIX=lenkit_
REACT_APP_DATABASE_NAME=lenkit_db
VITE_DATABASE_PREFIX=lenkit_
VITE_DATABASE_NAME=lenkit_db

# Build Configuration
BUILD_PATH=build-lenkit
PUBLIC_URL=.

# API Configuration
REACT_APP_API_BASE_URL=http://localhost:4006
REACT_APP_WS_URL=ws://localhost:5006

# Feature Flags
REACT_APP_ENABLE_DEV_TOOLS=true
REACT_APP_ENABLE_HOT_RELOAD=true
REACT_APP_ENABLE_ANALYTICS=false

# App-Specific Settings
# Loan Management Specific
REACT_APP_ENABLE_CREDIT_SCORING=true
REACT_APP_ENABLE_PAYMENT_GATEWAY=true
REACT_APP_DEFAULT_INTEREST_RATE=0.05

# Electron Configuration (if applicable)
ELECTRON_IS_DEV=true
ELECTRON_APP_NAME=Lenkit
ELECTRON_APP_ID=net.haclab.lenkit

# Security
REACT_APP_ENABLE_HTTPS=false
HTTPS=false

# Performance
REACT_APP_BUNDLE_ANALYZER=false
REACT_APP_OPTIMIZE_IMAGES=true

# Logging
REACT_APP_LOG_LEVEL=debug
REACT_APP_ENABLE_CONSOLE_LOGS=true
