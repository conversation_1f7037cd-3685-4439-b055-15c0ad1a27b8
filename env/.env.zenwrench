# Environment configuration for ZenWrench (zenwrench)
# Generated automatically - modify with caution

# App Identity
REACT_APP_NAME=zenwrench
REACT_APP_DISPLAY_NAME=ZenWrench
REACT_APP_VERSION=0.0.168
REACT_APP_TYPE=Garage Management System
REACT_APP_COLOR=#722ed1

# Vite Environment Variables (for Vite compatibility)
VITE_APP_NAME=zenwrench
VITE_APP_DISPLAY_NAME=ZenWrench
VITE_APP_VERSION=0.0.168
VITE_APP_TYPE=Garage Management System
VITE_APP_COLOR=#722ed1

# Development
PORT=3010
BROWSER=none
FAST_REFRESH=true
GENERATE_SOURCEMAP=true

# Database
REACT_APP_DATABASE_PREFIX=zenwrench_
REACT_APP_DATABASE_NAME=zenwrench_db
VITE_DATABASE_PREFIX=zenwrench_
VITE_DATABASE_NAME=zenwrench_db

# Build Configuration
BUILD_PATH=build-zenwrench
PUBLIC_URL=.

# API Configuration
REACT_APP_API_BASE_URL=http://localhost:4010
REACT_APP_WS_URL=ws://localhost:5010

# Feature Flags
REACT_APP_ENABLE_DEV_TOOLS=true
REACT_APP_ENABLE_HOT_RELOAD=true
REACT_APP_ENABLE_ANALYTICS=false

# App-Specific Settings
# Garage Management Specific
REACT_APP_ENABLE_DIAGNOSTIC_TOOLS=true
REACT_APP_ENABLE_CUSTOMER_PORTAL=true
REACT_APP_DEFAULT_WARRANTY_PERIOD=90

# Electron Configuration (if applicable)
ELECTRON_IS_DEV=true
ELECTRON_APP_NAME=ZenWrench
ELECTRON_APP_ID=net.haclab.zenwrench

# Security
REACT_APP_ENABLE_HTTPS=false
HTTPS=false

# Performance
REACT_APP_BUNDLE_ANALYZER=false
REACT_APP_OPTIMIZE_IMAGES=true

# Logging
REACT_APP_LOG_LEVEL=debug
REACT_APP_ENABLE_CONSOLE_LOGS=true
