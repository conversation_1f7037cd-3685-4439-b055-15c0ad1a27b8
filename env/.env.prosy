# Environment configuration for Prosy (prosy)
# Generated automatically - modify with caution

# App Identity
REACT_APP_NAME=prosy
REACT_APP_DISPLAY_NAME=Prosy
REACT_APP_VERSION=0.0.63
REACT_APP_TYPE=Property Management System
REACT_APP_COLOR=#151a4b

# Vite Environment Variables (for Vite compatibility)
VITE_APP_NAME=prosy
VITE_APP_DISPLAY_NAME=Prosy
VITE_APP_VERSION=0.0.63
VITE_APP_TYPE=Property Management System
VITE_APP_COLOR=#151a4b

# Development
PORT=3008
BROWSER=none
FAST_REFRESH=true
GENERATE_SOURCEMAP=true

# Database
REACT_APP_DATABASE_PREFIX=prosy_
REACT_APP_DATABASE_NAME=prosy_db
VITE_DATABASE_PREFIX=prosy_
VITE_DATABASE_NAME=prosy_db

# Build Configuration
BUILD_PATH=build-prosy
PUBLIC_URL=.

# API Configuration
REACT_APP_API_BASE_URL=http://localhost:4008
REACT_APP_WS_URL=ws://localhost:5008

# Feature Flags
REACT_APP_ENABLE_DEV_TOOLS=true
REACT_APP_ENABLE_HOT_RELOAD=true
REACT_APP_ENABLE_ANALYTICS=false

# App-Specific Settings
# Property Management Specific
REACT_APP_ENABLE_TENANT_PORTAL=true
REACT_APP_ENABLE_MAINTENANCE_REQUESTS=true
REACT_APP_DEFAULT_LEASE_TERM=12

# Electron Configuration (if applicable)
ELECTRON_IS_DEV=true
ELECTRON_APP_NAME=Prosy
ELECTRON_APP_ID=net.haclab.prosy

# Security
REACT_APP_ENABLE_HTTPS=false
HTTPS=false

# Performance
REACT_APP_BUNDLE_ANALYZER=false
REACT_APP_OPTIMIZE_IMAGES=true

# Logging
REACT_APP_LOG_LEVEL=debug
REACT_APP_ENABLE_CONSOLE_LOGS=true
