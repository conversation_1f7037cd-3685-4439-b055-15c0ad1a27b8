# Environment configuration for Kanify (kanify)
# Generated automatically - modify with caution

# App Identity
REACT_APP_NAME=kanify
REACT_APP_DISPLAY_NAME=Kanify
REACT_APP_VERSION=0.0.2
REACT_APP_TYPE=Garage Management System
REACT_APP_COLOR=#FF4500

# Vite Environment Variables (for Vite compatibility)
VITE_APP_NAME=kanify
VITE_APP_DISPLAY_NAME=Kanify
VITE_APP_VERSION=0.0.2
VITE_APP_TYPE=Garage Management System
VITE_APP_COLOR=#FF4500

# Development
PORT=3004
BROWSER=none
FAST_REFRESH=true
GENERATE_SOURCEMAP=true

# Database
REACT_APP_DATABASE_PREFIX=kanify_
REACT_APP_DATABASE_NAME=kanify_db
VITE_DATABASE_PREFIX=kanify_
VITE_DATABASE_NAME=kanify_db

# Build Configuration
BUILD_PATH=build-kanify
PUBLIC_URL=.

# API Configuration
REACT_APP_API_BASE_URL=http://localhost:4004
REACT_APP_WS_URL=ws://localhost:5004

# Feature Flags
REACT_APP_ENABLE_DEV_TOOLS=true
REACT_APP_ENABLE_HOT_RELOAD=true
REACT_APP_ENABLE_ANALYTICS=false

# App-Specific Settings
# Garage Management Specific
REACT_APP_ENABLE_VEHICLE_TRACKING=true
REACT_APP_ENABLE_PARTS_INVENTORY=true
REACT_APP_DEFAULT_LABOR_RATE=50

# Electron Configuration (if applicable)
ELECTRON_IS_DEV=true
ELECTRON_APP_NAME=Kanify
ELECTRON_APP_ID=net.haclab.kanify

# Security
REACT_APP_ENABLE_HTTPS=false
HTTPS=false

# Performance
REACT_APP_BUNDLE_ANALYZER=false
REACT_APP_OPTIMIZE_IMAGES=true

# Logging
REACT_APP_LOG_LEVEL=debug
REACT_APP_ENABLE_CONSOLE_LOGS=true
