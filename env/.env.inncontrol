# Environment configuration for InnControl (inncontrol)
# Generated automatically - modify with caution

# App Identity
REACT_APP_NAME=inncontrol
REACT_APP_DISPLAY_NAME=InnControl
REACT_APP_VERSION=0.1.24
REACT_APP_TYPE=Hotel Management System
REACT_APP_COLOR=#eb2f96

# Vite Environment Variables (for Vite compatibility)
VITE_APP_NAME=inncontrol
VITE_APP_DISPLAY_NAME=InnControl
VITE_APP_VERSION=0.1.24
VITE_APP_TYPE=Hotel Management System
VITE_APP_COLOR=#eb2f96

# Development
PORT=3003
BROWSER=none
FAST_REFRESH=true
GENERATE_SOURCEMAP=true

# Database
REACT_APP_DATABASE_PREFIX=inncontrol_
REACT_APP_DATABASE_NAME=inncontrol_db
VITE_DATABASE_PREFIX=inncontrol_
VITE_DATABASE_NAME=inncontrol_db

# Build Configuration
BUILD_PATH=build-inncontrol
PUBLIC_URL=.

# API Configuration
REACT_APP_API_BASE_URL=http://localhost:4003
REACT_APP_WS_URL=ws://localhost:5003

# Feature Flags
REACT_APP_ENABLE_DEV_TOOLS=true
REACT_APP_ENABLE_HOT_RELOAD=true
REACT_APP_ENABLE_ANALYTICS=false

# App-Specific Settings
# Hotel Management Specific
REACT_APP_ENABLE_CHANNEL_MANAGER=true
REACT_APP_ENABLE_REVENUE_MANAGEMENT=true
REACT_APP_DEFAULT_CURRENCY=USD

# Electron Configuration (if applicable)
ELECTRON_IS_DEV=true
ELECTRON_APP_NAME=InnControl
ELECTRON_APP_ID=net.haclab.inncontrol

# Security
REACT_APP_ENABLE_HTTPS=false
HTTPS=false

# Performance
REACT_APP_BUNDLE_ANALYZER=false
REACT_APP_OPTIMIZE_IMAGES=true

# Logging
REACT_APP_LOG_LEVEL=debug
REACT_APP_ENABLE_CONSOLE_LOGS=true
