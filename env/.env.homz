# Environment configuration for Homz (homz)
# Generated automatically - modify with caution

# App Identity
REACT_APP_NAME=homz
REACT_APP_DISPLAY_NAME=Homz
REACT_APP_VERSION=0.1.26
REACT_APP_TYPE=Hotel Management System
REACT_APP_COLOR=#FF4500

# Vite Environment Variables (for Vite compatibility)
VITE_APP_NAME=homz
VITE_APP_DISPLAY_NAME=Homz
VITE_APP_VERSION=0.1.26
VITE_APP_TYPE=Hotel Management System
VITE_APP_COLOR=#FF4500

# Development
PORT=3002
BROWSER=none
FAST_REFRESH=true
GENERATE_SOURCEMAP=true

# Database
REACT_APP_DATABASE_PREFIX=homz_
REACT_APP_DATABASE_NAME=homz_db
VITE_DATABASE_PREFIX=homz_
VITE_DATABASE_NAME=homz_db

# Build Configuration
BUILD_PATH=build-homz
PUBLIC_URL=.

# API Configuration
REACT_APP_API_BASE_URL=http://localhost:4002
REACT_APP_WS_URL=ws://localhost:5002

# Feature Flags
REACT_APP_ENABLE_DEV_TOOLS=true
REACT_APP_ENABLE_HOT_RELOAD=true
REACT_APP_ENABLE_ANALYTICS=false

# App-Specific Settings
# Hotel Management Specific
REACT_APP_ENABLE_BOOKING_ENGINE=true
REACT_APP_ENABLE_HOUSEKEEPING=true
REACT_APP_DEFAULT_TIMEZONE=UTC

# Electron Configuration (if applicable)
ELECTRON_IS_DEV=true
ELECTRON_APP_NAME=Homz
ELECTRON_APP_ID=net.haclab.homz

# Security
REACT_APP_ENABLE_HTTPS=false
HTTPS=false

# Performance
REACT_APP_BUNDLE_ANALYZER=false
REACT_APP_OPTIMIZE_IMAGES=true

# Logging
REACT_APP_LOG_LEVEL=debug
REACT_APP_ENABLE_CONSOLE_LOGS=true
