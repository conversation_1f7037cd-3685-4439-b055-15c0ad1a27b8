# Environment configuration for Kyeyo CV (kyeyo)
# Generated automatically - modify with caution

# App Identity
REACT_APP_NAME=kyeyo
REACT_APP_DISPLAY_NAME=Kyeyo CV
REACT_APP_VERSION=0.1.12
REACT_APP_TYPE=Recruitment Management System
REACT_APP_COLOR=#391085

# Vite Environment Variables (for Vite compatibility)
VITE_APP_NAME=kyeyo
VITE_APP_DISPLAY_NAME=Kyeyo CV
VITE_APP_VERSION=0.1.12
VITE_APP_TYPE=Recruitment Management System
VITE_APP_COLOR=#391085

# Development
PORT=3005
BROWSER=none
FAST_REFRESH=true
GENERATE_SOURCEMAP=true

# Database
REACT_APP_DATABASE_PREFIX=kyeyo_
REACT_APP_DATABASE_NAME=kyeyo_db
VITE_DATABASE_PREFIX=kyeyo_
VITE_DATABASE_NAME=kyeyo_db

# Build Configuration
BUILD_PATH=build-kyeyo
PUBLIC_URL=.

# API Configuration
REACT_APP_API_BASE_URL=http://localhost:4005
REACT_APP_WS_URL=ws://localhost:5005

# Feature Flags
REACT_APP_ENABLE_DEV_TOOLS=true
REACT_APP_ENABLE_HOT_RELOAD=true
REACT_APP_ENABLE_ANALYTICS=false

# App-Specific Settings
# Recruitment Management Specific
REACT_APP_ENABLE_CV_PARSING=true
REACT_APP_ENABLE_INTERVIEW_SCHEDULING=true
REACT_APP_DEFAULT_LANGUAGE=en

# Electron Configuration (if applicable)
ELECTRON_IS_DEV=true
ELECTRON_APP_NAME=Kyeyo CV
ELECTRON_APP_ID=net.haclab.kyeyo

# Security
REACT_APP_ENABLE_HTTPS=false
HTTPS=false

# Performance
REACT_APP_BUNDLE_ANALYZER=false
REACT_APP_OPTIMIZE_IMAGES=true

# Logging
REACT_APP_LOG_LEVEL=debug
REACT_APP_ENABLE_CONSOLE_LOGS=true
