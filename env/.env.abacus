# Environment configuration for Abacus (abacus)
# Generated automatically - modify with caution

# App Identity
REACT_APP_NAME=abacus
REACT_APP_DISPLAY_NAME=Abacus
REACT_APP_VERSION=0.0.54
REACT_APP_TYPE=Inventory Management Software
REACT_APP_COLOR=#0718c4

# Vite Environment Variables (for Vite compatibility)
VITE_APP_NAME=abacus
VITE_APP_DISPLAY_NAME=Abacus
VITE_APP_VERSION=0.0.54
VITE_APP_TYPE=Inventory Management Software
VITE_APP_COLOR=#0718c4

# Development
PORT=3000
BROWSER=none
FAST_REFRESH=true
GENERATE_SOURCEMAP=true

# Database
REACT_APP_DATABASE_PREFIX=abacus_
REACT_APP_DATABASE_NAME=abacus_db
VITE_DATABASE_PREFIX=abacus_
VITE_DATABASE_NAME=abacus_db

# Build Configuration
BUILD_PATH=build-abacus
PUBLIC_URL=.

# API Configuration
REACT_APP_API_BASE_URL=http://localhost:4000
REACT_APP_WS_URL=ws://localhost:5000

# Feature Flags
REACT_APP_ENABLE_DEV_TOOLS=true
REACT_APP_ENABLE_HOT_RELOAD=true
REACT_APP_ENABLE_ANALYTICS=false

# App-Specific Settings
# Inventory Management Specific
REACT_APP_ENABLE_BARCODE_SCANNER=true
REACT_APP_ENABLE_STOCK_ALERTS=true
REACT_APP_DEFAULT_CURRENCY=USD

# Electron Configuration (if applicable)
ELECTRON_IS_DEV=true
ELECTRON_APP_NAME=Abacus
ELECTRON_APP_ID=net.haclab.abacus

# Security
REACT_APP_ENABLE_HTTPS=false
HTTPS=false

# Performance
REACT_APP_BUNDLE_ANALYZER=false
REACT_APP_OPTIMIZE_IMAGES=true

# Logging
REACT_APP_LOG_LEVEL=debug
REACT_APP_ENABLE_CONSOLE_LOGS=true
