# Master Environment Configuration
# This file contains shared settings across all apps

# Development Server
DEV_SERVER_PORT=3001
WS_SERVER_PORT=3002

# Build Configuration
NODE_ENV=development
CI=false

# Shared Database Settings
DB_HOST=localhost
DB_PORT=5432

# Shared API Settings
API_TIMEOUT=30000
API_RETRY_ATTEMPTS=3

# Shared Feature Flags
ENABLE_MULTI_APP_DEV=true
ENABLE_HOT_SWITCHING=true
ENABLE_PARALLEL_DEV=true

# Monitoring
ENABLE_PERFORMANCE_MONITORING=true
ENABLE_ERROR_TRACKING=true

# Security
ENABLE_CORS=true
CORS_ORIGIN=http://localhost:3000
