# Environment configuration for Smart (smart)
# Generated automatically - modify with caution

# App Identity
REACT_APP_NAME=smart
REACT_APP_DISPLAY_NAME=Smart
REACT_APP_VERSION=0.0.1
REACT_APP_TYPE=School Monitoring and Records Tracking System
REACT_APP_COLOR=#0087ff

# Vite Environment Variables (for Vite compatibility)
VITE_APP_NAME=smart
VITE_APP_DISPLAY_NAME=Smart
VITE_APP_VERSION=0.0.1
VITE_APP_TYPE=School Monitoring and Records Tracking System
VITE_APP_COLOR=#0087ff

# Development
PORT=3009
BROWSER=none
FAST_REFRESH=true
GENERATE_SOURCEMAP=true

# Database
REACT_APP_DATABASE_PREFIX=smart_
REACT_APP_DATABASE_NAME=smart_db
VITE_DATABASE_PREFIX=smart_
VITE_DATABASE_NAME=smart_db

# Build Configuration
BUILD_PATH=build-smart
PUBLIC_URL=.

# API Configuration
REACT_APP_API_BASE_URL=http://localhost:4009
REACT_APP_WS_URL=ws://localhost:5009

# Feature Flags
REACT_APP_ENABLE_DEV_TOOLS=true
REACT_APP_ENABLE_HOT_RELOAD=true
REACT_APP_ENABLE_ANALYTICS=false

# App-Specific Settings
# School Management Specific
REACT_APP_ENABLE_GRADEBOOK=true
REACT_APP_ENABLE_PARENT_PORTAL=true
REACT_APP_DEFAULT_ACADEMIC_YEAR=2024

# Electron Configuration (if applicable)
ELECTRON_IS_DEV=true
ELECTRON_APP_NAME=Smart
ELECTRON_APP_ID=net.haclab.smart

# Security
REACT_APP_ENABLE_HTTPS=false
HTTPS=false

# Performance
REACT_APP_BUNDLE_ANALYZER=false
REACT_APP_OPTIMIZE_IMAGES=true

# Logging
REACT_APP_LOG_LEVEL=debug
REACT_APP_ENABLE_CONSOLE_LOGS=true
