# Environment configuration for Evia RMS (evia)
# Generated automatically - modify with caution

# App Identity
REACT_APP_NAME=evia
REACT_APP_DISPLAY_NAME=Evia RMS
REACT_APP_VERSION=0.0.17
REACT_APP_TYPE=Restaurant Management Software
REACT_APP_COLOR=#722ed1

# Vite Environment Variables (for Vite compatibility)
VITE_APP_NAME=evia
VITE_APP_DISPLAY_NAME=Evia RMS
VITE_APP_VERSION=0.0.17
VITE_APP_TYPE=Restaurant Management Software
VITE_APP_COLOR=#722ed1

# Development
PORT=3001
BROWSER=none
FAST_REFRESH=true
GENERATE_SOURCEMAP=true

# Database
REACT_APP_DATABASE_PREFIX=evia_
REACT_APP_DATABASE_NAME=evia_db
VITE_DATABASE_PREFIX=evia_
VITE_DATABASE_NAME=evia_db

# Build Configuration
BUILD_PATH=build-evia
PUBLIC_URL=.

# API Configuration
REACT_APP_API_BASE_URL=http://localhost:4001
REACT_APP_WS_URL=ws://localhost:5001

# Feature Flags
REACT_APP_ENABLE_DEV_TOOLS=true
REACT_APP_ENABLE_HOT_RELOAD=true
REACT_APP_ENABLE_ANALYTICS=false

# App-Specific Settings
# Restaurant Management Specific
REACT_APP_ENABLE_POS=true
REACT_APP_ENABLE_KITCHEN_DISPLAY=true
REACT_APP_DEFAULT_TAX_RATE=0.1

# Electron Configuration (if applicable)
ELECTRON_IS_DEV=true
ELECTRON_APP_NAME=Evia RMS
ELECTRON_APP_ID=net.haclab.evia

# Security
REACT_APP_ENABLE_HTTPS=false
HTTPS=false

# Performance
REACT_APP_BUNDLE_ANALYZER=false
REACT_APP_OPTIMIZE_IMAGES=true

# Logging
REACT_APP_LOG_LEVEL=debug
REACT_APP_ENABLE_CONSOLE_LOGS=true
