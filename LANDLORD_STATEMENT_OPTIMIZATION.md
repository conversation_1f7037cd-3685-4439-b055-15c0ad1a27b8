# Simple Landlord Statement Optimization

## Problem
Landlord statements were slow because they:
1. Loaded ALL data from 6+ databases
2. Filtered everything in memory
3. Used complex nested loops

## Solution: Database-Level Filtering

### Before (Slow)
```javascript
// Load everything
const [properties, units, occupations, receipts, expenses, invoices] = await Promise.all([
  pouchDatabase("properties").getAllData(),
  pouchDatabase("units").getAllData(),
  pouchDatabase("occupations").getAllData(),
  pouchDatabase("receipts").getAllData(),
  pouchDatabase("expenses").getAllData(),
  pouchDatabase("invoices").getAllData()
]);

// Filter in memory (slow!)
const landlordProperties = properties.filter(p => p.landlord?.value === landlordId);
const landlordUnits = units.filter(u => propertyIds.has(u.property.value));
// ... more filtering
```

### After (Fast)
```javascript
// Get only landlord's properties
const properties = await pouchDatabase("properties").find({
  selector: { 'landlord.value': landlordId }
});

// Get only units for these properties
const units = await pouchDatabase("units").find({
  selector: { 'property.value': { $in: propertyIds } }
});

// Get only relevant receipts
const receipts = await pouchDatabase("receipts").find({
  selector: { 'occupancy.value': { $in: occupationIds } }
});
```

## Key Changes Made

1. **Database Filtering**: Use `find()` with selectors instead of `getAllData()`
2. **Targeted Queries**: Only fetch data that belongs to the landlord
3. **Removed Memory Filtering**: No more `.filter()` operations on large arrays
4. **Simplified Processing**: Data comes pre-filtered from database

## Performance Impact

- **Before**: Load 10,000+ records → Filter to 100 relevant ones
- **After**: Load only the 100 relevant records directly

This makes landlord statements as fast as tenant statements because:
- Tenant statements already use direct filtering (`occupancy.value === occupationId`)
- Now landlord statements use the same approach with database-level filtering

## Required Database Indexes

For optimal performance, ensure these indexes exist:
```javascript
// Properties by landlord
{ fields: ['landlord.value'] }

// Units by property
{ fields: ['property.value'] }

// Occupations by unit
{ fields: ['unit.value'] }

// Receipts by occupation
{ fields: ['occupancy.value'] }

// Expenses by property/unit/supplier
{ fields: ['property.value', 'offset'] }
{ fields: ['unit.value', 'offset'] }
{ fields: ['supplier.value', 'offset'] }

// Invoices by client
{ fields: ['client.value'] }
```

## Result
Landlord statements now perform as fast as tenant statements while maintaining all transaction data and business logic.
