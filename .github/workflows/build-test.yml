name: Build Test

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  test-build:
    runs-on: ${{ matrix.os }}
    
    strategy:
      matrix:
        os: [windows-latest, macos-latest, ubuntu-latest]
        
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'yarn'
        
    - name: Install dependencies
      run: yarn install --frozen-lockfile
      
    - name: Run tests
      run: yarn test --watchAll=false --coverage
      
    - name: Build React app
      run: yarn build
      
    - name: Test Electron build (Windows)
      if: matrix.os == 'windows-latest'
      run: yarn dist-win64 --publish=never
      
    - name: Test Electron build (macOS)
      if: matrix.os == 'macos-latest'
      run: yarn dist-mac --publish=never
      
    - name: Test Electron build (Linux)
      if: matrix.os == 'ubuntu-latest'
      run: yarn dist-linux --publish=never
