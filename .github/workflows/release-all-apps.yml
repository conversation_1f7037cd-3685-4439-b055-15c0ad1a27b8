name: Release All Apps

on:
  workflow_dispatch:
    inputs:
      version_bump:
        description: 'Version bump type'
        required: true
        default: 'patch'
        type: choice
        options:
          - patch
          - minor
          - major
      apps_to_release:
        description: 'Apps to release (comma-separated, or "all" for all apps)'
        required: true
        default: 'all'
      create_draft:
        description: 'Create draft releases'
        required: false
        default: false
        type: boolean

jobs:
  prepare-release:
    runs-on: ubuntu-latest
    outputs:
      apps-matrix: ${{ steps.generate-matrix.outputs.apps-matrix }}
      updated-apps: ${{ steps.update-versions.outputs.updated-apps }}
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        token: ${{ secrets.GITHUB_TOKEN }}
        
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'yarn'
        
    - name: Install dependencies
      run: yarn install --frozen-lockfile
      
    - name: Generate apps matrix
      id: generate-matrix
      run: |
        if [ "${{ github.event.inputs.apps_to_release }}" = "all" ]; then
          APPS=$(node -e "
            const apps = require('./src/config/apps.json');
            console.log(JSON.stringify(Object.keys(apps)));
          ")
        else
          APPS='["${{ github.event.inputs.apps_to_release }}"]'
          APPS=$(echo $APPS | sed 's/,/","/g' | sed 's/\["/["/' | sed 's/"\]/"]/')
        fi
        echo "apps-matrix=$APPS" >> $GITHUB_OUTPUT
        
    - name: Update app versions
      id: update-versions
      run: |
        node -e "
        const fs = require('fs');
        const apps = require('./src/config/apps.json');
        const appsToUpdate = ${{ steps.generate-matrix.outputs.apps-matrix }};
        const bumpType = '${{ github.event.inputs.version_bump }}';
        
        function incrementVersion(version, type) {
          const parts = version.split('.').map(Number);
          switch(type) {
            case 'major': return \`\${parts[0] + 1}.0.0\`;
            case 'minor': return \`\${parts[0]}.\${parts[1] + 1}.0\`;
            case 'patch': return \`\${parts[0]}.\${parts[1]}.\${parts[2] + 1}\`;
            default: return version;
          }
        }
        
        const updatedApps = {};
        appsToUpdate.forEach(appName => {
          if (apps[appName]) {
            const oldVersion = apps[appName].version;
            const newVersion = incrementVersion(oldVersion, bumpType);
            apps[appName].version = newVersion;
            updatedApps[appName] = { oldVersion, newVersion };
            console.log(\`Updated \${appName}: \${oldVersion} -> \${newVersion}\`);
          }
        });
        
        fs.writeFileSync('./src/config/apps.json', JSON.stringify(apps, null, 2));
        fs.writeFileSync('./updated-apps.json', JSON.stringify(updatedApps, null, 2));
        "
        
        UPDATED_APPS=$(cat updated-apps.json)
        echo "updated-apps=$UPDATED_APPS" >> $GITHUB_OUTPUT
        
    - name: Commit version updates
      run: |
        git config --local user.email "<EMAIL>"
        git config --local user.name "GitHub Action"
        git add src/config/apps.json
        git commit -m "🚀 Bump versions for release: ${{ github.event.inputs.version_bump }}" || exit 0
        git push

  build-and-release:
    needs: prepare-release
    runs-on: ${{ matrix.os }}
    
    strategy:
      fail-fast: false
      matrix:
        app: ${{ fromJson(needs.prepare-release.outputs.apps-matrix) }}
        os: [windows-latest, macos-latest, ubuntu-latest]
        
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        ref: ${{ github.sha }}
        
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'yarn'
        
    - name: Install dependencies
      run: yarn install --frozen-lockfile
      
    - name: Initialize app configuration
      run: node initApp.js ${{ matrix.app }}
      
    - name: Build React app
      run: yarn build
      
    - name: Build Electron app (Windows)
      if: matrix.os == 'windows-latest'
      run: yarn dist-win64
      env:
        GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        
    - name: Build Electron app (macOS)
      if: matrix.os == 'macos-latest'
      run: yarn dist-mac
      env:
        GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        CSC_LINK: ${{ secrets.CSC_LINK }}
        CSC_KEY_PASSWORD: ${{ secrets.CSC_KEY_PASSWORD }}
        
    - name: Build Electron app (Linux)
      if: matrix.os == 'ubuntu-latest'
      run: yarn dist-linux
      env:
        GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        
    - name: Upload build artifacts
      uses: actions/upload-artifact@v4
      with:
        name: ${{ matrix.app }}-${{ matrix.os }}-dist
        path: dist/
        retention-days: 7

  create-releases:
    needs: [prepare-release, build-and-release]
    runs-on: ubuntu-latest
    
    strategy:
      matrix:
        app: ${{ fromJson(needs.prepare-release.outputs.apps-matrix) }}
        
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Download all artifacts for app
      uses: actions/download-artifact@v4
      with:
        pattern: ${{ matrix.app }}-*-dist
        path: ./artifacts/${{ matrix.app }}/
        merge-multiple: true
        
    - name: Get app info
      id: app-info
      run: |
        APP_INFO=$(node -e "
          const apps = require('./src/config/apps.json');
          const app = apps['${{ matrix.app }}'];
          console.log(JSON.stringify(app));
        ")
        echo "app-info=$APP_INFO" >> $GITHUB_OUTPUT
        
    - name: Create GitHub Release
      uses: softprops/action-gh-release@v1
      with:
        tag_name: ${{ matrix.app }}-v${{ fromJson(steps.app-info.outputs.app-info).version }}
        name: ${{ fromJson(steps.app-info.outputs.app-info).name }} v${{ fromJson(steps.app-info.outputs.app-info).version }}
        body: |
          ## ${{ fromJson(steps.app-info.outputs.app-info).name }} - ${{ fromJson(steps.app-info.outputs.app-info).type }}
          
          ### Version: ${{ fromJson(steps.app-info.outputs.app-info).version }}
          
          ### Changes
          - Version bump: ${{ github.event.inputs.version_bump }}
          - Auto-generated release from GitHub Actions
          
          ### Downloads
          - Windows: `${{ matrix.app }}_x64_setup.exe`
          - macOS: `${{ matrix.app }}-${{ fromJson(steps.app-info.outputs.app-info).version }}.dmg`
          - Linux: `${{ matrix.app }}_${{ fromJson(steps.app-info.outputs.app-info).version }}_amd64.deb`
          
          ### Installation
          Download the appropriate file for your operating system and follow the installation instructions.
        draft: ${{ github.event.inputs.create_draft }}
        prerelease: false
        files: |
          ./artifacts/${{ matrix.app }}/**/*
        repository: haclab-co/${{ matrix.app }}-releases
        token: ${{ secrets.GITHUB_TOKEN }}

  notify-completion:
    needs: [prepare-release, build-and-release, create-releases]
    runs-on: ubuntu-latest
    if: always()
    
    steps:
    - name: Notify completion
      run: |
        echo "🎉 Release process completed!"
        echo "📦 Apps released: ${{ needs.prepare-release.outputs.apps-matrix }}"
        echo "📈 Version bump: ${{ github.event.inputs.version_bump }}"
        echo "✅ All releases created successfully!"
