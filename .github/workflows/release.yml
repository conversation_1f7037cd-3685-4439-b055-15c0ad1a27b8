name: Build and Release Single App

on:
  push:
    tags:
      - '*-v*'
  workflow_dispatch:
    inputs:
      app_name:
        description: 'App name to release'
        required: true
        type: choice
        options:
          - abacus
          - evia
          - homz
          - inncontrol
          - kanify
          - kyeyo
          - lenkit
          - mission-control
          - prosy
          - zenwrench
      version_bump:
        description: 'Version bump type'
        required: true
        default: 'patch'
        type: choice
        options:
          - patch
          - minor
          - major

jobs:
  prepare-app:
    runs-on: ubuntu-latest
    outputs:
      app-name: ${{ steps.get-app.outputs.app-name }}
      new-version: ${{ steps.update-version.outputs.new-version }}

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Get app name from tag or input
      id: get-app
      run: |
        if [ "${{ github.event_name }}" = "push" ]; then
          APP_NAME=$(echo ${{ github.ref_name }} | sed 's/-v.*//')
        else
          APP_NAME="${{ github.event.inputs.app_name }}"
        fi
        echo "app-name=$APP_NAME" >> $GITHUB_OUTPUT

    - name: Update app version
      id: update-version
      if: github.event_name == 'workflow_dispatch'
      run: |
        node -e "
        const fs = require('fs');
        const apps = require('./src/config/apps.json');
        const appName = '${{ steps.get-app.outputs.app-name }}';
        const bumpType = '${{ github.event.inputs.version_bump }}';

        function incrementVersion(version, type) {
          const parts = version.split('.').map(Number);
          switch(type) {
            case 'major': return \`\${parts[0] + 1}.0.0\`;
            case 'minor': return \`\${parts[0]}.\${parts[1] + 1}.0\`;
            case 'patch': return \`\${parts[0]}.\${parts[1]}.\${parts[2] + 1}\`;
            default: return version;
          }
        }

        if (apps[appName]) {
          const oldVersion = apps[appName].version;
          const newVersion = incrementVersion(oldVersion, bumpType);
          apps[appName].version = newVersion;
          console.log(\`Updated \${appName}: \${oldVersion} -> \${newVersion}\`);

          fs.writeFileSync('./src/config/apps.json', JSON.stringify(apps, null, 2));
          console.log('new-version=' + newVersion);
        }
        " | tee -a $GITHUB_OUTPUT

    - name: Commit version update
      if: github.event_name == 'workflow_dispatch'
      run: |
        git config --local user.email "<EMAIL>"
        git config --local user.name "GitHub Action"
        git add src/config/apps.json
        git commit -m "🚀 Bump ${{ steps.get-app.outputs.app-name }} version: ${{ github.event.inputs.version_bump }}" || exit 0
        git push

  build:
    needs: prepare-app
    runs-on: ${{ matrix.os }}

    strategy:
      matrix:
        os: [windows-latest, macos-latest, ubuntu-latest]

    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        ref: ${{ github.sha }}

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'yarn'

    - name: Install dependencies
      run: yarn install --frozen-lockfile

    - name: Initialize app configuration
      run: node initApp.js ${{ needs.prepare-app.outputs.app-name }}

    - name: Build React app
      run: yarn build
      
    - name: Build Electron app with differential updates (Windows)
      if: matrix.os == 'windows-latest'
      run: |
        # Use the new differential release script
        node scripts/release-differential.js
      env:
        GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        
    - name: Build Electron app (macOS)
      if: matrix.os == 'macos-latest'
      run: yarn dist-mac
      env:
        GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        CSC_LINK: ${{ secrets.CSC_LINK }}
        CSC_KEY_PASSWORD: ${{ secrets.CSC_KEY_PASSWORD }}
        
    - name: Build Electron app (Linux)
      if: matrix.os == 'ubuntu-latest'
      run: yarn dist-linux
      env:
        GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        
    - name: Upload artifacts
      uses: actions/upload-artifact@v4
      with:
        name: ${{ needs.prepare-app.outputs.app-name }}-dist-${{ matrix.os }}
        path: dist/

  release:
    needs: [prepare-app, build]
    runs-on: ubuntu-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Download all artifacts
      uses: actions/download-artifact@v4
      with:
        pattern: ${{ needs.prepare-app.outputs.app-name }}-dist-*
        path: dist-artifacts/
        merge-multiple: true

    - name: Get app info
      id: app-info
      run: |
        APP_INFO=$(node -e "
          const apps = require('./src/config/apps.json');
          const app = apps['${{ needs.prepare-app.outputs.app-name }}'];
          console.log(JSON.stringify(app));
        ")
        echo "app-info=$APP_INFO" >> $GITHUB_OUTPUT

    - name: Create Release
      uses: softprops/action-gh-release@v1
      with:
        tag_name: ${{ needs.prepare-app.outputs.app-name }}-v${{ fromJson(steps.app-info.outputs.app-info).version }}
        name: ${{ fromJson(steps.app-info.outputs.app-info).name }} v${{ fromJson(steps.app-info.outputs.app-info).version }}
        body: |
          ## ${{ fromJson(steps.app-info.outputs.app-info).name }} - ${{ fromJson(steps.app-info.outputs.app-info).type }}

          ### Version: ${{ fromJson(steps.app-info.outputs.app-info).version }}

          ### Installation
          Download the appropriate file for your operating system:
          - **Windows**: `${{ needs.prepare-app.outputs.app-name }}_x64_setup.exe`
          - **macOS**: `${{ needs.prepare-app.outputs.app-name }}-${{ fromJson(steps.app-info.outputs.app-info).version }}.dmg`
          - **Linux**: `${{ needs.prepare-app.outputs.app-name }}_${{ fromJson(steps.app-info.outputs.app-info).version }}_amd64.deb`

          ### Auto-Update
          This version includes automatic update functionality. The app will check for updates automatically.
        draft: false
        prerelease: false
        files: |
          dist-artifacts/**/*
        generate_release_notes: true
        repository: haclab-co/${{ needs.prepare-app.outputs.app-name }}-releases
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
