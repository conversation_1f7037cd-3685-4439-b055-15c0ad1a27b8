/**
 * PERFORMANCE FIX: File Processing Manager
 * Manages background file processing using web workers
 */

class FileProcessingManager {
  constructor() {
    this.worker = null;
    this.pendingOperations = new Map();
    this.operationId = 0;
    this.isInitialized = false;
  }

  /**
   * Initialize the file processing worker
   */
  async initialize() {
    if (this.isInitialized) {
      return;
    }

    try {
      // Check if we're in a web worker environment
      if (typeof Worker !== 'undefined') {
        this.worker = new Worker('/src/workers/fileProcessor.js');
        this.worker.onmessage = this.handleWorkerMessage.bind(this);
        this.worker.onerror = this.handleWorkerError.bind(this);
        this.isInitialized = true;
        console.log('[FileProcessingManager] Worker initialized successfully');
      } else {
        console.warn('[FileProcessingManager] Web Workers not supported, falling back to main thread processing');
        this.isInitialized = true;
      }
    } catch (error) {
      console.error('[FileProcessingManager] Failed to initialize worker:', error);
      this.isInitialized = true; // Still mark as initialized to use fallback
    }
  }

  /**
   * Handle messages from the worker
   */
  handleWorkerMessage(e) {
    const { type, id, success, result, results, error } = e.data;
    const operation = this.pendingOperations.get(id);

    if (!operation) {
      console.warn('[FileProcessingManager] Received message for unknown operation:', id);
      return;
    }

    this.pendingOperations.delete(id);

    if (success) {
      switch (type) {
        case 'FILE_PROCESSED':
          operation.resolve(result);
          break;
        case 'FILES_BATCH_PROCESSED':
          operation.resolve(results);
          break;
        default:
          operation.reject(new Error(`Unknown response type: ${type}`));
      }
    } else {
      operation.reject(new Error(error));
    }
  }

  /**
   * Handle worker errors
   */
  handleWorkerError(error) {
    console.error('[FileProcessingManager] Worker error:', error);
    
    // Reject all pending operations
    for (const [id, operation] of this.pendingOperations) {
      operation.reject(new Error('Worker error occurred'));
    }
    this.pendingOperations.clear();
  }

  /**
   * Process a single file
   */
  async processFile(file, options = {}) {
    await this.initialize();

    if (this.worker) {
      return this.processFileWithWorker(file, options);
    } else {
      return this.processFileMainThread(file, options);
    }
  }

  /**
   * Process file using web worker
   */
  async processFileWithWorker(file, options) {
    const id = ++this.operationId;

    return new Promise((resolve, reject) => {
      this.pendingOperations.set(id, { resolve, reject });

      this.worker.postMessage({
        type: 'PROCESS_FILE',
        id,
        data: { file, options }
      });

      // Set timeout to prevent hanging
      setTimeout(() => {
        if (this.pendingOperations.has(id)) {
          this.pendingOperations.delete(id);
          reject(new Error('File processing timeout'));
        }
      }, 30000); // 30 second timeout
    });
  }

  /**
   * Process multiple files in batch
   */
  async processFilesBatch(files, options = {}) {
    await this.initialize();

    if (this.worker) {
      return this.processFilesBatchWithWorker(files, options);
    } else {
      return this.processFilesBatchMainThread(files, options);
    }
  }

  /**
   * Process files batch using web worker
   */
  async processFilesBatchWithWorker(files, options) {
    const id = ++this.operationId;

    return new Promise((resolve, reject) => {
      this.pendingOperations.set(id, { resolve, reject });

      this.worker.postMessage({
        type: 'PROCESS_FILES_BATCH',
        id,
        data: { files, options }
      });

      // Set timeout based on number of files
      const timeout = Math.max(30000, files.length * 5000); // 5 seconds per file, minimum 30 seconds
      setTimeout(() => {
        if (this.pendingOperations.has(id)) {
          this.pendingOperations.delete(id);
          reject(new Error('Batch file processing timeout'));
        }
      }, timeout);
    });
  }

  /**
   * Fallback: Process file on main thread
   */
  async processFileMainThread(file, options = {}) {
    const { maxSize = 10 * 1024 * 1024 } = options;

    if (file.size > maxSize) {
      throw new Error(`File ${file.name} is too large (${file.size} bytes). Maximum allowed: ${maxSize} bytes`);
    }

    // Use non-blocking processing
    return new Promise((resolve, reject) => {
      setTimeout(async () => {
        try {
          const arrayBuffer = await file.arrayBuffer();
          const uint8Array = new Uint8Array(arrayBuffer);

          resolve({
            content_type: file.type,
            type: file.type,
            name: file.name,
            size: file.size,
            lastModified: file.lastModified,
            data: Array.from(uint8Array),
            processedAt: new Date().toISOString()
          });
        } catch (error) {
          reject(error);
        }
      }, 0);
    });
  }

  /**
   * Fallback: Process files batch on main thread
   */
  async processFilesBatchMainThread(files, options = {}) {
    const { concurrency = 2 } = options; // Lower concurrency for main thread
    const results = [];

    for (let i = 0; i < files.length; i += concurrency) {
      const batch = files.slice(i, i + concurrency);
      
      const batchPromises = batch.map(async (file, index) => {
        try {
          const result = await this.processFileMainThread(file, options);
          return {
            success: true,
            index: i + index,
            result
          };
        } catch (error) {
          return {
            success: false,
            index: i + index,
            error: error.message,
            fileName: file.name
          };
        }
      });

      const batchResults = await Promise.all(batchPromises);
      results.push(...batchResults);

      // Yield control between batches
      if (i + concurrency < files.length) {
        await new Promise(resolve => setTimeout(resolve, 10));
      }
    }

    return results;
  }

  /**
   * Cleanup resources
   */
  destroy() {
    if (this.worker) {
      this.worker.terminate();
      this.worker = null;
    }
    
    // Reject all pending operations
    for (const [id, operation] of this.pendingOperations) {
      operation.reject(new Error('FileProcessingManager destroyed'));
    }
    this.pendingOperations.clear();
    this.isInitialized = false;
  }
}

// Create singleton instance
const fileProcessingManager = new FileProcessingManager();

export default fileProcessingManager;
