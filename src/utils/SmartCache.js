/**
 * PERFORMANCE FIX: Smart Cache System
 * Intelligent caching for frequently accessed database data
 */

class SmartCache {
  constructor(options = {}) {
    this.maxSize = options.maxSize || 100; // Maximum number of cached items
    this.defaultTTL = options.defaultTTL || 300000; // 5 minutes default TTL
    this.cleanupInterval = options.cleanupInterval || 60000; // 1 minute cleanup interval
    this.hitRateThreshold = options.hitRateThreshold || 0.7; // 70% hit rate threshold

    // Cache storage
    this.cache = new Map();
    this.accessTimes = new Map();
    this.hitCounts = new Map();
    this.totalRequests = new Map();

    // Statistics
    this.stats = {
      hits: 0,
      misses: 0,
      evictions: 0,
      cleanups: 0
    };

    // Start cleanup interval
    this.startCleanup();

    console.log('[SmartCache] Initialized with options:', {
      maxSize: this.maxSize,
      defaultTTL: this.defaultTTL,
      cleanupInterval: this.cleanupInterval
    });
  }

  /**
   * Get cached data
   */
  get(key) {
    const item = this.cache.get(key);
    const now = Date.now();

    if (!item) {
      this.stats.misses++;
      this.updateRequestStats(key, false);
      return null;
    }

    // Check if expired
    if (item.expiresAt && now > item.expiresAt) {
      this.cache.delete(key);
      this.accessTimes.delete(key);
      this.stats.misses++;
      this.updateRequestStats(key, false);
      return null;
    }

    // Update access time and hit count
    this.accessTimes.set(key, now);
    this.stats.hits++;
    this.updateRequestStats(key, true);

    console.log(`[SmartCache] Cache hit for key: ${key}`);
    return item.data;
  }

  /**
   * Set cached data with duplicate detection
   */
  set(key, data, ttl = null) {
    const now = Date.now();
    const expiresAt = ttl ? now + ttl : now + this.defaultTTL;

    // Check for potential duplicate keys (same data, different prefixes)
    const normalizedKey = this.normalizeKey(key);
    const existingKey = this.findSimilarKey(normalizedKey);

    if (existingKey && existingKey !== key) {
      // Update existing entry instead of creating duplicate
      const existingItem = this.cache.get(existingKey);
      if (existingItem && this.isSimilarData(existingItem.data, data)) {
        this.accessTimes.set(existingKey, now);
        console.log(`[SmartCache] Updated existing similar cache entry: ${existingKey}`);
        return;
      }
    }

    // Check if we need to evict items
    if (this.cache.size >= this.maxSize && !this.cache.has(key)) {
      this.evictLeastUsed();
    }

    const item = {
      data,
      createdAt: now,
      expiresAt,
      size: this.estimateSize(data)
    };

    this.cache.set(key, item);
    this.accessTimes.set(key, now);

    console.log(`[SmartCache] Cached data for key: ${key} (TTL: ${ttl || this.defaultTTL}ms)`);
  }

  /**
   * Normalize cache key to detect duplicates
   */
  normalizeKey(key) {
    // Remove database prefixes to find similar queries
    return key.replace(/^[^:]+_[^:]+_([^:]+):/, '$1:');
  }

  /**
   * Find similar cache key
   */
  findSimilarKey(normalizedKey) {
    for (const key of this.cache.keys()) {
      if (this.normalizeKey(key) === normalizedKey) {
        return key;
      }
    }
    return null;
  }

  /**
   * Check if two data sets are similar (same length and structure)
   */
  isSimilarData(data1, data2) {
    if (Array.isArray(data1) && Array.isArray(data2)) {
      return data1.length === data2.length;
    }
    if (typeof data1 === 'object' && typeof data2 === 'object') {
      return JSON.stringify(data1).length === JSON.stringify(data2).length;
    }
    return data1 === data2;
  }

  /**
   * Check if key exists and is not expired
   */
  has(key) {
    const item = this.cache.get(key);
    if (!item) return false;

    const now = Date.now();
    if (item.expiresAt && now > item.expiresAt) {
      this.cache.delete(key);
      this.accessTimes.delete(key);
      return false;
    }

    return true;
  }

  /**
   * Delete cached item
   */
  delete(key) {
    const deleted = this.cache.delete(key);
    this.accessTimes.delete(key);
    this.hitCounts.delete(key);
    this.totalRequests.delete(key);
    return deleted;
  }

  /**
   * Clear all cached items
   */
  clear() {
    this.cache.clear();
    this.accessTimes.clear();
    this.hitCounts.clear();
    this.totalRequests.clear();
    console.log('[SmartCache] Cache cleared');
  }

  /**
   * Get cache statistics
   */
  getStats() {
    const totalRequests = this.stats.hits + this.stats.misses;
    const hitRate = totalRequests > 0 ? (this.stats.hits / totalRequests) : 0;

    return {
      ...this.stats,
      totalRequests,
      hitRate: Math.round(hitRate * 100) / 100,
      cacheSize: this.cache.size,
      maxSize: this.maxSize,
      memoryUsage: this.getMemoryUsage()
    };
  }

  /**
   * Update request statistics for intelligent caching
   */
  updateRequestStats(key, isHit) {
    if (!this.hitCounts.has(key)) {
      this.hitCounts.set(key, 0);
      this.totalRequests.set(key, 0);
    }

    if (isHit) {
      this.hitCounts.set(key, this.hitCounts.get(key) + 1);
    }
    this.totalRequests.set(key, this.totalRequests.get(key) + 1);
  }

  /**
   * Get hit rate for a specific key
   */
  getKeyHitRate(key) {
    const hits = this.hitCounts.get(key) || 0;
    const total = this.totalRequests.get(key) || 0;
    return total > 0 ? hits / total : 0;
  }

  /**
   * Evict least recently used items
   */
  evictLeastUsed() {
    if (this.cache.size === 0) return;

    // Find the least recently used item
    let oldestKey = null;
    let oldestTime = Date.now();

    for (const [key, time] of this.accessTimes) {
      if (time < oldestTime) {
        oldestTime = time;
        oldestKey = key;
      }
    }

    if (oldestKey) {
      this.cache.delete(oldestKey);
      this.accessTimes.delete(oldestKey);
      this.stats.evictions++;
      console.log(`[SmartCache] Evicted least used item: ${oldestKey}`);
    }
  }

  /**
   * Estimate memory usage of cached data
   */
  estimateSize(data) {
    try {
      return JSON.stringify(data).length * 2; // Rough estimate (UTF-16)
    } catch (error) {
      return 1000; // Default estimate for non-serializable data
    }
  }

  /**
   * Get total memory usage estimate
   */
  getMemoryUsage() {
    let totalSize = 0;
    for (const item of this.cache.values()) {
      totalSize += item.size || 0;
    }
    return totalSize;
  }

  /**
   * Start cleanup interval
   */
  startCleanup() {
    this.cleanupIntervalId = setInterval(() => {
      this.cleanup();

      // Run optimization every 5 cleanup cycles
      if (!this.optimizationCounter) this.optimizationCounter = 0;
      this.optimizationCounter++;

      if (this.optimizationCounter >= 5) {
        this.optimize();
        this.optimizationCounter = 0;
      }
    }, this.cleanupInterval);
  }

  /**
   * Cleanup expired items
   */
  cleanup() {
    const now = Date.now();
    const keysToDelete = [];

    for (const [key, item] of this.cache) {
      if (item.expiresAt && now > item.expiresAt) {
        keysToDelete.push(key);
      }
    }

    keysToDelete.forEach(key => {
      this.cache.delete(key);
      this.accessTimes.delete(key);
    });

    if (keysToDelete.length > 0) {
      this.stats.cleanups++;
      console.log(`[SmartCache] Cleaned up ${keysToDelete.length} expired items`);
    }
  }

  /**
   * Get cache keys sorted by access frequency
   */
  getKeysByFrequency() {
    const keyStats = [];

    for (const [key] of this.cache) {
      const hitRate = this.getKeyHitRate(key);
      const totalRequests = this.totalRequests.get(key) || 0;
      keyStats.push({ key, hitRate, totalRequests });
    }

    return keyStats.sort((a, b) => {
      // Sort by hit rate first, then by total requests
      if (b.hitRate !== a.hitRate) {
        return b.hitRate - a.hitRate;
      }
      return b.totalRequests - a.totalRequests;
    });
  }

  /**
   * Optimize cache by removing low-performing items
   */
  optimize() {
    const keyStats = this.getKeysByFrequency();
    const lowPerformingKeys = keyStats
      .filter(stat => stat.hitRate < this.hitRateThreshold && stat.totalRequests > 5)
      .map(stat => stat.key);

    lowPerformingKeys.forEach(key => {
      this.delete(key);
    });

    if (lowPerformingKeys.length > 0) {
      console.log(`[SmartCache] Optimized cache by removing ${lowPerformingKeys.length} low-performing items`);
    }

    return lowPerformingKeys.length;
  }

  /**
   * Destroy cache and cleanup resources
   */
  destroy() {
    if (this.cleanupIntervalId) {
      clearInterval(this.cleanupIntervalId);
      this.cleanupIntervalId = null;
    }

    this.clear();
    console.log('[SmartCache] Destroyed');
  }
}

// Create singleton instances for different cache types
export const queryCache = new SmartCache({
  maxSize: 50,
  defaultTTL: 300000, // 5 minutes
  cleanupInterval: 60000
});

export const documentCache = new SmartCache({
  maxSize: 200,
  defaultTTL: 600000, // 10 minutes
  cleanupInterval: 120000
});

export const metadataCache = new SmartCache({
  maxSize: 30,
  defaultTTL: 1800000, // 30 minutes
  cleanupInterval: 300000
});

export default SmartCache;
