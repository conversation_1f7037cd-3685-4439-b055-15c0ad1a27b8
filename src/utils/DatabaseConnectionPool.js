/**
 * PERFORMANCE FIX: Database Connection Pool Manager
 * Optimizes database connections and reduces overhead
 */

class DatabaseConnectionPool {
  constructor(options = {}) {
    this.maxConnections = options.maxConnections || 10;
    this.minConnections = options.minConnections || 2;
    this.connectionTimeout = options.connectionTimeout || 30000; // 30 seconds
    this.idleTimeout = options.idleTimeout || 300000; // 5 minutes
    this.retryAttempts = options.retryAttempts || 3;
    this.retryDelay = options.retryDelay || 1000;

    // Connection pools by database key
    this.pools = new Map();
    this.activeConnections = new Map();
    this.connectionStats = new Map();

    // Cleanup interval
    this.cleanupInterval = setInterval(() => {
      this.cleanupIdleConnections();
    }, 60000); // Check every minute

    console.log('[DatabaseConnectionPool] Initialized with options:', {
      maxConnections: this.maxConnections,
      minConnections: this.minConnections,
      connectionTimeout: this.connectionTimeout,
      idleTimeout: this.idleTimeout
    });
  }

  /**
   * Get or create a connection pool for a database
   */
  getPool(dbKey) {
    if (!this.pools.has(dbKey)) {
      this.pools.set(dbKey, {
        connections: [],
        waitingQueue: [],
        stats: {
          created: 0,
          acquired: 0,
          released: 0,
          errors: 0,
          timeouts: 0
        }
      });
      
      this.connectionStats.set(dbKey, {
        totalConnections: 0,
        activeConnections: 0,
        idleConnections: 0,
        lastActivity: Date.now()
      });
    }
    return this.pools.get(dbKey);
  }

  /**
   * Acquire a database connection
   */
  async acquireConnection(dbKey, connectionFactory) {
    const pool = this.getPool(dbKey);
    const stats = this.connectionStats.get(dbKey);

    // Update activity timestamp
    stats.lastActivity = Date.now();

    // Try to get an idle connection first
    const idleConnection = pool.connections.find(conn => !conn.inUse);
    if (idleConnection) {
      idleConnection.inUse = true;
      idleConnection.lastUsed = Date.now();
      stats.activeConnections++;
      stats.idleConnections--;
      pool.stats.acquired++;
      
      console.log(`[DatabaseConnectionPool] Reused connection for ${dbKey}`);
      return idleConnection.connection;
    }

    // Create new connection if under limit
    if (pool.connections.length < this.maxConnections) {
      try {
        const connection = await this.createConnection(dbKey, connectionFactory);
        const connectionWrapper = {
          connection,
          inUse: true,
          created: Date.now(),
          lastUsed: Date.now()
        };

        pool.connections.push(connectionWrapper);
        stats.totalConnections++;
        stats.activeConnections++;
        pool.stats.created++;
        pool.stats.acquired++;

        console.log(`[DatabaseConnectionPool] Created new connection for ${dbKey} (${pool.connections.length}/${this.maxConnections})`);
        return connection;
      } catch (error) {
        pool.stats.errors++;
        console.error(`[DatabaseConnectionPool] Failed to create connection for ${dbKey}:`, error);
        throw error;
      }
    }

    // Wait for available connection
    return this.waitForConnection(dbKey, connectionFactory);
  }

  /**
   * Release a database connection back to the pool
   */
  releaseConnection(dbKey, connection) {
    const pool = this.getPool(dbKey);
    const stats = this.connectionStats.get(dbKey);

    const connectionWrapper = pool.connections.find(conn => conn.connection === connection);
    if (connectionWrapper) {
      connectionWrapper.inUse = false;
      connectionWrapper.lastUsed = Date.now();
      stats.activeConnections--;
      stats.idleConnections++;
      pool.stats.released++;

      // Process waiting queue
      if (pool.waitingQueue.length > 0) {
        const { resolve } = pool.waitingQueue.shift();
        connectionWrapper.inUse = true;
        stats.activeConnections++;
        stats.idleConnections--;
        resolve(connection);
      }

      console.log(`[DatabaseConnectionPool] Released connection for ${dbKey}`);
    } else {
      console.warn(`[DatabaseConnectionPool] Attempted to release unknown connection for ${dbKey}`);
    }
  }

  /**
   * Create a new database connection
   */
  async createConnection(dbKey, connectionFactory) {
    const startTime = Date.now();
    
    try {
      const connection = await Promise.race([
        connectionFactory(),
        new Promise((_, reject) => 
          setTimeout(() => reject(new Error('Connection timeout')), this.connectionTimeout)
        )
      ]);

      const duration = Date.now() - startTime;
      console.log(`[DatabaseConnectionPool] Connection created for ${dbKey} in ${duration}ms`);
      
      return connection;
    } catch (error) {
      const duration = Date.now() - startTime;
      console.error(`[DatabaseConnectionPool] Connection creation failed for ${dbKey} after ${duration}ms:`, error);
      throw error;
    }
  }

  /**
   * Wait for an available connection
   */
  async waitForConnection(dbKey, connectionFactory) {
    const pool = this.getPool(dbKey);

    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        // Remove from waiting queue
        const index = pool.waitingQueue.findIndex(item => item.resolve === resolve);
        if (index !== -1) {
          pool.waitingQueue.splice(index, 1);
        }
        
        pool.stats.timeouts++;
        reject(new Error(`Connection timeout for ${dbKey}`));
      }, this.connectionTimeout);

      pool.waitingQueue.push({
        resolve: (connection) => {
          clearTimeout(timeout);
          resolve(connection);
        },
        reject: (error) => {
          clearTimeout(timeout);
          reject(error);
        }
      });
    });
  }

  /**
   * Cleanup idle connections
   */
  cleanupIdleConnections() {
    const now = Date.now();

    for (const [dbKey, pool] of this.pools) {
      const stats = this.connectionStats.get(dbKey);
      
      // Remove idle connections that have exceeded idle timeout
      const connectionsToRemove = [];
      
      pool.connections.forEach((connWrapper, index) => {
        if (!connWrapper.inUse && (now - connWrapper.lastUsed) > this.idleTimeout) {
          // Keep minimum connections
          const idleCount = pool.connections.filter(c => !c.inUse).length;
          if (idleCount > this.minConnections) {
            connectionsToRemove.push(index);
          }
        }
      });

      // Remove connections in reverse order to maintain indices
      connectionsToRemove.reverse().forEach(index => {
        const connWrapper = pool.connections[index];
        try {
          // Close connection if it has a close method
          if (connWrapper.connection && typeof connWrapper.connection.close === 'function') {
            connWrapper.connection.close();
          }
        } catch (error) {
          console.warn(`[DatabaseConnectionPool] Error closing connection for ${dbKey}:`, error);
        }
        
        pool.connections.splice(index, 1);
        stats.totalConnections--;
        stats.idleConnections--;
      });

      if (connectionsToRemove.length > 0) {
        console.log(`[DatabaseConnectionPool] Cleaned up ${connectionsToRemove.length} idle connections for ${dbKey}`);
      }
    }
  }

  /**
   * Get connection pool statistics
   */
  getStats(dbKey = null) {
    if (dbKey) {
      const pool = this.pools.get(dbKey);
      const stats = this.connectionStats.get(dbKey);
      
      if (!pool || !stats) {
        return null;
      }

      return {
        dbKey,
        totalConnections: stats.totalConnections,
        activeConnections: stats.activeConnections,
        idleConnections: stats.idleConnections,
        waitingQueue: pool.waitingQueue.length,
        lastActivity: stats.lastActivity,
        poolStats: pool.stats
      };
    }

    // Return stats for all pools
    const allStats = {};
    for (const dbKey of this.pools.keys()) {
      allStats[dbKey] = this.getStats(dbKey);
    }
    return allStats;
  }

  /**
   * Close all connections and cleanup
   */
  async destroy() {
    console.log('[DatabaseConnectionPool] Destroying connection pool...');
    
    // Clear cleanup interval
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = null;
    }

    // Close all connections
    for (const [dbKey, pool] of this.pools) {
      for (const connWrapper of pool.connections) {
        try {
          if (connWrapper.connection && typeof connWrapper.connection.close === 'function') {
            await connWrapper.connection.close();
          }
        } catch (error) {
          console.warn(`[DatabaseConnectionPool] Error closing connection for ${dbKey}:`, error);
        }
      }
      
      // Reject all waiting requests
      pool.waitingQueue.forEach(({ reject }) => {
        reject(new Error('Connection pool destroyed'));
      });
    }

    this.pools.clear();
    this.activeConnections.clear();
    this.connectionStats.clear();
    
    console.log('[DatabaseConnectionPool] Destroyed successfully');
  }
}

// Create singleton instance
const databaseConnectionPool = new DatabaseConnectionPool({
  maxConnections: 8,
  minConnections: 2,
  connectionTimeout: 15000,
  idleTimeout: 300000
});

export default databaseConnectionPool;
