/**
 * PERFORMANCE FIX: Data Processing Manager
 * Manages background data processing using web workers (PouchDB-safe)
 * 
 * This manager handles CPU-intensive data operations in web workers
 * while keeping all PouchDB operations in the main thread to avoid
 * IndexedDB and browser API limitations in worker contexts.
 */

class DataProcessingManager {
  constructor() {
    this.worker = null;
    this.pendingOperations = new Map();
    this.operationId = 0;
    this.isInitialized = false;
    this.fallbackMode = false;
  }

  /**
   * Initialize the data processing worker
   */
  async initialize() {
    if (this.isInitialized) {
      return;
    }

    try {
      // Check if we're in a web worker environment and workers are supported
      if (typeof Worker !== 'undefined' && typeof window !== 'undefined') {
        this.worker = new Worker('/src/workers/databaseWorker.js');
        this.worker.onmessage = this.handleWorkerMessage.bind(this);
        this.worker.onerror = this.handleWorkerError.bind(this);
        this.isInitialized = true;
        console.log('[DataProcessingManager] Worker initialized successfully');
      } else {
        console.warn('[DataProcessingManager] Web Workers not supported or not in browser context, using fallback mode');
        this.fallbackMode = true;
        this.isInitialized = true;
      }
    } catch (error) {
      console.error('[DataProcessingManager] Failed to initialize worker:', error);
      this.fallbackMode = true;
      this.isInitialized = true;
    }
  }

  /**
   * Handle messages from the worker
   */
  handleWorkerMessage(e) {
    const { type, id, success, results, documents, result, statistics, error } = e.data;
    const operation = this.pendingOperations.get(id);

    if (!operation) {
      console.warn('[DataProcessingManager] Received message for unknown operation:', id);
      return;
    }

    this.pendingOperations.delete(id);

    if (success) {
      switch (type) {
        case 'QUERY_RESULTS_PROCESSED':
          operation.resolve(results);
          break;
        case 'DOCUMENTS_FILTERED':
          operation.resolve(documents);
          break;
        case 'DOCUMENTS_SORTED':
          operation.resolve(documents);
          break;
        case 'DATA_AGGREGATED':
          operation.resolve(result);
          break;
        case 'DOCUMENTS_VALIDATED':
          operation.resolve(results);
          break;
        case 'DOCUMENTS_TRANSFORMED':
          operation.resolve(documents);
          break;
        case 'STATISTICS_CALCULATED':
          operation.resolve(statistics);
          break;
        default:
          operation.reject(new Error(`Unknown response type: ${type}`));
      }
    } else {
      operation.reject(new Error(error));
    }
  }

  /**
   * Handle worker errors
   */
  handleWorkerError(error) {
    console.error('[DataProcessingManager] Worker error:', error);

    // Reject all pending operations
    for (const [id, operation] of this.pendingOperations) {
      operation.reject(new Error('Worker error occurred'));
    }
    this.pendingOperations.clear();

    // Switch to fallback mode
    this.fallbackMode = true;
  }

  /**
   * Process query results with complex transformations
   */
  async processQueryResults(results, options = {}) {
    await this.initialize();

    if (this.worker && !this.fallbackMode) {
      return this.processWithWorker('PROCESS_QUERY_RESULTS', { results, options });
    } else {
      return this.processQueryResultsMainThread(results, options);
    }
  }

  /**
   * Filter documents based on complex criteria
   */
  async filterDocuments(documents, filters) {
    await this.initialize();

    if (this.worker && !this.fallbackMode) {
      return this.processWithWorker('FILTER_DOCUMENTS', { documents, filters });
    } else {
      return this.filterDocumentsMainThread(documents, filters);
    }
  }

  /**
   * Sort documents by multiple criteria
   */
  async sortDocuments(documents, sortOptions) {
    await this.initialize();

    if (this.worker && !this.fallbackMode) {
      return this.processWithWorker('SORT_DOCUMENTS', { documents, sortOptions });
    } else {
      return this.sortDocumentsMainThread(documents, sortOptions);
    }
  }

  /**
   * Aggregate data with various operations
   */
  async aggregateData(documents, aggregation) {
    await this.initialize();

    if (this.worker && !this.fallbackMode) {
      return this.processWithWorker('AGGREGATE_DATA', { documents, aggregation });
    } else {
      return this.aggregateDataMainThread(documents, aggregation);
    }
  }

  /**
   * Calculate statistics for document fields
   */
  async calculateStatistics(documents, fields) {
    await this.initialize();

    if (this.worker && !this.fallbackMode) {
      return this.processWithWorker('CALCULATE_STATISTICS', { documents, fields });
    } else {
      return this.calculateStatisticsMainThread(documents, fields);
    }
  }

  /**
   * Generic worker processing method
   */
  async processWithWorker(type, data) {
    const id = ++this.operationId;

    return new Promise((resolve, reject) => {
      this.pendingOperations.set(id, { resolve, reject });

      this.worker.postMessage({
        type,
        id,
        data
      });

      // Set timeout to prevent hanging
      setTimeout(() => {
        if (this.pendingOperations.has(id)) {
          this.pendingOperations.delete(id);
          reject(new Error(`${type} processing timeout`));
        }
      }, 30000); // 30 second timeout
    });
  }

  /**
   * Fallback: Process query results on main thread
   */
  async processQueryResultsMainThread(results, options = {}) {
    return new Promise((resolve) => {
      setTimeout(() => {
        // Simple processing for fallback
        const processedResults = {
          results: results,
          metadata: options.includeMetadata ? { processedAt: new Date().toISOString() } : undefined,
          stats: options.calculateStats ? { totalCount: results.length, processedCount: results.length } : undefined
        };
        resolve(processedResults);
      }, 0);
    });
  }

  /**
   * Fallback: Filter documents on main thread
   */
  async filterDocumentsMainThread(documents, filters) {
    return new Promise((resolve) => {
      setTimeout(() => {
        if (!filters || Object.keys(filters).length === 0) {
          resolve(documents);
          return;
        }

        const filtered = documents.filter(doc => {
          return Object.entries(filters).every(([field, criteria]) => {
            const value = this.getNestedValue(doc, field);
            return value === criteria; // Simple equality check for fallback
          });
        });

        resolve(filtered);
      }, 0);
    });
  }

  /**
   * Fallback: Sort documents on main thread
   */
  async sortDocumentsMainThread(documents, sortOptions) {
    return new Promise((resolve) => {
      setTimeout(() => {
        if (!sortOptions || sortOptions.length === 0) {
          resolve(documents);
          return;
        }

        const sorted = [...documents].sort((a, b) => {
          const { field, direction = 'asc' } = sortOptions[0]; // Use first sort option only for fallback
          const aValue = this.getNestedValue(a, field);
          const bValue = this.getNestedValue(b, field);

          if (aValue < bValue) return direction === 'desc' ? 1 : -1;
          if (aValue > bValue) return direction === 'desc' ? -1 : 1;
          return 0;
        });

        resolve(sorted);
      }, 0);
    });
  }

  /**
   * Fallback: Aggregate data on main thread
   */
  async aggregateDataMainThread(documents, aggregation) {
    return new Promise((resolve) => {
      setTimeout(() => {
        // Simple count aggregation for fallback
        const result = {
          count: documents.length,
          processed: true
        };
        resolve(result);
      }, 0);
    });
  }

  /**
   * Fallback: Calculate statistics on main thread
   */
  async calculateStatisticsMainThread(documents, fields) {
    return new Promise((resolve) => {
      setTimeout(() => {
        const stats = {};
        fields.forEach(field => {
          const values = documents.map(doc => this.getNestedValue(doc, field)).filter(v => v !== undefined);
          stats[field] = {
            count: values.length,
            uniqueCount: new Set(values).size,
            type: 'mixed'
          };
        });
        resolve(stats);
      }, 0);
    });
  }

  /**
   * Get nested value from object using dot notation
   */
  getNestedValue(obj, path) {
    return path.split('.').reduce((current, key) => {
      return current && current[key] !== undefined ? current[key] : undefined;
    }, obj);
  }

  /**
   * Cleanup resources
   */
  destroy() {
    if (this.worker) {
      this.worker.terminate();
      this.worker = null;
    }

    // Reject all pending operations
    for (const [id, operation] of this.pendingOperations) {
      operation.reject(new Error('DataProcessingManager destroyed'));
    }
    this.pendingOperations.clear();
    this.isInitialized = false;
    this.fallbackMode = false;
  }
}

// Create singleton instance
const dataProcessingManager = new DataProcessingManager();

export default dataProcessingManager;
