import PouchDB from "pouchdb";
import PouchDBFind from "pouchdb-find";
import { EventEmitter } from "events";
import fileProcessingManager from "../utils/FileProcessingManager.js";
import { queryCache, documentCache } from "../utils/SmartCache.js";
import dataProcessingManager from "../utils/DataProcessingManager.js";


/**
 * Unified Capacitor Database Interface
* Provides database operations for all web environments:
* - Capacitor mobile apps (iOS/Android) with native features
* - Capacitor web apps with Capacitor framework
* - Pure web browsers with graceful fallbacks
* Maintains API compatibility with existing SimplifiedDB interface
*/


// Optional Capacitor imports with graceful fallbacks
let Capacitor = null;
let Device = null;
let Network = null;

// Try to import Capacitor core - this determines if we're in a Capacitor environment
try {
  Capacitor = require('@capacitor/core').Capacitor;
} catch (e) {
  console.log('[CapacitorDB] Running in pure web environment (no Capacitor framework)');
}

// Try to import Capacitor plugins if available
try {
  Device = require('@capacitor/device').Device;
} catch (e) {
  // Silent fallback - this is expected in pure web environments
}

try {
  Network = require('@capacitor/network').Network;
} catch (e) {
  // Silent fallback - this is expected in pure web environments
}

// Initialize PouchDB plugins
PouchDB.plugin(PouchDBFind);

export default class CapacitorDB extends EventEmitter {
  constructor(name, databasePrefix, lan_details, branch) {
    super();

    // MEMORY LEAK FIX: Increase max listeners to prevent warnings
    this.setMaxListeners(20);

    if (!name?.trim()) {
      throw new Error('Invalid database name');
    }

    this.name = name.trim();
    this.databasePrefix = databasePrefix?.trim() || '';
    this.dbKey = `${this.databasePrefix}${this.name}`;
    this.currentUser = { name: "sys" };
    this.branch = this.normalizeBranch(branch);
    this.lan_details = lan_details;

    // Detect environment and set platform-specific optimizations
    this.environment = this.detectEnvironment();
    this.platformOptimizations = this.getPlatformOptimizations();

    // Initialize PouchDB for local storage
    this.pouchDb = new PouchDB(this.dbKey, {
      auto_compaction: true,
      revs_limit: 10  // Keep more revisions for proper conflict resolution
    });

    // PERFORMANCE FIX: Removed conflicting db assignment (getter exists at line 915)
    // this.db = this.pouchDb; // This conflicts with the getter

    // Initialize sync-related properties
    this.syncHandler = null;
    this.instantSyncTimeout = null;
    this.syncInProgress = false;

    // Initialize database
    this.initializeDatabase();

    // Setup event listeners
    this.setupEventListeners();

    console.log(`[CapacitorDB] Initialized ${this.dbKey} for ${this.environment.description}`);
  }

  /**
   * Detect the current environment and available features
   */
  detectEnvironment() {
    const hasCapacitor = !!Capacitor;
    const hasDevice = !!Device;
    const hasNetwork = !!Network;

    let platform = 'web';
    let isNative = false;
    let description = 'Pure web browser';

    if (hasCapacitor) {
      try {
        platform = Capacitor.getPlatform();
        isNative = platform === 'ios' || platform === 'android';
        description = isNative
          ? `Capacitor native mobile (${platform})`
          : `Capacitor web (${platform})`;
      } catch (error) {
        // Fallback if getPlatform fails
        description = 'Capacitor environment (platform detection failed)';
      }
    }

    return {
      hasCapacitor,
      hasDevice,
      hasNetwork,
      platform,
      isNative,
      isPureWeb: !hasCapacitor,
      isCapacitorWeb: hasCapacitor && !isNative,
      description
    };
  }

  /**
   * Get platform-specific optimizations
   */
  getPlatformOptimizations() {
    const env = this.environment;

    if (env.isNative) {
      // Native mobile optimizations
      return {
        cacheTimeout: 3000,    // 3 seconds - faster for mobile
        maxCacheSize: 5,       // Smaller cache for mobile memory
        logBatchSize: 8,       // Smaller batches for mobile
        logBatchTimeout: 4000, // Faster processing for mobile
        syncBatchSize: 20,     // Smaller sync batches for mobile
        deviceInfoLevel: 'full' // Full device info available
      };
    } else if (env.isCapacitorWeb) {
      // Capacitor web optimizations
      return {
        cacheTimeout: 4000,    // 4 seconds - balanced for web
        maxCacheSize: 8,       // Medium cache for web
        logBatchSize: 10,      // Standard batch size
        logBatchTimeout: 5000, // Standard timeout
        syncBatchSize: 30,     // Medium sync batches
        deviceInfoLevel: 'limited' // Limited device info in web
      };
    } else {
      // Pure web optimizations (WebDB equivalent)
      return {
        cacheTimeout: 4000,    // 4 seconds - web standard
        maxCacheSize: 8,       // Standard web cache
        logBatchSize: 10,      // Standard batch size
        logBatchTimeout: 5000, // Standard timeout
        syncBatchSize: 25,     // Standard sync batches
        deviceInfoLevel: 'basic' // Basic browser info only
      };
    }
  }

  /**
   * Normalize branch value
   */
  normalizeBranch(branch) {
    if (!branch || branch === 'null' || branch === 'undefined') {
      return 'none';
    }
    if (branch === 'all') {
      return 'all';
    }
    return branch;
  }

  /**
   * Initialize database
   */
  async initializeDatabase() {
    try {
      // Create indexes for better query performance
      await this.createIndexes();

      this.emit('initialized');
      console.log(`[CapacitorDB] Database ${this.dbKey} initialized`);
    } catch (error) {
      console.error(`[CapacitorDB] Initialization error for ${this.dbKey}:`, error);
      this.emit('error', error);
    }
  }

  /**
   * Create comprehensive database indexes for optimal performance
   */
  async createIndexes() {
    try {
      console.log(`[CapacitorDB] Creating comprehensive indexes for ${this.dbKey}...`);

      // PERFORMANCE FIX: Comprehensive indexing strategy
      const indexes = [
        // Basic timestamp indexes for sorting and filtering
        { fields: ['createdAt'] },
        { fields: ['updatedAt'] },

        // Type and branch indexes for filtering
        { fields: ['type'] },
        { fields: ['branch'] },

        // Composite indexes for common query patterns
        { fields: ['type', 'createdAt'] },
        { fields: ['branch', 'createdAt'] },
        { fields: ['type', 'branch'] },
        { fields: ['type', 'branch', 'createdAt'] },

        // Status and state indexes
        { fields: ['status'] },
        { fields: ['state'] },
        { fields: ['active'] },

        // User-related indexes
        { fields: ['createdBy'] },
        { fields: ['updatedBy'] },
        { fields: ['assignedTo'] },

        // Business-specific indexes
        { fields: ['customerId'] },
        { fields: ['customerName'] },
        { fields: ['invoiceNumber'] },
        { fields: ['referenceNumber'] },
        { fields: ['jobId'] },
        { fields: ['productId'] },
        { fields: ['categoryId'] },

        // Date range indexes for reports
        { fields: ['date'] },
        { fields: ['dueDate'] },
        { fields: ['completedDate'] },

        // Search optimization indexes
        { fields: ['name'] },
        { fields: ['title'] },
        { fields: ['description'] },
        { fields: ['email'] },
        { fields: ['phone'] },

        // Composite indexes for complex queries
        { fields: ['status', 'createdAt'] },
        { fields: ['customerId', 'createdAt'] },
        { fields: ['type', 'status', 'createdAt'] },
      ];

      // Create indexes in batches to prevent overwhelming the database
      const batchSize = 5;
      for (let i = 0; i < indexes.length; i += batchSize) {
        const batch = indexes.slice(i, i + batchSize);

        await Promise.all(batch.map(async (indexDef) => {
          try {
            await this.pouchDb.createIndex({ index: indexDef });
            console.log(`[CapacitorDB] Created index: ${indexDef.fields.join(', ')}`);
          } catch (error) {
            // Ignore "index already exists" errors
            if (!error.message.includes('already exists')) {
              console.warn(`[CapacitorDB] Failed to create index ${indexDef.fields.join(', ')}:`, error.message);
            }
          }
        }));

        // Small delay between batches to prevent overwhelming
        if (i + batchSize < indexes.length) {
          await new Promise(resolve => setTimeout(resolve, 10));
        }
      }

      console.log(`[CapacitorDB] Completed creating ${indexes.length} indexes for ${this.dbKey}`);
    } catch (error) {
      console.error(`[CapacitorDB] Error creating indexes for ${this.dbKey}:`, error);
    }
  }

  /**
   * Setup event listeners
   */
  setupEventListeners() {
    // MEMORY LEAK FIX: Store change listener for proper cleanup
    this.changeListener = this.pouchDb.changes({
      since: 'now',
      live: true,
      include_docs: true
    });

    // Store bound handlers for proper cleanup
    this._changeHandler = (change) => {
      if (change.doc) {
        if (change.deleted) {
          this.emit('documentDeleted', change.doc);
        } else if (change.doc._rev.startsWith('1-')) {
          this.emit('documentCreated', change.doc);
        } else {
          this.emit('documentUpdated', change.doc);
        }
      }
    };

    this._errorHandler = (error) => {
      console.error(`[CapacitorDB] Changes error:`, error);
    };

    // Add listeners with stored references
    this.changeListener.on('change', this._changeHandler);
    this.changeListener.on('error', this._errorHandler);

    // Setup network status monitoring for sync
    if (Network && Capacitor.isPluginAvailable('Network')) {
      try {
        Network.addListener('networkStatusChange', (status) => {
          if (status.connected) {
            console.log('[CapacitorDB] Network connected, triggering sync');
            this.triggerInstantSync('network_reconnect');
          }
        });
      } catch (error) {
        console.warn('[CapacitorDB] Failed to add network listener:', error);

        // Fallback to window online/offline events
        window.addEventListener('online', () => {
          console.log('[CapacitorDB] Network online event, triggering sync');
          this.triggerInstantSync('network_reconnect');
        });
      }
    } else {
      // Fallback to window online/offline events
      window.addEventListener('online', () => {
        console.log('[CapacitorDB] Network online event, triggering sync');
        this.triggerInstantSync('network_reconnect');
      });
    }
  }

  /**
   * PERFORMANCE FIX: Optimized save with background file processing
   */
  async save(data, user) {
    try {
      this.currentUser = user || this.currentUser;

      const isUpdate = !!data._id;
      const docId = data._id || Date.now().toString(36).toUpperCase();

      // PERFORMANCE FIX: Process files in background if present
      let processedData = data;
      if (this.hasFileObjects(data)) {
        console.log(`[CapacitorDB] File objects detected, processing in background...`);
        processedData = await this.preprocessDataForBackground(data);
      }

      // Prepare document with required fields
      const preparedDoc = {
        ...processedData,
        _id: docId,
        updatedAt: new Date().toISOString(),
        branch: this.normalizeBranch(processedData.branch) || this.branch,
        ...(isUpdate ? {} : {
          createdAt: new Date().toISOString(),
          createdBy: this.currentUser,
          referenceNumber: processedData.referenceNumber || this.generateReferenceNumber()
        })
      };

      // Get existing document for updates to preserve _rev
      if (isUpdate) {
        try {
          const existingDoc = await this.pouchDb.get(docId);
          preparedDoc._rev = existingDoc._rev;
        } catch (err) {
          if (err.status !== 404) {
            throw err;
          }
          // Document doesn't exist, treat as new
        }
      }

      // Save to PouchDB
      const result = await this.pouchDb.put(preparedDoc);
      preparedDoc._rev = result.rev;

      // PERFORMANCE FIX: Batch logging operations to reduce overhead
      this.queueLogOperation({
        description: `${isUpdate ? 'Updated' : 'Created'} data in ${this.name}`,
        details: { _id: docId, operation: isUpdate ? 'update' : 'create' }, // Reduced details
        user: this.currentUser,
        action: isUpdate ? "edit" : "create",
        type: isUpdate ? "edit" : "add",
      });

      // PERFORMANCE FIX: Invalidate cache after successful save
      this.invalidateCache();

      // Emit events
      this.emit(isUpdate ? 'documentUpdated' : 'documentCreated', preparedDoc);

      // PERFORMANCE FIX: Debounced sync trigger to reduce sync frequency
      this.debouncedSyncTrigger('create_update');

      return { id: docId, doc: preparedDoc };
    } catch (error) {
      console.error(`[CapacitorDB] Save failed for ${this.dbKey}:`, error);
      throw error;
    }
  }

  /**
   * PERFORMANCE FIX: Environment-aware queue log operations for batch processing
   */
  queueLogOperation(logData) {
    if (!this.logQueue) {
      this.logQueue = [];
    }

    const opts = this.platformOptimizations;

    this.logQueue.push({
      ...logData,
      timestamp: new Date().toISOString()
    });

    // Process log queue using environment-specific settings
    if (this.logQueue.length >= opts.logBatchSize) {
      this.processLogQueue();
    } else if (!this.logQueueTimer) {
      this.logQueueTimer = setTimeout(() => {
        this.processLogQueue();
      }, opts.logBatchTimeout);
    }
  }

  /**
   * PERFORMANCE FIX: Process queued log operations in batch
   */
  async processLogQueue() {
    if (!this.logQueue || this.logQueue.length === 0) return;

    const logsToProcess = this.logQueue.splice(0);
    this.logQueueTimer = null;

    try {
      // Batch process logs
      await this.bulkAddLogs(logsToProcess);
    } catch (error) {
      console.warn(`[CapacitorDB] Failed to process log queue:`, error);
    }
  }

  /**
   * PERFORMANCE FIX: Debounced sync trigger to reduce sync frequency
   */
  debouncedSyncTrigger(operation) {
    if (this.syncTriggerTimeout) {
      clearTimeout(this.syncTriggerTimeout);
    }

    this.syncTriggerTimeout = setTimeout(() => {
      this.triggerInstantSync(operation);
    }, 100); // 100ms debounce
  }

  /**
   * PERFORMANCE FIX: Bulk add logs for batch processing
   */
  async bulkAddLogs(logs) {
    if (!logs || logs.length === 0) return;

    try {
      // Create logs database if it doesn't exist
      if (!this.logsDb) {
        this.logsDb = new PouchDB(`${this.databasePrefix}logs`);
      }

      // Prepare all log entries with environment-aware device info
      const logEntries = await Promise.all(logs.map(async (logData) => ({
        ...logData,
        _id: `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        createdAt: logData.timestamp || new Date().toISOString(),
        sourceDatabase: this.name,
        databasePrefix: this.databasePrefix,
        branch: this.branch,
        deviceInfo: await this.getEnvironmentAwareDeviceInfo()
      })));

      // Bulk save all logs
      await this.logsDb.bulkDocs(logEntries);

      console.log(`[CapacitorDB] Successfully saved ${logs.length} logs in batch`);
    } catch (error) {
      console.warn(`[CapacitorDB] Failed to bulk add logs:`, error);

      // Fallback: save logs individually
      for (const log of logs) {
        try {
          await this.addLog(log);
        } catch (individualError) {
          console.warn(`[CapacitorDB] Failed to add individual log:`, individualError);
        }
      }
    }
  }

  /**
   * PERFORMANCE FIX: Force process log queue immediately (useful for testing/debugging)
   */
  async flushLogQueue() {
    if (this.logQueueTimer) {
      clearTimeout(this.logQueueTimer);
      this.logQueueTimer = null;
    }
    await this.processLogQueue();
  }

  /**
   * Save a document (alias for save)
   */
  async saveDocument(data, user) {
    return await this.save(data, user);
  }

  /**
   * Generate a reference number for new documents
   */
  generateReferenceNumber(prefix = '', index = 0) {
    const timestamp = Date.now();
    const random = Math.floor(Math.random() * 10000);
    return `${prefix}${timestamp}${index}${random}`;
  }

  /**
   * PERFORMANCE FIX: Check if data contains File objects
   */
  hasFileObjects(data) {
    if (!data || typeof data !== 'object') return false;

    for (const value of Object.values(data)) {
      if (value && value.file && value.file instanceof File) {
        return true;
      }
      if (value && value.fileList) {
        return true;
      }
    }
    return false;
  }

  /**
   * PERFORMANCE FIX: Background file processing for CapacitorDB
   */
  async preprocessDataForBackground(data) {
    const processedData = { ...data };
    const filesToProcess = [];

    // Collect all files that need processing
    for (const [key, value] of Object.entries(processedData)) {
      if (value && value.file && value.file instanceof File) {
        filesToProcess.push({ key, file: value.file });
        // Remove the original file object from the data
        delete processedData[key];
      } else if (value && value.fileList) {
        // Handle fileList if present
        delete processedData[key];
      }
    }

    // Process files in background if any were found
    if (filesToProcess.length > 0) {
      console.log(`[CapacitorDB] Processing ${filesToProcess.length} files in background`);

      try {
        // Process files using background worker
        const fileResults = await fileProcessingManager.processFilesBatch(
          filesToProcess.map(item => item.file),
          {
            maxSize: 10 * 1024 * 1024, // 10MB limit
            concurrency: 3
          }
        );

        // Convert results to PouchDB attachments format
        const attachments = {};
        fileResults.forEach((result, index) => {
          if (result.success) {
            const { key } = filesToProcess[index];
            const fileData = result.result;

            // Convert to PouchDB attachment format
            attachments[key] = {
              content_type: fileData.content_type,
              data: new Uint8Array(fileData.data)
            };
          } else {
            console.error(`[CapacitorDB] Failed to process file for field ${filesToProcess[index].key}:`, result.error);
          }
        });

        // Add attachments to the document if any were processed successfully
        if (Object.keys(attachments).length > 0) {
          processedData._attachments = attachments;
        }

        console.log(`[CapacitorDB] Successfully processed ${fileResults.filter(r => r.success).length}/${filesToProcess.length} files`);
      } catch (error) {
        console.error(`[CapacitorDB] Background file processing failed:`, error);
        // Continue without attachments rather than failing completely
      }
    }

    return processedData;
  }

  /**
   * Get a document by ID
   */
  async get(id) {
    try {
      return await this.pouchDb.get(id);
    } catch (error) {
      if (error.status === 404) {
        return null;
      }
      console.error(`[CapacitorDB] Get failed for ${this.dbKey}:`, error);
      throw error;
    }
  }

  /**
   * Get a document by ID (alias for get)
   */
  async getDocument(id) {
    return await this.get(id);
  }

  /**
   * PERFORMANCE FIX: Environment-aware optimized getAll with smart pagination and caching
   */
  async getAll(options = {}) {
    try {
      // PERFORMANCE FIX: Smart pagination for large datasets
      const {
        limit,
        skip,
        page,
        pageSize = 50, // Default page size for better performance
        ...otherOptions
      } = options;

      // Calculate pagination parameters
      let finalLimit = limit;
      let finalSkip = skip;

      if (page && pageSize && !limit && !skip) {
        finalLimit = pageSize;
        finalSkip = (page - 1) * pageSize;
      }

      // PERFORMANCE FIX: Smart cache key generation
      const cacheKey = `${this.dbKey}:getAll:${JSON.stringify({
        ...otherOptions,
        limit: finalLimit,
        skip: finalSkip
      })}`;

      // Check smart cache first
      const cachedData = queryCache.get(cacheKey);
      if (cachedData) {
        console.log(`[CapacitorDB] Smart cache hit for ${this.dbKey} (${cachedData.length} docs)`);
        return cachedData;
      }

      // PERFORMANCE FIX: Optimize options for current environment
      const optimizedOptions = {
        include_docs: true,
        conflicts: false, // Skip conflicts unless explicitly requested
        attachments: false, // Skip attachments unless explicitly requested
        descending: otherOptions.descending !== undefined ? otherOptions.descending : true, // Default to newest first
        ...otherOptions,
        // Pagination parameters
        ...(finalLimit && { limit: finalLimit }),
        ...(finalSkip && { skip: finalSkip }),
      };

      console.log(`[CapacitorDB] Fetching data for ${this.dbKey}:`, {
        limit: finalLimit,
        skip: finalSkip,
        page,
        pageSize
      });

      const result = await this.pouchDb.allDocs(optimizedOptions);

      const data = result.rows
        .filter(row => row.doc && !row.doc._id.startsWith('_'))
        .map(row => row.doc);

      console.log(`[CapacitorDB] Retrieved ${data.length} documents for ${this.dbKey}`);

      // PERFORMANCE FIX: Cache using smart cache with dynamic TTL
      const ttl = this.calculateCacheTTL(data.length, finalLimit);
      queryCache.set(cacheKey, data, ttl);

      return data;
    } catch (error) {
      console.error(`[CapacitorDB] GetAll failed for ${this.dbKey}:`, error);
      return [];
    }
  }

  /**
   * PERFORMANCE FIX: Get paginated results with metadata
   */
  async getPaginated(page = 1, pageSize = 50, options = {}) {
    try {
      const skip = (page - 1) * pageSize;

      // Get the requested page
      const docs = await this.getAll({
        ...options,
        limit: pageSize,
        skip: skip
      });

      // Get total count (cached for 30 seconds)
      const totalCount = await this.getCount(options);

      return {
        docs,
        pagination: {
          page,
          pageSize,
          totalCount,
          totalPages: Math.ceil(totalCount / pageSize),
          hasNextPage: page * pageSize < totalCount,
          hasPrevPage: page > 1
        }
      };
    } catch (error) {
      console.error(`[CapacitorDB] GetPaginated failed for ${this.dbKey}:`, error);
      return {
        docs: [],
        pagination: {
          page,
          pageSize,
          totalCount: 0,
          totalPages: 0,
          hasNextPage: false,
          hasPrevPage: false
        }
      };
    }
  }

  /**
   * PERFORMANCE FIX: Get document count with smart caching
   */
  async getCount(options = {}) {
    try {
      const cacheKey = `${this.dbKey}:count:${JSON.stringify(options)}`;

      // Check smart cache first
      const cachedCount = queryCache.get(cacheKey);
      if (cachedCount !== null) {
        return cachedCount;
      }

      // Get count from database (without loading all documents)
      const result = await this.pouchDb.allDocs({
        ...options,
        include_docs: false, // Don't load document content for counting
        attachments: false,
        conflicts: false
      });

      const count = result.rows.filter(row => !row.id.startsWith('_')).length;

      // Cache with longer TTL for counts (they change less frequently)
      queryCache.set(cacheKey, count, 60000); // 1 minute TTL
      return count;
    } catch (error) {
      console.error(`[CapacitorDB] GetCount error for ${this.dbKey}:`, error);
      return 0;
    }
  }

  /**
   * PERFORMANCE FIX: Calculate dynamic cache TTL based on data characteristics
   */
  calculateCacheTTL(resultCount, limit) {
    // Base TTL
    let ttl = 300000; // 5 minutes default

    // Shorter TTL for paginated results (they change more frequently)
    if (limit) {
      ttl = 120000; // 2 minutes
    }

    // Shorter TTL for small result sets (likely to change)
    if (resultCount < 10) {
      ttl = 60000; // 1 minute
    }

    // Longer TTL for large result sets (less likely to change completely)
    if (resultCount > 100) {
      ttl = 600000; // 10 minutes
    }

    return ttl;
  }

  /**
   * PERFORMANCE FIX: Invalidate cache when data changes
   */
  invalidateCache(pattern = null) {
    if (pattern) {
      // Invalidate specific cache entries matching pattern
      const keysToDelete = [];
      for (const key of queryCache.cache.keys()) {
        if (key.startsWith(`${this.dbKey}:`) && key.includes(pattern)) {
          keysToDelete.push(key);
        }
      }
      keysToDelete.forEach(key => queryCache.delete(key));
      console.log(`[CapacitorDB] Invalidated ${keysToDelete.length} cache entries matching pattern: ${pattern}`);
    } else {
      // Invalidate all cache entries for this database
      const keysToDelete = [];
      for (const key of queryCache.cache.keys()) {
        if (key.startsWith(`${this.dbKey}:`)) {
          keysToDelete.push(key);
        }
      }
      keysToDelete.forEach(key => queryCache.delete(key));
      console.log(`[CapacitorDB] Invalidated ${keysToDelete.length} cache entries for ${this.dbKey}`);
    }
  }

  /**
   * PERFORMANCE FIX: Filter documents using background processing for large datasets
   */
  async filterDocuments(documents, filters) {
    try {
      // Use background processing for large datasets
      if (documents.length > 100) {
        console.log(`[CapacitorDB] Using background processing for filtering ${documents.length} documents`);
        return await dataProcessingManager.filterDocuments(documents, filters);
      } else {
        // Process small datasets on main thread
        return documents.filter(doc => {
          return Object.entries(filters).every(([field, criteria]) => {
            const value = this.getNestedValue(doc, field);
            return value === criteria;
          });
        });
      }
    } catch (error) {
      console.error(`[CapacitorDB] Document filtering failed:`, error);
      return documents; // Return original documents on error
    }
  }

  /**
   * PERFORMANCE FIX: Sort documents using background processing for large datasets
   */
  async sortDocuments(documents, sortOptions) {
    try {
      // Use background processing for large datasets
      if (documents.length > 100) {
        console.log(`[CapacitorDB] Using background processing for sorting ${documents.length} documents`);
        return await dataProcessingManager.sortDocuments(documents, sortOptions);
      } else {
        // Process small datasets on main thread
        return [...documents].sort((a, b) => {
          for (const { field, direction = 'asc' } of sortOptions) {
            const aValue = this.getNestedValue(a, field);
            const bValue = this.getNestedValue(b, field);

            let comparison = 0;
            if (aValue < bValue) comparison = -1;
            else if (aValue > bValue) comparison = 1;

            if (comparison !== 0) {
              return direction === 'desc' ? -comparison : comparison;
            }
          }
          return 0;
        });
      }
    } catch (error) {
      console.error(`[CapacitorDB] Document sorting failed:`, error);
      return documents; // Return original documents on error
    }
  }

  /**
   * PERFORMANCE FIX: Calculate statistics using background processing
   */
  async calculateStatistics(documents, fields) {
    try {
      console.log(`[CapacitorDB] Calculating statistics for ${documents.length} documents`);
      return await dataProcessingManager.calculateStatistics(documents, fields);
    } catch (error) {
      console.error(`[CapacitorDB] Statistics calculation failed:`, error);
      return {}; // Return empty stats on error
    }
  }

  /**
   * Helper method to get nested values
   */
  getNestedValue(obj, path) {
    return path.split('.').reduce((current, key) => {
      return current && current[key] !== undefined ? current[key] : undefined;
    }, obj);
  }

  /**
   * Get all documents (alias for getAll)
   */
  async getAllData(options = {}) {
    return await this.getAll(options);
  }

  /**
   * Delete a document
   */
  async delete(id, user) {
    try {
      this.currentUser = user || this.currentUser;

      // Get the document first
      const doc = await this.pouchDb.get(id);

      // Delete the document
      const result = await this.pouchDb.remove(doc);

      // Add user activity log
      this.addLog({
        description: `Deleted data from ${this.name}`,
        details: { _id: id },
        user: this.currentUser,
        action: "delete",
        type: "delete",
      }).catch(logError => {
        console.warn(`[CapacitorDB] Failed to log delete operation:`, logError);
      });

      // Emit event
      this.emit('documentDeleted', { _id: id, _rev: result.rev });

      // Trigger sync
      this.triggerInstantSync('delete');

      return { id, rev: result.rev };
    } catch (error) {
      if (error.status === 404) {
        return { id, notFound: true };
      }
      console.error(`[CapacitorDB] Delete failed for ${this.dbKey}:`, error);
      throw error;
    }
  }

  /**
   * Delete a document (alias for delete)
   */
  async deleteDocument(data, user) {
    return await this.delete(data._id, user);
  }

  /**
   * PERFORMANCE FIX: Enhanced bulk save with better error handling and performance
   */
  async bulkSave(docs, user) {
    try {
      console.log(`[CapacitorDB] Bulk saving ${docs.length} documents to ${this.dbKey}`);

      if (!docs || docs.length === 0) {
        return [];
      }

      this.currentUser = user || this.currentUser;
      const timestamp = new Date().toISOString();
      const baseTimestamp = Date.now();

      // Prepare documents with required fields
      const preparedDocs = docs.map((doc, i) => {
        const isUpdate = !!doc._id;
        const docId = doc._id || (baseTimestamp + i).toString(36).toUpperCase();

        return {
          ...doc,
          _id: docId,
          updatedAt: timestamp,
          ...(isUpdate ? {} : {
            createdAt: timestamp,
            createdBy: this.currentUser?.name || this.currentUser,
            referenceNumber: doc.referenceNumber || this.generateReferenceNumber('', i + 1)
          }),
          branch: this.normalizeBranch(doc.branch) || this.branch
        };
      });

      // Save to PouchDB using native bulkDocs for optimal performance
      const bulkResult = await this.pouchDb.bulkDocs(preparedDocs);

      // Process results and handle errors
      const results = bulkResult.map((result, index) => {
        if (result.ok) {
          return {
            success: true,
            id: result.id,
            rev: result.rev,
            doc: preparedDocs[index]
          };
        } else {
          return {
            success: false,
            id: result.id,
            error: result.error || result.reason || 'Unknown error'
          };
        }
      });

      // Get successful documents for events and logging
      const successfulDocs = results
        .filter(r => r.success)
        .map(r => r.doc);

      if (successfulDocs.length > 0) {
        // PERFORMANCE FIX: Invalidate cache after successful bulk save
        this.invalidateCache();

        // Add user activity log (non-blocking)
        this.addLog({
          description: `Bulk added ${successfulDocs.length} documents to ${this.name}`,
          details: {
            count: successfulDocs.length,
            total: docs.length,
            documents: successfulDocs.map(d => d._id)
          },
          user: this.currentUser,
          action: "bulk_create",
          type: "bulk_add",
        }).catch(logError => {
          console.warn(`[CapacitorDB] Failed to log bulk save operation:`, logError);
        });

        // Emit event for successful documents
        this.emit('bulkAdded', successfulDocs);

        // Trigger sync (debounced)
        this.triggerInstantSync('bulk_save');
      }

      console.log(`[CapacitorDB] Bulk save completed: ${successfulDocs.length}/${docs.length} successful`);

      // Return results in the expected format
      return results.map(r => ({
        id: r.id,
        rev: r.rev,
        success: r.success,
        error: r.error
      }));

    } catch (error) {
      console.error(`[CapacitorDB] Bulk save failed for ${this.dbKey}:`, error);
      throw error;
    }
  }

  /**
   * Get all documents with options (PouchDB allDocs equivalent)
   */
  async allDocs(options = {}) {
    try {
      return await this.pouchDb.allDocs(options);
    } catch (error) {
      console.error(`[CapacitorDB] AllDocs failed for ${this.dbKey}:`, error);
      throw error;
    }
  }

  /**
   * Find documents using Mango queries
   */
  async find(selector, options = {}) {
    try {
      return await this.pouchDb.find({
        selector,
        ...options
      });
    } catch (error) {
      console.error(`[CapacitorDB] Find failed for ${this.dbKey}:`, error);
      throw error;
    }
  }

  /**
   * Trigger instant sync after CUD operations
   * CRUD OPERATION SYNC CONTROL: This method is now deprecated in favor of centralized sync control
   */
  triggerInstantSync(operationType) {
    // CRUD OPERATION SYNC CONTROL: Log the operation but don't trigger immediate sync
    // The sync will be handled by the SyncService with proper CRUD operation control
    console.log(`[CapacitorDB] CRUD operation completed (${operationType}) - sync will be handled by centralized sync control`);

    // Fallback: Keep original behavior for backward compatibility
    if (this.instantSyncTimeout) {
      clearTimeout(this.instantSyncTimeout);
    }

    this.instantSyncTimeout = setTimeout(async () => {
      try {
        if (this.syncInProgress) {
          console.log(`[CapacitorDB] Sync already in progress, skipping ${operationType} trigger`);
          return;
        }

        console.log(`[CapacitorDB] Triggering sync after ${operationType}`);
        await this.syncDatabases();
      } catch (error) {
        console.warn(`[CapacitorDB] Instant sync trigger failed for ${this.dbKey}:`, error);
      }
    }, 100); // 100ms debounce
  }

  /**
   * Sync with a specific target URL
   */
  async syncThis(targetUrl, options = {}) {
    try {
      console.log(`[CapacitorDB] Syncing ${this.dbKey} with ${targetUrl}`);

      this.syncInProgress = true;

      // Cancel any existing sync
      if (this.syncHandler) {
        this.syncHandler.cancel();
      }

      // Start new sync
      this.syncHandler = this.pouchDb.sync(targetUrl, {
        live: false,
        retry: true,
        batch_size: 25,
        timeout: 30000,
        attachments: true, // Include attachments in sync operations
        binary: true, // Support binary attachments
        ...options
      });

      const result = await this.syncHandler;
      this.syncInProgress = false;

      console.log(`[CapacitorDB] Sync completed successfully for ${this.dbKey}:`, {
        docs_read: result.pull?.docs_read || 0,
        docs_written: result.pull?.docs_written || 0,
        doc_write_failures: result.pull?.doc_write_failures || 0
      });

      return {
        success: true,
        docs_read: result.pull?.docs_read || 0,
        docs_written: result.pull?.docs_written || 0,
        doc_write_failures: result.pull?.doc_write_failures || 0
      };
    } catch (error) {
      this.syncInProgress = false;
      console.error(`[CapacitorDB] syncThis failed for ${this.dbKey}:`, error);
      throw error;
    }
  }

  /**
   * Trigger database synchronization
   */
  async syncDatabases(lanUrl) {
    try {
      // Check network connectivity
      let isConnected = true;

      try {
        if (Network && Capacitor.isPluginAvailable('Network')) {
          const status = await Network.getStatus();
          isConnected = status.connected;
        } else {
          isConnected = navigator.onLine;
        }
      } catch (error) {
        console.warn('[CapacitorDB] Failed to check network status:', error);
        isConnected = navigator.onLine; // Fallback
      }

      if (!isConnected) {
        console.log(`[CapacitorDB] Device is offline, skipping sync`);
        return { success: false, message: 'Device is offline' };
      }

      // Check if we have a remote URL to sync with
      const remoteUrl = lanUrl || (this.lan_details ?
        `http://${this.lan_details.username}:${this.lan_details.password}@${this.lan_details.host}:${this.lan_details.port}/${this.dbKey}` :
        null);

      if (!remoteUrl) {
        console.log(`[CapacitorDB] No remote URL available for sync`);
        return { success: false, message: 'No remote URL available' };
      }

      return await this.syncThis(remoteUrl);
    } catch (error) {
      console.error(`[CapacitorDB] Sync failed for ${this.dbKey}:`, error);
      throw error;
    }
  }

  /**
   * Get user options (alias for getAllData)
   */
  async fetchUserOptions() {
    return await this.getAllData();
  }

  /**
   * Close the database
   */
  async close() {
    try {
      // Cancel any active sync
      if (this.syncHandler) {
        this.syncHandler.cancel();
      }

      // Clear timeout
      if (this.instantSyncTimeout) {
        clearTimeout(this.instantSyncTimeout);
      }

      // Close PouchDB
      await this.pouchDb.close();

      // Remove all listeners
      this.removeAllListeners();

      console.log(`[CapacitorDB] Database ${this.dbKey} closed`);
    } catch (error) {
      console.error(`[CapacitorDB] Close error for ${this.dbKey}:`, error);
    }
  }

  /**
   * Get sync status
   */
  async getSyncStatus() {
    // For now, return a simple status object
    // This could be enhanced with actual sync tracking in the future
    return {
      isHealthy: true,
      lastSyncTimes: {},
      pendingOperations: new Set()
    };
  }

  /**
   * Force full sync
   */
  async forceFullSync() {
    try {
      return await this.syncDatabases();
    } catch (error) {
      console.error(`[CapacitorDB] Failed to force full sync:`, error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Get an attachment from a document
   */
  async getAttachment(id, name) {
    try {
      const attachment = await this.pouchDb.getAttachment(id, name);
      if (!attachment) return null;

      // Convert blob to base64
      return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onloadend = () => {
          const base64data = reader.result;
          resolve(base64data);
        };
        reader.onerror = reject;
        reader.readAsDataURL(attachment);
      });
    } catch (error) {
      console.error(`[CapacitorDB] Get attachment error for ${this.dbKey}:`, error);
      return null;
    }
  }

  /**
   * Put an attachment to a document
   */
  async putAttachment(id, name, rev, attachment, type) {
    try {
      const result = await this.pouchDb.putAttachment(id, name, rev, attachment, type);
      return result;
    } catch (error) {
      console.error(`[CapacitorDB] Put attachment failed for ${this.dbKey}:`, error);
      throw error;
    }
  }

  /**
   * Remove an attachment from a document
   */
  async removeAttachment(id, name, rev) {
    try {
      const result = await this.pouchDb.removeAttachment(id, name, rev);
      return result;
    } catch (error) {
      console.error(`[CapacitorDB] Remove attachment failed for ${this.dbKey}:`, error);
      throw error;
    }
  }

  /**
   * Create an index for faster queries
   */
  async createIndex(index) {
    try {
      const result = await this.pouchDb.createIndex(index);
      return result;
    } catch (error) {
      console.error(`[CapacitorDB] Create index failed for ${this.dbKey}:`, error);
      throw error;
    }
  }

  /**
   * Get database info
   */
  async info() {
    try {
      return await this.pouchDb.info();
    } catch (error) {
      console.error(`[CapacitorDB] Get info failed for ${this.dbKey}:`, error);
      throw error;
    }
  }

  /**
   * Compact the database
   */
  async compact() {
    try {
      const result = await this.pouchDb.compact();
      return result;
    } catch (error) {
      console.error(`[CapacitorDB] Compact failed for ${this.dbKey}:`, error);
      throw error;
    }
  }

  /**
   * Add a log entry for user activity tracking
   */
  async addLog(data) {
    try {
      // Create logs database if it doesn't exist
      if (!this.logsDb) {
        this.logsDb = new PouchDB(`${this.databasePrefix}logs`);
      }

      // Prepare log data
      const logEntry = {
        ...data,
        _id: Date.now().toString(36).toUpperCase(),
        createdAt: new Date().toISOString(),
        sourceDatabase: this.name,
        databasePrefix: this.databasePrefix,
        branch: this.branch
      };

      // Add device info based on environment capabilities
      logEntry.deviceInfo = await this.getEnvironmentAwareDeviceInfo();

      // Save log entry
      await this.logsDb.put(logEntry);
      console.log(`[CapacitorDB] Log added successfully to logs database`);
    } catch (err) {
      console.warn(`[CapacitorDB] Silent error in addLog for database ${this.name}:`, err);
    }
  }

  /**
   * Get logs from the logs database
   */
  async getLogs(options = {}) {
    try {
      // Create logs database if it doesn't exist
      if (!this.logsDb) {
        this.logsDb = new PouchDB(`${this.databasePrefix}logs`);
      }

      const result = await this.logsDb.allDocs({
        include_docs: true,
        ...options
      });

      return result.rows
        .filter(row => row.doc && !row.doc._id.startsWith('_'))
        .map(row => row.doc);
    } catch (error) {
      console.error(`[CapacitorDB] Get logs failed:`, error);
      return [];
    }
  }

  // Backward compatibility methods
  async bulkAdd(docs, user) {
    return await this.bulkSave(docs, user);
  }

  async addDocument(doc, user) {
    return await this.save(doc, user);
  }

  // Backward compatibility properties
  get db() {
    return this.pouchDb;
  }

  /**
   * Get device info based on environment capabilities
   */
  async getEnvironmentAwareDeviceInfo() {
    const env = this.environment;
    const opts = this.platformOptimizations;

    try {
      if (env.hasDevice && env.hasCapacitor) {
        // Try to get full device info (Capacitor native/web)
        try {
          const deviceInfo = await Device.getInfo();
          let networkStatus = { connected: navigator.onLine };

          if (env.hasNetwork) {
            try {
              networkStatus = await Network.getStatus();
            } catch (networkErr) {
              // Fallback to basic network info
              networkStatus = { connected: navigator.onLine };
            }
          }

          return {
            ...deviceInfo,
            network: networkStatus,
            timestamp: new Date().toISOString(),
            userAgent: navigator.userAgent,
            environment: env.description,
            deviceInfoLevel: opts.deviceInfoLevel
          };
        } catch (deviceErr) {
          // Fallback to Capacitor basic info
          return this.getCapacitorFallbackDeviceInfo();
        }
      } else if (env.hasCapacitor) {
        // Capacitor environment but no Device plugin
        return this.getCapacitorFallbackDeviceInfo();
      } else {
        // Pure web environment (WebDB equivalent)
        return this.getPureWebDeviceInfo();
      }
    } catch (error) {
      // Ultimate fallback
      return this.getPureWebDeviceInfo();
    }
  }

  /**
   * Get Capacitor fallback device info
   */
  getCapacitorFallbackDeviceInfo() {
    const platform = this.environment.platform;

    return {
      platform: platform,
      model: 'Unknown',
      operatingSystem: platform,
      osVersion: 'Unknown',
      manufacturer: 'Unknown',
      isVirtual: false,
      webViewVersion: 'Unknown',
      network: { connected: navigator.onLine },
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      environment: this.environment.description,
      deviceInfoLevel: this.platformOptimizations.deviceInfoLevel
    };
  }

  /**
   * Get pure web device info (WebDB equivalent)
   */
  getPureWebDeviceInfo() {
    return {
      network: { online: navigator.onLine },
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      platform: navigator.platform,
      language: navigator.language,
      cookieEnabled: navigator.cookieEnabled,
      onLine: navigator.onLine,
      environment: this.environment.description,
      deviceInfoLevel: this.platformOptimizations.deviceInfoLevel
    };
  }

  /**
   * Get fallback device information when Capacitor plugins are not available
   * (Updated to use environment-aware approach)
   */
  getFallbackDeviceInfo() {
    return this.getPureWebDeviceInfo();
  }

  /**
   * Destroy the database instance and cleanup all resources
   */
  destroy() {
    console.log(`[CapacitorDB] Destroying instance for ${this.dbKey}`);

    // MEMORY LEAK FIX: Clean up change listeners first
    if (this.changeListener) {
      try {
        // Remove specific handlers
        if (this._changeHandler) {
          this.changeListener.removeListener('change', this._changeHandler);
          this._changeHandler = null;
        }
        if (this._errorHandler) {
          this.changeListener.removeListener('error', this._errorHandler);
          this._errorHandler = null;
        }

        // Remove any remaining listeners and cancel
        this.changeListener.removeAllListeners();
        this.changeListener.cancel();
        this.changeListener = null;
      } catch (error) {
        console.warn(`[CapacitorDB] Error cleaning up change listener:`, error);
      }
    }

    // Cancel any active sync
    if (this.syncHandler) {
      this.syncHandler.cancel();
      this.syncHandler = null;
    }

    // Clear any pending timeouts
    if (this.syncTimeout) {
      clearTimeout(this.syncTimeout);
      this.syncTimeout = null;
    }

    // Close PouchDB instance
    if (this.pouchDb) {
      this.pouchDb.close().catch(() => {
        // Ignore close errors during cleanup
      });
      this.pouchDb = null;
    }

    // Remove all event listeners
    this.removeAllListeners();
  }
}