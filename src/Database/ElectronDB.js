import { EventEmitter } from "events";
import fileProcessingManager from "../utils/FileProcessingManager.js";
import { queryCache, documentCache } from "../utils/SmartCache.js";
import dataProcessingManager from "../utils/DataProcessingManager.js";

// Conditional import for Electron environment
let ipcRenderer;
try {
  if (typeof window !== 'undefined' && window.require) {
    const { ipcRenderer: electronIpcRenderer } = window.require('electron');
    ipcRenderer = electronIpcRenderer;
  }
} catch (error) {
  // Not in Electron environment
  console.warn('Not running in Electron environment');
}

/**
 * Electron Database Interface
 * Provides IPC-based database operations that communicate with Electron main process
 * Maintains API compatibility with existing SimplifiedDB interface
 */



class ElectronDB extends EventEmitter {
  constructor(name, databasePrefix, lan_details, branch) {
    super();

    // MEMORY LEAK FIX: Increase max listeners to prevent warnings
    this.setMaxListeners(20);

    if (!ipcRenderer) {
      throw new Error('ElectronDB can only be used in Electron renderer process');
    }

    if (!name?.trim()) {
      throw new Error('Invalid database name');
    }

    this.name = name.trim();
    this.databasePrefix = databasePrefix?.trim() || '';
    this.dbKey = `${this.databasePrefix}${this.name}`;
    this.currentUser = { name: "sys" };
    this.branch = this.normalizeBranch(branch);
    this.lan_details = lan_details;

    // Initialize database in main process
    this.initializeDatabase();

    // Listen for real-time updates from main process
    this.setupEventListeners();

    console.log(`[ElectronDB] Initialized ${this.dbKey} with Electron backend`);
  }

  /**
   * Normalize branch value
   */
  normalizeBranch(branch) {
    if (!branch || branch === 'null' || branch === 'undefined') {
      return 'none';
    }
    if (branch === 'all') {
      return 'all';
    }
    return branch;
  }

  /**
   * Initialize database in main process
   */
  async initializeDatabase() {
    try {
      const result = await ipcRenderer.invoke('db-initialize', {
        name: this.name,
        databasePrefix: this.databasePrefix,
        lan_details: this.lan_details,
        branch: this.branch
      });

      if (result.success) {
        // PERFORMANCE FIX: Create indexes after database initialization
        await this.createIndexes();

        this.emit('initialized');
        // console.log(`[ElectronDB] Database ${this.dbKey} initialized in main process`);
      } else {
        console.error(`[ElectronDB] Failed to initialize ${this.dbKey}:`, result.error);
        this.emit('error', new Error(result.error));
      }
    } catch (error) {
      console.error(`[ElectronDB] Initialization error for ${this.dbKey}:`, error);
      this.emit('error', error);
    }
  }

  /**
   * Setup event listeners for real-time updates
   */
  setupEventListeners() {
    console.log(`[ElectronDB] Setting up event listeners for dbKey: ${this.dbKey}`);

    // Store bound handlers for proper cleanup
    this._documentChangedHandler = (event, data) => {
      if (data.dbKey === this.dbKey) {
        console.log(`[ElectronDB] EMITTING LOCAL EVENTS for ${this.dbKey}:`, data.type);

        this.emit('dbChange', {
          type: data.type,
          doc: data.doc,
          docs: data.docs,
          id: data.id
        });

        // Emit specific events for backward compatibility
        switch (data.type) {
          case 'create':
            this.emit('documentCreated', data.doc);
            break;
          case 'update':
            this.emit('documentUpdated', data.doc);
            break;
          case 'delete':
            this.emit('documentDeleted', data.doc);
            break;
          case 'bulk_create':
            this.emit('bulkAdded', data.docs);
            break;
        }
      }
    };

    this._syncCompleteHandler = (event, data) => {
      if (data.dbKey === this.dbKey) {
        this.emit('syncComplete', data);
      }
    };

    this._syncErrorHandler = (event, data) => {
      if (data.dbKey === this.dbKey) {
        this.emit('syncError', data);
      }
    };

    this._syncProgressHandler = (event, data) => {
      if (data.dbKey === this.dbKey) {
        this.emit('syncProgress', data);
      }
    };

    // Add listeners with stored references
    ipcRenderer.on('db-document-changed', this._documentChangedHandler);
    ipcRenderer.on('sync-complete', this._syncCompleteHandler);
    ipcRenderer.on('sync-error', this._syncErrorHandler);
    ipcRenderer.on('sync-progress', this._syncProgressHandler);
  }

  /**
   * PERFORMANCE FIX: Optimized save with reduced IPC overhead (reverted from batching)
   */
  async save(data, user) {
    try {
      // Validate input data
      if (!data || typeof data !== 'object') {
        throw new Error('Invalid data provided to save method');
      }

      // Debug logging to help identify issues
      console.log(`[ElectronDB] Saving data to ${this.dbKey}:`, {
        hasData: !!data,
        dataKeys: data ? Object.keys(data) : [],
        user: user || this.currentUser
      });

      // PERFORMANCE FIX: Optimized data preprocessing
      let processedData = data;
      const hasFileObjects = this.hasFileObjects(data);

      if (hasFileObjects) {
        console.log(`[ElectronDB] File objects detected, preprocessing...`);
        processedData = await this.preprocessDataForIPC(data);
      } else {
        console.log(`[ElectronDB] No file objects detected, using optimized shallow copy`);
        // PERFORMANCE FIX: Use optimized shallow copy instead of expensive JSON operations
        processedData = this.createOptimizedCopy(data);
      }

      console.log(`[ElectronDB] Final processed data:`, {
        hasProcessedData: !!processedData,
        processedDataKeys: processedData ? Object.keys(processedData) : []
      });

      const result = await ipcRenderer.invoke('db-save', {
        dbKey: this.dbKey,
        data: processedData,
        user: user || this.currentUser
      });

      console.log(`[ElectronDB] IPC result:`, result);

      if (result && result.success) {
        // PERFORMANCE FIX: Invalidate cache after successful save
        this.invalidateCache();

        // PERFORMANCE FIX: Debounced sync trigger instead of instant sync
        this.debouncedSyncTrigger('save');
        return { id: result.id, doc: result.doc };
      } else {
        throw new Error(result ? result.error : 'Unknown error from IPC call');
      }
    } catch (error) {
      console.error(`[ElectronDB] Save failed for ${this.dbKey}:`, error);
      throw error;
    }
  }

  /**
   * PERFORMANCE FIX: Check if data contains File objects
   */
  hasFileObjects(data) {
    if (!data || typeof data !== 'object') return false;

    for (const value of Object.values(data)) {
      if (value && value.file && value.file instanceof File) {
        return true;
      }
      if (value && value.fileList) {
        return true;
      }
    }
    return false;
  }

  /**
   * PERFORMANCE FIX: Create optimized copy for IPC transfer
   * Avoids expensive JSON.parse(JSON.stringify()) for simple objects
   */
  createOptimizedCopy(data) {
    if (!data || typeof data !== 'object') {
      return data;
    }

    // For arrays, create shallow copy
    if (Array.isArray(data)) {
      return [...data];
    }

    // For objects, create optimized shallow copy
    const copy = {};
    for (const [key, value] of Object.entries(data)) {
      // Skip functions and undefined values
      if (typeof value === 'function' || value === undefined) {
        continue;
      }

      // Handle nested objects and arrays recursively (but limit depth)
      if (value && typeof value === 'object' && !this.isSpecialObject(value)) {
        copy[key] = this.createOptimizedCopy(value);
      } else {
        copy[key] = value;
      }
    }

    return copy;
  }

  /**
   * PERFORMANCE FIX: Check if object is a special type that shouldn't be deep copied
   */
  isSpecialObject(obj) {
    return (
      obj instanceof Date ||
      obj instanceof RegExp ||
      obj instanceof File ||
      obj instanceof Blob ||
      obj instanceof ArrayBuffer ||
      obj instanceof DataView ||
      (typeof obj.constructor === 'function' && obj.constructor.name !== 'Object')
    );
  }

  /**
   * PERFORMANCE FIX: Debounced sync trigger to reduce sync frequency
   */
  debouncedSyncTrigger(operation) {
    if (this.syncTriggerTimeout) {
      clearTimeout(this.syncTriggerTimeout);
    }

    this.syncTriggerTimeout = setTimeout(() => {
      this.triggerInstantSync(operation);
    }, 100); // 100ms debounce
  }



  /**
   * PERFORMANCE FIX: Background file processing using web workers
   * Prevents UI blocking during file operations
   */
  async preprocessDataForIPC(data) {
    const processedData = this.createOptimizedCopy(data);
    const attachments = {};
    const filesToProcess = [];

    // Collect all files that need processing
    for (const [key, value] of Object.entries(processedData)) {
      if (value && value.file && value.file instanceof File) {
        filesToProcess.push({ key, file: value.file });
        // Remove the original file object from the data
        delete processedData[key];
      } else if (value && value.fileList) {
        // Handle fileList if present
        delete processedData[key];
      }
    }

    // Process files in background if any were found
    if (filesToProcess.length > 0) {
      console.log(`[ElectronDB] Processing ${filesToProcess.length} files in background`);

      try {
        // Process files using background worker
        const fileResults = await fileProcessingManager.processFilesBatch(
          filesToProcess.map(item => item.file),
          {
            maxSize: 10 * 1024 * 1024, // 10MB limit
            concurrency: 3
          }
        );

        // Map results back to attachments
        fileResults.forEach((result, index) => {
          if (result.success) {
            const { key } = filesToProcess[index];
            attachments[key] = result.result;
          } else {
            console.error(`[ElectronDB] Failed to process file for field ${filesToProcess[index].key}:`, result.error);
          }
        });

        console.log(`[ElectronDB] Successfully processed ${fileResults.filter(r => r.success).length}/${filesToProcess.length} files`);
      } catch (error) {
        console.error(`[ElectronDB] Background file processing failed:`, error);
        // Continue without attachments rather than failing completely
      }
    }

    // Add attachments to the document if any were processed successfully
    if (Object.keys(attachments).length > 0) {
      processedData._attachments = attachments;
    }

    return processedData;
  }

  /**
   * PERFORMANCE FIX: Process individual file for IPC with optimized chunking
   */
  async processFileForIPC(key, file) {
    try {
      // PERFORMANCE FIX: Use streaming for large files
      if (file.size > 1024 * 1024) { // 1MB threshold
        console.log(`[ElectronDB] Processing large file ${file.name} (${file.size} bytes) for field ${key} in chunks`);
      }

      const arrayBuffer = await file.arrayBuffer();

      // PERFORMANCE FIX: Use Uint8Array directly instead of converting to regular array
      // This reduces memory usage and serialization time
      const uint8Array = new Uint8Array(arrayBuffer);

      return {
        content_type: file.type,
        type: file.type,
        name: file.name,
        size: file.size,
        data: Array.from(uint8Array) // Still need regular array for JSON serialization
      };
    } catch (error) {
      console.error(`[ElectronDB] Error processing file ${file.name}:`, error);
      return null;
    }
  }

  /**
   * Save a document (alias for save)
   */
  async saveDocument(data, user) {
    return await this.save(data, user);
  }

  /**
   * Get a document by ID
   */
  async get(id) {
    try {
      const result = await ipcRenderer.invoke('db-get', {
        dbKey: this.dbKey,
        id
      });

      if (result.success) {
        return result.doc;
      } else {
        return null; // Document not found
      }
    } catch (error) {
      console.error(`[ElectronDB] Get failed for ${this.dbKey}:`, error);
      return null;
    }
  }

  /**
   * Get a document by ID (alias for get)
   */
  async getDocument(id) {
    return await this.get(id);
  }

  /**
   * PERFORMANCE FIX: Optimized getAll with smart caching and pagination
   */
  async getAll(options = {}) {
    try {
      // PERFORMANCE FIX: Smart pagination for large datasets
      const {
        limit,
        skip,
        page,
        pageSize = 50, // Default page size for better performance
        ...otherOptions
      } = options;

      // Calculate pagination parameters
      let finalLimit = limit;
      let finalSkip = skip;

      if (page && pageSize && !limit && !skip) {
        finalLimit = pageSize;
        finalSkip = (page - 1) * pageSize;
      }

      // PERFORMANCE FIX: Smart cache key generation with normalized dbKey
      const normalizedDbKey = this.dbKey.replace(/^[^_]+_[^_]+_/, ''); // Remove prefix for consistent caching
      const cacheKey = `${normalizedDbKey}:getAll:${JSON.stringify({
        ...otherOptions,
        limit: finalLimit,
        skip: finalSkip
      })}`;

      // Check smart cache first
      const cachedData = queryCache.get(cacheKey);
      if (cachedData) {
        console.log(`[ElectronDB] Smart cache hit for ${this.dbKey} (${cachedData.length} docs)`);
        return cachedData;
      }

      // PERFORMANCE FIX: Add performance hints for large datasets
      const optimizedOptions = {
        ...otherOptions,
        // Pagination parameters
        ...(finalLimit && { limit: finalLimit }),
        ...(finalSkip && { skip: finalSkip }),
        // Performance hints
        conflicts: false, // Skip conflict info unless explicitly requested
        attachments: false, // Skip attachments unless explicitly requested
        include_docs: true, // Ensure we get document content
        descending: otherOptions.descending !== undefined ? otherOptions.descending : true, // Default to newest first
      };

      console.log(`[ElectronDB] Fetching data for ${this.dbKey}:`, {
        limit: finalLimit,
        skip: finalSkip,
        page,
        pageSize
      });

      const result = await ipcRenderer.invoke('db-get-all', {
        dbKey: this.dbKey,
        options: optimizedOptions
      });

      if (result.success) {
        console.log(`[ElectronDB] Retrieved ${result.docs.length} documents for ${this.dbKey}`);

        // PERFORMANCE FIX: Cache using smart cache with dynamic TTL
        const ttl = this.calculateCacheTTL(result.docs.length, finalLimit);
        queryCache.set(cacheKey, result.docs, ttl);

        return result.docs;
      } else {
        console.error(`[ElectronDB] GetAll failed for ${this.dbKey}:`, result.error);
        return [];
      }
    } catch (error) {
      console.error(`[ElectronDB] GetAll failed for ${this.dbKey}:`, error);
      return [];
    }
  }

  /**
   * PERFORMANCE FIX: Get paginated results with metadata
   */
  async getPaginated(page = 1, pageSize = 50, options = {}) {
    try {
      const skip = (page - 1) * pageSize;

      // Get the requested page
      const docs = await this.getAll({
        ...options,
        limit: pageSize,
        skip: skip
      });

      // Get total count (cached for 30 seconds)
      const totalCount = await this.getCount(options);

      return {
        docs,
        pagination: {
          page,
          pageSize,
          totalCount,
          totalPages: Math.ceil(totalCount / pageSize),
          hasNextPage: page * pageSize < totalCount,
          hasPrevPage: page > 1
        }
      };
    } catch (error) {
      console.error(`[ElectronDB] GetPaginated failed for ${this.dbKey}:`, error);
      return {
        docs: [],
        pagination: {
          page,
          pageSize,
          totalCount: 0,
          totalPages: 0,
          hasNextPage: false,
          hasPrevPage: false
        }
      };
    }
  }

  /**
   * PERFORMANCE FIX: Get document count with smart caching
   */
  async getCount(options = {}) {
    try {
      const normalizedDbKey = this.dbKey.replace(/^[^_]+_[^_]+_/, '');
      const cacheKey = `${normalizedDbKey}:count:${JSON.stringify(options)}`;

      // Check smart cache first
      const cachedCount = queryCache.get(cacheKey);
      if (cachedCount !== null) {
        return cachedCount;
      }

      // Get count from database
      const result = await ipcRenderer.invoke('db-get-count', {
        dbKey: this.dbKey,
        options
      });

      if (result.success) {
        // Cache with longer TTL for counts (they change less frequently)
        queryCache.set(cacheKey, result.count, 60000); // 1 minute TTL
        return result.count;
      } else {
        console.warn(`[ElectronDB] GetCount failed for ${this.dbKey}:`, result.error);
        return 0;
      }
    } catch (error) {
      console.error(`[ElectronDB] GetCount error for ${this.dbKey}:`, error);
      return 0;
    }
  }

  /**
   * PERFORMANCE FIX: Calculate dynamic cache TTL based on data characteristics
   */
  calculateCacheTTL(resultCount, limit) {
    // Base TTL
    let ttl = 300000; // 5 minutes default

    // Shorter TTL for paginated results (they change more frequently)
    if (limit) {
      ttl = 120000; // 2 minutes
    }

    // Shorter TTL for small result sets (likely to change)
    if (resultCount < 10) {
      ttl = 60000; // 1 minute
    }

    // Longer TTL for large result sets (less likely to change completely)
    if (resultCount > 100) {
      ttl = 600000; // 10 minutes
    }

    return ttl;
  }

  /**
   * PERFORMANCE FIX: Invalidate cache when data changes (with normalized keys)
   */
  invalidateCache(pattern = null) {
    const normalizedDbKey = this.dbKey.replace(/^[^_]+_[^_]+_/, '');
    const keysToDelete = [];

    if (pattern) {
      // Invalidate specific cache entries matching pattern
      for (const key of queryCache.cache.keys()) {
        if ((key.startsWith(`${this.dbKey}:`) || key.startsWith(`${normalizedDbKey}:`)) && key.includes(pattern)) {
          keysToDelete.push(key);
        }
      }
      console.log(`[ElectronDB] Invalidated ${keysToDelete.length} cache entries matching pattern: ${pattern}`);
    } else {
      // Invalidate all cache entries for this database (both original and normalized keys)
      for (const key of queryCache.cache.keys()) {
        if (key.startsWith(`${this.dbKey}:`) || key.startsWith(`${normalizedDbKey}:`)) {
          keysToDelete.push(key);
        }
      }
      console.log(`[ElectronDB] Invalidated ${keysToDelete.length} cache entries for ${this.dbKey}`);
    }

    // Remove duplicates and delete
    const uniqueKeys = [...new Set(keysToDelete)];
    uniqueKeys.forEach(key => queryCache.delete(key));
  }

  /**
   * PERFORMANCE FIX: Filter documents using background processing for large datasets
   */
  async filterDocuments(documents, filters) {
    try {
      // Use background processing for large datasets
      if (documents.length > 100) {
        console.log(`[ElectronDB] Using background processing for filtering ${documents.length} documents`);
        return await dataProcessingManager.filterDocuments(documents, filters);
      } else {
        // Process small datasets on main thread
        return documents.filter(doc => {
          return Object.entries(filters).every(([field, criteria]) => {
            const value = this.getNestedValue(doc, field);
            return value === criteria;
          });
        });
      }
    } catch (error) {
      console.error(`[ElectronDB] Document filtering failed:`, error);
      return documents; // Return original documents on error
    }
  }

  /**
   * PERFORMANCE FIX: Sort documents using background processing for large datasets
   */
  async sortDocuments(documents, sortOptions) {
    try {
      // Use background processing for large datasets
      if (documents.length > 100) {
        console.log(`[ElectronDB] Using background processing for sorting ${documents.length} documents`);
        return await dataProcessingManager.sortDocuments(documents, sortOptions);
      } else {
        // Process small datasets on main thread
        return [...documents].sort((a, b) => {
          for (const { field, direction = 'asc' } of sortOptions) {
            const aValue = this.getNestedValue(a, field);
            const bValue = this.getNestedValue(b, field);

            let comparison = 0;
            if (aValue < bValue) comparison = -1;
            else if (aValue > bValue) comparison = 1;

            if (comparison !== 0) {
              return direction === 'desc' ? -comparison : comparison;
            }
          }
          return 0;
        });
      }
    } catch (error) {
      console.error(`[ElectronDB] Document sorting failed:`, error);
      return documents; // Return original documents on error
    }
  }

  /**
   * PERFORMANCE FIX: Calculate statistics using background processing
   */
  async calculateStatistics(documents, fields) {
    try {
      console.log(`[ElectronDB] Calculating statistics for ${documents.length} documents`);
      return await dataProcessingManager.calculateStatistics(documents, fields);
    } catch (error) {
      console.error(`[ElectronDB] Statistics calculation failed:`, error);
      return {}; // Return empty stats on error
    }
  }

  /**
   * Helper method to get nested values
   */
  getNestedValue(obj, path) {
    return path.split('.').reduce((current, key) => {
      return current && current[key] !== undefined ? current[key] : undefined;
    }, obj);
  }

  /**
   * Get all documents (alias for getAll)
   */
  async getAllData(options = {}) {
    return await this.getAll(options);
  }

  /**
   * Delete a document
   */
  async delete(id, user) {
    try {
      const result = await ipcRenderer.invoke('db-delete', {
        dbKey: this.dbKey,
        id,
        user: user || this.currentUser
      });

      if (result.success) {
        // Trigger instant sync after successful delete
        this.triggerInstantSync('delete');
        return { id: result.id, rev: result.rev };
      } else {
        throw new Error(result.error);
      }
    } catch (error) {
      console.error(`[ElectronDB] Delete failed for ${this.dbKey}:`, error);
      throw error;
    }
  }

  /**
   * Delete a document (alias for delete)
   */
  async deleteDocument(data, user) {
    return await this.delete(data._id, user);
  }

  /**
   * PERFORMANCE FIX: Enhanced bulk save with better logging and error handling
   */
  async bulkSave(docs, user) {
    try {
      console.log(`[ElectronDB] Bulk saving ${docs.length} documents to ${this.dbKey}`);

      if (!docs || docs.length === 0) {
        return [];
      }

      const result = await ipcRenderer.invoke('db-bulk-save', {
        dbKey: this.dbKey,
        docs,
        user: user || this.currentUser
      });

      if (result.success) {
        const successCount = result.results.filter(r => r.success).length;
        console.log(`[ElectronDB] Bulk save completed: ${successCount}/${docs.length} successful`);

        // PERFORMANCE FIX: Invalidate cache after successful bulk save
        if (successCount > 0) {
          this.invalidateCache();
        }

        // Trigger debounced sync after successful bulk save
        this.debouncedSyncTrigger('bulk_save');
        return result.results;
      } else {
        throw new Error(result.error);
      }
    } catch (error) {
      console.error(`[ElectronDB] Bulk save failed for ${this.dbKey}:`, error);
      throw error;
    }
  }

  /**
   * Get all documents with options (PouchDB allDocs equivalent)
   * This is the method that useSimpleTableData hook expects
   */
  async allDocs(options = {}) {
    try {
      const result = await ipcRenderer.invoke('db-all-docs', {
        dbKey: this.dbKey,
        options
      });

      if (result.success) {
        return result.result;
      } else {
        throw new Error(result.error);
      }
    } catch (error) {
      console.error(`[ElectronDB] AllDocs failed for ${this.dbKey}:`, error);
      throw error;
    }
  }

  /**
   * Find documents using Mango queries
   */
  async find(selector, options = {}) {
    try {
      const result = await ipcRenderer.invoke('db-find', {
        dbKey: this.dbKey,
        selector,
        options
      });

      if (result.success) {
        return { docs: result.docs };
      } else {
        throw new Error(result.error);
      }
    } catch (error) {
      console.error(`[ElectronDB] Find failed for ${this.dbKey}:`, error);
      throw error;
    }
  }

  /**
   * Trigger instant sync after CUD operations
   * CRUD OPERATION SYNC CONTROL: This method is now deprecated in favor of SyncService CRUD control
   */
  triggerInstantSync(operationType) {
    // CRUD OPERATION SYNC CONTROL: Use new sync service if available
    // The sync service will handle pausing and resuming sync automatically
    console.log(`[ElectronDB] CRUD operation completed (${operationType}) - sync will be handled by SyncService`);

    // Fallback: Keep original behavior if sync service is not available
    if (this.instantSyncTimeout) {
      clearTimeout(this.instantSyncTimeout);
    }

    this.instantSyncTimeout = setTimeout(async () => {
      try {
        await ipcRenderer.invoke('sync-trigger-instant', {
          dbKey: this.dbKey,
          operationType
        });
      } catch (error) {
        console.warn(`[ElectronDB] Instant sync trigger failed for ${this.dbKey}:`, error);
      }
    }, 100); // 100ms debounce
  }

  /**
   * Sync with a specific target URL (compatibility method for existing code)
   * @param {string} targetUrl - Target database URL to sync with
   * @param {Object} options - Sync options (filters, etc.)
   * @returns {Promise} - Promise that resolves when sync is complete
   */
  async syncThis(targetUrl, options = {}) {
    try {
      console.log(`[ElectronDB] Syncing ${this.dbKey} with ${targetUrl}`);

      // Handle filter functions that can't be serialized over IPC
      const serializableOptions = { ...options };

      // If there's a filter function, we'll let the main process handle filtering
      // based on the database name and prefix (organization-based filtering)
      if (options.filter && typeof options.filter === 'function') {
        console.log(`[ElectronDB] Filter function detected - will be handled by main process based on organization`);
        delete serializableOptions.filter;

        // The main process will automatically apply organization-based filtering
        // based on the database name and prefix
      }

      // Remove any other non-serializable properties
      Object.keys(serializableOptions).forEach(key => {
        if (typeof serializableOptions[key] === 'function') {
          console.warn(`[ElectronDB] Removing non-serializable property: ${key}`);
          delete serializableOptions[key];
        }
      });

      // Use the DatabaseHandler's syncThis method via IPC
      const result = await ipcRenderer.invoke('db-sync-this', {
        dbKey: this.dbKey,
        targetUrl,
        options: serializableOptions
      });

      if (result.success) {
        console.log(`[ElectronDB] Sync completed successfully for ${this.dbKey}:`, {
          docs_read: result.docs_read || 0,
          docs_written: result.docs_written || 0,
          doc_write_failures: result.doc_write_failures || 0
        });
        return {
          success: true,
          docs_read: result.docs_read || 0,
          docs_written: result.docs_written || 0,
          doc_write_failures: result.doc_write_failures || 0
        };
      } else {
        throw new Error(result.error || 'Sync failed');
      }
    } catch (error) {
      console.error(`[ElectronDB] syncThis failed for ${this.dbKey}:`, error);
      throw error;
    }
  }

  /**
   * Trigger database synchronization
   * @param {string} lanUrl - Optional LAN URL for specific sync target
   * @returns {Promise} - Promise that resolves when sync is complete
   */
  async syncDatabases(lanUrl) {
    try {
      if (lanUrl) {
        // For LAN sync with specific URL, we need to update LAN details first
        console.log(`[ElectronDB] Triggering LAN sync for ${this.dbKey} to ${lanUrl}`);

        // Extract LAN details from URL
        const urlMatch = lanUrl.match(/^http:\/\/([^:]+):([^@]+)@([^:]+):(\d+)\//);
        if (urlMatch) {
          const [, username, password, host, port] = urlMatch;
          const lan_details = { username, password, host, port };

          // Update LAN details for this database
          const updateResult = await ipcRenderer.invoke('db-update-lan-details', {
            dbKey: this.dbKey,
            lan_details
          });

          if (updateResult.success) {
            console.log(`[ElectronDB] Updated LAN details for ${this.dbKey}`);

            // The sync service will automatically pick up the new LAN details
            // and start syncing in the background. For now, we'll return success
            // since the LAN details have been updated successfully.
            console.log(`[ElectronDB] LAN sync initiated for ${this.dbKey}`);
            return { success: true, message: 'LAN sync initiated' };
          } else {
            throw new Error(updateResult.error || 'Failed to update LAN details');
          }
        } else {
          throw new Error('Invalid LAN URL format');
        }
      } else {
        // General sync - trigger instant sync for this database
        console.log(`[ElectronDB] Triggering instant sync for ${this.dbKey}`);

        try {
          const result = await ipcRenderer.invoke('sync-trigger-instant', {
            dbKey: this.dbKey,
            operationType: 'general'
          });

          if (result.success) {
            console.log(`[ElectronDB] Instant sync triggered successfully for ${this.dbKey}`);
            return { success: true, message: 'Instant sync triggered' };
          } else {
            throw new Error(result.error || 'Failed to trigger instant sync');
          }
        } catch (syncError) {
          console.error(`[ElectronDB] Failed to trigger instant sync for ${this.dbKey}:`, syncError);
          throw syncError;
        }
      }
    } catch (error) {
      console.error(`[ElectronDB] Sync failed for ${this.dbKey}:`, error);
      throw error;
    }
  }

  /**
   * Get user options (alias for getAllData)
   */
  async fetchUserOptions() {
    return await this.getAllData();
  }

  /**
   * Close the database and cleanup event listeners
   */
  async close() {
    try {
      // Remove IPC event listeners first
      this.removeEventListeners();

      const result = await ipcRenderer.invoke('db-close', {
        dbKey: this.dbKey
      });

      if (result.success) {
        this.removeAllListeners();
        console.log(`[ElectronDB] Database ${this.dbKey} closed`);
      } else {
        console.error(`[ElectronDB] Failed to close ${this.dbKey}:`, result.error);
      }
    } catch (error) {
      console.error(`[ElectronDB] Close error for ${this.dbKey}:`, error);
    }
  }

  /**
   * Remove IPC event listeners to prevent memory leaks
   */
  removeEventListeners() {
    if (this._documentChangedHandler) {
      ipcRenderer.off('db-document-changed', this._documentChangedHandler);
      this._documentChangedHandler = null;
    }
    if (this._syncCompleteHandler) {
      ipcRenderer.off('sync-complete', this._syncCompleteHandler);
      this._syncCompleteHandler = null;
    }
    if (this._syncErrorHandler) {
      ipcRenderer.off('sync-error', this._syncErrorHandler);
      this._syncErrorHandler = null;
    }
    if (this._syncProgressHandler) {
      ipcRenderer.off('sync-progress', this._syncProgressHandler);
      this._syncProgressHandler = null;
    }
    console.log(`[ElectronDB] Removed IPC event listeners for ${this.dbKey}`);
  }

  /**
   * Destroy the database instance and cleanup all resources
   */
  destroy() {
    console.log(`[ElectronDB] Destroying instance for ${this.dbKey}`);

    // Clear any pending timeouts
    if (this.syncTriggerTimeout) {
      clearTimeout(this.syncTriggerTimeout);
      this.syncTriggerTimeout = null;
    }
    if (this.instantSyncTimeout) {
      clearTimeout(this.instantSyncTimeout);
      this.instantSyncTimeout = null;
    }

    // Clear cache
    if (this.getAllCache) {
      this.getAllCache.clear();
      this.getAllCache = null;
    }

    // Remove event listeners
    this.removeEventListeners();
    this.removeAllListeners();
  }

  /**
   * Get sync status
   */
  async getSyncStatus() {
    try {
      const result = await ipcRenderer.invoke('sync-status');
      return result[this.dbKey] || { isHealthy: false, lastSyncTimes: {}, pendingOperations: new Set() };
    } catch (error) {
      console.error(`[ElectronDB] Failed to get sync status:`, error);
      return { isHealthy: false, lastSyncTimes: {}, pendingOperations: new Set() };
    }
  }



  /**
   * Get an attachment from a document
   * @param {string} id - Document ID
   * @param {string} name - Attachment name
   * @returns {Promise<string>} - Base64-encoded attachment
   */
  async getAttachment(id, name) {
    try {
      // Validate input parameters before making IPC call
      if (!id || typeof id !== 'string' || !name || typeof name !== 'string') {
        console.log(`[ElectronDB] Invalid parameters for getAttachment: id='${id}', name='${name}' in ${this.dbKey}`);
        return null;
      }

      const result = await ipcRenderer.invoke('db-get-attachment', {
        dbKey: this.dbKey,
        id,
        name
      });

      if (result.success) {
        return result.attachment;
      } else {
        console.warn(`[ElectronDB] Get attachment failed:`, result.error);
        return null;
      }
    } catch (error) {
      console.error(`[ElectronDB] Get attachment error for ${this.dbKey}:`, error);
      return null;
    }
  }

  /**
   * Put an attachment to a document
   * @param {string} id - Document ID
   * @param {string} name - Attachment name
   * @param {string} rev - Document revision
   * @param {Blob|Buffer} attachment - Attachment data
   * @param {string} type - Content type
   * @returns {Promise<Object>} - Result object
   */
  async putAttachment(id, name, rev, attachment, type) {
    try {
      const result = await ipcRenderer.invoke('db-put-attachment', {
        dbKey: this.dbKey,
        id,
        name,
        rev,
        attachment,
        type
      });

      if (result.success) {
        return result.result;
      } else {
        throw new Error(result.error);
      }
    } catch (error) {
      console.error(`[ElectronDB] Put attachment failed for ${this.dbKey}:`, error);
      throw error;
    }
  }

  /**
   * Remove an attachment from a document
   * @param {string} id - Document ID
   * @param {string} name - Attachment name
   * @param {string} rev - Document revision
   * @returns {Promise<Object>} - Result object
   */
  async removeAttachment(id, name, rev) {
    try {
      const result = await ipcRenderer.invoke('db-remove-attachment', {
        dbKey: this.dbKey,
        id,
        name,
        rev
      });

      if (result.success) {
        return result.result;
      } else {
        throw new Error(result.error);
      }
    } catch (error) {
      console.error(`[ElectronDB] Remove attachment failed for ${this.dbKey}:`, error);
      throw error;
    }
  }

  /**
   * Authenticate with the database
   * @param {string} username - Username
   * @param {string} password - Password
   * @returns {Promise<Object>} - Authentication result
   */
  async authenticate(username, password) {
    try {
      const result = await ipcRenderer.invoke('db-authenticate', {
        dbKey: this.dbKey,
        username,
        password
      });

      if (result.success) {
        return result.result;
      } else {
        throw new Error(result.error);
      }
    } catch (error) {
      console.error(`[ElectronDB] Authentication failed for ${this.dbKey}:`, error);
      throw error;
    }
  }

  /**
   * Dump database contents
   * @returns {Promise<Object>} - Database dump
   */
  async dumpDatabase() {
    try {
      const result = await ipcRenderer.invoke('db-dump', {
        dbKey: this.dbKey
      });

      if (result.success) {
        return result.dump;
      } else {
        throw new Error(result.error);
      }
    } catch (error) {
      console.error(`[ElectronDB] Database dump failed for ${this.dbKey}:`, error);
      throw error;
    }
  }

  /**
   * Create an index for faster queries
   * @param {Object} index - Index definition
   * @returns {Promise<Object>} - Index creation result
   */
  async createIndex(index) {
    try {
      const result = await ipcRenderer.invoke('db-create-index', {
        dbKey: this.dbKey,
        index
      });

      if (result.success) {
        return result.result;
      } else {
        throw new Error(result.error);
      }
    } catch (error) {
      console.error(`[ElectronDB] Create index failed for ${this.dbKey}:`, error);
      throw error;
    }
  }

  /**
   * Create comprehensive database indexes for optimal performance
   */
  async createIndexes() {
    try {
      console.log(`[ElectronDB] Creating comprehensive indexes for ${this.dbKey}...`);

      // PERFORMANCE FIX: Comprehensive indexing strategy
      const indexes = [
        // Basic timestamp indexes for sorting and filtering
        { fields: ['createdAt'] },
        { fields: ['updatedAt'] },

        // Type and branch indexes for filtering
        { fields: ['type'] },
        { fields: ['branch'] },

        // Composite indexes for common query patterns
        { fields: ['type', 'createdAt'] },
        { fields: ['branch', 'createdAt'] },
        { fields: ['type', 'branch'] },
        { fields: ['type', 'branch', 'createdAt'] },

        // Status and state indexes
        { fields: ['status'] },
        { fields: ['state'] },
        { fields: ['active'] },

        // User-related indexes
        { fields: ['createdBy'] },
        { fields: ['updatedBy'] },
        { fields: ['assignedTo'] },

        // Business-specific indexes
        { fields: ['customerId'] },
        { fields: ['customerName'] },
        { fields: ['invoiceNumber'] },
        { fields: ['referenceNumber'] },
        { fields: ['jobId'] },
        { fields: ['productId'] },
        { fields: ['categoryId'] },

        // Date range indexes for reports
        { fields: ['date'] },
        { fields: ['dueDate'] },
        { fields: ['completedDate'] },

        // Search optimization indexes
        { fields: ['name'] },
        { fields: ['title'] },
        { fields: ['description'] },
        { fields: ['email'] },
        { fields: ['phone'] },

        // Composite indexes for complex queries
        { fields: ['status', 'createdAt'] },
        { fields: ['customerId', 'createdAt'] },
        { fields: ['type', 'status', 'createdAt'] },
      ];

      // Create indexes in batches to prevent overwhelming the database
      const batchSize = 5;
      let successCount = 0;

      for (let i = 0; i < indexes.length; i += batchSize) {
        const batch = indexes.slice(i, i + batchSize);

        const results = await Promise.allSettled(batch.map(async (indexDef) => {
          try {
            await this.createIndex({ index: indexDef });
            console.log(`[ElectronDB] Created index: ${indexDef.fields.join(', ')}`);
            return true;
          } catch (error) {
            // Ignore "index already exists" errors
            if (!error.message.includes('already exists')) {
              console.warn(`[ElectronDB] Failed to create index ${indexDef.fields.join(', ')}:`, error.message);
            }
            return false;
          }
        }));

        successCount += results.filter(r => r.status === 'fulfilled' && r.value).length;

        // Small delay between batches to prevent overwhelming
        if (i + batchSize < indexes.length) {
          await new Promise(resolve => setTimeout(resolve, 10));
        }
      }

      console.log(`[ElectronDB] Completed creating indexes for ${this.dbKey}: ${successCount}/${indexes.length} successful`);
    } catch (error) {
      console.error(`[ElectronDB] Error creating indexes for ${this.dbKey}:`, error);
    }
  }

  /**
   * Get database info
   * @returns {Promise<Object>} - Database information
   */
  async info() {
    try {
      const result = await ipcRenderer.invoke('db-info', {
        dbKey: this.dbKey
      });

      if (result.success) {
        return result.info;
      } else {
        throw new Error(result.error);
      }
    } catch (error) {
      console.error(`[ElectronDB] Get info failed for ${this.dbKey}:`, error);
      throw error;
    }
  }

  /**
   * Compact the database
   * @returns {Promise<Object>} - Compaction result
   */
  async compact() {
    try {
      const result = await ipcRenderer.invoke('db-compact', {
        dbKey: this.dbKey
      });

      if (result.success) {
        return result.result;
      } else {
        throw new Error(result.error);
      }
    } catch (error) {
      console.error(`[ElectronDB] Compact failed for ${this.dbKey}:`, error);
      throw error;
    }
  }

  /**
   * Get sync statistics
   * @returns {Promise<Object>} - Sync statistics
   */
  async getSyncStats() {
    try {
      const result = await ipcRenderer.invoke('db-sync-stats', {
        dbKey: this.dbKey
      });

      if (result.success) {
        return result.stats;
      } else {
        return null;
      }
    } catch (error) {
      console.error(`[ElectronDB] Get sync stats failed for ${this.dbKey}:`, error);
      return null;
    }
  }

  /**
   * Force a full sync
   * @returns {Promise<Object>} - Sync result
   */
  async forceFullSync() {
    try {
      const result = await ipcRenderer.invoke('db-force-sync', {
        dbKey: this.dbKey
      });

      if (result.success) {
        return result.result;
      } else {
        throw new Error(result.error);
      }
    } catch (error) {
      console.error(`[ElectronDB] Force sync failed for ${this.dbKey}:`, error);
      throw error;
    }
  }

  /**
   * Add a log entry for user activity tracking
   * Maintains compatibility with previous database system
   * @param {Object} data - Log data
   */
  async addLog(data) {
    try {
      // Prepare log data with database context to track source
      const logEntry = {
        ...data,
        _id: Date.now().toString(36).toUpperCase(),
        createdAt: new Date().toISOString(),
        sourceDatabase: this.name, // Track which database this log came from
        databasePrefix: this.databasePrefix, // Track the prefix for debugging
        branch: this.branch, // Track the branch context
      };

      // Try to add device info if available
      try {
        // Add basic device info
        logEntry.deviceInfo = {
          network: { online: navigator.onLine },
          timestamp: new Date().toISOString(),
          userAgent: navigator.userAgent
        };
      } catch (deviceErr) {
        // Continue without device info if it fails
        console.warn('[ElectronDB] Failed to add device info to log:', deviceErr);
      }

      // Send log to main process for handling
      const result = await ipcRenderer.invoke('db-add-log', {
        databasePrefix: this.databasePrefix,
        logData: logEntry
      });

      if (result.success) {
        console.log(`[ElectronDB] Log added successfully to logs database`);
      } else {
        console.warn(`[ElectronDB] Failed to add log:`, result.error);
      }

    } catch (err) {
      // Error adding log handled silently to prevent disruption
      console.warn(`[ElectronDB] Silent error in addLog for database ${this.name}:`, err);
    }
  }

  // Backward compatibility methods
  async bulkAdd(docs, user) {
    return await this.bulkSave(docs, user);
  }

  async addDocument(doc, user) {
    return await this.save(doc, user);
  }

  /**
   * Get logs from the logs database
   */
  async getLogs(options = {}) {
    try {
      const result = await ipcRenderer.invoke('db-get-logs', {
        databasePrefix: this.databasePrefix,
        options
      });

      if (result.success) {
        return result.logs;
      } else {
        throw new Error(result.error);
      }
    } catch (error) {
      console.error(`[ElectronDB] Get logs failed:`, error);
      throw error;
    }
  }

  // Backward compatibility properties
  get db() {
    return this; // Return self for backward compatibility
  }

  get pouchDb() {
    return this; // Return self for backward compatibility
  }
}


export default ElectronDB;