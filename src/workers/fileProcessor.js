/**
 * PERFORMANCE FIX: Web Worker for background file processing
 * Prevents UI blocking during file operations
 */

// File processing worker
self.onmessage = async function(e) {
  const { type, data, id } = e.data;

  try {
    switch (type) {
      case 'PROCESS_FILE':
        const result = await processFile(data.file, data.options);
        self.postMessage({
          type: 'FILE_PROCESSED',
          id,
          success: true,
          result
        });
        break;

      case 'PROCESS_FILES_BATCH':
        const results = await processFilesBatch(data.files, data.options);
        self.postMessage({
          type: 'FILES_BATCH_PROCESSED',
          id,
          success: true,
          results
        });
        break;

      default:
        throw new Error(`Unknown message type: ${type}`);
    }
  } catch (error) {
    self.postMessage({
      type: 'ERROR',
      id,
      success: false,
      error: error.message
    });
  }
};

/**
 * Process a single file
 */
async function processFile(file, options = {}) {
  const { maxSize = 10 * 1024 * 1024 } = options; // 10MB default limit

  // Validate file size
  if (file.size > maxSize) {
    throw new Error(`File ${file.name} is too large (${file.size} bytes). Maximum allowed: ${maxSize} bytes`);
  }

  // Read file as ArrayBuffer
  const arrayBuffer = await file.arrayBuffer();
  const uint8Array = new Uint8Array(arrayBuffer);

  // Create optimized attachment object
  return {
    content_type: file.type,
    type: file.type,
    name: file.name,
    size: file.size,
    lastModified: file.lastModified,
    data: Array.from(uint8Array), // Convert to regular array for JSON serialization
    processedAt: new Date().toISOString()
  };
}

/**
 * Process multiple files in batch
 */
async function processFilesBatch(files, options = {}) {
  const { concurrency = 3 } = options;
  const results = [];
  
  // Process files in batches to prevent memory issues
  for (let i = 0; i < files.length; i += concurrency) {
    const batch = files.slice(i, i + concurrency);
    
    const batchPromises = batch.map(async (file, index) => {
      try {
        const result = await processFile(file, options);
        return {
          success: true,
          index: i + index,
          result
        };
      } catch (error) {
        return {
          success: false,
          index: i + index,
          error: error.message,
          fileName: file.name
        };
      }
    });

    const batchResults = await Promise.all(batchPromises);
    results.push(...batchResults);

    // Small delay between batches to prevent overwhelming
    if (i + concurrency < files.length) {
      await new Promise(resolve => setTimeout(resolve, 10));
    }
  }

  return results;
}

/**
 * Utility function to create chunks for large files
 */
function createChunks(uint8Array, chunkSize = 64 * 1024) { // 64KB chunks
  const chunks = [];
  for (let i = 0; i < uint8Array.length; i += chunkSize) {
    chunks.push(uint8Array.slice(i, i + chunkSize));
  }
  return chunks;
}

/**
 * Process large file in chunks to prevent memory issues
 */
async function processLargeFile(file, options = {}) {
  const { chunkSize = 64 * 1024 } = options; // 64KB chunks
  
  const arrayBuffer = await file.arrayBuffer();
  const uint8Array = new Uint8Array(arrayBuffer);
  
  // Process in chunks if file is large
  if (uint8Array.length > chunkSize * 10) { // Process in chunks if > 640KB
    const chunks = createChunks(uint8Array, chunkSize);
    const processedChunks = [];
    
    for (let i = 0; i < chunks.length; i++) {
      processedChunks.push(Array.from(chunks[i]));
      
      // Yield control periodically
      if (i % 10 === 0) {
        await new Promise(resolve => setTimeout(resolve, 1));
      }
    }
    
    return {
      content_type: file.type,
      type: file.type,
      name: file.name,
      size: file.size,
      lastModified: file.lastModified,
      chunks: processedChunks,
      isChunked: true,
      processedAt: new Date().toISOString()
    };
  } else {
    // Process normally for smaller files
    return {
      content_type: file.type,
      type: file.type,
      name: file.name,
      size: file.size,
      lastModified: file.lastModified,
      data: Array.from(uint8Array),
      isChunked: false,
      processedAt: new Date().toISOString()
    };
  }
}
