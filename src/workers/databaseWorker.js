/**
 * PERFORMANCE FIX: Database Worker for CPU-intensive operations
 * Handles heavy data processing operations in background to prevent UI blocking
 *
 * IMPORTANT: This worker does NOT interact with PouchDB directly to avoid
 * IndexedDB and browser API limitations in worker contexts. It only processes
 * plain JavaScript data that has already been retrieved from the database.
 */

// Data processing worker - NO PouchDB operations
self.onmessage = async function (e) {
  const { type, data, id } = e.data;

  try {
    switch (type) {
      case 'PROCESS_QUERY_RESULTS':
        const processedResults = await processQueryResults(data.results, data.options);
        self.postMessage({
          type: 'QUERY_RESULTS_PROCESSED',
          id,
          success: true,
          results: processedResults
        });
        break;

      case 'FILTER_DOCUMENTS':
        const filteredDocs = await filterDocuments(data.documents, data.filters);
        self.postMessage({
          type: 'DOCUMENTS_FILTERED',
          id,
          success: true,
          documents: filteredDocs
        });
        break;

      case 'SORT_DOCUMENTS':
        const sortedDocs = await sortDocuments(data.documents, data.sortOptions);
        self.postMessage({
          type: 'DOCUMENTS_SORTED',
          id,
          success: true,
          documents: sortedDocs
        });
        break;

      case 'AGGREGATE_DATA':
        const aggregatedData = await aggregateData(data.documents, data.aggregation);
        self.postMessage({
          type: 'DATA_AGGREGATED',
          id,
          success: true,
          result: aggregatedData
        });
        break;

      case 'VALIDATE_DOCUMENTS':
        const validationResults = await validateDocuments(data.documents, data.schema);
        self.postMessage({
          type: 'DOCUMENTS_VALIDATED',
          id,
          success: true,
          results: validationResults
        });
        break;

      case 'TRANSFORM_DOCUMENTS':
        const transformedDocs = await transformDocuments(data.documents, data.transformer);
        self.postMessage({
          type: 'DOCUMENTS_TRANSFORMED',
          id,
          success: true,
          documents: transformedDocs
        });
        break;

      case 'CALCULATE_STATISTICS':
        const stats = await calculateStatistics(data.documents, data.fields);
        self.postMessage({
          type: 'STATISTICS_CALCULATED',
          id,
          success: true,
          statistics: stats
        });
        break;

      default:
        throw new Error(`Unknown message type: ${type}`);
    }
  } catch (error) {
    self.postMessage({
      type: 'ERROR',
      id,
      success: false,
      error: error.message
    });
  }
};

/**
 * Process query results with complex transformations
 */
async function processQueryResults(results, options = {}) {
  const {
    includeMetadata = false,
    calculateStats = false,
    groupBy = null,
    transform = null
  } = options;

  let processedResults = [...results];

  // Apply transformations
  if (transform && typeof transform === 'function') {
    processedResults = processedResults.map(transform);
  }

  // Group by field if specified
  if (groupBy) {
    const grouped = {};
    processedResults.forEach(doc => {
      const key = doc[groupBy] || 'undefined';
      if (!grouped[key]) {
        grouped[key] = [];
      }
      grouped[key].push(doc);
    });
    processedResults = grouped;
  }

  // Calculate statistics if requested
  let stats = null;
  if (calculateStats) {
    stats = {
      totalCount: results.length,
      processedCount: Array.isArray(processedResults) ? processedResults.length : Object.keys(processedResults).length,
      processingTime: Date.now()
    };
  }

  return {
    results: processedResults,
    ...(includeMetadata && { metadata: { processedAt: new Date().toISOString() } }),
    ...(stats && { stats })
  };
}

/**
 * Filter documents based on complex criteria
 */
async function filterDocuments(documents, filters) {
  if (!filters || Object.keys(filters).length === 0) {
    return documents;
  }

  return documents.filter(doc => {
    return Object.entries(filters).every(([field, criteria]) => {
      const value = getNestedValue(doc, field);

      if (typeof criteria === 'object' && criteria !== null) {
        // Handle complex criteria
        if (criteria.$eq !== undefined) return value === criteria.$eq;
        if (criteria.$ne !== undefined) return value !== criteria.$ne;
        if (criteria.$gt !== undefined) return value > criteria.$gt;
        if (criteria.$gte !== undefined) return value >= criteria.$gte;
        if (criteria.$lt !== undefined) return value < criteria.$lt;
        if (criteria.$lte !== undefined) return value <= criteria.$lte;
        if (criteria.$in !== undefined) return criteria.$in.includes(value);
        if (criteria.$nin !== undefined) return !criteria.$nin.includes(value);
        if (criteria.$regex !== undefined) {
          const regex = new RegExp(criteria.$regex, criteria.$options || '');
          return regex.test(String(value));
        }
        if (criteria.$exists !== undefined) {
          return criteria.$exists ? (value !== undefined) : (value === undefined);
        }
      } else {
        // Simple equality check
        return value === criteria;
      }

      return true;
    });
  });
}

/**
 * Sort documents by multiple criteria
 */
async function sortDocuments(documents, sortOptions) {
  if (!sortOptions || sortOptions.length === 0) {
    return documents;
  }

  return documents.sort((a, b) => {
    for (const { field, direction = 'asc' } of sortOptions) {
      const aValue = getNestedValue(a, field);
      const bValue = getNestedValue(b, field);

      let comparison = 0;

      if (aValue < bValue) comparison = -1;
      else if (aValue > bValue) comparison = 1;

      if (comparison !== 0) {
        return direction === 'desc' ? -comparison : comparison;
      }
    }
    return 0;
  });
}

/**
 * Aggregate data with various operations
 */
async function aggregateData(documents, aggregation) {
  const { groupBy, operations = [] } = aggregation;

  if (!groupBy) {
    // Simple aggregation without grouping
    const result = {};
    operations.forEach(op => {
      result[op.name] = performAggregation(documents, op);
    });
    return result;
  }

  // Group documents first
  const groups = {};
  documents.forEach(doc => {
    const key = getNestedValue(doc, groupBy);
    if (!groups[key]) {
      groups[key] = [];
    }
    groups[key].push(doc);
  });

  // Apply aggregations to each group
  const result = {};
  Object.entries(groups).forEach(([key, groupDocs]) => {
    result[key] = {};
    operations.forEach(op => {
      result[key][op.name] = performAggregation(groupDocs, op);
    });
  });

  return result;
}

/**
 * Perform individual aggregation operation
 */
function performAggregation(documents, operation) {
  const { type, field } = operation;

  switch (type) {
    case 'count':
      return documents.length;

    case 'sum':
      return documents.reduce((sum, doc) => {
        const value = getNestedValue(doc, field);
        return sum + (typeof value === 'number' ? value : 0);
      }, 0);

    case 'avg':
      const sum = documents.reduce((sum, doc) => {
        const value = getNestedValue(doc, field);
        return sum + (typeof value === 'number' ? value : 0);
      }, 0);
      return documents.length > 0 ? sum / documents.length : 0;

    case 'min':
      return documents.reduce((min, doc) => {
        const value = getNestedValue(doc, field);
        return typeof value === 'number' && (min === null || value < min) ? value : min;
      }, null);

    case 'max':
      return documents.reduce((max, doc) => {
        const value = getNestedValue(doc, field);
        return typeof value === 'number' && (max === null || value > max) ? value : max;
      }, null);

    default:
      return null;
  }
}

/**
 * Validate documents against schema
 */
async function validateDocuments(documents, schema) {
  return documents.map(doc => {
    const errors = [];

    // Check required fields
    if (schema.required) {
      schema.required.forEach(field => {
        if (getNestedValue(doc, field) === undefined) {
          errors.push(`Missing required field: ${field}`);
        }
      });
    }

    // Check field types
    if (schema.properties) {
      Object.entries(schema.properties).forEach(([field, fieldSchema]) => {
        const value = getNestedValue(doc, field);
        if (value !== undefined && fieldSchema.type) {
          const actualType = Array.isArray(value) ? 'array' : typeof value;
          if (actualType !== fieldSchema.type) {
            errors.push(`Field ${field} should be ${fieldSchema.type}, got ${actualType}`);
          }
        }
      });
    }

    return {
      document: doc,
      valid: errors.length === 0,
      errors
    };
  });
}

/**
 * Transform documents using provided transformer function
 */
async function transformDocuments(documents, transformer) {
  if (typeof transformer !== 'function') {
    return documents;
  }

  return documents.map((doc, index) => {
    try {
      return transformer(doc, index);
    } catch (error) {
      console.warn('Document transformation failed:', error);
      return doc; // Return original document if transformation fails
    }
  });
}

/**
 * Calculate statistics for document fields
 */
async function calculateStatistics(documents, fields) {
  const stats = {};

  fields.forEach(field => {
    const values = documents
      .map(doc => getNestedValue(doc, field))
      .filter(value => value !== undefined && value !== null);

    if (values.length === 0) {
      stats[field] = { count: 0, type: 'empty' };
      return;
    }

    const numericValues = values.filter(v => typeof v === 'number');
    const stringValues = values.filter(v => typeof v === 'string');
    const dateValues = values.filter(v => v instanceof Date || (typeof v === 'string' && !isNaN(Date.parse(v))));

    stats[field] = {
      count: values.length,
      uniqueCount: new Set(values).size,
      type: numericValues.length > 0 ? 'numeric' : stringValues.length > 0 ? 'string' : 'mixed'
    };

    // Numeric statistics
    if (numericValues.length > 0) {
      const sorted = numericValues.sort((a, b) => a - b);
      stats[field].numeric = {
        min: sorted[0],
        max: sorted[sorted.length - 1],
        sum: numericValues.reduce((sum, val) => sum + val, 0),
        avg: numericValues.reduce((sum, val) => sum + val, 0) / numericValues.length,
        median: sorted[Math.floor(sorted.length / 2)]
      };
    }

    // String statistics
    if (stringValues.length > 0) {
      const lengths = stringValues.map(s => s.length);
      stats[field].string = {
        minLength: Math.min(...lengths),
        maxLength: Math.max(...lengths),
        avgLength: lengths.reduce((sum, len) => sum + len, 0) / lengths.length,
        mostCommon: getMostCommonValue(stringValues)
      };
    }

    // Date statistics
    if (dateValues.length > 0) {
      const dates = dateValues.map(d => new Date(d)).filter(d => !isNaN(d));
      if (dates.length > 0) {
        const sortedDates = dates.sort((a, b) => a - b);
        stats[field].date = {
          earliest: sortedDates[0],
          latest: sortedDates[sortedDates.length - 1],
          range: sortedDates[sortedDates.length - 1] - sortedDates[0]
        };
      }
    }
  });

  return stats;
}

/**
 * Get most common value in array
 */
function getMostCommonValue(values) {
  const counts = {};
  values.forEach(value => {
    counts[value] = (counts[value] || 0) + 1;
  });

  let mostCommon = null;
  let maxCount = 0;

  Object.entries(counts).forEach(([value, count]) => {
    if (count > maxCount) {
      maxCount = count;
      mostCommon = value;
    }
  });

  return { value: mostCommon, count: maxCount };
}

/**
 * Get nested value from object using dot notation
 */
function getNestedValue(obj, path) {
  return path.split('.').reduce((current, key) => {
    return current && current[key] !== undefined ? current[key] : undefined;
  }, obj);
}
