import AppDatabase from "../../../Utils/AppDatabase";


function calculateStockValue(purchases, currentStock) {
  let remainingStock = currentStock;
  let stockValue = 0;

  // Iterate through the purchases array
  for (const purchase of purchases) {
    const { quantity, price, total_cost = 0 } = purchase;

    if (remainingStock <= 0) break; // Stop if no more stock is needed

    if (remainingStock >= quantity) {
      // Use up the entire purchase
      stockValue += quantity * (total_cost ? total_cost / quantity : price);
      remainingStock -= quantity;
    } else {
      // Use only part of the purchase
      stockValue += remainingStock * (total_cost ? total_cost / quantity : price);
      remainingStock = 0; // All stock accounted for
    }
  }

  return stockValue;
}

// Helper function to check for data inconsistencies
function checkDataConsistency(productId, allReturnedProducts, allReturnedStock) {
  // Filter for the specific product
  const returnedProducts = allReturnedProducts.filter(item => item.product && item.product.value === productId);
  const returnedStockItems = allReturnedStock.filter(item => item.product && item.product.value === productId);

  console.log(`DATA CONSISTENCY CHECK for product ${productId}:`);
  console.log(`- Found ${returnedProducts.length} returned products`);
  returnedProducts.forEach((item, index) => {
    console.log(`  ${index + 1}. Quantity: ${item.quantity}, Date: ${item.date}`);
  });

  console.log(`- Found ${returnedStockItems.length} returned stock items`);
  returnedStockItems.forEach((item, index) => {
    console.log(`  ${index + 1}. Quantity: ${item.quantity}, Date: ${item.date}`);
  });

  // Check for potential issues
  const issues = [];

  // Check for missing quantities
  returnedProducts.forEach((item, index) => {
    if (!item.quantity && item.quantity !== 0) {
      issues.push(`Returned product #${index + 1} has no quantity`);
    }
  });

  returnedStockItems.forEach((item, index) => {
    if (!item.quantity && item.quantity !== 0) {
      issues.push(`Returned stock #${index + 1} has no quantity`);
    }
  });

  // Report issues
  if (issues.length > 0) {
    console.log(`ISSUES FOUND for product ${productId}:`);
    issues.forEach(issue => console.log(`- ${issue}`));
  } else {
    console.log(`No data consistency issues found for product ${productId}`);
  }
}

export const buffProducts = async (results, pouchDatabase, databasePrefix, collection, branchParam, showExternal = false) => {
  // Use the passed branch parameter or fall back to localStorage
  const SELECTED_BRANCH = branchParam || localStorage.getItem("SELECTED_BRANCH");

  // Special handling for "all" branch - we don't want to filter by branch in this case
  const shouldFilterByBranch = SELECTED_BRANCH && SELECTED_BRANCH !== "all" && SELECTED_BRANCH !== "none";

  // Debug log for branch filtering
  console.log(`buffProducts: Branch=${SELECTED_BRANCH}, shouldFilterByBranch=${shouldFilterByBranch}`);

  // Use the new Electron database system
  const pricesDb = AppDatabase("product_prices", databasePrefix);
  const stockDB = AppDatabase("stock_purchasing", databasePrefix);
  const ReturnedProductsDB = AppDatabase("returned_products", databasePrefix);
  const invoicesDB = AppDatabase("invoices", databasePrefix);
  const ReturnedStockDB = AppDatabase("returned_stock", databasePrefix);
  const stockAdjustments = AppDatabase("stock_adjustments", databasePrefix);
  const nonPurchasedStock = AppDatabase("non_purchased_stock", databasePrefix);

  const dbData = await Promise.all([
    await pricesDb.getAllData(),
    await stockDB.getAllData(),
    await invoicesDB.getAllData(),
    await ReturnedProductsDB.getAllData(),
    await ReturnedStockDB.getAllData(),
    await stockAdjustments.getAllData(),
    await nonPurchasedStock.getAllData(),
  ]);

  const [
    prices,
    stock,
    invoices,
    returned,
    returnedStock,
    adjustments,
    nonPurchased,
  ] = dbData;

  let allSold = [];
  let allReturnedProducts = [];
  let allReturnedStock = [];
  let allAdjustments = [];
  let allNonPurchased = [];



  (shouldFilterByBranch
    ? invoices.filter((i) => i.branch === SELECTED_BRANCH)
    : invoices
  ).forEach((element) => {
    element.items &&
      allSold.push(...element.items.map((e) => ({ ...e, date: element.date, _id: element._id })));
  });

  (shouldFilterByBranch
    ? returned.filter((i) => i.branch === SELECTED_BRANCH)
    : returned
  ).forEach((element) => {
    // Add debug logging for returned products
    console.log("Processing returned product:", element._id, element.items ? `with ${element.items.length} items` : "without items");

    if (element.items && Array.isArray(element.items)) {
      // Make sure we're correctly mapping the items with all necessary properties
      const mappedItems = element.items.map((e) => ({
        ...e,
        date: element.date,
        _id: element._id,
        // Ensure product value is properly set
        product: e.product
      }));

      console.log(`Adding ${mappedItems.length} returned product items`);
      allReturnedProducts.push(...mappedItems);
    }

    // This seems to be for single-item returns, but might be causing issues
    // if it's adding the same product twice
    if (element.unit_cost && !element.items) {
      console.log("Adding single returned product with unit_cost");
      allReturnedProducts.push({
        ...element,
        date: element.date,
        price: element.unit_cost,
      });
    }
  });

  (shouldFilterByBranch
    ? returnedStock.filter((i) => i.branch === SELECTED_BRANCH)
    : returnedStock
  ).forEach((element) => {
    // Add debug logging for returned stock
    console.log("Processing returned stock:", element._id, element.items ? `with ${element.items.length} items` : "without items");

    if (element.items && Array.isArray(element.items)) {
      // Make sure we're correctly mapping the items with all necessary properties
      const mappedItems = element.items.map((e) => ({
        ...e,
        date: element.date,
        _id: element._id,
        // Ensure product value is properly set
        product: e.product
      }));

      console.log(`Adding ${mappedItems.length} returned stock items`);
      allReturnedStock.push(...mappedItems);
    }
  });

  (shouldFilterByBranch
    ? adjustments.filter((i) => i.branch === SELECTED_BRANCH)
    : adjustments
  ).forEach((element) => {
    allAdjustments.push(
      ...(element.items ? element.items : []).map((e) => ({
        ...e,
        date: element.date,
      }))
    );
  });

  (shouldFilterByBranch
    ? nonPurchased.filter((i) => i.branch === SELECTED_BRANCH)
    : nonPurchased
  ).forEach((element) => {



    // allNonPurchased.push(
    //   ...(element.items ? element.items : [])?.map((e) => ({
    //     ...e,
    //     date: element.date,
    //   }))
    // );
    allNonPurchased.push(
      ...(element.items?.map((e) => ({
        ...e,
        date: element.date,
      })) || [])
    );
  });

  // Filter products based on external status if showExternal is false
  return results
    .filter(product => showExternal || !product.external)
    .map((product) => {
      const newPrices = prices.filter((doc) => doc && doc.product && doc.product.value === product._id);

      const soldProducts = allSold.filter(
        (element) => element.product && element.product.value === product._id
      );

      const returnedProducts = allReturnedProducts.filter(
        (element) => element.product && element.product.value === product._id
      );

      const returnedStockProducts = allReturnedStock.filter(
        (element) => element.product && element.product.value === product._id
      );

      // Run consistency check for the specific product with ID P2000
      if (product._id === 'P2000') {
        checkDataConsistency(product._id, allReturnedProducts, allReturnedStock);
      }

      product.returned_list = returnedProducts;
      product.returned_stoked_list = returnedStockProducts;

      // Filter adjustments for this product
      const productAdjustments = allAdjustments
        .filter((element) => element.product && element.product.value === product._id);

      // Store the list of adjustments for movement history
      product.adjustments_list = productAdjustments;

      // Calculate total adjustments
      product.adjustments = productAdjustments
        .reduce(
          (pv, cv) => pv + Number(cv.add_subtract ? 1 : -1) * Number(cv.quantity || 0),
          0
        );

      // Calculate returned products (from customers) - should increase stock
      if (returnedProducts.length > 0) {
        // Log each returned product for debugging
        console.log(`Found ${returnedProducts.length} returned products for ${product.name || product._id}`);

        // Check for specific product ID P2000
        if (product._id === 'P2000') {
          console.log('DETAILED RETURNED PRODUCTS for P2000:');
          returnedProducts.forEach((item, index) => {
            console.log(`  ${index + 1}. ID: ${item._id}, Date: ${item.date}, Quantity: ${item.quantity}, Reference: ${item.reference || 'N/A'}`);
          });
        }

        // Log each returned product for debugging
        returnedProducts.forEach(item => {
          console.log(`  - Returned product: quantity=${item.quantity}, product=${item.product?.label || 'unknown'}`);
        });

        // Calculate total returned with explicit number conversion
        product.returned = returnedProducts.reduce(
          (pv, cv) => {
            // Make sure we're using a valid number and handle potential undefined/null values
            const quantity = Number(cv.quantity) || 0;
            console.log(`    Adding returned quantity: ${quantity} (raw value: ${cv.quantity})`);
            return pv + quantity;
          },
          0
        );

        // Double-check the calculation
        console.log(`Total returned for ${product.name || product._id}: ${product.returned}`);
      } else {
        product.returned = 0;
      }

      // Calculate returned stock (to suppliers) - should decrease stock
      if (returnedStockProducts.length > 0) {
        // Log each returned stock for debugging
        console.log(`Found ${returnedStockProducts.length} returned stock items for ${product.name || product._id}`);

        // Check for specific product ID P2000
        if (product._id === 'P2000') {
          console.log('DETAILED RETURNED STOCK for P2000:');
          returnedStockProducts.forEach((item, index) => {
            console.log(`  ${index + 1}. ID: ${item._id}, Date: ${item.date}, Quantity: ${item.quantity}, Reference: ${item.reference || 'N/A'}`);
          });
        }

        returnedStockProducts.forEach(item => {
          console.log(`  - Returned stock: quantity=${item.quantity}, product=${item.product?.label || 'unknown'}`);
        });

        // Calculate total returned stock with explicit number conversion
        product.returned_stock = returnedStockProducts.reduce(
          (pv, cv) => {
            // Make sure we're using a valid number and handle potential undefined/null values
            const quantity = Number(cv.quantity) || 0;
            console.log(`    Adding returned stock quantity: ${quantity} (raw value: ${cv.quantity})`);
            return pv + quantity;
          },
          0
        );

        // Double-check the calculation
        console.log(`Total returned stock for ${product.name || product._id}: ${product.returned_stock}`);
      } else {
        product.returned_stock = 0;
      }

      product.sold_list = soldProducts;
      if (soldProducts.length > 0) {
        // Check for specific product ID P2000
        if (product._id === 'P2000') {
          console.log('DETAILED SOLD PRODUCTS for P2000:');
          soldProducts.forEach((item, index) => {
            console.log(`  ${index + 1}. ID: ${item._id}, Date: ${item.date}, Quantity: ${item.quantity}, Reference: ${item.reference || 'N/A'}`);
          });
        }

        // Calculate total sold with explicit number conversion
        product.sold = soldProducts.reduce(
          (pv, cv) => {
            // Make sure we're using a valid number and handle potential undefined/null values
            const quantity = Number(cv.quantity) || 0;
            if (product._id === 'P2000') {
              console.log(`    Adding sold quantity: ${quantity} (raw value: ${cv.quantity})`);
            }
            return pv + quantity;
          },
          0
        );

        // Double-check the calculation for P2000
        if (product._id === 'P2000') {
          console.log(`Total sold for ${product.name || product._id}: ${product.sold}`);
        }
      } else {
        product.sold = 0;
      }

      if (newPrices.length > 0) {
        product.sell = newPrices.reverse()[0].price;
      } else {
        product.sell = 0;
      }

      let stockRecords = [];

      (shouldFilterByBranch
        ? stock.filter((i) => i.branch === SELECTED_BRANCH)
        : stock
      ).forEach((invoice) => {
        if (invoice.items) {
          invoice.items.forEach((item) =>
            stockRecords.push({
              date: invoice.date,
              _id: invoice._id,
              ...item,
            })
          );
        } else {
          stockRecords.push({ price: invoice.unit_cost, ...invoice });
        }
      });

      (shouldFilterByBranch
        ? nonPurchased.filter((i) => i.branch === SELECTED_BRANCH)
        : nonPurchased
      ).forEach((invoice) => {
        if (invoice.items) {
          invoice.items.forEach((item) =>
            stockRecords.push({
              date: invoice.date,
              _id: invoice._id,
              ...item,
            })
          );
        } else {
          stockRecords.push({ price: invoice.price, ...invoice });
        }
      });

      const newStock = stockRecords.filter(
        (doc) => doc.product.value === product._id
      );
      product.stock_list = newStock;
      if (newStock.length > 0) {
        // Check for specific product ID P2000
        if (product._id === 'P2000') {
          console.log('DETAILED STOCK PURCHASES for P2000:');
          newStock.forEach((item, index) => {
            console.log(`  ${index + 1}. ID: ${item._id}, Date: ${item.date}, Quantity: ${item.quantity}, Unit Cost: ${item.unit_cost || item.price || 'N/A'}`);
          });
        }

        const lastStock = newStock.sort((a, b) => new Date(b.date) - new Date(a.date))[0];
        const unit_cost = lastStock.unit_cost
          ? lastStock.unit_cost
          : lastStock.price;
        const total_cost = lastStock.total_cost;
        const quantity = lastStock.quantity;
        product.cost = unit_cost ? unit_cost : total_cost / quantity;

        // Calculate total stoked with explicit number conversion
        product.stoked = newStock.reduce(
          (pv, cv) => {
            // Make sure we're using a valid number and handle potential undefined/null values
            const quantity = Number(cv.quantity) || 0;
            if (product._id === 'P2000') {
              console.log(`    Adding stoked quantity: ${quantity} (raw value: ${cv.quantity})`);
            }
            return pv + quantity;
          },
          0
        );

        // Double-check the calculation for P2000
        if (product._id === 'P2000') {
          console.log(`Total stoked for ${product.name || product._id}: ${product.stoked}`);
        }
      } else {
        product.cost = 0;
        product.stoked = 0;
      }

      // Calculate current stock with enhanced logging
      // Formula: stoked (purchases) - sold + returned (from customers) - returned_stock (to suppliers) + adjustments
      const stoked = Number(product.stoked) || 0;
      const sold = Number(product.sold) || 0;
      const returned = Number(product.returned) || 0;
      const returned_stock = Number(product.returned_stock) || 0;
      const adjustments = Number(product.adjustments) || 0;

      const currentStock = stoked - sold + returned - returned_stock + adjustments;

      // Add current stock as a separate property for easier debugging
      product.currentStock = currentStock;

      product.stock_value = calculateStockValue(newStock, currentStock);

      // Add detailed logging for debugging cost and stock issues
      return product;
    });
};

export const buffCategories = async (results, database, databasePrefix, collection, SELECTED_BRANCH) => {
  const productsDB = AppDatabase("products", databasePrefix);

  const products = await productsDB.getAllData();
  return results.map((r) => {
    return {
      ...r,
      products: products
        .filter((p) => p.category && p.category.value === r._id).length,
    };
  });
};
