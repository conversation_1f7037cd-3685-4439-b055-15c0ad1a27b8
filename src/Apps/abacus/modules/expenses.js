import moment from "moment";


export const expenses = {
  name: "Expenditure",
  icon: "KeyOutlined",
  path: "expenditure/expenses",
  parent: "expenditure",
  collection: "expenses",
  singular: "Expense",
  columns: [
    {
      valueType: "date",
      dataIndex: "date",
      title: "Date",
      isPrintable: true,
      noBackDate: true,
      isRequired: true,
      initialValue: moment().startOf("day"), // Use moment.js to get the current date
    },
    {
      dataIndex: "supplier",
      title: "Supplier",
      type: "dbSelect",
      valueType: "select",
      collection: "suppliers",
      isRequired: true,
      isPrintable: true,
      label: ["name"],
    },
    {
      dataIndex: "expense_category",
      title: "Expense Category",
      type: "dbSelect",
      valueType: "select",
      collection: "expense_categories",
      isPrintable: true,
      isRequired: true,
      label: ["name"],
    },
    {
      title: "Method Of Payment",
      dataIndex: "method_of_payment",
      sorter: true,
      hideInTable: true,
      valueType: "select",
      isRequired: true,
      isPrintable: true,
      width: "lg",
      valueEnum: {
        Cash: {
          text: "Cash",
        },
        Cheque: {
          text: "Cheque",
        },
        Bank: {
          text: "Bank",
        },
        "Airtel Money": {
          text: "Airtel Money",
        },
        "MTN Mobile Money": {
          text: "MTN Mobile Money",
        },
      },
    },
    {
      valueType: "textarea",
      isPrintable: true,
      dataIndex: "description",
      title: "Description",
    },
    {
      valueType: "money",
      isRequired: true,
      isPrintable: true,
      dataIndex: "amount",
      title: "Amount",
    },
  ],
};
