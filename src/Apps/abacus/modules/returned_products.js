import moment from "moment";
import { numberFormat } from "../../../Utils/functions.js";


export const returned_products = {
  name: "Returned Products",
  icon: "UndoOutlined",
  path: "/inventory/returned_products",
  parent: "inventory",
  collection: "returned_products",
  singular: "Returned Product(s)",
  columns: [
    {
      dataIndex: "customer",
      title: "Customer",
      type: "dbSelect",
      valueType: "select",
      collection: "customers",
      label: ["name"],
      isRequired: true,
    },
    {
      valueType: "date",
      dataIndex: "date",
      title: "Date",
      isRequired: true,
      initialValue: moment().startOf("day"),
    },

    {
      valueType: "formList",
      dataIndex: "items",
      title: "Items",
      fieldProps: {
        initialValue: [{}],
        creatorButtonProps: {
          block: true,
          style: {
            width: "100%",
          },
          copyIconProps: false,
          creatorButtonText: "Add Item",
        },
      },
      columns: [
        {
          valueType: "group",
          colProps: {
            md: 24,
          },
          columns: [
            {
              dataIndex: "product",
              title: "Product",
              type: "dbSelect",
              valueType: "select",
              collection: "products",
              label: ["name", " ", "sku", " - ", "measurements", "units"],
              colProps: {
                md: 12,
              },
              formItemProps: {
                rules: [
                  {
                    required: true,
                    message: `Please Select a product`,
                  },
                ],
              },
            },
            {
              valueType: "digit",
              dataIndex: "quantity",
              title: "Quantity",
              formItemProps: {
                rules: [
                  {
                    required: true,
                    message: `Quantity is required`,
                  },
                ],
              },
              colProps: {
                md: 4,
              },
              dependency: {
                value: "product",
                rules: [{ required: true, message: "Please select a product" }],
              },
            },
            {
              valueType: "money",
              dataIndex: "price",
              title: "Price",
              colProps: {
                md: 4,
              },
              formItemProps: {
                rules: [
                  {
                    required: true,
                    message: `Price is required`,
                  },
                ],
              },
              dependency: {
                value: "product",
                rules: [{ required: true, message: "Please select a product" }],
              },
            },
            {
              valueType: "dependency",
              fieldProps: {
                name: ["price", "quantity"],
                colProps: {
                  md: 4,
                },
              },
              columns: ({ price, quantity }) => {
                return [
                  {
                    title: "Total",
                    dataIndex: "total",
                    colProps: {
                      md: 4,
                    },
                    fieldProps: {
                      disabled: true,
                      value: (price ? price : 0) * (quantity ? quantity : 0),
                    },
                    valueType: "money",
                  },
                ];
              },
            },
          ],
        },
      ],
      render: (record, { items }) => {
        let allItems = "";
        items &&
          items.map(
            (item) =>
            (allItems = item
              ? allItems + item.product.label + ", "
              : allItems)
          );
        return allItems;
      },
    },

    {
      valueType: "dependency",
      title: "Total",
      fieldProps: {
        name: ["items"],
      },

      render: (text, record) => {
        const items = record.items;
        let total = 0;
        if (items) {
          items.forEach(
            (item) =>
            (total = item
              ? total +
              (item.price ? item.price : 0) *
              (item.quantity ? item.quantity : 0)
              : total)
          );
        } else {
          total += record.total_cost
            ? record.total_cost
            : record.quantity * record.unit_cost;
        }

        return total;
      },
      columns: ({ items }) => {
        return [
          {
            title: "Total",
            dataIndex: "total",
            colProps: {
              md: 8,
              offset: 16,
            },
            fieldProps: {
              disabled: true,
              value: items.reduce(
                (pv, cv) =>
                  pv +
                  (cv.price ? cv.price : 0) * (cv.quantity ? cv.quantity : 0),
                0
              ),
            },
            valueType: "money",
          },
        ];
      },
    },

    {
      valueType: "text",
      dataIndex: "description",
      title: "Description, Remarks or Notes",
      hideInTable: true,
    },
  ],
};
