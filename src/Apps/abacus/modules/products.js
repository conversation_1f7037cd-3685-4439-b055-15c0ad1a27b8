import { numberFormat } from "../../../Utils/functions.js";


var products = {
  name: "Products",
  icon: "ShoppingOutlined",
  path: "/product_management/products",
  parent: "product_management",
  collection: "products",
  singular: "Product",
  multi_Branch: true,
  columns: [
    {
      valueType: "text",
      dataIndex: "name",
      title: "Name",
      sorter: true,
      isRequired: true,
      render: (text, record) => {
        if (!record.units || record.units.toLowerCase() == "pc") {
          return record.name;
        } else {
          return `${record.name} - ${record.measurements} ${record.units}`;
        }
      },
    },
    {
      dataIndex: "category",
      title: "Category",
      type: "dbSelect",
      collection: "categories",
      isRequired: true,
      label: ["name"],
    },
    {
      valueType: "text",
      dataIndex: "measurements",
      title: "Measurements",
      hideInTable: true,
      isRequired: true,
    },
    {
      valueType: "select",
      dataIndex: "units",
      title: "Units",
      hideInTable: true,
      isRequired: true,
      valueEnum: {
        pc: {
          text: "Piece",
          status: "default",
        },
        gr: {
          text: "Gram",
          status: "default",
        },
        kg: {
          text: "Kilogram",
          status: "default",
        },
        ml: {
          text: "Milliliter",
          status: "default",
        },
        ltr: {
          text: "Liter",
          status: "default",
        },
        m: {
          text: "Meter",
          status: "default",
        },
        cm: {
          text: "Centimeter",
          status: "default",
        },
        ft: {
          text: "Foot",
          status: "default",
        },
        set: {
          text: "Set",
          status: "default",
        },
        pair: {
          text: "Pair",
          status: "default",
        },
      },
    },
    {
      dataIndex: "brand",
      title: "Product Brand",
      type: "dbSelect",
      collection: "brands",
      label: ["name"],
      hideInTable: true,
    },
    {
      title: 'Country of Origin',
      dataIndex: 'country',
      sorter: true,
      hideInTable: true,
      valueType: 'select',
      hideInTable: true,
    },
    {
      valueType: "textarea",
      dataIndex: "description",
      title: "Description",
      hideInTable: true,
    },
    {
      valueType: "text",
      dataIndex: "sku",
      title: "SKU/Item No",
      // hideInTable: true,
    },
    {
      dataIndex: "bin_location",
      title: "Bin Location",
      valueType: "text",
    },
    {
      dataIndex: "stock_alert",
      title: "Minimum Stock",
      valueType: "digit",
      hideInTable: true,
      default: 3,
    },
    {
      valueType: "switch",
      dataIndex: "external",
      title: "External",
      fieldProps: {
        checkedChildren: "Yes",
        unCheckedChildren: "No",
      },
      initialValue: false
    },
    {
      valueType: "money",
      dataIndex: "cost",
      title: "Cost",
      hideInForm: true,
      sorter: true,
      render: (text, record) => numberFormat(record.cost),
    },
    {
      valueType: "money",
      dataIndex: "sell",
      title: "Sell",
      hideInForm: true,
      sorter: true,
      render: (text, record) => numberFormat(record.sell),
    },
    {
      valueType: "money",
      dataIndex: "ideal_selling_price",
      title: "Ideal Sell",
      hideInForm: true,
      hideInTable: true,
      render: (text, record) => {
        return numberFormat(record.cost + (33 / 100) * record.cost);
      },
    },
    {
      valueType: "digit",
      dataIndex: "profit",
      title: "Profit",
      hideInForm: true,
      render: (text, record) => {
        return numberFormat(record.sell - record.cost);
      },
    },
    {
      valueType: "digit",
      dataIndex: "percent_profit",
      title: "% Profit",
      hideInForm: true,
      render: (text, record) => {
        return `${numberFormat(
          ((record.sell - record.cost) / record.cost) * 100
        )}%`;
      },
    },
    {
      valueType: "digit",
      dataIndex: "stock_in",
      title: "Stock In",
      sorter: true,
      hideInForm: true,
      render: (text, record) => {
        return numberFormat(
          (record.stoked ? record.stoked : 0) -
          (record.sold ? record.sold : 0) +
          (record.returned ? record.returned : 0) -
          (record.returned_stock ? record.returned_stock : 0) +
          (record.adjustments ? record.adjustments : 0)
        );
      },
    },
    // {
    //   valueType: "digit",
    //   dataIndex: "stock_value",
    //   title: "Stock Value",
    //   hideInForm: true,
    // },
  ],
};

export default products;
