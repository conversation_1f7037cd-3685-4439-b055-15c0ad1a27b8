import moment from "moment";


export const requisitions = {
  name: "Requisitions",
  icon: "FlagOutlined",
  path: "/requisitions",
  collection: "requisitions",
  singular: "Requisition",
  columns: [
    {
      title: "Date",
      dataIndex: "date",
      sorter: true,
      valueType: "date",
      initialValue: moment().startOf("day"),
      isRequired: true,
    },

    {
      valueType: "treeSelect",
      dataIndex: "expense-category",
      title: "Expense Category",
      type: "categoryTreeSelect",
      collection: "expense_categories",
      isPrintable: true,
      isRequired: true,
      label: ["name"],
      isRequired: true,
    },
    {
      title: "Supplier",
      dataIndex: "supplier",
      type: "dbSelect",
      valueType: "select",
      collection: "suppliers",
      label: ["name"],
      isRequired: true,
      sorter: true,
    },

    {
      title: "Requested By",
      dataIndex: "receiver",
      type: "dbSelect",
      valueType: "select",
      collection: "users",
      label: ["first_name", "last_name"],
      sorter: true,
      isRequired: true,
      // initialValue: requester,
    },
    {
      title: "Authorised By",
      dataIndex: "authoriser",
      type: "dbSelect",
      valueType: "select",
      collection: "users",
      label: ["label"],
      sorter: true,
      // initialValue: requester,
      hideInForm: true,
    },

    {
      title: "Purpose/Description",
      dataIndex: "purpose",
      valueType: "textarea",
      sorter: true,
      isRequired: true,
    },
    {
      title: "Amount",
      dataIndex: "amount",
      valueType: "digit",
      sorter: true,
      isRequired: true,
    },
    {
      title: "Approved By",
      dataIndex: "approvedBy",
      type: "dbSelect",
      valueType: "select",
      collection: "users",
      label: ["label"],
      sorter: true,
      hideInForm: true,
    },
  ],
};
