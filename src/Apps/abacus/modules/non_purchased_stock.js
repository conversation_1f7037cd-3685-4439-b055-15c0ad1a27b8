import moment from "moment";
import { numberFormat } from "../../../Utils/functions.js";


export const non_purchased_stock = {
  name: "Non Purchased Stock",
  icon: "ShoppingCartOutlined",
  path: "/inventory/non_purchased_stock",
  parent: "inventory",
  collection: "non_purchased_stock",
  singular: "Non Purchased Stock",
  columns: [
    {
      valueType: "date",
      dataIndex: "date",
      title: "Date",
      isPrintable: true,
      isRequired: true,
      initialValue: moment().startOf("day"), // Use moment.js to get the current date
    },
    // {
    //   dataIndex: "product",
    //   title: "Product",
    //   type: "dbSelect",
    //   valueType: "select",
    //   collection: "products",
    //   label: ["name", " - ", "measurements", "units"],
    //   isPrintable: true,
    // },
    {
      dataIndex: "source",
      title: "Source",
      valueType: "text",
      isPrintable: true,
      isRequired: true,
    },
    // {
    //   valueType: "digit",
    //   dataIndex: "quantity",
    //   title: "Quantity",
    //   isPrintable: true,
    // },
    // {
    //   valueType: "digit",
    //   dataIndex: "unit_cost",
    //   title: "Unit Cost",
    //   isPrintable: true,
    //   render: (text, record) => {
    //     return numberFormat(
    //       record.unit_cost
    //         ? record.unit_cost
    //         : record.total_cost / record.quantity
    //     );
    //   },
    // },
    // {
    //   valueType: "digit",
    //   dataIndex: "total_cost",
    //   title: "Total Cost",
    //   isPrintable: true,
    //   render: (text, record) => {
    //     return numberFormat(
    //       record.total_cost
    //         ? record.total_cost
    //         : record.quantity * record.unit_cost
    //     );
    //   },
    // },

    {
      valueType: "formList",
      dataIndex: "items",
      title: "Items",
      fieldProps: {
        initialValue: [{}],
        creatorButtonProps: {
          block: true,
          style: {
            width: "100%",
          },
          copyIconProps: false,
          creatorButtonText: "Add Item",
        },
      },
      columns: [
        {
          valueType: "group",
          colProps: {
            md: 24,
          },
          columns: [
            {
              dataIndex: "product",
              title: "Product",
              type: "dbSelect",
              valueType: "select",
              collection: "products",
              label: ["name", " ", "sku", " - ", "measurements", "units"],
              colProps: {
                md: 12,
              },
              formItemProps: {
                rules: [
                  {
                    required: true,
                    message: `Please Select a product`,
                  },
                ],
              },
            },
            {
              valueType: "digit",
              dataIndex: "quantity",
              title: "Quantity",
              formItemProps: {
                rules: [
                  {
                    required: true,
                    message: `Quantity is required`,
                  },
                ],
              },
              colProps: {
                md: 3,
              },
              dependency: {
                value: "product",
                rules: [{ required: true, message: "Please select a product" }],
              },
            },
            {
              valueType: "digit",
              dataIndex: "price",
              title: "Cost Value",
              colProps: {
                md: 3,
              },
              dependency: {
                value: "product",
                rules: [{ required: true, message: "Please select a product" }],
              },
            },
            {
              valueType: "date",
              dataIndex: "expiry_date",
              title: "Expiry Date",
              colProps: {
                md: 3,
              },
            },
            {
              valueType: "dependency",
              fieldProps: {
                name: ["price", "quantity"],
                colProps: {
                  md: 3,
                },
              },
              columns: ({ price, quantity }) => {
                return [
                  {
                    title: "Total",
                    dataIndex: "total",
                    colProps: {
                      md: 3,
                    },
                    fieldProps: {
                      disabled: true,
                      value: (price ? price : 0) * (quantity ? quantity : 0),
                    },
                    valueType: "digit",
                  },
                ];
              },
            },
          ],
        },
      ],
      render: (record, { items }) => {
        let allItems = "";
        items &&
          items.map(
            (item) =>
            (allItems = item
              ? allItems + item.product.label + ", "
              : allItems)
          );
        return allItems;
      },
    },

    {
      valueType: "dependency",
      title: "Total",
      fieldProps: {
        name: ["items"],
      },

      render: (text, record) => {
        const items = record.items;
        let total = 0;
        if (items) {
          items.forEach(
            (item) =>
            (total = item
              ? total +
              (item.price ? item.price : 0) *
              (item.quantity ? item.quantity : 0)
              : total)
          );
        } else {
          total += record.total_cost
            ? record.total_cost
            : record.quantity * record.unit_cost;
        }

        return total;
      },
      columns: ({ items }) => {
        return [
          {
            title: "Total",
            dataIndex: "total",
            colProps: {
              md: 8,
              offset: 16,
            },
            fieldProps: {
              disabled: true,
              value: items.reduce(
                (pv, cv) =>
                  pv +
                  (cv.price ? cv.price : 0) * (cv.quantity ? cv.quantity : 0),
                0
              ),
            },
            valueType: "digit",
          },
        ];
      },
    },
  ],
};
