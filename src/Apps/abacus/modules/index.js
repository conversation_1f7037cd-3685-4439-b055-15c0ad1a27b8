import products from "./products";
import { ai } from "../../Universal/modules/ai";
import { branches } from "./branches";
import { brands } from "./brands";
import { categories } from "./categories";
import { customers } from "./customers";
import { damaged_products } from "./damaged_products";
import { expense_categories } from "./expense_categories";
import { expenses } from "./expenses";
import { goods_receipts } from "../../zenwrench/modules/goods_receipts";
import { invoices } from "./invoices";
import { local_purchase_orders } from "./local_purchase_orders";
import { non_purchased_stock } from "./non_purchased_stock";
import { product_prices } from "./product_prices";
import { quotations } from "./quotations";
import { receipts } from "./receipts";
import { requisitions } from "./requisitions";
import { returned_products } from "./returned_products";
import { returned_stock } from "./returned_stock";
import { stock_adjustments } from "./stock_adjustments";
import { stock_orders } from "./stock_orders";
import { stock_payments } from "./stock_payments";
import { stock_purchasing } from "./stock_purchasing";
import { suppliers } from "./suppliers";
import { teams } from "./teams";
import { transfers } from "./transfer";


export const modules = {
  product_management: {
    name: "Product",
    description: "Product Management",
    icon: "ShoppingOutlined",
    path: "/product_management",
    columns: [],
  },
  sales: {
    name: "Sales",
    description: "Sales",
    icon: "ShoppingCartOutlined",
    path: "/sales",
    columns: [],
  },
  inventory: {
    name: "Inventory",
    description: "Inventory",
    icon: "AppstoreOutlined",
    path: "/inventory",
    columns: [],
  },
  expenditure: {
    name: "Expenditure",
    description: "Expenditure",
    icon: "DollarOutlined",
    path: "/expenditure",
    columns: [],
  },
  partners: {
    name: "Partners",
    description: "Partners",
    icon: "TeamOutlined",
    path: "/partners",
    columns: [],
  },
  categories: categories,
  products: products,
  branches: branches,
  customers: customers,
  product_prices: product_prices,
  stock_purchasing: stock_purchasing,
  stock_orders: stock_orders,
  local_purchase_orders: local_purchase_orders,
  goods_receipts: goods_receipts,
  stock_payments: stock_payments,
  transfers: transfers,
  returned_products: returned_products,
  returned_stock: returned_stock,
  stock_adjustments: stock_adjustments,
  non_purchased_stock: non_purchased_stock,
  damaged_products: damaged_products,
  suppliers: suppliers,
  expenses: expenses,
  expense_categories: expense_categories,
  requisitions: requisitions,
  invoices: invoices,
  receipts: receipts,
  quotations: quotations,
  teams: teams,
  brands: brands,
  ai: ai,
};
