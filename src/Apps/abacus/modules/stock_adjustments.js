import moment from "moment";
import { numberFormat } from "../../../Utils/functions.js";


export const stock_adjustments = {
  name: "Stock Adjustment",
  icon: "UndoOutlined",
  path: "/inventory/stock_adjustments",
  parent: "inventory",
  collection: "stock_adjustments",
  singular: "Stock Adjustment(s)",
  columns: [
    {
      valueType: "date",
      dataIndex: "date",
      title: "Date",
      initialValue: moment().startOf("day"),
    },

    {
      valueType: "formList",
      dataIndex: "items",
      title: "Items",
      fieldProps: {
        initialValue: [{}],
        creatorButtonProps: {
          block: true,
          style: {
            width: "100%",
          },
          copyIconProps: false,
          creatorButtonText: "Add Item",
        },
      },
      columns: [
        {
          valueType: "group",
          colProps: {
            md: 24,
          },
          columns: [
            {
              dataIndex: "product",
              title: "Product",
              type: "dbSelect",
              valueType: "select",
              collection: "products",
              label: ["name", " ", "sku", " - ", "measurements", "units"],
              colProps: {
                md: 8,
              },
              formItemProps: {
                rules: [
                  {
                    required: true,
                    message: `Please Select a product`,
                  },
                ],
              },
            },
            {
              valueType: "digit",
              dataIndex: "quantity",
              title: "Quantity",
              colProps: {
                md: 8,
              },
            },
            {
              title: "Add/Subtract",
              dataIndex: "add_subtract",
              valueType: "switch",
              colProps: {
                md: 8,
              },
              fieldProps: {
                checkedChildren: "Add",
                unCheckedChildren: "Subtract",
              },
            },
          ],
        },
      ],
      render: (record, { items }) => {
        let allItems = "";
        items &&
          items.map(
            (item) =>
            (allItems = item
              ? allItems + item.product.label + ", "
              : allItems)
          );
        return allItems;
      },
    },
    {
      valueType: "textarea",
      dataIndex: "description",
      title: "Description, Remarks or Notes",
    },
  ],
};
