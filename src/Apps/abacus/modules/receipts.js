

export const receipts = {
  name: "Receipts",
  icon: "DollarOutlined",
  path: "/sales/receipts",
  parent: "sales",
  collection: "receipts",
  singular: "Receipt",
  columns: [
    {
      title: "Invoice",
      dataIndex: "invoice",
      type: "dbSelect",
      collection: "invoices",
      label: ["_id"],
      isRequired: true,
      isPrintable: true,
    },
    {
      title: "Payment Date",
      dataIndex: "date",
      valueType: "date",
      isRequired: true,
      isPrintable: true,
      noBackDate: true,
    },
    {
      type: "dbSelect",
      label: ["name"],
      dataIndex: "account",
      title: "Account",
      collection: "accounts",
      isRequired: true,
      filters: true,
      onFilter: true,
      isPrintable: true,
    },
    {
      title: "Method Of Payment",
      dataIndex: "method_of_payment",
      sorter: true,
      hideInTable: true,
      valueType: "select",
      isRequired: true,
      isPrintable: true,
      width: "lg",
      valueEnum: {
        Cash: {
          text: "Cash",
        },
        Cheque: {
          text: "Cheque",
        },
        Bank: {
          text: "Bank",
        },
        "Airtel Money": {
          text: "Airtel Money",
        },
        "MTN Mobile Money": {
          text: "MTN Mobile Money",
        },
      },
    },
    {
      title: "Amount Paid",
      dataIndex: "amount",
      isRequired: true,
      isPrintable: true,
      type: "money",
    },
    {
      title: "Description",
      dataIndex: "description",
      valueType: "textarea",
      width: "lg",
      isPrintable: true,
      colProps: {
        md: 24,
      },
    },
  ],
};
