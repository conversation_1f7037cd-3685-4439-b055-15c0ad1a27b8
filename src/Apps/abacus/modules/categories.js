

export const categories = {
  name: "Categories",
  icon: "GroupOutlined",
  path: "/product_management/categories",
  parent: "product_management",
  collection: "categories",
  singular: "category",
  multi_Branch: true,
  columns: [
    {
      title: "Name",
      dataIndex: "name",
      tip: "Category name",
      valueType: "text",
      isRequired: true,
      formItemProps: {
        rules: [
          {
            required: true,
            message: "This field is needed",
          },
        ],
      },
    },
    {
      dataIndex: "code",
      title: "Category Code",
      valueType: "text",
      hideInTable: true,
    },
    {
      dataIndex: "products",
      title: "Products",
      valueType: "text",
      hideInForm: true,
    },
  ],
};
