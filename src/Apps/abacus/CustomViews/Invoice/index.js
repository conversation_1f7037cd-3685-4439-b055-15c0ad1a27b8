import "./css.css";
import DocumentFooter from "../DocumentFooter";
import PrintComponents from "react-print-components";
import React, { useEffect, useState } from "react";
import RenderBlob from "../../../../Components/RenderBlob";
import moment from "moment";
import { Float<PERSON>utton, Flex, Divider, Button, message, Spin } from "antd";
import { PrinterOutlined, FileTextOutlined } from "@ant-design/icons";
import { numberFormat } from "../../../../Utils/functions";


const Invoice = (props) => {
  const { data, title = "invoice", bottomText = "", pouchDatabase, databasePrefix } = props;

  console.log('Invoice props:', {
    hasData: !!data,
    hasPouchDatabase: typeof pouchDatabase === 'function',
    databasePrefix,
    dataCustomer: data?.customer
  });



  // Add default value for appSettings
  const appSettings = JSON.parse(localStorage.getItem("APP_SETTINGS")) || {
    documentHeader: "V1", // Set a default header version
    currency: "UGX",      // Set a default currency
  };

  const subTotal = data.items.reduce(
    (acc, item) => acc + item.price * item.quantity,
    0
  );
  const discount = data.discounted ? data.discount : 0;
  const tax = data.taxable ? subTotal * 0.18 : 0;
  const grandTotal = subTotal + tax - discount;

  const [company, setCompany] = useState(null);
  const [customer, setCustomer] = useState(null);
  const [products, setProducts] = useState([]);
  const [units, setUnits] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchProducts = async () => {
      try {
        const productsData = await pouchDatabase("products", databasePrefix).getAllData();
        // Transform data to match the expected format (with rows structure)
        const transformedData = productsData.filter(doc => !doc._id.startsWith("_")).map(doc => ({
          id: doc._id,
          doc: doc
        }));
        setProducts(transformedData);
      } catch (error) {
        console.error("Error fetching products:", error);
        setProducts([]);
      }
    };

    fetchProducts();
  }, [pouchDatabase, databasePrefix]);
  useEffect(() => {
    const fetchCustomer = async () => {
      if (data && data.customer) {
        try {
          const customerId = data.customer.value || data.customer._id;
          const customerData = await pouchDatabase("customers", databasePrefix).getDocument(customerId);
          setCustomer(customerData);
        } catch (error) {
          console.error("Error fetching customer:", error);
          setCustomer(null);
        }
      }
    };

    fetchCustomer();
  }, [data, pouchDatabase, databasePrefix]);

  useEffect(() => {
    const fetchUnits = async () => {
      try {
        const unitsData = await pouchDatabase("units", databasePrefix).getAllData();
        setUnits(unitsData.filter(doc => !doc._id.startsWith("_")));
      } catch (error) {
        console.error("Error fetching units:", error);
        setUnits([]);
      }
    };

    fetchUnits();
  }, [pouchDatabase, databasePrefix]);


  useEffect(() => {
    const fetchCompany = async () => {
      try {
        const organizationsData = await pouchDatabase("organizations", databasePrefix).getAllData();
        if (organizationsData && organizationsData.length > 0) {
          let comp = organizationsData[0];

          if (comp._attachments && comp._attachments.logo) {
            try {
              const logoBlob = await pouchDatabase("organizations", databasePrefix).getAttachment(comp._id, "logo");
              comp.logo = logoBlob;
              setCompany(comp);
            } catch (attachmentError) {
              console.error("Error fetching logo attachment:", attachmentError);
              setCompany(comp);
            }
          } else {
            setCompany(comp);
          }
        } else {
          console.warn("No organization data found");
          setCompany(null);
        }
      } catch (error) {
        console.error("Error fetching organization:", error);
        setCompany(null);
      }
    };

    fetchCompany();
  }, [pouchDatabase, databasePrefix]);

  // Update loading state when essential data is loaded
  useEffect(() => {
    // Essential data: company, customer, and products
    // Units can be empty, so we don't require them
    if (company && customer && products.length >= 0) {
      setLoading(false);
    }
  }, [company, customer, products, units]);

  const emptyRows = [];
  // Safely access documentHeader with optional chaining
  for (
    let index = 0;
    index < (appSettings?.documentHeader === "V2" ? 28 : 24) - data.items.length;
    index++
  ) {
    emptyRows.push({});
  }

  if (loading) {
    return (
      <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '400px' }}>
        <Spin size="large" tip="Loading invoice data..." />
      </div>
    );
  }

  return (
    // <div className="tm_invoice_wrap">
    //   <div className="tm_invoice tm_style1" id="tm_download_section">
    <div>
      {appSettings?.documentHeader === "V2" && (
        <>
          <Flex vertical={false} justify="space-between">
            <RenderBlob blob={company.logo} size={150} />
            <div>
              <strong style={{ fontSize: 25, fontWeight: "bold" }}>
                {title.toUpperCase()}
              </strong>
              <p class="tm_invoice_number tm_m0 tm_f11">
                {title} No: <b class="tm_primary_color">{data._id}</b>
                <br /> Date:{" "}
                <b class="tm_primary_color">
                  {moment(data.date).format("DD MMM YYYY")}
                </b>
              </p>
            </div>
            <p class="tm_mb2 tm_f12">
              {company.address}
              <br />
              Tel: {company.phone} / {company.alternative_phone} <br />
              Email: {company.email} <br />
              {company.website && company.website}
            </p>
          </Flex>
          <Divider />
          <Flex vertical={false} justify="space-between">
            <div class="tm_mb2 tm_f10">
              <p class="tm_mb2">
                <b class="tm_primary_color">To:</b>
              </p>
              <p>
                <b style={{ fontSize: 15 }}>{customer.name}</b>
                <br />
                {customer.phone}{" "}
                {customer.alternative_phone &&
                  `/ ${customer.alternative_phone}`}{" "}
                <br />
                {customer.email && (
                  <>
                    {customer.email} <br />
                  </>
                )}
                {customer.address && (
                  <>
                    {customer.address} <br />
                  </>
                )}
              </p>
            </div>
          </Flex>
        </>
      )}

      {(!appSettings?.documentHeader ||
        appSettings?.documentHeader === "V1") && (
          <Flex vertical={true}>
            <center>
              <RenderBlob blob={company.logo} size={250} />
              <p class="tm_f12">
                {company.address}
                <br />
                Tel: {company.phone} / {company.alternative_phone} <br />
                Email: {company.email} <br />
                {company.website && company.website}
              </p>
            </center>
            <Flex vertical={false} justify="space-between">
              <div>
                <p class="tm_mb2 tm_f11">
                  <b class="tm_primary_color">To:</b>
                </p>
                <p>
                  <b style={{ fontSize: 15 }}>{customer.name}</b>
                  <br />
                  {customer.phone}{" "}
                  {customer.alternative_phone &&
                    `/ ${customer.alternative_phone}`}{" "}
                  <br />
                  {customer.email && (
                    <>
                      {customer.email} <br />
                    </>
                  )}
                  {customer.address && (
                    <>
                      {customer.address} <br />
                    </>
                  )}
                  {customer._id && (
                    <>
                      Customer ID : <strong>{customer._id} <br /></strong>
                    </>
                  )}
                </p>
              </div>
              <div>
                <strong style={{ fontSize: 30, fontWeight: "bold" }}>
                  {title.toUpperCase()}
                </strong>
                <p class="tm_invoice_number tm_m0">
                  {title} No: <b class="tm_primary_color">{data._id}</b>
                </p>
                <p class="tm_invoice_date tm_m0">
                  Date:{" "}
                  <b class="tm_primary_color">
                    {moment(data.date).format("DD MMM YYYY")}
                  </b>
                </p>
              </div>
            </Flex>
          </Flex>
        )}
      <div className="tm_table tm_style">
        <center style={{ minHeight: 430 }}>
          <div
            className="tm_border"
          // style={{ width: 500 }}
          >
            <div className="main">
              <table>
                <thead>
                  <tr>
                    <th className="tm_width_1 tm_semi_bold tm_primary_color tm_gray_bg tm_text_center">
                      S/N
                    </th>
                    <th className="tm_width_3 tm_semi_bold tm_primary_color tm_gray_bg">
                      Description
                    </th>
                    <th className="tm_width_1 tm_semi_bold tm_primary_color tm_gray_bg tm_text_left">
                      Qty
                    </th>
                    <th className="tm_width_1 tm_semi_bold tm_primary_color tm_gray_bg tm_text_right">
                      Rate
                    </th>
                    <th className="tm_width_1 tm_semi_bold tm_primary_color tm_gray_bg tm_text_right">
                      Total
                    </th>
                  </tr>
                </thead>
                <tbody className="tm_border_none" style={{ border: "none", fontSize: 10 }}>
                  {data.items && data.items.map((item, index) => {
                    const PartUnit = products && products.find(
                      (part) => part.id === item.product.value
                    );

                    const unit =
                      PartUnit && PartUnit.doc && PartUnit.doc.unit && units
                        ? units.find(
                          (u) => u._id === PartUnit.doc.unit.value
                        )?.abbreviation
                        : "";

                    return (
                      <tr className="main" key={`item-${index}`}>
                        <td className="tm_width_1 tm_text_center">
                          {index + 1}
                        </td>
                        <td className="tm_width_3">
                          {
                            (() => {
                              const product = products && products.find((p) => p.id === item.product.value)?.doc;
                              return product ? (product.name + (product.sku ? ` (${product.sku})` : '')) : 'Unknown Product';
                            })()
                          }
                        </td>
                        <td className="tm_width_1 tm_text_left">
                          {item.quantity}{" "}
                          {item.quantity === 1 || unit === ""
                            ? unit
                            : unit + "s"}
                        </td>
                        <td className="tm_width_1 tm_text_right">
                          {numberFormat(item.price)}
                        </td>
                        <td className="tm_width_1 tm_text_right">
                          {numberFormat(item.price * item.quantity)}
                        </td>
                      </tr>
                    );
                  })}
                  {emptyRows.map((_, index) => {
                    return (
                      <tr className="main" key={`empty-row-${index}`}>
                        <td className="tm_width_1 tm_text_center">
                          {" "}
                          &nbsp;
                        </td>
                        <td className="tm_width_3"></td>
                        <td className="tm_width_1 tm_text_left"></td>
                        <td className="tm_width_1 tm_text_right"></td>
                        <td className="tm_width_1 tm_text_right"></td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            </div>
          </div>
        </center>
        <div className="tm_invoice_footer tm_mb20 tm_m0_md">
          <div className="tm_left_footer">
            {appSettings.mobile_wallet_settings && (
              <p className="tm_mb2 tm_f12">
                <b className="tm_primary_color">
                  Mobile Wallet Payment Method:{" "}
                </b>
                <br />
                {appSettings.mobile_wallet_settings.map((item) => (
                  <div>
                    {item.mobile_wallet} : <b>{item.id}</b>
                  </div>
                ))}
                <br />
              </p>
            )}
            {appSettings.acc_no && (
              <p className="tm_m0 tm_f12">
                <b className="tm_primary_color">Bank Details: </b>
                <br />
                {appSettings.acc_name} : <b>{appSettings.acc_no}</b>
                <br />
                {appSettings.bank_name} - {appSettings.bank_address} Branch
              </p>
            )}
          </div>
          <div className="tm_right_footer">
            <table>
              <tbody>
                <tr>
                  <td className="tm_width_3 tm_primary_color tm_border_none tm_bold tm_f15">
                    Subtotal
                  </td>
                  <td className="tm_width_3 tm_primary_color tm_text_right tm_border_none tm_bold tm_f15">
                    {appSettings.currency} {numberFormat(subTotal)}
                  </td>
                </tr>
                <tr>
                  <td className="tm_width_3 tm_primary_color tm_border_none tm_pt0 tm_f15">
                    Tax <span className="tm_ternary_color">(18%)</span>
                  </td>
                  <td className="tm_width_3 tm_primary_color tm_text_right tm_border_none tm_pt0 tm_f15">
                    {appSettings.currency} {numberFormat(tax)}
                  </td>
                </tr>
                {data.discounted && (
                  <tr>
                    <td className="tm_width_3 tm_primary_color tm_border_none tm_pt0 tm_f15">
                      Discount
                      {data.discount_description && (
                        <span className="tm_secondary_color">{` (${data.discount_description})`}</span>
                      )}
                    </td>
                    <td className="tm_width_3 tm_primary_color tm_text_right tm_border_none tm_pt0 tm_f15">
                      {appSettings.currency} {numberFormat(discount)}
                    </td>
                  </tr>
                )}
                <tr className="tm_border_top tm_border_bottom">
                  <td className="tm_width_3 tm_border_top_0 tm_bold tm_f15 tm_primary_color">
                    Grand Total{" "}
                  </td>
                  <td className="tm_width_3 tm_border_top_0 tm_bold tm_f15 tm_primary_color tm_text_right">
                    {appSettings.currency} {numberFormat(grandTotal)}
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
        <center>
          <strong style={{ fontSize: "11px" }}>{bottomText}</strong>
        </center>
        <div className="tm_invoice_footer tm_type1">
          <DocumentFooter />
        </div>
      </div>
    </div>
    // </div>
    // </div>
  );
};

const PrintableInvoice = (props) => {
  return (
    <>
      <PrintComponents
        trigger={
          <FloatButton icon={<PrinterOutlined />} tooltip={<div>Print</div>} />
        }
      >
        <Invoice {...props} />
      </PrintComponents>
      <Invoice {...props} />
    </>
  );
};

export default PrintableInvoice;