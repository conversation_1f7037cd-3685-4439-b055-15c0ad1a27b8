import "./styles.css";
import PrintComponents from "react-print-components";
import React, { useState, useEffect } from "react";
import moment from "moment";
import { Card, Row, Col, Typography, Descriptions, Table, Tag, Button, Space, Avatar, Skeleton, Badge, Tooltip, Statistic } from "antd";
import { OrderedListOutlined, DollarOutlined, UserOutlined, CalendarOutlined, PrinterOutlined, ShopOutlined, InfoCircleOutlined, ShoppingCartOutlined } from "@ant-design/icons";
import { PageHeader } from "@ant-design/pro-components";
import { numberFormat } from "../../../../Utils/functions";


const { Title, Text } = Typography;

const StockOrder = ({ data, pouchDatabase, databasePrefix }) => {
  const [loading, setLoading] = useState(true);
  const [supplier, setSupplier] = useState(null);
  const [purchaseOrder, setPurchaseOrder] = useState(null);

  // Format date for display
  const formatDate = (dateString) => {
    return moment(dateString).format("DD MMM YYYY");
  };

  // Calculate order statistics
  const calculateStatistics = () => {
    // Make sure items is an array before using reduce
    const items = Array.isArray(data.items) ? data.items : [];

    const total = items.reduce(
      (sum, item) => {
        // Make sure quantity and price are numbers
        const quantity = typeof item.quantity === 'number' ? item.quantity : 0;
        const price = typeof item.price === 'number' ? item.price : 0;
        return sum + quantity * price;
      },
      0
    );

    let status = "Draft";
    let statusColor = "default";

    switch (data.status) {
      case "approved":
        status = "Approved";
        statusColor = "processing";
        break;
      case "converted":
        status = "Converted to Stock Purchasing";
        statusColor = "success";
        break;
      case "cancelled":
        status = "Cancelled";
        statusColor = "error";
        break;
      default:
        status = "Draft";
        statusColor = "default";
    }

    return {
      total,
      status,
      statusColor
    };
  };

  const statistics = calculateStatistics();

  // Fetch supplier details
  useEffect(() => {
    const fetchData = async () => {
      try {
        // Fetch supplier details
        if (data.supplier && data.supplier.value) {
          const supplierDB = pouchDatabase("suppliers", databasePrefix);
          const supplierData = await supplierDB.get(data.supplier.value);
          setSupplier(supplierData);
        }

        // Fetch stock purchasing entry if this stock order has been converted
        if (data.status === "converted" && data.stockPurchasingId) {
          const stockPurchasingDb = pouchDatabase("stock_purchasing", databasePrefix);
          try {
            const stockPurchasingData = await stockPurchasingDb.get(data.stockPurchasingId);
            setPurchaseOrder(stockPurchasingData);
          } catch (error) {
            console.error("Error fetching stock purchasing entry:", error);
          }
        }

        setLoading(false);
      } catch (error) {
        console.error("Error fetching data:", error);
        setLoading(false);
      }
    };

    fetchData();
  }, [data, pouchDatabase, databasePrefix]);

  // Printable version of the stock order
  const PrintableStockOrder = () => (
    <div className="print-container">
      <div className="print-header">
        <h1>Stock Order #{data._id}</h1>
        <p>Date: {formatDate(data.date)}</p>
        <p>Expected Delivery: {data.expected_delivery_date ? formatDate(data.expected_delivery_date) : "Not specified"}</p>
      </div>

      <div className="print-supplier">
        <h2>Supplier</h2>
        <p>{supplier?.name}</p>
        <p>{supplier?.address}</p>
        <p>Phone: {supplier?.phone}</p>
      </div>

      <div className="print-items">
        <h2>Order Items</h2>
        <table className="print-table">
          <thead>
            <tr>
              <th>Product</th>
              <th>Quantity</th>
              <th>Price</th>
              <th>Total</th>
            </tr>
          </thead>
          <tbody>
            {Array.isArray(data.items) ? data.items.map((item, index) => (
              <tr key={index}>
                <td>{item.product?.label || 'Unknown Product'}</td>
                <td>{item.quantity || 0}</td>
                <td>{numberFormat(item.price || 0)}</td>
                <td>{numberFormat((item.quantity || 0) * (item.price || 0))}</td>
              </tr>
            )) : <tr><td colSpan="4">No items</td></tr>}
          </tbody>
          <tfoot>
            <tr>
              <td colSpan="3" style={{ textAlign: 'right' }}><strong>Total:</strong></td>
              <td><strong>{numberFormat(statistics.total)}</strong></td>
            </tr>
          </tfoot>
        </table>
      </div>

      {data.notes && (
        <div className="print-notes">
          <h2>Notes</h2>
          <p>{data.notes}</p>
        </div>
      )}
    </div>
  );

  return (
    <>
      <PageHeader
        className="site-page-header-responsive"
        onBack={() => window.history.back()}
        title={`Stock Order #${data._id}`}
        subTitle={formatDate(data.date)}
        tags={[
          <Tag color={statistics.statusColor} key="status">
            {statistics.status}
          </Tag>
        ]}
        extra={[
          <PrintComponents
            key="print"
            trigger={
              <Button icon={<PrinterOutlined />} type="primary">
                Print Stock Order
              </Button>
            }
          >
            <PrintableStockOrder />
          </PrintComponents>,
        ]}
      />

      <div style={{ padding: "0 24px 24px" }}>
        {loading ? (
          <Row gutter={[24, 24]}>
            <Col span={24}>
              <Skeleton active paragraph={{ rows: 6 }} />
            </Col>
          </Row>
        ) : (
          <Row gutter={[24, 24]}>
            <Col xs={24} lg={8}>
              <Card
                title={<><InfoCircleOutlined /> Order Information</>}
                className="stock-card"
              >
                <Descriptions column={1} size="small" bordered>
                  <Descriptions.Item label="Order Number">{data._id}</Descriptions.Item>
                  <Descriptions.Item label="Date">{formatDate(data.date)}</Descriptions.Item>
                  <Descriptions.Item label="Expected Delivery">
                    {data.expected_delivery_date ? formatDate(data.expected_delivery_date) : "Not specified"}
                  </Descriptions.Item>
                  <Descriptions.Item label="Status">
                    <Badge
                      status={statistics.statusColor}
                      text={statistics.status}
                    />
                  </Descriptions.Item>
                  {data.entrant && (
                    <Descriptions.Item label="Created By">{data.entrant.label}</Descriptions.Item>
                  )}
                  {data.status === "converted" && data.stockPurchasingId && (
                    <Descriptions.Item label="Stock Purchasing">
                      <a href={`#/inventory/stock_purchasing/${data.stockPurchasingId}`}>
                        {data.stockPurchasingId}
                      </a>
                    </Descriptions.Item>
                  )}
                </Descriptions>
              </Card>

              <Card
                title={<><UserOutlined /> Supplier Information</>}
                className="stock-card"
                style={{ marginTop: 16 }}
              >
                {supplier ? (
                  <Descriptions column={1} size="small" bordered>
                    <Descriptions.Item label="Name">{supplier.name}</Descriptions.Item>
                    {supplier.address && (
                      <Descriptions.Item label="Address">{supplier.address}</Descriptions.Item>
                    )}
                    {supplier.phone && (
                      <Descriptions.Item label="Phone">{supplier.phone}</Descriptions.Item>
                    )}
                    {supplier.email && (
                      <Descriptions.Item label="Email">{supplier.email}</Descriptions.Item>
                    )}
                  </Descriptions>
                ) : (
                  <Text type="secondary">No supplier information available</Text>
                )}
              </Card>

              <Card
                title={<><DollarOutlined /> Order Summary</>}
                className="stock-card"
                style={{ marginTop: 16 }}
              >
                <Statistic
                  title="Total Amount"
                  value={statistics.total}
                  precision={2}
                  prefix="$"
                  style={{ marginBottom: 16 }}
                />
                <Statistic
                  title="Total Items"
                  value={Array.isArray(data.items) ? data.items.length : 0}
                  prefix={<ShoppingCartOutlined />}
                />
              </Card>
            </Col>

            <Col xs={24} lg={16}>
              <Card
                title={<><OrderedListOutlined /> Order Items</>}
                className="stock-card"
              >
                <Table
                  dataSource={Array.isArray(data.items) ? data.items : []}
                  rowKey={(record, index) => index}
                  pagination={false}
                  columns={[
                    {
                      title: 'Product',
                      dataIndex: 'product',
                      key: 'product',
                      render: product => (
                        <Space>
                          <Avatar
                            shape="square"
                            size="small"
                            icon={<ShopOutlined />}
                            style={{ backgroundColor: '#1890ff' }}
                          />
                          <Tooltip title={`View product details: ${product.label}`}>
                            <a href={`#/product_management/products/${product.value}`}>{product.label}</a>
                          </Tooltip>
                        </Space>
                      )
                    },
                    {
                      title: 'Quantity',
                      dataIndex: 'quantity',
                      key: 'quantity',
                      align: 'right',
                      width: '15%'
                    },
                    {
                      title: 'Unit Price',
                      dataIndex: 'price',
                      key: 'price',
                      align: 'right',
                      width: '20%',
                      render: price => numberFormat(price)
                    },
                    {
                      title: 'Total',
                      key: 'total',
                      align: 'right',
                      width: '20%',
                      render: (_, record) => numberFormat(record.quantity * record.price)
                    }
                  ]}
                  summary={() => (
                    <Table.Summary fixed>
                      <Table.Summary.Row>
                        <Table.Summary.Cell index={0} colSpan={3} align="right">
                          <strong>Total:</strong>
                        </Table.Summary.Cell>
                        <Table.Summary.Cell index={1} align="right">
                          <strong>{numberFormat(statistics.total)}</strong>
                        </Table.Summary.Cell>
                      </Table.Summary.Row>
                    </Table.Summary>
                  )}
                />
              </Card>

              {data.notes && (
                <Card
                  title={<><CalendarOutlined /> Notes</>}
                  className="stock-card"
                  style={{ marginTop: 16 }}
                >
                  <Text>{data.notes}</Text>
                </Card>
              )}
            </Col>
          </Row>
        )}
      </div>
    </>
  );
};

export default StockOrder;