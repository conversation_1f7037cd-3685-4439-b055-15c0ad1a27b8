import React, { useMemo } from "react";
import moment from "moment";
import { Area, Column } from "@ant-design/plots";
import { Divider, Table, Card, Row, Col, Tabs, Typography, Tag, Space, Progress, Badge, Avatar, Empty, Descriptions } from "antd";
import { ProDescriptions, StatisticCard } from "@ant-design/pro-components";
import { ShoppingOutlined, BarChartOutlined, HistoryOutlined, InfoCircleOutlined, RiseOutlined, FallOutlined, ExclamationCircleOutlined, CheckCircleOutlined } from "@ant-design/icons";
import { numberFormat } from "../../../../Utils/functions";


const { Statistic } = StatisticCard;

const Product = (props) => {
  const { columns, data, singular } = props;

  // Calculate current stock level with explicit number conversion
  const currentStock = useMemo(() => {
    const stoked = Number(data.stoked || 0);
    const sold = Number(data.sold || 0);
    const returned = Number(data.returned || 0);
    const returned_stock = Number(data.returned_stock || 0);
    const adjustments = Number(data.adjustments || 0);

    return stoked - sold + returned - returned_stock + adjustments;
  }, [data]);

  // Calculate stock value
  const stockValue = useMemo(() => {
    return currentStock * (data.cost || 0);
  }, [currentStock, data.cost]);

  // Calculate potential revenue
  const potentialRevenue = useMemo(() => {
    return currentStock * (data.sell || 0);
  }, [currentStock, data.sell]);

  // Calculate potential profit
  const potentialProfit = useMemo(() => {
    return potentialRevenue - stockValue;
  }, [potentialRevenue, stockValue]);

  // Calculate profit margin percentage
  const profitMarginPercentage = useMemo(() => {
    if (stockValue === 0) return 0;
    return (potentialProfit / stockValue) * 100;
  }, [potentialProfit, stockValue]);

  // Determine stock status
  const stockStatus = useMemo(() => {
    if (!data.stock_alert) return "normal";
    if (currentStock <= 0) return "out_of_stock";
    if (currentStock <= data.stock_alert) return "low_stock";
    return "normal";
  }, [currentStock, data.stock_alert]);

  // Create product movement history with all types of transactions
  let product_movement = [
    // Stock purchases (in)
    ...data.stock_list.map((s) => ({
      date: s.date,
      in: s.quantity,
      price: s.price ? s.price : s.total_cost / s.quantity,
      id: `SP-${s._id}`,
      type: 'Purchase'
    })),

    // Products returned by customers (in)
    ...data.returned_list.map((s) => ({
      date: s.date,
      in: s.quantity,
      price: s.price,
      id: `RG-${s._id}`,
      type: 'Customer Return'
    })),

    // Products sold (out)
    ...data.sold_list.map((s) => ({
      date: s.date,
      out: Number(s.quantity),
      price: s.price,
      id: `IV-${s._id || s.value}`,
      type: 'Sale'
    })),

    // Stock returned to suppliers (out)
    ...(data.returned_stoked_list || []).map((s) => ({
      date: s.date,
      out: Number(s.quantity),
      price: s.price,
      id: `RS-${s._id}`,
      type: 'Supplier Return'
    })),

    // Inventory adjustments (in/out based on add_subtract flag)
    ...(data.adjustments_list || []).map((s, index) => ({
      date: s.date,
      in: s.add_subtract ? Number(s.quantity) : undefined,
      out: !s.add_subtract ? Number(s.quantity) : undefined,
      price: s.price || 0,
      id: `ADJ-${s._id || s.id || `ADJ${index + 1}`}`,
      type: s.add_subtract ? 'Adjustment (+)' : 'Adjustment (-)'
    })),
  ];

  // Normalize all transaction data and ensure proper date/quantity parsing
  product_movement = product_movement.map((entry, index) => ({
    ...entry,
    date: new Date(entry.date),
    in: entry.in ? Number(entry.in) : 0,
    out: entry.out ? Number(entry.out) : 0,
    sortKey: `${new Date(entry.date).getTime()}_${entry.id}_${index}`
  }));

  // Sort by date (oldest first), then by ID for consistent ordering of same-date transactions
  product_movement = product_movement.sort((a, b) => {
    const dateCompare = a.date.getTime() - b.date.getTime();
    if (dateCompare !== 0) return dateCompare;
    return a.id.localeCompare(b.id);
  });

  // Calculate running balance sequentially from oldest to newest
  let runningBalance = 0;

  product_movement = product_movement.map((entry) => {
    const netChange = entry.in - entry.out;
    runningBalance += netChange;

    return {
      ...entry,
      stock: runningBalance,
      date: entry.date.toISOString()
    };
  });








  // Prepare data for stock trend chart
  const stockTrendData = useMemo(() => {
    return product_movement.map(entry => ({
      date: moment(new Date(entry.date)).format('YYYY-MM-DD'),
      stock: entry.stock
    }));
  }, [product_movement]);

  // Prepare data for movement chart
  const movementChartData = useMemo(() => {
    const last10Movements = [...product_movement]
      .reverse()
      .slice(0, 10)
      .reverse();

    return last10Movements.map(entry => ({
      date: moment(new Date(entry.date)).format('MM/DD'),
      value: entry.in || (entry.out ? -entry.out : 0),
      type: entry.in ? 'In' : 'Out'
    }));
  }, [product_movement]);

  // Format date for display
  const formatDate = (dateString) => {
    // Handle both string dates and ISO string dates
    return moment(new Date(dateString)).format('MMM DD, YYYY');
  };

  return (
    <div>
      <Row gutter={[16, 16]}>
        <Col xs={24} md={6}>
          <Card className="product-image-card" style={{ height: '100%' }}>
            <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center', height: '100%' }}>
              <Avatar
                shape="square"
                size={120}
                icon={<ShoppingOutlined />}
                style={{ backgroundColor: '#1890ff', marginBottom: 16 }}
              />
              <Typography.Title level={4} style={{ margin: 0, textAlign: 'center' }}>
                {data.name}
              </Typography.Title>
              {data.sku && (
                <Typography.Text type="secondary" style={{ textAlign: 'center' }}>
                  SKU: {data.sku}
                </Typography.Text>
              )}
              <Space style={{ marginTop: 16 }}>
                {stockStatus === "out_of_stock" && (
                  <Tag color="error" icon={<ExclamationCircleOutlined />}>Out of Stock</Tag>
                )}
                {stockStatus === "low_stock" && (
                  <Tag color="warning" icon={<ExclamationCircleOutlined />}>Low Stock</Tag>
                )}
                {stockStatus === "normal" && (
                  <Tag color="success" icon={<CheckCircleOutlined />}>In Stock</Tag>
                )}
                {data.category && (
                  <Tag color="blue">{data.category.label}</Tag>
                )}
                {data.brand && (
                  <Tag color="purple">{data.brand.label}</Tag>
                )}
              </Space>
            </div>
          </Card>
        </Col>

        <Col xs={24} md={18}>
          <Row gutter={[16, 16]}>
            <Col xs={24} sm={12} lg={6}>
              <StatisticCard
                statistic={{
                  title: 'Current Stock',
                  value: currentStock,
                  suffix: data.units || 'pc',
                  description: data.stock_alert ? `Min. Stock: ${data.stock_alert}` : 'No min. stock set',
                }}
              />
            </Col>
            <Col xs={24} sm={12} lg={6}>
              <StatisticCard
                statistic={{
                  title: 'Stock Value',
                  value: numberFormat(stockValue),
                  prefix: '',
                  description: `Cost: ${numberFormat(data.cost || 0)} per unit`,
                }}
              />
            </Col>
            <Col xs={24} sm={12} lg={6}>
              <StatisticCard
                statistic={{
                  title: 'Selling Price',
                  value: numberFormat(data.sell || 0),
                  prefix: '',
                  description: `Potential Revenue: ${numberFormat(potentialRevenue)}`,
                }}
              />
            </Col>
            <Col xs={24} sm={12} lg={6}>
              <StatisticCard
                statistic={{
                  title: 'Profit Margin',
                  value: profitMarginPercentage.toFixed(1),
                  suffix: '%',
                  description: (
                    <span>
                      {profitMarginPercentage > 0 ? (
                        <RiseOutlined style={{ color: '#52c41a' }} />
                      ) : (
                        <FallOutlined style={{ color: '#f5222d' }} />
                      )}
                      {' '}
                      Potential Profit: {numberFormat(potentialProfit)}
                    </span>
                  ),
                }}
              />
            </Col>
          </Row>
        </Col>
        <Col xs={24} md={24}>

          <Tabs defaultActiveKey="details" type="card" style={{ marginTop: 16, width: '100%' }} size="large">
            <Tabs.TabPane
              tab={<span><InfoCircleOutlined /> Details</span>}
              key="details"
            >
              <Card bordered={false}>
                <Row gutter={[24, 24]}>
                  <Col span={24}>
                    <Typography.Title level={5} style={{ marginTop: 0 }}>
                      Product Information
                    </Typography.Title>
                    <Divider style={{ margin: '12px 0' }} />
                  </Col>

                  <Col xs={24} md={12}>
                    <Card title="Basic Information" bordered={false} size="small">
                      <Descriptions column={1} bordered size="small">
                        <Descriptions.Item label="Name">{data.name}</Descriptions.Item>
                        {data.sku && <Descriptions.Item label="SKU/Item No">{data.sku}</Descriptions.Item>}
                        {data.category && <Descriptions.Item label="Category">{data.category.label}</Descriptions.Item>}
                        {data.brand && <Descriptions.Item label="Brand">{data.brand.label}</Descriptions.Item>}
                        {data.country && <Descriptions.Item label="Country of Origin">{data.country && data.country.label}</Descriptions.Item>}
                        <Descriptions.Item label="Unit">{data.units || 'pc'}</Descriptions.Item>
                        {data.measurements && <Descriptions.Item label="Measurements">{data.measurements}</Descriptions.Item>}
                      </Descriptions>
                    </Card>
                  </Col>

                  <Col xs={24} md={12}>
                    <Card title="Pricing & Stock" bordered={false} size="small">
                      <Descriptions column={1} bordered size="small">
                        <Descriptions.Item label="Cost Price">{numberFormat(data.cost || 0)}</Descriptions.Item>
                        <Descriptions.Item label="Selling Price">{numberFormat(data.sell || 0)}</Descriptions.Item>
                        <Descriptions.Item label="Profit">{numberFormat((data.sell || 0) - (data.cost || 0))}</Descriptions.Item>
                        <Descriptions.Item label="Profit Margin">{profitMarginPercentage.toFixed(1)}%</Descriptions.Item>
                        <Descriptions.Item label="Current Stock">{currentStock} {data.units || 'pc'}</Descriptions.Item>
                        <Descriptions.Item label="Stock Value">{numberFormat(stockValue)}</Descriptions.Item>
                      </Descriptions>
                    </Card>
                  </Col>

                  {data.description && (
                    <Col span={24}>
                      <Card title="Description" bordered={false} size="small">
                        <Typography.Paragraph>
                          {data.description}
                        </Typography.Paragraph>
                      </Card>
                    </Col>
                  )}

                  <Col span={24}>
                    <Card title="Additional Information" bordered={false} size="small">
                      <Descriptions column={2} bordered size="small">
                        <Descriptions.Item label="Bin Location">{data.bin_location || 'Not specified'}</Descriptions.Item>
                        <Descriptions.Item label="Minimum Stock">{data.stock_alert || 'Not set'}</Descriptions.Item>
                        <Descriptions.Item label="External Product">{data.external ? 'Yes' : 'No'}</Descriptions.Item>
                        <Descriptions.Item label="Ideal Selling Price">{numberFormat(data.cost + (33 / 100) * data.cost)}</Descriptions.Item>
                      </Descriptions>
                    </Card>
                  </Col>
                </Row>
              </Card>
            </Tabs.TabPane>

            <Tabs.TabPane
              tab={<span><HistoryOutlined /> Movement History</span>}
              key="movement"
            >
              <Card bordered={false}>
                <Table
                  dataSource={[...product_movement].reverse()}
                  pagination={false}
                  scroll={{ y: 400 }}
                  rowKey={(record) => record.sortKey || record.id}
                  columns={[
                    {
                      dataIndex: "date",
                      title: "Date",
                      render: (text) => formatDate(text),
                      sorter: (a, b) => new Date(a.date) - new Date(b.date),
                      defaultSortOrder: 'descend',
                      sortDirections: ['descend', 'ascend'],
                      width: 120
                    },
                    {
                      title: "Reference",
                      dataIndex: "id",
                      width: 120
                    },
                    {
                      title: "Transaction Type",
                      dataIndex: "type",
                      width: 150,
                      render: (type) => {
                        let color = 'default';
                        if (type === 'Purchase') color = 'blue';
                        if (type === 'Sale') color = 'orange';
                        if (type === 'Customer Return') color = 'green';
                        if (type === 'Supplier Return') color = 'red';
                        if (type === 'Adjustment (+)') color = 'cyan';
                        if (type === 'Adjustment (-)') color = 'magenta';

                        return <Tag color={color}>{type || 'Unknown'}</Tag>;
                      }
                    },
                    {
                      dataIndex: "in",
                      title: "Stock In",
                      render: (value, record) =>
                        record.in ? (
                          <Tag color="success">
                            {value} @ {numberFormat(record.price || 0)}
                          </Tag>
                        ) : "-",
                    },
                    {
                      dataIndex: "out",
                      title: "Stock Out",
                      render: (value, record) =>
                        record.out ? (
                          <Tag color="error">
                            {value} @ {numberFormat(record.price || 0)}
                          </Tag>
                        ) : "-",
                    },
                    {
                      dataIndex: "stock",
                      title: "Balance",
                      width: 100,
                      render: (value) => (
                        <Typography.Text
                          strong
                          style={{
                            color: value < 0 ? '#ff4d4f' : value === 0 ? '#faad14' : '#52c41a'
                          }}
                        >
                          {value}
                        </Typography.Text>
                      )
                    },
                  ]}
                />
              </Card>
            </Tabs.TabPane>

            <Tabs.TabPane
              tab={<span><BarChartOutlined /> Analytics</span>}
              key="analytics"
            >
              <Card bordered={false}>
                <Row gutter={[16, 16]}>
                  <Col span={24}>
                    <Typography.Title level={5} style={{ marginTop: 0 }}>
                      Stock Analytics
                    </Typography.Title>
                    <Divider style={{ margin: '12px 0' }} />
                  </Col>
                  <Col span={24}>
                    <Card title="Stock Level Trend" bordered={false} size="small">
                      {stockTrendData.length > 0 ? (
                        <Area
                          data={stockTrendData}
                          xField="date"
                          yField="stock"
                          smooth
                          areaStyle={{
                            fill: 'l(270) 0:#ffffff 0.5:#7ec2f3 1:#1890ff',
                          }}
                        />
                      ) : (
                        <Empty description="No stock data available" />
                      )}
                    </Card>
                  </Col>
                  <Col span={24}>
                    <Card title="Recent Stock Movements" bordered={false} size="small">
                      {movementChartData.length > 0 ? (
                        <Column
                          data={movementChartData}
                          xField="date"
                          yField="value"
                          seriesField="type"
                          isGroup
                          columnStyle={{
                            radius: [20, 20, 0, 0],
                          }}
                          color={['#1890ff', '#ff4d4f']}
                        />
                      ) : (
                        <Empty description="No movement data available" />
                      )}
                    </Card>
                  </Col>
                </Row>
              </Card>
            </Tabs.TabPane>
          </Tabs>
        </Col>
      </Row>
    </div>
  );
};
export default Product;