import "./styles.css";
import AppDatabase from "../../../../Utils/AppDatabase";
import PrintComponents from "react-print-components";
import React, { useEffect, useState, useMemo } from "react";
import moment from "moment";
import { <PERSON><PERSON><PERSON>, Pie } from "@ant-design/plots";
import { Descriptions, Divider, Layout, Typography, Button, Flex, Card, Row, Col, Table, Tag, Space, Empty, Statistic, Avatar, Tooltip } from "antd";
import { GroupOutlined, PrinterOutlined, ShoppingOutlined, InfoCircleOutlined, BarChartOutlined, TagOutlined, DollarOutlined, AppstoreOutlined } from "@ant-design/icons";
import { PageHeader } from "@ant-design/pro-components";
import { buffProducts } from "../../modulesProperties/utils";
import { numberFormat } from "../../../../Utils/functions";


const { Content } = Layout;
const { Title, Text, Paragraph } = Typography;

const Category = (props) => {
  const { data, singular, columns } = props;
  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [company, setCompany] = useState(null);

  // Fetch related products and company info
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        // Get the selected branch from localStorage
        const SELECTED_BRANCH = localStorage.getItem("SELECTED_BRANCH");

        // Get database prefix from localStorage or use default
        const databasePrefix = localStorage.getItem("DB_PREFIX") || "";

        // Fetch products related to this category
        const productsDB = AppDatabase("products", databasePrefix);
        const allProducts = await productsDB.getAllData();

        // Process products with buffProducts to get accurate stock information
        const buffedProducts = await buffProducts(allProducts, null, databasePrefix, "products", SELECTED_BRANCH);

        // Filter for products in this category
        const categoryProducts = buffedProducts.filter(product =>
          product.category && product.category.value === data._id
        );

        setProducts(categoryProducts);

        // Fetch company info for printing
        const companyDB = AppDatabase("company", databasePrefix);
        const companyData = await companyDB.getAllData();
        if (companyData.length > 0) {
          setCompany(companyData[0]);
        }
      } catch (error) {
        console.error("Error fetching data:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [data._id]);

  // Calculate statistics
  const statistics = useMemo(() => {
    if (!products.length) return { count: 0, stockValue: 0, revenue: 0, profit: 0 };

    let stockValue = 0;
    let potentialRevenue = 0;

    products.forEach(product => {
      // Use the currentStock property added by buffProducts
      const currentStock = product.currentStock || 0;

      stockValue += currentStock * (product.cost || 0);
      potentialRevenue += currentStock * (product.sell || 0);
    });

    return {
      count: products.length,
      stockValue,
      revenue: potentialRevenue,
      profit: potentialRevenue - stockValue
    };
  }, [products]);

  // Prepare data for stock value chart
  const stockValueChartData = useMemo(() => {
    if (!products.length) return [];

    return products.map(product => {
      // Use the currentStock property added by buffProducts
      const currentStock = product.currentStock || 0;
      const stockValue = currentStock * (product.cost || 0);

      return {
        name: product.name,
        value: stockValue
      };
    })
      .sort((a, b) => b.value - a.value)
      .slice(0, 10);
  }, [products]);

  // Prepare data for product distribution chart
  const productDistributionData = useMemo(() => {
    if (!products.length) return [];

    // Count products with stock
    let inStock = 0;
    let lowStock = 0;
    let outOfStock = 0;

    products.forEach(product => {
      // Use the currentStock property added by buffProducts
      const currentStock = product.currentStock || 0;

      if (currentStock <= 0) {
        outOfStock++;
      } else if (product.stock_alert && currentStock <= product.stock_alert) {
        lowStock++;
      } else {
        inStock++;
      }
    });

    return [
      { type: 'In Stock', value: inStock },
      { type: 'Low Stock', value: lowStock },
      { type: 'Out of Stock', value: outOfStock }
    ];
  }, [products]);

  // Printable category component
  const PrintableCategory = () => (
    <div style={{ padding: "20px", maxWidth: "800px", margin: "0 auto" }}>
      {company && (
        <Flex align="center" justify="space-between" style={{ marginBottom: "20px" }}>
          <div>
            {company.logo && <img src={company.logo} alt="Company Logo" style={{ maxHeight: 100 }} />}
          </div>
          <div style={{ textAlign: "center" }}>
            <Title level={2}>PRODUCT CATEGORY</Title>
            <Text>Category: {data.name}</Text>
          </div>
          <div style={{ textAlign: "right" }}>
            <Text>
              {company.address}
              <br />
              Tel: {company.phone}
              <br />
              Email: {company.email}
            </Text>
          </div>
        </Flex>
      )}

      <Divider />

      <Title level={4}>Category Information</Title>
      <Descriptions column={2} bordered>
        <Descriptions.Item label="Name">{data.name}</Descriptions.Item>
        {data.code && <Descriptions.Item label="Category Code">{data.code}</Descriptions.Item>}
        <Descriptions.Item label="Products Count">{products.length}</Descriptions.Item>
        <Descriptions.Item label="Stock Value">{numberFormat(statistics.stockValue)}</Descriptions.Item>
      </Descriptions>

      <Divider />

      <Title level={4}>Products in this Category</Title>
      <Table
        dataSource={products}
        rowKey="_id"
        pagination={false}
        size="small"
        columns={[
          {
            title: 'Name',
            dataIndex: 'name',
            key: 'name',
          },
          {
            title: 'SKU',
            dataIndex: 'sku',
            key: 'sku',
            render: sku => sku || 'N/A'
          },
          {
            title: 'Cost',
            dataIndex: 'cost',
            key: 'cost',
            render: cost => numberFormat(cost || 0)
          },
          {
            title: 'Selling Price',
            dataIndex: 'sell',
            key: 'sell',
            render: sell => numberFormat(sell || 0)
          },
          {
            title: 'Current Stock',
            key: 'stock',
            render: (_, record) => {
              // Use the currentStock property added by buffProducts
              return record.currentStock || 0;
            }
          }
        ]}
      />

      <div style={{ textAlign: 'right', marginTop: 20 }}>
        <Text type="secondary">Generated on {moment().format('MMMM Do YYYY, h:mm:ss a')}</Text>
      </div>
    </div>
  );

  // Columns for the products table
  const productColumns = [
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
      render: (text, record) => (
        <Space>
          <Avatar
            shape="square"
            size="small"
            icon={<ShoppingOutlined />}
            style={{ backgroundColor: '#1890ff' }}
          />
          <Tooltip title={`View product details: ${text}`}>
            <a href={`#/product_management/products/${record._id}`}>{text}</a>
          </Tooltip>
        </Space>
      )
    },
    {
      title: 'SKU',
      dataIndex: 'sku',
      key: 'sku',
      width: '15%',
      render: sku => sku || 'N/A'
    },
    {
      title: 'Cost',
      dataIndex: 'cost',
      key: 'cost',
      width: '15%',
      sorter: (a, b) => (a.cost || 0) - (b.cost || 0),
      render: cost => numberFormat(cost || 0)
    },
    {
      title: 'Selling Price',
      dataIndex: 'sell',
      key: 'sell',
      width: '15%',
      sorter: (a, b) => (a.sell || 0) - (b.sell || 0),
      render: sell => numberFormat(sell || 0)
    },
    {
      title: 'Current Stock',
      key: 'stock',
      width: '15%',
      sorter: (a, b) => {
        // Use the currentStock property added by buffProducts
        const stockA = a.currentStock || 0;
        const stockB = b.currentStock || 0;
        return stockA - stockB;
      },
      render: (_, record) => {
        // Use the currentStock property added by buffProducts
        const currentStock = record.currentStock || 0;

        let color = 'success';
        if (currentStock <= 0) {
          color = 'error';
        } else if (record.stock_alert && currentStock <= record.stock_alert) {
          color = 'warning';
        }

        return <Tag color={color}>{currentStock} {record.units || 'pc'}</Tag>;
      }
    },
    {
      title: 'Status',
      key: 'status',
      width: '15%',
      filters: [
        { text: 'In Stock', value: 'in_stock' },
        { text: 'Low Stock', value: 'low_stock' },
        { text: 'Out of Stock', value: 'out_of_stock' },
      ],
      onFilter: (value, record) => {
        // Use the currentStock property added by buffProducts
        const currentStock = record.currentStock || 0;

        if (value === 'out_of_stock') {
          return currentStock <= 0;
        } else if (value === 'low_stock') {
          return currentStock > 0 && record.stock_alert && currentStock <= record.stock_alert;
        } else {
          return currentStock > 0 && (!record.stock_alert || currentStock > record.stock_alert);
        }
      },
      render: (_, record) => {
        // Use the currentStock property added by buffProducts
        const currentStock = record.currentStock || 0;

        if (currentStock <= 0) {
          return <Tag color="error">Out of Stock</Tag>;
        } else if (record.stock_alert && currentStock <= record.stock_alert) {
          return <Tag color="warning">Low Stock</Tag>;
        } else {
          return <Tag color="success">In Stock</Tag>;
        }
      }
    }
  ];

  return (
    <>
      <PageHeader
        backIcon={<GroupOutlined style={{ fontSize: 30 }} />}
        onBack={() => window.history.back()}
        title={data.name}
        subTitle="Product Category"
        extra={[
          <PrintComponents
            key="print"
            trigger={
              <Button icon={<PrinterOutlined />} type="primary">
                Print Category Details
              </Button>
            }
          >
            <PrintableCategory />
          </PrintComponents>,
        ]}
      />

      <Content style={{ padding: "0 24px 24px" }}>
        <Row gutter={[24, 24]}>
          {/* <Col xs={24} lg={8}>
            <Card title={<><InfoCircleOutlined /> Category Details</>} className="category-card">
              <Descriptions bordered column={{ xxl: 1, xl: 1, lg: 1, md: 1, sm: 1, xs: 1 }}>
                <Descriptions.Item label="Name">{data.name}</Descriptions.Item>
                {data.code && <Descriptions.Item label="Category Code">{data.code}</Descriptions.Item>}
                <Descriptions.Item label="Products Count">
                  <Tag color="blue" icon={<ShoppingOutlined />}>{products.length}</Tag>
                </Descriptions.Item>
              </Descriptions>
            </Card>
          </Col> */}

          <Col xs={24} lg={24}>
            <Card title={<><BarChartOutlined /> Category Statistics</>} className="category-card">
              <Row gutter={[16, 16]}>
                <Col xs={12} sm={6}>
                  <Statistic
                    title="Products"
                    value={statistics.count}
                    prefix={<ShoppingOutlined />}
                  />
                </Col>
                <Col xs={12} sm={6}>
                  <Statistic
                    title="Stock Value"
                    value={numberFormat(statistics.stockValue)}
                    prefix={<DollarOutlined />}
                  />
                </Col>
                <Col xs={12} sm={6}>
                  <Statistic
                    title="Potential Revenue"
                    value={numberFormat(statistics.revenue)}
                    prefix={<DollarOutlined />}
                  />
                </Col>
                <Col xs={12} sm={6}>
                  <Statistic
                    title="Potential Profit"
                    value={numberFormat(statistics.profit)}
                    prefix={<DollarOutlined />}
                    valueStyle={{ color: statistics.profit >= 0 ? '#3f8600' : '#cf1322' }}
                  />
                </Col>
              </Row>
            </Card>
          </Col>
        </Row>


        {products.length > 0 && (
          <Row gutter={[24, 24]} style={{ marginTop: 24 }}>
            <Col xs={24} lg={12}>
              <Card title="Top Products by Stock Value" bordered={false}>
                {stockValueChartData.length > 0 ? (
                  <Column
                    data={stockValueChartData}
                    xField="name"
                    yField="value"
                    label={{
                      position: 'middle',
                      style: {
                        fill: '#FFFFFF',
                        opacity: 0.6,
                      },
                    }}
                    xAxis={{
                      label: {
                        autoHide: true,
                        autoRotate: false,
                      },
                    }}
                    meta={{
                      value: {
                        formatter: (v) => `${numberFormat(v)}`,
                      },
                    }}
                  />
                ) : (
                  <Empty description="No stock value data available" />
                )}
              </Card>
            </Col>
            <Col xs={24} lg={12}>
              <Card title="Product Stock Status Distribution" bordered={false}>
                {productDistributionData.length > 0 ? (
                  <Pie
                    data={productDistributionData}
                    angleField="value"
                    colorField="type"
                    radius={0.8}
                    label={{
                      type: 'outer',
                      content: '{name} {percentage}',
                    }}
                    interactions={[{ type: 'pie-legend-active' }, { type: 'element-active' }]}
                    legend={{
                      position: 'bottom',
                    }}
                    color={['#52c41a', '#faad14', '#f5222d']}
                  />
                ) : (
                  <Empty description="No product distribution data available" />
                )}
              </Card>
            </Col>
          </Row>
        )}


        <Card
          title={<><AppstoreOutlined /> Products in this Category</>}
          className="category-card"
          style={{ marginTop: 24 }}
        >
          <Table
            dataSource={products}
            columns={productColumns}
            rowKey="_id"
            loading={loading}
            pagination={false}
          />
        </Card>
      </Content>
    </>
  );
};

export default Category;