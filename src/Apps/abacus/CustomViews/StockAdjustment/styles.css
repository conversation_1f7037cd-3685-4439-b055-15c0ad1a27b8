/* Stock Adjustment Custom View Styles */

/* Card Styles */
.stock-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  margin-bottom: 16px;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.stock-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
}

.stock-card .ant-card-head {
  background-color: #fafafa;
  border-bottom: 1px solid #f0f0f0;
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
  padding: 12px 16px;
}

.stock-card .ant-card-head-title {
  font-weight: 600;
  font-size: 16px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.stock-card .ant-card-body {
  padding: 16px;
}

/* Status Tag */
.status-tag {
  font-weight: 500;
  padding: 4px 8px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  gap: 4px;
}

/* Information Card */
.info-descriptions {
  margin-bottom: 16px;
}

.info-descriptions .ant-descriptions-item-label {
  font-weight: 500;
  width: 120px;
}

.description-section {
  margin-top: 16px;
}

.description-text {
  background-color: #f9f9f9;
  padding: 12px;
  border-radius: 4px;
  border-left: 3px solid #1890ff;
}

/* Summary Card */
.summary-card .ant-statistic {
  margin-bottom: 8px;
}

.main-statistic {
  text-align: center;
  margin-bottom: 0;
}

.main-statistic .ant-statistic-content {
  font-size: 28px;
  font-weight: 600;
  color: #1890ff;
}

.adjustment-stats {
  text-align: center;
}

.stat-value {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.65);
  margin-top: 4px;
}

.total-value-stat {
  text-align: center;
}

.total-value-stat .ant-statistic-content {
  font-size: 24px;
  font-weight: 600;
  color: #1890ff;
}

.distribution-section {
  text-align: center;
  margin-top: 16px;
}

.progress-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: 14px;
}

.categories-section {
  margin-top: 16px;
}

.category-timeline {
  margin-top: 16px;
  padding: 0 8px;
}

.category-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 0;
}

.category-name {
  font-weight: 500;
}

.category-stats {
  display: flex;
  gap: 16px;
  color: rgba(0, 0, 0, 0.65);
  font-size: 13px;
}

.category-value {
  font-weight: 500;
  color: #1890ff;
}

/* Items Card */
.items-card {
  height: 100%;
}

.items-table {
  margin-top: 8px;
}

.product-cell {
  display: flex;
  align-items: flex-start;
}

.product-link {
  font-weight: 500;
  color: #1890ff;
}

.product-sku {
  font-size: 12px;
  color: rgba(0, 0, 0, 0.45);
  margin-top: 2px;
}

.adjustment-tag {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  font-weight: 500;
  padding: 2px 8px;
}

.quantity-cell {
  font-weight: 500;
}

.unit-text {
  font-size: 12px;
  color: rgba(0, 0, 0, 0.45);
}

.cost-cell {
  font-weight: 500;
}

.value-cell {
  font-weight: 600;
}

.positive-value {
  color: #52c41a;
}

.negative-value {
  color: #f5222d;
}

.summary-value {
  font-size: 16px;
  color: #1890ff;
}

.empty-state {
  padding: 40px 0;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .stock-card {
    margin-bottom: 12px;
  }

  .stock-card .ant-card-head-title {
    font-size: 15px;
  }

  .main-statistic .ant-statistic-content {
    font-size: 24px;
  }

  .total-value-stat .ant-statistic-content {
    font-size: 20px;
  }

  .category-stats {
    flex-direction: column;
    gap: 4px;
  }
}