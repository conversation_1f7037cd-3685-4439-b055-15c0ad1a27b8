import "./styles.css";
import PrintComponents from "react-print-components";
import React, { useEffect, useState, useMemo } from "react";
import moment from "moment";
import { Card, Row, Col, Typography, Descriptions, Table, Tag, Divider, Space, Button, Statistic, Avatar, Empty, Skeleton, Flex, Badge, Tooltip } from "antd";
import { PageHeader } from "@ant-design/pro-components";
import { WarningOutlined, ShoppingCartOutlined, CalendarOutlined, FileTextOutlined, PrinterOutlined, InfoCircleOutlined, DollarOutlined, ExclamationCircleOutlined } from "@ant-design/icons";
import { numberFormat } from "../../../../Utils/functions";


const { Title, Text, Paragraph } = Typography;

const DamagedProduct = (props) => {
  const { data, singular, pouchDatabase, databasePrefix } = props;
  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [company, setCompany] = useState(null);

  // Fetch product data
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        // Fetch products for items in damaged products
        if (data.items && data.items.length > 0) {
          const allProducts = await pouchDatabase("products", databasePrefix).getAllData();
          const filteredProducts = allProducts.filter(product => !product._id.startsWith('_'));
          setProducts(filteredProducts);
        }

        // Fetch company info for printing
        const organizationsData = await pouchDatabase("organizations", databasePrefix).getAllData();
        if (organizationsData && organizationsData.length > 0) {
          setCompany(organizationsData[0]);
        }
      } catch (error) {
        console.error("Error fetching data:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [data.items, pouchDatabase, databasePrefix]);

  // Prepare damaged items with product details
  const damagedItems = useMemo(() => {
    if (!data.items || !products.length) return [];

    return data.items.map(item => {
      const product = products.find(p => p._id === item.product.value);
      return {
        ...item,
        productDetails: product || null
      };
    });
  }, [data.items, products]);

  // Calculate damage statistics
  const statistics = useMemo(() => {
    if (!damagedItems.length) return { totalItems: 0, totalValue: 0 };

    const totalItems = damagedItems.reduce((sum, item) => sum + (item.quantity || 0), 0);
    const totalValue = damagedItems.reduce((sum, item) => {
      const cost = item.productDetails ? item.productDetails.cost || 0 : 0;
      return sum + (cost * (item.quantity || 0));
    }, 0);

    return {
      totalItems,
      totalValue
    };
  }, [damagedItems]);

  // Format date for display
  const formatDate = (dateString) => {
    return moment(dateString).format('MMM DD, YYYY');
  };

  // Printable damaged products component
  const PrintableDamaged = () => (
    <div style={{ padding: "20px", maxWidth: "800px", margin: "0 auto" }}>
      {company && (
        <Flex align="center" justify="space-between" style={{ marginBottom: "20px" }}>
          <div>
            {company.logo && <img src={company.logo} alt="Company Logo" style={{ maxHeight: 100 }} />}
          </div>
          <div style={{ textAlign: "center" }}>
            <Title level={2}>DAMAGED PRODUCTS</Title>
            <Text>Reference #: {data._id}</Text>
          </div>
          <div style={{ textAlign: "right" }}>
            <Text>
              {company.address}
              <br />
              Tel: {company.phone}
              <br />
              Email: {company.email}
            </Text>
          </div>
        </Flex>
      )}

      <Divider />

      <Title level={5}>Damage Information</Title>
      <Descriptions bordered column={2}>
        <Descriptions.Item label="Reference ID">{data._id}</Descriptions.Item>
        <Descriptions.Item label="Date">{formatDate(data.date)}</Descriptions.Item>
        <Descriptions.Item label="Total Items">{statistics.totalItems}</Descriptions.Item>
        <Descriptions.Item label="Total Value">{numberFormat(statistics.totalValue)}</Descriptions.Item>
        {data.damage_type && (
          <Descriptions.Item label="Damage Type">{data.damage_type}</Descriptions.Item>
        )}
        {data.description && (
          <Descriptions.Item label="Description" span={2}>{data.description}</Descriptions.Item>
        )}
      </Descriptions>

      <Divider />

      <Title level={5}>Damaged Items</Title>
      <Table
        dataSource={damagedItems}
        pagination={false}
        size="small"
        columns={[
          {
            title: 'Product',
            dataIndex: 'product',
            key: 'product',
            render: product => product.label
          },
          {
            title: 'Quantity',
            dataIndex: 'quantity',
            key: 'quantity',
            align: 'right'
          },
          {
            title: 'Unit Cost',
            key: 'cost',
            align: 'right',
            render: (_, record) => numberFormat(record.productDetails ? record.productDetails.cost || 0 : 0)
          },
          {
            title: 'Total Value',
            key: 'value',
            align: 'right',
            render: (_, record) => {
              const cost = record.productDetails ? record.productDetails.cost || 0 : 0;
              return numberFormat(cost * (record.quantity || 0));
            }
          }
        ]}
        summary={() => (
          <Table.Summary>
            <Table.Summary.Row>
              <Table.Summary.Cell colSpan={3} index={0}>
                <Text strong>Total Value</Text>
              </Table.Summary.Cell>
              <Table.Summary.Cell index={1} align="right">
                <Text strong>{numberFormat(statistics.totalValue)}</Text>
              </Table.Summary.Cell>
            </Table.Summary.Row>
          </Table.Summary>
        )}
      />

      <div style={{ textAlign: 'right', marginTop: 20 }}>
        <Text type="secondary">Generated on {moment().format('MMMM Do YYYY, h:mm:ss a')}</Text>
      </div>
    </div>
  );

  return (
    <>
      <PageHeader
        className="site-page-header-responsive"
        onBack={() => window.history.back()}
        title={`Damaged Products #${data._id}`}
        subTitle={formatDate(data.date)}
        tags={[
          <Tag color="red" key="damaged">
            <WarningOutlined /> Damaged Products
          </Tag>
        ]}
        extra={[
          <PrintComponents
            key="print"
            trigger={
              <Button icon={<PrinterOutlined />} type="primary">
                Print Damage Record
              </Button>
            }
          >
            <PrintableDamaged />
          </PrintComponents>,
        ]}
      />

      <div style={{ padding: "0 24px 24px" }}>
        {loading ? (
          <Row gutter={[24, 24]}>
            <Col span={24}>
              <Skeleton active paragraph={{ rows: 6 }} />
            </Col>
          </Row>
        ) : (
          <Row gutter={[24, 24]}>
            <Col xs={24} lg={8}>
              <Card
                title={<><InfoCircleOutlined /> Damage Information</>}
                className="stock-card"
              >
                <Descriptions column={1} size="small" bordered>
                  <Descriptions.Item label="Reference ID">{data._id}</Descriptions.Item>
                  <Descriptions.Item label="Date">{formatDate(data.date)}</Descriptions.Item>
                  {data.damage_type && (
                    <Descriptions.Item label="Damage Type">{data.damage_type}</Descriptions.Item>
                  )}
                  {data.entrant && (
                    <Descriptions.Item label="Created By">{data.entrant.label}</Descriptions.Item>
                  )}
                </Descriptions>

                {data.description && (
                  <>
                    <Divider orientation="left">Description</Divider>
                    <Paragraph>{data.description}</Paragraph>
                  </>
                )}
              </Card>

              <Card
                title={<><ExclamationCircleOutlined /> Damage Summary</>}
                className="stock-card"
                style={{ marginTop: 16 }}
              >
                <Statistic
                  title="Total Items"
                  value={statistics.totalItems}
                  prefix={<ShoppingCartOutlined />}
                  style={{ marginBottom: 16 }}
                />
                <Statistic
                  title="Total Loss"
                  value={numberFormat(statistics.totalValue)}
                  prefix={<DollarOutlined />}
                  valueStyle={{ color: '#f5222d' }}
                />
              </Card>
            </Col>

            <Col xs={24} lg={16}>
              <Card
                title={<><ShoppingCartOutlined /> Damaged Items</>}
                className="stock-card"
              >
                {damagedItems.length > 0 ? (
                  <Table
                    dataSource={damagedItems}
                    rowKey={(record, index) => index}
                    pagination={false}
                    columns={[
                      {
                        title: 'Product',
                        dataIndex: 'product',
                        key: 'product',
                        render: product => (
                          <Space>
                            <Avatar
                              shape="square"
                              size="small"
                              icon={<ShoppingCartOutlined />}
                              style={{ backgroundColor: '#1890ff' }}
                            />
                            <Tooltip title={`View product details: ${product.label}`}>
                              <a href={`#/inventory/products/${product.value}`}>{product.label}</a>
                            </Tooltip>
                          </Space>
                        )
                      },
                      {
                        title: 'Quantity',
                        dataIndex: 'quantity',
                        key: 'quantity',
                        align: 'right',
                        width: '15%'
                      },
                      {
                        title: 'Unit Cost',
                        key: 'cost',
                        align: 'right',
                        width: '20%',
                        render: (_, record) => numberFormat(record.productDetails ? record.productDetails.cost || 0 : 0)
                      },
                      {
                        title: 'Total Loss',
                        key: 'value',
                        align: 'right',
                        width: '20%',
                        render: (_, record) => {
                          const cost = record.productDetails ? record.productDetails.cost || 0 : 0;
                          return numberFormat(cost * (record.quantity || 0));
                        }
                      }
                    ]}
                    summary={() => (
                      <Table.Summary>
                        <Table.Summary.Row>
                          <Table.Summary.Cell colSpan={3} index={0}>
                            <Text strong>Total Loss</Text>
                          </Table.Summary.Cell>
                          <Table.Summary.Cell index={1} align="right">
                            <Text strong style={{ color: '#f5222d' }}>{numberFormat(statistics.totalValue)}</Text>
                          </Table.Summary.Cell>
                        </Table.Summary.Row>
                      </Table.Summary>
                    )}
                  />
                ) : (
                  <Empty description="No items in this damage record" />
                )}
              </Card>
            </Col>
          </Row>
        )}
      </div>
    </>
  );
};

export default DamagedProduct;