import InvoiceTemplate from "../../Universal/customViews/Components/InvoiceTemplate";
import React from "react";


const StockPurchaseInvoice = ({ data }) => {
  const { _id, customer, items, date, total, status } = data;

  const my_items = items
    ? items.map((item) => {
        return {
          name: item.product.label,
          price: item.price,
          quantity: item.quantity,
        };
      })
    : [
        {
          name: data.product.label,
          quantity: data.quantity,
          price: data.unit_cost
            ? data.unit_cost
            : data.total_cost / data.quantity,
        },
      ];

  

  const newInvoiceData = {
    date,
    id: _id,
    client: "",
    operator: { title: "Entered By", name: data.entrant?.label },
    items: [...my_items],
  };

  return <InvoiceTemplate data={newInvoiceData} documentTitle="Invoice" />;
};

export default StockPurchaseInvoice;