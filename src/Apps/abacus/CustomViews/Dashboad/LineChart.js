import React, { useEffect, useState } from "react";
import moment from "moment";
import { Line } from "@ant-design/plots";
import { buffProducts } from "../../modulesProperties/utils";


const formatData = {
  date: (text) => moment(text).format("MMM YY"),
  amount: (text) => text.toLocaleString(),
  // currencyFormatter.format(text, { code: "" })
};

const SELECTED_BRANCH = localStorage.getItem("SELECTED_BRANCH");

const LineChart = ({ pouchDatabase, databasePrefix, currentUser }) => {
  const [data, setData] = useState([]);

  const incoiceDB = pouchDatabase("invoices", databasePrefix);
  const expenseDB = pouchDatabase("expenses", databasePrefix);
  const productsDB = pouchDatabase("products", databasePrefix);

  useEffect(() => {
    incoiceDB.on("dbChange", processChartData);
    expenseDB.on("dbChange", processChartData);
    productsDB.on("dbChange", processChartData);

    return () => {
      incoiceDB.off("dbChange", processChartData);
      expenseDB.off("dbChange", processChartData);
      productsDB.off("dbChange", processChartData);
    };
  }, [incoiceDB, expenseDB, productsDB]);

  const processChartData = () => {
    const SELECTED_BRANCH = localStorage.getItem("SELECTED_BRANCH");
    Promise.all([
      incoiceDB.getAllData(),
      expenseDB.getAllData(),
      productsDB.getAllData().then((r) => buffProducts(r, null, null, "products", SELECTED_BRANCH)),
      // pouchDatabase("withdraws", databasePrefix).getAllData(),
    ]).then((values) => {
      const income =
        SELECTED_BRANCH && SELECTED_BRANCH !== "none"
          ? values[0].filter((i) => i.branch === SELECTED_BRANCH)
          : values[0];
      const expenses =
        SELECTED_BRANCH && SELECTED_BRANCH !== "none"
          ? values[1].filter((i) => i.branch === SELECTED_BRANCH)
          : values[1];
      const products = values[2];
      // const withdraws =
      //   SELECTED_BRANCH && SELECTED_BRANCH !== "none"
      //     ? values[3].filter((i) => i.branch === SELECTED_BRANCH)
      //     : values[3];

      let chartData = [];

      for (let i = 11; i >= 0; i--) {
        let currentDate = moment(new Date());
        currentDate = currentDate.subtract(i, "months");

        let incomeThisMonth = income.filter((n) => {
          var d = moment(n.date);
          return currentDate.format("MMM YY") === d.format("MMM YY");
        });
        var incomeThisMonthAmount = incomeThisMonth.reduce(function (
          sumSoFar,
          row
        ) {
          return (
            sumSoFar +
            parseInt(
              row.items.reduce(
                (pv, cv) => pv + parseInt(cv.quantity * cv.price),
                0
              )
            )
          );
        },
          0);

        var salesCostMonthAmount = incomeThisMonth.reduce(function (
          sumSoFar,
          row
        ) {
          return (
            sumSoFar +
            parseInt(
              row.items.reduce((pv, cv) => {
                const cvCost = products.find((p) => p._id === cv.product.value);
                return (
                  pv +
                  parseInt(cv.quantity) *
                  parseInt(cvCost ? (cvCost.cost ? cvCost.cost : 0) : 0)
                );
              }, 0)
            )
          );
        },
          0);

        let expensesThisMonth = expenses.filter((n) => {
          var d = moment(n.date);
          return currentDate.format("MMM YY") === d.format("MMM YY");
        });
        var expensesThisMonthAmount = expensesThisMonth.reduce(function (
          sumSoFar,
          row
        ) {
          return sumSoFar + parseInt(row.amount);
        },
          0);

        chartData.push(
          {
            year: currentDate.format("MMM YY"),
            value: incomeThisMonthAmount,
            category: "Sales",
          },

          {
            year: currentDate.format("MMM YY"),
            value: salesCostMonthAmount,
            category: "Sales Cost",
          },
          {
            year: currentDate.format("MMM YY"),
            value: incomeThisMonthAmount - salesCostMonthAmount,
            category: "Gross Profit",
          },
          {
            year: currentDate.format("MMM YY"),
            value:
              incomeThisMonthAmount -
              salesCostMonthAmount -
              expensesThisMonthAmount,
            category: "Net Profit",
          },
          {
            year: currentDate.format("MMM YY"),
            value: expensesThisMonthAmount,
            category: "Expence",
          }
          // {
          //   year: currentDate.format("MMM YY"),
          //   value: loansThisMonthAmount - paymentsThisMonthAmount,
          //   category: "Arrears",
          // }
        );
      }
      setData(chartData);
    });
  };

  useEffect(() => {
    processChartData();
  }, []);

  const config = {
    data,
    xField: "year",
    yField: "value",
    seriesField: "category",
    smooth: true,
    xAxis: {
      //   type: 'time',
    },
    tooltip: {
      formatter: (datum) => {
        return { name: datum.category, value: datum.value.toLocaleString() };
      },
    },
    point: {
      size: 4,
    },
    color: ["#1677ff", "#fadb14", "#13c2c2", "#52c41a", "#f5222d"],
    yAxis: {
      label: {
        // 数值格式化为千分位
        formatter: (v) =>
          `${v}`.replace(/\d{1,3}(?=(\d{3})+$)/g, (s) => `${s},`),
      },
    },
  };

  return <Line {...config} />;
};

export default LineChart;