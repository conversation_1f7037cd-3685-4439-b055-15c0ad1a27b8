import React, { useEffect, useState } from "react";
import moment from "moment";
import { Bar, Column, Histogram, Line } from "@ant-design/plots";
import { buffProducts } from "../../modulesProperties/utils";


const formatData = {
  date: (text) => moment(text).format("MMM YY"),
  amount: (text) => text.toLocaleString(),
  // currencyFormatter.format(text, { code: "" })
};

const SELECTED_BRANCH = localStorage.getItem("SELECTED_BRANCH");

const SalesPurchases = ({ pouchDatabase, databasePrefix, currentUser }) => {
  const [data, setData] = useState([]);

  useEffect(() => {
    const SELECTED_BRANCH = localStorage.getItem("SELECTED_BRANCH");
    Promise.all([
      pouchDatabase("invoices", databasePrefix).getAllData(),
      pouchDatabase("expenses", databasePrefix).getAllData(),
      pouchDatabase("products", databasePrefix)
        .getAllData()
        .then((r) => buffProducts(r, pouchDatabase, databasePrefix, "products", SELECTED_BRANCH)),
      pouchDatabase("stock_purchasing", databasePrefix).getAllData(),
    ]).then((values) => {
      const income =
        SELECTED_BRANCH && SELECTED_BRANCH !== "none"
          ? values[0].filter((i) => i.branch === SELECTED_BRANCH)
          : values[0];
      const expenses =
        SELECTED_BRANCH && SELECTED_BRANCH !== "none"
          ? values[1].filter((i) => i.branch === SELECTED_BRANCH)
          : values[1];
      const products =
        SELECTED_BRANCH && SELECTED_BRANCH !== "none"
          ? values[2].filter((i) => i.branch === SELECTED_BRANCH)
          : values[2];
      const stockPurchasing =
        SELECTED_BRANCH && SELECTED_BRANCH !== "none"
          ? values[3].filter((i) => i.branch === SELECTED_BRANCH)
          : values[3];

      // const withdraws =
      //   SELECTED_BRANCH && SELECTED_BRANCH !== "none"
      //     ? values[3].filter((i) => i.branch === SELECTED_BRANCH)
      //     : values[3];

      let chartData = [];


      for (let i = 11; i >= 0; i--) {
        let currentDate = moment(new Date());
        currentDate = currentDate.subtract(i, "months");

        let incomeThisMonth = income.filter((n) => {
          var d = moment(n.date);
          return currentDate.format("MMM YY") === d.format("MMM YY");
        });
        var incomeThisMonthAmount = incomeThisMonth.reduce(function (
          sumSoFar,
          row
        ) {
          return (
            sumSoFar +
            parseInt(
              row.items.reduce(
                (pv, cv) => pv + parseInt(cv.quantity * cv.price),
                0
              )
            )
          );
        },
          0);

        let stockRecords = [];

        stockPurchasing.forEach((invoice, index) => {
          if (invoice.items) {
            invoice.items.forEach((item) =>
              stockRecords.push({
                date: invoice.date,
                ...item,
              })
            );
          } else {
            stockRecords.push({
              price: invoice.unit_cost
                ? invoice.unit_cost
                : invoice.total_cost / invoice.quantity,
              ...invoice,
            });
          }
        });

        let purchasesThisMonth = stockRecords.filter((n) => {
          var d = moment(n.date);
          return currentDate.format("MMM YY") === d.format("MMM YY");
        });
        var purchasesThisMonthAmount = purchasesThisMonth.reduce(function (
          sumSoFar,
          row
        ) {
          return sumSoFar + parseInt(row.price * row.quantity);
        },
          0);

        chartData.push(
          {
            year: currentDate.format("MMM YY"),
            value: incomeThisMonthAmount,
            category: "Sales",
          },
          {
            year: currentDate.format("MMM YY"),
            value: purchasesThisMonthAmount,
            category: "Purchases",
          }
        );
      }
      setData(chartData);
    });
  }, []);


  const config = {
    data,
    isGroup: true,
    xField: "year",
    yField: "value",
    seriesField: "category",
    smooth: true,
    xAxis: {
      //   type: 'time',
    },
    tooltip: {
      formatter: (datum) => {
        return { name: datum.category, value: datum.value.toLocaleString() };
      },
    },
    point: {
      size: 4,
    },
    color: ["#1677ff", "#fadb14"],
    yAxis: {
      label: {
        // 数值格式化为千分位
        formatter: (v) =>
          `${v}`.replace(/\d{1,3}(?=(\d{3})+$)/g, (s) => `${s},`),
      },
    },
  };

  return <Column {...config} />;
};

export default SalesPurchases;