import * as XLSX from 'xlsx';
import React, { useState } from 'react';
import { InboxOutlined } from "@ant-design/icons";
import { Upload, Button, message, Spin } from "antd";


const ExcelToJson = ({pouchDatabase, databasePrefix}) => {
  const [jsonData, setJsonData] = useState(null);
  const [loading, setLoading] = useState(false);

  // Handler to process the file and convert it to JSON
  const handleFileUpload = (file) => {
    setLoading(true);
    const reader = new FileReader();

    reader.onload = (e) => {
      const ab = e.target.result;
      const workbook = XLSX.read(ab, { type: 'array' });
      
      // Get the first sheet in the workbook
      const sheet = workbook.Sheets[workbook.SheetNames[0]];
      
      // Convert the sheet to JSON
      const json = XLSX.utils.sheet_to_json(sheet);
      
      // Set the JSON data
      setJsonData(json);
      setLoading(false);
    };

    reader.readAsArrayBuffer(file);
    return false; // Prevent upload to server
  };

  const handleDownloadJson = () => {
    const blob = new Blob([JSON.stringify(jsonData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'data.json';
    a.click();
    URL.revokeObjectURL(url);
  };




  const handleImport = () => {
    
  const generatedCategories = [];
  const generatedProducts = [];
  const generatedStockPurchase = [];

  pouchDatabase("products", databasePrefix).getAllData().then((result) => {
    jsonData&&jsonData.forEach((item) => {
      

      if(item.DESCRIPTION){

        
        const prod = result.find((c) =>c.name.toLowerCase() === item.DESCRIPTION.toLowerCase() && c.sku === item["PART NUMBER"])
        
        const openingStock = {
          date: new Date().toISOString(),
          source: "Opening Stock",
          branch:"M3FLSISJ",
          items: [{
            product: {
              value: prod._id,
              label: prod.name,
              ...prod
            },
          quantity: item["CLOSING STOCK"],
          price: item[" UNIT COST PRICE "],
        }]
        }
        generatedStockPurchase.push(
          openingStock
        );
      }
    });
    // pouchDatabase("non_purchased_stock", databasePrefix).bulkAdd(generatedStockPurchase);
    // 
  });
  };
  

  return (
    <div style={{ padding: '20px', marginBottom: '40px' }}>

    <button
    onClick={handleImport}
    >Import</button>

      <h3>Upload Excel File</h3>
      
      <Upload.Dragger
        name="file"
        accept=".xlsx,.xls"
        customRequest={({ file, onSuccess }) => {
          // Call the file handler here
          handleFileUpload(file);
          onSuccess();  // Trigger success callback
        }}
        showUploadList={false}
      >
        <p className="ant-upload-drag-icon">
          <InboxOutlined />
        </p>
        <p className="ant-upload-text">Click or drag an Excel file to this area</p>
        <p className="ant-upload-hint">Only .xlsx or .xls files are supported</p>
      </Upload.Dragger>

      {loading && <Spin size="large" style={{ marginTop: '20px' }} />}

      {jsonData && (
        <div style={{ marginTop: '20px' }}>
          <h4>Parsed JSON Data:</h4>
          <pre>{JSON.stringify(jsonData, null, 2)}</pre>
          
          <Button 
            style={{ marginTop: '10px' }}
            type="primary" 
            onClick={handleDownloadJson}
          >
            Download JSON
          </Button>
        </div>
      )}
    </div>
  );
};

export default ExcelToJson;