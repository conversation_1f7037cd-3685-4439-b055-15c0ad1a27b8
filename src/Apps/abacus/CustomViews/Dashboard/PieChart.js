import React, { useState, useEffect, useCallback } from "react";
import moment from "moment";
import { Empty, Spin } from "antd";
import { Pie } from "@ant-design/plots";
import { buffProducts } from "../../modulesProperties/utils";
import { filterByBranch } from "../../../../Utils/branchFiltering";


const PieChart = ({ pouchDatabase, databasePrefix, by, selectedMonth }) => {
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(true);

  // Format the month for display and debugging
  const monthLabel = selectedMonth ? selectedMonth.format('YYYY-MM') : 'current';

  // Define the chart configuration
  const config = {
    appendPadding: 10,
    legend: false,
    data,
    angleField: "value",
    colorField: "product",
    radius: 0.75,
    label: {
      labelHeight: 30,
      content: ({ product, percent }) => `${product}\n${(percent * 100).toFixed(1)}%`,
    },
  };

  // Function to fetch and process data
  const fetchData = useCallback(async () => {
    console.log(`Fetching data for ${monthLabel} month, metric: ${by}`);
    setLoading(true);

    try {
      // Fetch data from databases
      const [invoicesData, productsData] = await Promise.all([
        pouchDatabase("invoices", databasePrefix).getAllData(),
        pouchDatabase("products", databasePrefix)
          .getAllData()
          .then(r => buffProducts(r, pouchDatabase, databasePrefix, "products", localStorage.getItem("SELECTED_BRANCH")))
      ]);

      // Get selected branch
      const SELECTED_BRANCH = localStorage.getItem("SELECTED_BRANCH");

      // Determine filter date
      const filterDate = selectedMonth ? moment(selectedMonth) : moment();
      const filterMonthYear = filterDate.format('YYYY-MM');

      console.log(`Filtering invoices for ${filterMonthYear}`);

      // Filter invoices by branch and date
      const filteredInvoices = filterByBranch(invoicesData || [], SELECTED_BRANCH)
        .filter(invoice => {
          if (!invoice.date) return false;
          const invoiceDate = moment(invoice.date);
          return invoiceDate.format('YYYY-MM') === filterMonthYear;
        });

      console.log(`Found ${filteredInvoices.length} invoices for ${filterMonthYear}`);

      // Process data for chart
      const productMap = new Map();

      // Initialize with product names
      (productsData || []).forEach(product => {
        productMap.set(product._id, {
          product: product.name,
          value: 0,
          productObj: product
        });
      });

      // Process invoices
      filteredInvoices.forEach(invoice => {
        if (!invoice.items || !Array.isArray(invoice.items)) return;

        invoice.items.forEach(item => {
          if (!item.product || !item.product.value) return;

          const productId = item.product.value;
          const productEntry = productMap.get(productId);

          if (!productEntry) return;

          // Use the label from the invoice item if available
          if (item.product.label) {
            productEntry.product = item.product.label;
          }

          // Calculate value based on metric type
          if (by === "quantity") {
            productEntry.value += Number(item.quantity || 0);
          } else { // profit
            const product = productEntry.productObj;
            const cost = product ? (product.cost || 0) : 0;
            const price = item.price || 0;
            const quantity = Number(item.quantity || 0);

            productEntry.value += (price * quantity) - (cost * quantity);
          }
        });
      });

      // Convert map to array and filter out zero values
      let chartData = Array.from(productMap.values())
        .filter(entry => entry.value > 0)
        .map(({ product, value }) => ({ product, value }));

      // Sort and limit data
      const sortedData = chartData
        .sort((a, b) => b.value - a.value)
        .slice(0, 15);

      console.log(`Processed ${sortedData.length} products with data for ${filterMonthYear}`);

      // Update state
      setData(sortedData);
    } catch (error) {
      console.error('Error fetching pie chart data:', error);
      setData([]);
    } finally {
      setLoading(false);
    }
  }, [pouchDatabase, databasePrefix, by, selectedMonth, monthLabel]);

  // Fetch data when dependencies change
  useEffect(() => {
    fetchData();
  }, [fetchData]);

  // Render loading state or chart
  if (loading) {
    return (
      <div style={{ height: '100%', display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
        <Spin tip="Loading data..." />
      </div>
    );
  }

  if (data.length === 0) {
    return (
      <div style={{ height: '100%', display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
        <Empty description={`No data available for ${monthLabel}`} />
      </div>
    );
  }

  return <Pie {...config} />;
};

export default PieChart;