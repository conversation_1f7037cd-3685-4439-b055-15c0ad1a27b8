import ExcelT<PERSON><PERSON><PERSON> from "./ExcelToJson";
import <PERSON><PERSON><PERSON> from "./LineChart";
import POS from "./POS";
import <PERSON><PERSON><PERSON> from "./PieChart";
import React, { useEffect, useState } from "react";
import SalesPurchases from "./SalesPurchases";
import StatsGrid from "../../../../Components/Stats/StatsGrid";
import moment from "moment";
import { Card, Col, Row, Button, Modal } from "antd";


const Dashboard = (props) => {
  const [setstartData, setSetstartData] = useState({});
  const [incomeData, setIncomeData] = useState([
    {
      title: "Sales this month",
      icon: "receipt",
      value: 0,
      diff: 0,
    },
    {
      title: "Sales this Week",
      icon: "coin",
      value: "0",
      diff: 0,
    },
    {
      title: "Expence this month",
      icon: "discount",
      value: "0",
      diff: 0,
    },
    {
      title: "expence this week",
      icon: "user",
      value: "0",
      diff: 0,
    },
  ]);
  const { pouchDatabase, databasePrefix } = props;
  useEffect(() => {
    const SELECTED_BRANCH = localStorage.getItem("SELECTED_BRANCH");
    Promise.all([
      pouchDatabase("invoices", databasePrefix).getAllData(),
      pouchDatabase("expenses", databasePrefix).getAllData(),
      pouchDatabase("products", databasePrefix).getAllData(),
    ]).then((values) => {
      const [invoices, expences, products] = values;
      const sales_this_month = (
        SELECTED_BRANCH && SELECTED_BRANCH !== "none"
          ? invoices.filter(
              (i) =>
                i.branch === SELECTED_BRANCH &&
                moment(i.date).isSame(new Date(), "month")
            )
          : invoices
      )
        .filter((doc) => moment(doc.date).isSame(new Date(), "month"))
        .reduce(
          (p, c) =>
            p +
            c.items.reduce(
              (pv, cv) =>
                pv +
                (cv.price ? cv.price : 0) * (cv.quantity ? cv.quantity : 0),
              0
            ),
          0
        );

      const sales_this_week = invoices
        .filter((doc) => moment(doc.date).isSame(new Date(), "week"))
        .reduce(
          (p, c) =>
            p +
            c.items.reduce(
              (pv, cv) =>
                pv +
                (cv.price ? cv.price : 0) * (cv.quantity ? cv.quantity : 0),
              0
            ),
          0
        );

      const expence_this_month = (
        SELECTED_BRANCH && SELECTED_BRANCH !== "none"
          ? expences.filter(
              (i) =>
                i.branch === SELECTED_BRANCH &&
                moment(i.date).isSame(new Date(), "month")
            )
          : expences
      )
        .filter((doc) => moment(doc.date).isSame(new Date(), "month"))
        .reduce((p, c) => p + Number(c.amount), 0);

      const expence_this_week = (
        SELECTED_BRANCH && SELECTED_BRANCH !== "none"
          ? expences.filter(
              (i) =>
                i.branch === SELECTED_BRANCH &&
                moment(i.date).isSame(new Date(), "week")
            )
          : expences
      )
        .filter((doc) => moment(doc.date).isSame(new Date(), "week"))
        .reduce((p, c) => p + Number(c.amount), 0);

      setIncomeData([
        {
          title: "Sales this month",
          icon: "receipt",
          value: sales_this_month,
          diff: 0,
        },
        {
          title: "Sales this Week",
          icon: "coin",
          value: sales_this_week,
          diff: 0,
        },
        {
          title: "Expence this month",
          icon: "receipt",
          value: expence_this_month,
          diff: 0,
        },
        {
          title: "Expence this week",
          icon: "receipt",
          value: expence_this_week,
          diff: 0,
        },
      ]);
    });

    return () => {};
  }, []);

  return (
    <>
      <Row gutter={[5, 5]}>
       
            <POS
              pouchDatabase={pouchDatabase}
              databasePrefix={databasePrefix}
            />
            {/* <ExcelToJson pouchDatabase={pouchDatabase}
              databasePrefix={databasePrefix} /> */}

        <Col span={24}>
          <StatsGrid data={incomeData} />
        </Col>
        <Col xs={{ span: 24 }} md={{ span: 24 }} lg={{ span: 16 }}>
          <Card
            //   extra={tooltip}
            title="Business Analysis"
          >
            <div style={{ height: 300 }}>
              <LineChart
                pouchDatabase={pouchDatabase}
                databasePrefix={databasePrefix}
                // currentUser={currentUser}
              />
            </div>
          </Card>
        </Col>
        <Col xs={{ span: 24 }} md={{ span: 24 }} lg={{ span: 8 }}>
          <Card
            //   extra={tooltip}
            title="Top Products This Month By Quantity"
          >
            <div style={{ height: 300 }}>
              <PieChart
                pouchDatabase={pouchDatabase}
                databasePrefix={databasePrefix}
                by="quantity"
              />
            </div>
          </Card>
        </Col>
        <Col xs={{ span: 24 }} md={{ span: 24 }} lg={{ span: 16 }}>
          <Card
            //   extra={tooltip}
            title="Sales Vs Purchases"
          >
            <div style={{ height: 300 }}>
              <SalesPurchases
                pouchDatabase={pouchDatabase}
                databasePrefix={databasePrefix}
                // currentUser={currentUser}
              />
            </div>
          </Card>
        </Col>
        <Col xs={{ span: 24 }} md={{ span: 24 }} lg={{ span: 8 }}>
          <Card
            //   extra={tooltip}
            title="Top Products This Month By Profit"
          >
            <div style={{ height: 300 }}>
              <PieChart
                pouchDatabase={pouchDatabase}
                databasePrefix={databasePrefix}
                by="profit"
              />
            </div>
          </Card>
        </Col>
      </Row>
    </>
  );
};

export default Dashboard;