import POS from "./POS";
import React, { useState } from 'react';
import { Button, Collapse, Layout, Typography, Affix } from "antd";
import { ShoppingCartOutlined, LeftOutlined, RightOutlined } from "@ant-design/icons";
import { useMediaQuery } from "react-responsive";


const { Sider, Content } = Layout;
const { Title } = Typography;

const POSPanel = (props) => {
  const isMobile = useMediaQuery({ maxWidth: 768 });
  const [collapsed, setCollapsed] = useState(true);
  
  const toggleCollapsed = () => {
    setCollapsed(!collapsed);
  };
  
  return (
    <Layout style={{ height: '100%' }}>
      <Content style={{ 
        height: '100%', 
        transition: 'all 0.2s',
        marginRight: collapsed ? 0 : (isMobile ? '100%' : '400px')
      }}>
        {props.children}
      </Content>
      
      <Affix style={{ position: 'absolute', right: collapsed ? 0 : (isMobile ? '100%' : '400px'), top: '50%', zIndex: 1000 }}>
        <Button
          type="primary"
          onClick={toggleCollapsed}
          style={{ 
            height: '64px',
            width: '24px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            borderRadius: '4px 0 0 4px'
          }}
        >
          {collapsed ? <ShoppingCartOutlined /> : (isMobile ? <RightOutlined /> : <LeftOutlined />)}
        </Button>
      </Affix>
      
      <Sider
        width={isMobile ? '100%' : 400}
        style={{
          position: 'fixed',
          right: 0,
          height: '100%',
          transform: collapsed ? 'translateX(100%)' : 'translateX(0)',
          transition: 'all 0.3s',
          zIndex: 999,
          boxShadow: '-2px 0 8px rgba(0,0,0,0.15)'
        }}
      >
        <Layout style={{ height: '100%' }}>
          <div style={{ padding: '16px', background: '#fff', borderBottom: '1px solid #f0f0f0', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Title level={4} style={{ margin: 0 }}>Point of Sale</Title>
            <Button 
              type="text" 
              icon={isMobile ? <RightOutlined /> : <LeftOutlined />} 
              onClick={toggleCollapsed}
            />
          </div>
          <Content style={{ height: 'calc(100% - 64px)', overflow: 'auto' }}>
            <POS {...props} />
          </Content>
        </Layout>
      </Sider>
    </Layout>
  );
};

export default POSPanel;