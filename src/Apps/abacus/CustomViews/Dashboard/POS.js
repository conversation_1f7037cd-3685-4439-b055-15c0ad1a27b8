import React, { useState, useEffect, useRef } from "react";
import moment from "moment";
import { DeleteOutlined, BarcodeOutlined, SearchOutlined, ShoppingCartOutlined, AppstoreOutlined, TagsOutlined, PlusOutlined, MinusOutlined, DollarOutlined, InboxOutlined, StarOutlined, StarFilled } from "@ant-design/icons";
import { Layout, Menu, Card, Button, List, Divider, Input, InputNumber, Row, Col, Typography, Modal, Drawer, Tooltip, Select, message, App, Switch, Space, Tabs, Badge, Tag, } from "antd";
import { buffProducts } from "../../modulesProperties/utils";
import { useMediaQuery } from "react-responsive";


const { Header, Content, Sider } = Layout;
const { Title, Text } = Typography;

const POSContent = ({ pouchDatabase, databasePrefix, CRUD_USER }) => {
  const isMobile = useMediaQuery({ maxWidth: 768 });
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const [customers, setCustomers] = useState([]);
  const [selectedCustomer, setSelectedCustomer] = useState(null);
  const [savedOrders, setSavedOrders] = useState([]);
  const [showSavedOrders, setShowSavedOrders] = useState(false);
  const [drawerSize, setDrawerSize] = useState('80%');

  const showDrawer = () => {
    setIsDrawerOpen(true);
    // Adjust drawer size based on screen width
    if (isMobile) {
      setDrawerSize('100%');
    } else {
      setDrawerSize('80%');
    }
  };

  const handleClose = () => {
    setIsDrawerOpen(false);
  };

  const [cart, setCart] = useState([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [barcodeInput, setBarcodeInput] = useState("");
  const [products, setProducts] = useState([]);
  const [isLoadingProducts, setIsLoadingProducts] = useState(true);
  const [categories, setCategories] = useState([]);
  const [selectedCategory, setSelectedCategory] = useState(null);
  const [showFavoritesOnly, setShowFavoritesOnly] = useState(false);
  const [sortOption, setSortOption] = useState('name');
  const barcodeInputRef = useRef(null);

  const filteredProducts = products.filter((product) => {
    // First filter by category if one is selected
    const categoryMatch = !selectedCategory ||
      (product.category && product.category.value === selectedCategory);

    // Then filter by search term
    const searchMatch = !searchTerm ||
      product?.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      product?.code?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      false;

    // Filter by favorites if enabled
    const favoriteMatch = !showFavoritesOnly ||
      favoriteProducts.includes(product._id || product.id);

    return categoryMatch && searchMatch && favoriteMatch;
  }).sort((a, b) => {
    // Sort products based on selected sort option
    switch (sortOption) {
      case 'name':
        return a.name.localeCompare(b.name);
      case 'price_low':
        return a.price - b.price;
      case 'price_high':
        return b.price - a.price;
      case 'stock_low':
        return ((a.stoked || 0) - (a.sold || 0)) - ((b.stoked || 0) - (b.sold || 0));
      case 'stock_high':
        return ((b.stoked || 0) - (b.sold || 0)) - ((a.stoked || 0) - (a.sold || 0));
      case 'favorites':
        const aIsFav = favoriteProducts.includes(a._id || a.id) ? 1 : 0;
        const bIsFav = favoriteProducts.includes(b._id || b.id) ? 1 : 0;
        return bIsFav - aIsFav;
      default:
        return 0;
    }
  });

  useEffect(() => {
    // Load products
    setIsLoadingProducts(true);
    pouchDatabase("products", databasePrefix)
      .getAllData()
      .then((r) => buffProducts(r, pouchDatabase, databasePrefix, "products", localStorage.getItem("SELECTED_BRANCH")))
      .then((products) => {
        setProducts(products.map((p) => ({ ...p, id: p._id, price: p.sell })));

        // Extract unique categories from products
        const uniqueCategories = new Set();
        const categoryObjects = [];

        products.forEach(product => {
          if (product.category && product.category.value) {
            if (!uniqueCategories.has(product.category.value)) {
              uniqueCategories.add(product.category.value);
              categoryObjects.push({
                value: product.category.value,
                label: product.category.label || product.category.value
              });
            }
          }
        });

        setCategories(categoryObjects);
        setIsLoadingProducts(false);
      })
      .catch(error => {
        console.error('Error loading products:', error);
        message.error('Failed to load products');
        setIsLoadingProducts(false);
      });

    // Load customers
    pouchDatabase("customers", databasePrefix)
      .getAllData()
      .then((customers) => {
        setCustomers(
          customers.map((customer) => ({
            value: customer._id,
            label: customer.name,
            ...customer,
          }))
        );
      });

    // Load accounts
    pouchDatabase("accounts", databasePrefix)
      .getAllData()
      .then((accounts) => {
        const filteredAccounts = accounts.filter(account => !account._id.startsWith("_"));
        setAccounts(
          filteredAccounts.map((account) => ({
            value: account._id,
            label: account.name,
            ...account,
          }))
        );

        // Set default account if available
        if (filteredAccounts.length > 0) {
          setSelectedAccount({
            value: filteredAccounts[0]._id,
            label: filteredAccounts[0].name,
            ...filteredAccounts[0],
          });
        }
      });
  }, []);

  // Add calculation functions
  const calculateItemTotal = (item) => {
    return Number(item.quantity) * Number(item.price);
  };

  const getSubTotal = () => {
    return cart.reduce((sum, item) => sum + calculateItemTotal(item), 0);
  };

  // Add taxable state and payment method
  const [isTaxable, setIsTaxable] = useState(false);
  const [paymentMethod, setPaymentMethod] = useState("Cash");
  const [isProcessingPayment, setIsProcessingPayment] = useState(false);
  const [showPrintPreview, setShowPrintPreview] = useState(false);
  const [currentInvoice, setCurrentInvoice] = useState(null);
  const [accounts, setAccounts] = useState([]);
  const [selectedAccount, setSelectedAccount] = useState(null);

  const paymentMethods = [
    { value: "Cash", label: "Cash" },
    { value: "Card", label: "Card/Debit/Credit" },
    { value: "Mobile Money", label: "Mobile Money" },
    { value: "Bank Transfer", label: "Bank Transfer" },
    { value: "Cheque", label: "Cheque" },
  ];

  const getTaxAmount = () => {
    return isTaxable ? getSubTotal() * 0.18 : 0;
  };

  const getTotal = () => {
    return getSubTotal() + getTaxAmount();
  };

  // Handle barcode scanning
  const handleBarcodeInputChange = (e) => {
    setBarcodeInput(e.target.value);
  };

  const handleBarcodeSubmit = (e) => {
    e.preventDefault();
    if (!barcodeInput) return;

    // Find product by code/barcode
    const product = products.find(p =>
      p.code?.toLowerCase() === barcodeInput.toLowerCase() ||
      p.barcode?.toLowerCase() === barcodeInput.toLowerCase()
    );

    if (product) {
      addToCart(product);
      message.success(`Added ${product.name} to cart`);
    } else {
      message.error(`No product found with code/barcode: ${barcodeInput}`);
    }

    // Clear input and focus back on it for next scan
    setBarcodeInput('');
    if (barcodeInputRef.current) {
      barcodeInputRef.current.focus();
    }
  };

  const addToCart = (product) => {
    const existingItem = cart.find(item => item.id === product._id || item.id === product.id);
    const productId = product._id || product.id;

    if (existingItem) {
      // Update quantity if item exists
      updateQuantity(existingItem.id, existingItem.quantity + 1);
    } else {
      // Add new item
      setCart([...cart, {
        id: productId,
        name: product.name,
        quantity: 1,
        price: product.price || 0,
        stock: (product.stoked || 0) - (product.sold || 0)
      }]);
    }
  };

  const updateQuantity = (productId, newQuantity) => {
    if (newQuantity <= 0) return;

    const item = cart.find(item => item.id === productId);
    if (!item) return;

    // Check if new quantity exceeds available stock
    if (newQuantity > item.stock) {
      message.error(`Only ${item.stock} items available in stock`);
      return;
    }

    setCart(cart.map(item =>
      item.id === productId
        ? { ...item, quantity: Number(newQuantity) }
        : item
    ));
  };

  const updatePrice = (productId, newPrice) => {
    if (newPrice < 0) return;

    setCart(cart.map(item =>
      item.id === productId
        ? { ...item, price: Number(newPrice) }
        : item
    ));
  };

  const removeItem = (productId) => {
    setCart(cart.filter(item => item.id !== productId));
  };

  const handleCheckout = async () => {
    if (!selectedCustomer || cart.length === 0) return;

    setIsProcessingPayment(true);

    try {
      const subTotal = getSubTotal();
      const taxAmount = getTaxAmount();
      const total = getTotal();

      const invoiceData = {
        date: moment().toISOString(),
        customer: {
          value: selectedCustomer.value,
          label: selectedCustomer.label
        },
        items: cart.map(item => ({
          product: {
            value: item.id,
            label: item.name
          },
          quantity: Number(item.quantity),
          price: Number(item.price),
          total: calculateItemTotal(item)
        })),
        subTotal: subTotal,
        taxable: isTaxable,
        taxAmount: taxAmount,
        total: total,
        balance: total,
        not_paid: false,
        payment_method: paymentMethod,
        account: selectedAccount ? {
          value: selectedAccount.value,
          label: selectedAccount.label
        } : undefined,
        branch: localStorage.getItem("SELECTED_BRANCH"),
        createdAt: moment().toISOString(),
        entrant: JSON.parse(localStorage.getItem("USER")),
      };

      // Save the invoice
      const savedInvoice = await pouchDatabase("invoices", databasePrefix).saveDocument(invoiceData, CRUD_USER);

      // Create a receipt automatically
      try {
        if (!selectedAccount) {
          throw new Error("No account selected for receipt");
        }

        const receiptData = {
          invoice: {
            label: "INV-" + savedInvoice._id + " " + selectedCustomer.label,
            value: savedInvoice._id,
          },
          amount: total,
          date: moment().toISOString(),
          method_of_payment: paymentMethod,
          account: {
            label: selectedAccount.label,
            value: selectedAccount.value,
          },
          description: `Payment for ${selectedCustomer.label} (INV-${savedInvoice._id})`,
          branch: localStorage.getItem("SELECTED_BRANCH"),
        };

        await pouchDatabase("receipts", databasePrefix).saveDocument(receiptData, CRUD_USER);
        message.success("Receipt created successfully");
      } catch (receiptError) {
        console.error("Receipt creation error:", receiptError);
        message.warning("Invoice created but receipt creation failed");
      }

      // Set current invoice for printing
      setCurrentInvoice(savedInvoice);
      setShowPrintPreview(true);

      // Reset cart and customer selection
      setCart([]);
      setSelectedCustomer(null);
      message.success("Sale completed successfully");
    } catch (error) {
      console.error("Checkout error:", error);
      message.error("Failed to complete sale: " + error.message);
    } finally {
      setIsProcessingPayment(false);
    }
  };

  const handlePrintComplete = () => {
    setShowPrintPreview(false);
    setCurrentInvoice(null);
    setIsDrawerOpen(false);
  };

  // Hold/Save order functionality
  const handleSaveOrder = () => {
    if (!selectedCustomer || cart.length === 0) {
      message.error("Please select a customer and add items to cart");
      return;
    }

    const savedOrder = {
      id: Date.now().toString(),
      customer: selectedCustomer,
      items: [...cart],
      taxable: isTaxable,
      timestamp: moment().toISOString(),
      total: getTotal()
    };

    // Add to saved orders
    setSavedOrders([...savedOrders, savedOrder]);

    // Clear current cart
    setCart([]);
    setSelectedCustomer(null);
    setIsTaxable(false);

    message.success("Order saved successfully");
  };

  const handleRecallOrder = (order) => {
    // Confirm if current cart has items
    if (cart.length > 0) {
      if (!window.confirm("This will replace your current cart. Continue?")) {
        return;
      }
    }

    // Recall the order
    setCart(order.items);
    setSelectedCustomer(order.customer);
    setIsTaxable(order.taxable);

    // Remove from saved orders
    setSavedOrders(savedOrders.filter(o => o.id !== order.id));
    setShowSavedOrders(false);

    message.success("Order recalled successfully");
  };

  const handleDeleteSavedOrder = (orderId) => {
    setSavedOrders(savedOrders.filter(o => o.id !== orderId));
    message.success("Saved order deleted");
  };

  const PrintReceipt = ({ invoice }) => {
    if (!invoice) return null;

    return (
      <div className="print-container" style={{ padding: '20px', maxWidth: '400px', margin: '0 auto' }}>
        <div style={{ textAlign: 'center', marginBottom: '20px' }}>
          <h2 style={{ margin: '5px 0' }}>RECEIPT</h2>
          <p style={{ margin: '5px 0' }}>Invoice #: {invoice._id}</p>
          <p style={{ margin: '5px 0' }}>Date: {moment(invoice.date).format('DD/MM/YYYY HH:mm')}</p>
        </div>

        <div style={{ marginBottom: '20px' }}>
          <p><strong>Customer:</strong> {invoice.customer.label}</p>
          <p><strong>Payment Method:</strong> {invoice.payment_method}</p>
          <p><strong>Account:</strong> {invoice.account?.label || 'Not specified'}</p>
        </div>

        <table style={{ width: '100%', borderCollapse: 'collapse', marginBottom: '20px' }}>
          <thead>
            <tr>
              <th style={{ textAlign: 'left', padding: '5px', borderBottom: '1px solid #ddd' }}>Item</th>
              <th style={{ textAlign: 'right', padding: '5px', borderBottom: '1px solid #ddd' }}>Qty</th>
              <th style={{ textAlign: 'right', padding: '5px', borderBottom: '1px solid #ddd' }}>Price</th>
              <th style={{ textAlign: 'right', padding: '5px', borderBottom: '1px solid #ddd' }}>Total</th>
            </tr>
          </thead>
          <tbody>
            {invoice.items.map((item, index) => (
              <tr key={index}>
                <td style={{ padding: '5px', borderBottom: '1px solid #ddd' }}>{item.product.label}</td>
                <td style={{ textAlign: 'right', padding: '5px', borderBottom: '1px solid #ddd' }}>{item.quantity}</td>
                <td style={{ textAlign: 'right', padding: '5px', borderBottom: '1px solid #ddd' }}>{item.price.toLocaleString()}</td>
                <td style={{ textAlign: 'right', padding: '5px', borderBottom: '1px solid #ddd' }}>{item.total.toLocaleString()}</td>
              </tr>
            ))}
          </tbody>
        </table>

        <div style={{ marginBottom: '20px' }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', margin: '5px 0' }}>
            <span>Subtotal:</span>
            <span>{invoice.subTotal.toLocaleString()}</span>
          </div>
          {invoice.taxable && (
            <div style={{ display: 'flex', justifyContent: 'space-between', margin: '5px 0' }}>
              <span>Tax (18%):</span>
              <span>{invoice.taxAmount.toLocaleString()}</span>
            </div>
          )}
          <div style={{ display: 'flex', justifyContent: 'space-between', margin: '5px 0', fontWeight: 'bold' }}>
            <span>Total:</span>
            <span>{invoice.total.toLocaleString()}</span>
          </div>
        </div>

        <div style={{ textAlign: 'center', marginTop: '30px' }}>
          <p>Thank you for your business!</p>
        </div>

        <div style={{ marginTop: '20px', textAlign: 'center' }}>
          <Button type="primary" onClick={() => window.print()}>Print Receipt</Button>
          <Button style={{ marginLeft: '10px' }} onClick={handlePrintComplete}>Close</Button>
        </div>
      </div>
    );
  };

  // Track product quantities for quick-add
  const [quickAddQuantities, setQuickAddQuantities] = useState({});
  const [favoriteProducts, setFavoriteProducts] = useState([]);

  // Load favorite products from localStorage
  useEffect(() => {
    const savedFavorites = localStorage.getItem('POS_FAVORITE_PRODUCTS');
    if (savedFavorites) {
      try {
        setFavoriteProducts(JSON.parse(savedFavorites));
      } catch (e) {
        console.error('Error loading favorite products', e);
      }
    }
  }, []);

  // Save favorites to localStorage
  const toggleFavorite = (productId) => {
    const newFavorites = favoriteProducts.includes(productId)
      ? favoriteProducts.filter(id => id !== productId)
      : [...favoriteProducts, productId];

    setFavoriteProducts(newFavorites);
    localStorage.setItem('POS_FAVORITE_PRODUCTS', JSON.stringify(newFavorites));
  };

  const updateQuickAddQuantity = (productId, change) => {
    setQuickAddQuantities(prev => {
      const currentQty = prev[productId] || 1;
      const newQty = Math.max(1, currentQty + change);
      return { ...prev, [productId]: newQty };
    });
  };

  const quickAddToCart = (product) => {
    const quantity = quickAddQuantities[product._id || product.id] || 1;
    const productId = product._id || product.id;
    const existingItem = cart.find(item => item.id === productId);

    if (existingItem) {
      updateQuantity(productId, existingItem.quantity + quantity);
    } else {
      setCart([...cart, {
        id: productId,
        name: product.name,
        quantity: quantity,
        price: product.price || 0,
        stock: (product.stoked || 0) - (product.sold || 0)
      }]);
    }

    // Reset quantity after adding
    setQuickAddQuantities(prev => ({ ...prev, [productId]: 1 }));
    message.success(`Added ${quantity} × ${product.name} to cart`);
  };

  const productCard = (product) => {
    const stockLevel = (product.stoked || 0) - (product.sold || 0);
    const hasLowStock = stockLevel <= 5 && stockLevel > 0;
    const isOutOfStock = stockLevel <= 0;
    const productId = product._id || product.id;
    const isFavorite = favoriteProducts.includes(productId);
    const quickAddQty = quickAddQuantities[productId] || 1;

    // Calculate discount or special pricing if available
    const hasDiscount = product.originalPrice && product.originalPrice > product.price;
    const discountPercent = hasDiscount ?
      Math.round((1 - (product.price / product.originalPrice)) * 100) : 0;

    return (
      <Card
        size={isMobile ? "small" : "default"}
        className="product-card"
        style={{
          height: '100%',
          display: 'flex',
          flexDirection: 'column'
        }}
        bodyStyle={{
          flex: 1,
          display: 'flex',
          flexDirection: 'column',
          padding: isMobile ? '8px' : '12px'
        }}
        actions={[
          <div key="quantity" style={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
            <Button
              size="small"
              icon={<MinusOutlined />}
              onClick={() => updateQuickAddQuantity(productId, -1)}
              disabled={quickAddQty <= 1}
            />
            <span style={{ margin: '0 8px' }}>{quickAddQty}</span>
            <Button
              size="small"
              icon={<PlusOutlined />}
              onClick={() => updateQuickAddQuantity(productId, 1)}
              disabled={quickAddQty >= stockLevel && !isOutOfStock}
            />
          </div>,
          <Button
            type="primary"
            onClick={() => quickAddToCart(product)}
            size="small"
            icon={<ShoppingCartOutlined />}
            disabled={isOutOfStock}
            block
          >
            {isOutOfStock ? "Out of Stock" : "Add"}
          </Button>,
        ]}
        cover={
          <div style={{
            position: 'relative',
            padding: '8px 8px 0 8px',
            background: '#f5f5f5',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            height: '40px'
          }}>
            <div style={{
              position: 'absolute',
              top: '8px',
              right: '8px',
              cursor: 'pointer',
              color: isFavorite ? '#faad14' : '#d9d9d9',
              fontSize: '16px'
            }}>
              <Tooltip title={isFavorite ? "Remove from favorites" : "Add to favorites"}>
                {isFavorite ?
                  <StarFilled onClick={() => toggleFavorite(productId)} /> :
                  <StarOutlined onClick={() => toggleFavorite(productId)} />}
              </Tooltip>
            </div>
            {product.code && (
              <Tag color="blue">{product.code}</Tag>
            )}
          </div>
        }
      >
        <div style={{ flex: 1, display: 'flex', flexDirection: 'column' }}>
          <Tooltip title={product.name}>
            <div style={{
              fontWeight: 'bold',
              fontSize: isMobile ? '12px' : '14px',
              marginBottom: '4px',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              whiteSpace: 'nowrap'
            }}>
              {product.name}
            </div>
          </Tooltip>

          {/* Display measurements and units if available */}
          {product.measurements && product.units && (
            <div style={{
              fontSize: isMobile ? '10px' : '12px',
              color: '#8c8c8c',
              marginBottom: '4px'
            }}>
              {product.measurements} {product.units}
            </div>
          )}

          <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '4px' }}>
            <div>
              <Text
                type="secondary"
                style={{
                  fontSize: isMobile ? '10px' : '12px',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '4px'
                }}
              >
                <InboxOutlined />
                {isOutOfStock ? (
                  <Text type="danger" style={{ fontSize: isMobile ? '10px' : '12px' }}>Out of stock</Text>
                ) : hasLowStock ? (
                  <Text type="warning" style={{ fontSize: isMobile ? '10px' : '12px' }}>
                    {stockLevel} left
                  </Text>
                ) : (
                  <Text type="success" style={{ fontSize: isMobile ? '10px' : '12px' }}>
                    {stockLevel} in stock
                  </Text>
                )}
              </Text>
            </div>

            {product.category && (
              <Tooltip title={`Category: ${product.category.label}`}>
                <Tag
                  style={{
                    fontSize: isMobile ? '10px' : '12px',
                    lineHeight: 1.2,
                    margin: 0,
                    padding: '0 4px'
                  }}
                >
                  {product.category.label}
                </Tag>
              </Tooltip>
            )}
          </div>

          <div style={{
            marginTop: 'auto',
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'flex-end'
          }}>
            <div>
              <div style={{
                fontSize: isMobile ? '14px' : '16px',
                fontWeight: 'bold',
                color: hasDiscount ? '#52c41a' : 'inherit',
                display: 'flex',
                alignItems: 'center',
                gap: '4px'
              }}>
                {product.price.toLocaleString()}
                {product.units && product.units !== 'pc' && (
                  <span style={{ fontSize: isMobile ? '9px' : '11px', marginLeft: '2px' }}>
                  </span>
                )}
              </div>

              {hasDiscount && (
                <div style={{
                  fontSize: isMobile ? '10px' : '12px',
                  textDecoration: 'line-through',
                  color: '#999'
                }}>
                  {product.originalPrice.toLocaleString()}
                </div>
              )}
            </div>

            {hasDiscount && (
              <Tag color="green" style={{ margin: 0 }}>
                {discountPercent}% OFF
              </Tag>
            )}
          </div>
        </div>
      </Card>
    );
  };



  const cartItem = (item) => (
    <List.Item>
      <Row style={{ width: "100%" }} gutter={[8, 8]} align="middle">
        {isMobile ? (
          // Mobile layout for cart items
          <>
            <Col span={24}>
              <strong>{item.name}</strong>
            </Col>
            <Col span={8}>
              <InputNumber
                size="small"
                min={1}
                max={item.stock}
                value={item.quantity}
                onChange={(quantity) => updateQuantity(item.id, quantity)}
                style={{ width: '100%' }}
              />
            </Col>
            <Col span={8}>
              <InputNumber
                size="small"
                min={0}
                value={item.price}
                onChange={(price) => updatePrice(item.id, price)}
                formatter={value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                parser={value => value.replace(/\$\s?|(,*)/g, '')}
                style={{ width: '100%' }}
              />
            </Col>
            <Col span={6}>
              {calculateItemTotal(item).toLocaleString()}
            </Col>
            <Col span={2}>
              <Button
                size="small"
                onClick={() => removeItem(item.id)}
                type="text"
                danger
                icon={<DeleteOutlined />}
              />
            </Col>
          </>
        ) : (
          // Desktop layout for cart items
          <>
            <Col span={8}>{item.name}</Col>
            <Col span={2}>
              <InputNumber
                min={1}
                value={item.quantity}
                onChange={(quantity) => updateQuantity(item.id, quantity)}
              />
            </Col>
            <Col span={8}>
              <InputNumber
                min={0}
                value={item.price}
                onChange={(price) => updatePrice(item.id, price)}
                formatter={(value) => `X ${value}`}
                parser={(value) => value.replace("X", "")}
              />
            </Col>
            <Col span={4}>
              {(item.price * item.quantity).toLocaleString()}
            </Col>
            <Col span={2}>
              <Tooltip title="Remove">
                <Button
                  onClick={() => removeItem(item.id)}
                  type="danger"
                  shape="circle"
                  icon={<DeleteOutlined />}
                />
              </Tooltip>
            </Col>
          </>
        )}
      </Row>
    </List.Item>
  );

  const CartSummary = () => (
    <>
      <Divider />
      <Row justify="space-between" align="middle" style={{ marginBottom: 8 }}>
        <Col><Text>Subtotal:</Text></Col>
        <Col><Text>{getSubTotal().toLocaleString()}</Text></Col>
      </Row>
      <Row justify="space-between" align="middle" style={{ marginBottom: 8 }}>
        <Col>
          <Space>
            <Text>Tax (18%):</Text>
            <Switch
              checked={isTaxable}
              onChange={setIsTaxable}
              size="small"
            />
          </Space>
        </Col>
        <Col><Text>{getTaxAmount().toLocaleString()}</Text></Col>
      </Row>
      <Row justify="space-between" align="middle">
        <Col><Text strong>Total:</Text></Col>
        <Col><Text strong>{getTotal().toLocaleString()}</Text></Col>
      </Row>
    </>
  );

  return (
    <>
      <Button type="primary" onClick={showDrawer} icon={<ShoppingCartOutlined />}>
        POS
      </Button>
      <Drawer
        title={<Title level={isMobile ? 4 : 3}>Point of Sale</Title>}
        placement="right"
        width={drawerSize}
        onClose={handleClose}
        open={isDrawerOpen}
        bodyStyle={{ padding: 0, height: '100%' }}
        contentWrapperStyle={{ height: '100%' }}
        maskClosable={false}
        keyboard={false}
        extra={
          <Button
            type="primary"
            danger
            onClick={handleClose}
          >
            Close
          </Button>
        }
      >
        {showPrintPreview ? (
          <PrintReceipt invoice={currentInvoice} />
        ) : showSavedOrders ? (
          <div>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '16px' }}>
              <Title level={4}>Saved Orders</Title>
              <Button onClick={() => setShowSavedOrders(false)}>Back to POS</Button>
            </div>

            {savedOrders.length === 0 ? (
              <div style={{ textAlign: 'center', padding: '20px' }}>
                <p>No saved orders found</p>
              </div>
            ) : (
              <List
                itemLayout="horizontal"
                dataSource={savedOrders}
                renderItem={order => (
                  <List.Item
                    actions={[
                      <Button
                        key="recall"
                        type="primary"
                        onClick={() => handleRecallOrder(order)}
                        size={isMobile ? "small" : "default"}
                      >
                        Recall
                      </Button>,
                      <Button
                        key="delete"
                        danger
                        onClick={() => handleDeleteSavedOrder(order.id)}
                        size={isMobile ? "small" : "default"}
                      >
                        Delete
                      </Button>
                    ]}
                  >
                    <List.Item.Meta
                      title={<span>{order.customer.label} - {moment(order.timestamp).format('DD/MM/YYYY HH:mm')}</span>}
                      description={
                        <>
                          <p>Items: {order.items.length} | Total: {order.total.toLocaleString()}</p>
                          <div style={{ maxHeight: '100px', overflow: 'auto' }}>
                            {order.items.map((item, index) => (
                              <Tag key={index}>{item.quantity} x {item.name}</Tag>
                            ))}
                          </div>
                        </>
                      }
                    />
                  </List.Item>
                )}
              />
            )}
          </div>
        ) : (
          <Layout style={{ height: '100%' }}>
            <Content style={{ height: '100%', overflow: 'auto' }}>
              <div style={{ padding: isMobile ? "8px" : "16px", height: '100%', display: 'flex', flexDirection: 'column' }}>
                <Row gutter={[16, 16]} style={{ flex: 1, minHeight: 0 }}>
                  <Col xs={24} md={14} style={{ display: 'flex', flexDirection: 'column', height: '100%' }}>
                    <Row gutter={[8, 8]} style={{ marginBottom: "8px" }}>
                      <Col span={isMobile ? 16 : 18}>
                        <Select
                          showSearch
                          style={{ width: "100%" }}
                          placeholder="Select Customer"
                          optionFilterProp="label"
                          options={customers}
                          value={selectedCustomer?.value}
                          onChange={(value, option) => setSelectedCustomer(option)}
                          size={isMobile ? "small" : "default"}
                        />
                      </Col>
                      <Col span={isMobile ? 8 : 6}>
                        <Button.Group style={{ width: "100%" }}>
                          <Tooltip title="Save Order">
                            <Button
                              onClick={handleSaveOrder}
                              style={{ width: "50%" }}
                              disabled={!selectedCustomer || cart.length === 0}
                              size={isMobile ? "small" : "default"}
                            >
                              Save
                            </Button>
                          </Tooltip>
                          <Tooltip title="Recall Saved Orders">
                            <Button
                              onClick={() => setShowSavedOrders(true)}
                              style={{ width: "50%" }}
                              disabled={savedOrders.length === 0}
                              size={isMobile ? "small" : "default"}
                            >
                              Recall
                            </Button>
                          </Tooltip>
                        </Button.Group>
                      </Col>
                    </Row>

                    {/* Barcode Scanner Input */}
                    <form onSubmit={handleBarcodeSubmit} style={{ marginBottom: "8px" }}>
                      <Input
                        ref={barcodeInputRef}
                        placeholder="Scan barcode or enter product code"
                        value={barcodeInput}
                        onChange={handleBarcodeInputChange}
                        prefix={<BarcodeOutlined />}
                        suffix={
                          <Button
                            type="primary"
                            size="small"
                            onClick={handleBarcodeSubmit}
                            icon={<SearchOutlined />}
                          />
                        }
                        size={isMobile ? "small" : "default"}
                      />
                    </form>

                    <Divider orientation="left" style={{ margin: '8px 0' }}>
                      <Space>
                        <span>Product Catalog</span>
                        <Badge
                          count={filteredProducts.length}
                          style={{ backgroundColor: '#52c41a' }}
                          overflowCount={9999}
                          title={`${filteredProducts.length} products`}
                        />
                      </Space>
                    </Divider>

                    <Row gutter={[8, 8]} style={{ marginBottom: "8px" }}>
                      <Col xs={24} md={16}>
                        <Input
                          placeholder="Search for products by name or code"
                          onChange={(e) => setSearchTerm(e.target.value)}
                          prefix={<SearchOutlined />}
                          size={isMobile ? "small" : "default"}
                          allowClear
                        />
                      </Col>
                      <Col xs={24} md={8}>
                        <Select
                          style={{ width: '100%' }}
                          placeholder="Sort by"
                          value={sortOption}
                          onChange={setSortOption}
                          size={isMobile ? "small" : "default"}
                          options={[
                            { value: 'name', label: 'Name (A-Z)' },
                            { value: 'price_low', label: 'Price (Low to High)' },
                            { value: 'price_high', label: 'Price (High to Low)' },
                            { value: 'stock_low', label: 'Stock (Low to High)' },
                            { value: 'stock_high', label: 'Stock (High to Low)' },
                            { value: 'favorites', label: 'Favorites First' },
                          ]}
                        />
                      </Col>
                    </Row>

                    {/* Category Filters */}
                    <div style={{ marginBottom: "8px", display: "flex", flexWrap: "wrap", gap: "8px", alignItems: "center" }}>
                      <Text strong style={{ marginRight: '8px' }}>Categories:</Text>
                      <Switch
                        size="small"
                        checked={showFavoritesOnly}
                        onChange={setShowFavoritesOnly}
                        checkedChildren={<StarFilled />}
                        unCheckedChildren={<StarOutlined />}
                        style={{ marginRight: '8px' }}
                      />
                      <Text type={showFavoritesOnly ? 'success' : 'secondary'} style={{ fontSize: '12px' }}>
                        {showFavoritesOnly ? 'Showing favorites only' : 'Show all products'}
                      </Text>
                      <Tag.CheckableTag
                        checked={selectedCategory === null}
                        onChange={() => setSelectedCategory(null)}
                        style={{ fontSize: isMobile ? '12px' : '14px' }}
                      >
                        <AppstoreOutlined /> All Categories
                      </Tag.CheckableTag>

                      {categories.map(category => (
                        <Tag.CheckableTag
                          key={category.value}
                          checked={selectedCategory === category.value}
                          onChange={() => setSelectedCategory(category.value)}
                          style={{ fontSize: isMobile ? '12px' : '14px' }}
                        >
                          <TagsOutlined /> {category.label}
                        </Tag.CheckableTag>
                      ))}
                    </div>

                    <div style={{ flex: 1, overflow: 'hidden', minHeight: 0 }}>
                      <Row
                        gutter={[12, 12]}
                        style={{
                          height: '100%',
                          overflow: "auto",
                          marginRight: '-8px',
                          paddingRight: '8px'
                        }}
                      >
                        {isLoadingProducts ? (
                          // Loading state
                          Array.from({ length: 12 }).map((_, index) => (
                            <Col xs={12} sm={12} md={8} lg={8} key={`skeleton-${index}`}>
                              <Card
                                loading={true}
                                style={{ height: '100%' }}
                                bodyStyle={{ padding: isMobile ? '8px' : '12px' }}
                              />
                            </Col>
                          ))
                        ) : filteredProducts.length > 0 ? (
                          // Products grid
                          filteredProducts.map((product) => (
                            <Col xs={12} sm={12} md={8} lg={8} key={product.id || product._id}>
                              {productCard(product)}
                            </Col>
                          ))
                        ) : products.length === 0 ? (
                          // No products in database
                          <Col span={24} style={{ textAlign: 'center', padding: '40px 0' }}>
                            <div style={{ fontSize: '64px', color: '#d9d9d9', marginBottom: '16px' }}>
                              <InboxOutlined />
                            </div>
                            <Title level={4}>No Products Available</Title>
                            <Text type="secondary">There are no products in the database.</Text>
                          </Col>
                        ) : (
                          // No products match filters
                          <Col span={24} style={{ textAlign: 'center', padding: '40px 0' }}>
                            <div style={{ fontSize: '64px', color: '#d9d9d9', marginBottom: '16px' }}>
                              <SearchOutlined />
                            </div>
                            <Title level={4}>No Products Found</Title>
                            <Text type="secondary">Try adjusting your search or filters.</Text>
                            {showFavoritesOnly && (
                              <div style={{ marginTop: '16px' }}>
                                <Button
                                  type="primary"
                                  onClick={() => setShowFavoritesOnly(false)}
                                  icon={<StarOutlined />}
                                >
                                  Show All Products
                                </Button>
                              </div>
                            )}
                          </Col>
                        )}
                      </Row>
                    </div>
                  </Col>

                  <Col xs={24} md={10} style={{ display: 'flex', flexDirection: 'column', height: '100%' }}>
                    <Divider orientation="left" style={{ margin: '8px 0' }}>Cart Summary</Divider>
                    <List
                      bordered
                      size={isMobile ? "small" : "default"}
                      dataSource={cart}
                      renderItem={cartItem}
                      style={{
                        flex: 1,
                        overflow: "auto"
                      }}
                    />
                    <CartSummary />

                    {/* Payment Method and Account Selection */}
                    <div style={{ marginTop: '8px', marginBottom: '8px' }}>
                      <Row gutter={[16, 16]}>
                        <Col span={12}>
                          <div style={{ marginBottom: '8px' }}>
                            <Text strong>Payment Method:</Text>
                          </div>
                          <Select
                            style={{ width: '100%' }}
                            value={paymentMethod}
                            onChange={setPaymentMethod}
                            options={paymentMethods}
                            size={isMobile ? "small" : "default"}
                          />
                        </Col>
                        <Col span={12}>
                          <div style={{ marginBottom: '8px' }}>
                            <Text strong>Account:</Text>
                          </div>
                          <Select
                            style={{ width: '100%' }}
                            value={selectedAccount?.value}
                            onChange={(value, option) => setSelectedAccount(option)}
                            options={accounts}
                            size={isMobile ? "small" : "default"}
                            placeholder="Select Account"
                            showSearch
                            optionFilterProp="label"
                          />
                        </Col>
                      </Row>
                    </div>

                    <Button
                      type="primary"
                      block
                      size={isMobile ? "large" : "default"}
                      style={{ marginTop: "8px" }}
                      onClick={handleCheckout}
                      disabled={!selectedCustomer || !selectedAccount || cart.length === 0}
                      loading={isProcessingPayment}
                    >
                      Complete Sale
                    </Button>
                  </Col>
                </Row>
              </div>
            </Content>
          </Layout>
        )}
      </Drawer>
    </>
  );
};

const POS = (props) => {
  return (
    <App>
      <POSContent {...props} />
    </App>
  );
};

export default POS;