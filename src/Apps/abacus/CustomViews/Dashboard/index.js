import AdvancedDashboard from "./AdvancedDashboard";
import DashboardWrapper from "../../../../Components/Dashboard/DashboardWrapper";
import React from "react";
import StandardDashboard from "./StandardDashboard";


const Dashboard = (props) => {
  // Make sure modulesProperties is passed to the dashboard components

  // const fixing_stock = async () => {
  //   const { pouchDatabase, databasePrefix } = props;
  //   pouchDatabase("stock_purchasing", databasePrefix).getAllData().then((result) => {
  //     const stockwithoutitems = result.filter((item) => !item.items)
  //     console.log("old implimentation of stock purchase", stockwithoutitems)
  //     stockwithoutitems.forEach(async (item) => {
  //       delete item._rev
  //       const newdoc = {
  //         ...item,
  //         items: [{
  //           product: item.product,
  //           quantity: item.quantity,
  //           price: item.unit_cost ? item.unit_cost : item.total_cost / item.quantity
  //         }]
  //       }
  //       // const rez = await pouchDatabase("stock_purchasing", databasePrefix).saveDocument(newdoc)
  //       // console.log("old implimentation of stock purchase", rez)

  //     });
  //   });
  // }

  // fixing_stock()

  return (
    <DashboardWrapper
      StandardDashboard={() => <StandardDashboard {...props} />}
      AdvancedDashboard={() => <AdvancedDashboard {...props} />}
      currentUser={props.currentUser}
    />
  );
};

export default Dashboard;