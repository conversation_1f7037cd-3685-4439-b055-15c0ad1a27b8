import { getCachedAbacusDashboardData, cacheAbacusDashboardData, hasAbacusDashboardCache, clearAbacusDashboardCache, trackAbacusCacheMetrics, getAbacusCacheMetrics, shouldRefreshAbacusCache } from "../AbacusDashboardCache";


 /**
  * Tests for Abacus Dashboard Cache functionality
 */


// Mock localStorage
const localStorageMock = (() => {
  let store = {};
  return {
    getItem: (key) => store[key] || null,
    setItem: (key, value) => store[key] = value.toString(),
    removeItem: (key) => delete store[key],
    clear: () => store = {}
  };
})();

Object.defineProperty(window, 'localStorage', {
  value: localStorageMock
});

describe('AbacusDashboardCache', () => {
  const testDatabasePrefix = 'test_prefix';
  const testData = {
    invoices: [{ _id: '1', amount: 100 }],
    expenses: [{ _id: '2', amount: 50 }],
    products: [{ _id: '3', name: 'Test Product' }],
    purchases: [{ _id: '4', amount: 75 }],
    timestamp: Date.now()
  };

  beforeEach(() => {
    localStorage.clear();
  });

  describe('cacheAbacusDashboardData', () => {
    it('should cache data successfully', () => {
      const result = cacheAbacusDashboardData(testData, testDatabasePrefix);
      expect(result).toBe(true);
    });

    it('should return false for null data', () => {
      const result = cacheAbacusDashboardData(null, testDatabasePrefix);
      expect(result).toBe(false);
    });
  });

  describe('getCachedAbacusDashboardData', () => {
    it('should retrieve cached data', () => {
      cacheAbacusDashboardData(testData, testDatabasePrefix);
      const cachedData = getCachedAbacusDashboardData(testDatabasePrefix);
      
      expect(cachedData).toEqual(testData);
    });

    it('should return null for non-existent cache', () => {
      const cachedData = getCachedAbacusDashboardData('non_existent');
      expect(cachedData).toBe(null);
    });

    it('should return null for expired cache', () => {
      cacheAbacusDashboardData(testData, testDatabasePrefix);
      
      // Mock expired cache by setting very short expiration
      const cachedData = getCachedAbacusDashboardData(testDatabasePrefix, 0);
      expect(cachedData).toBe(null);
    });
  });

  describe('hasAbacusDashboardCache', () => {
    it('should return true for valid cache', () => {
      cacheAbacusDashboardData(testData, testDatabasePrefix);
      const hasCache = hasAbacusDashboardCache(testDatabasePrefix);
      expect(hasCache).toBe(true);
    });

    it('should return false for non-existent cache', () => {
      const hasCache = hasAbacusDashboardCache('non_existent');
      expect(hasCache).toBe(false);
    });

    it('should return false for expired cache', () => {
      cacheAbacusDashboardData(testData, testDatabasePrefix);
      const hasCache = hasAbacusDashboardCache(testDatabasePrefix, 0);
      expect(hasCache).toBe(false);
    });
  });

  describe('clearAbacusDashboardCache', () => {
    it('should clear cache successfully', () => {
      cacheAbacusDashboardData(testData, testDatabasePrefix);
      expect(hasAbacusDashboardCache(testDatabasePrefix)).toBe(true);
      
      const result = clearAbacusDashboardCache(testDatabasePrefix);
      expect(result).toBe(true);
      expect(hasAbacusDashboardCache(testDatabasePrefix)).toBe(false);
    });
  });

  describe('trackAbacusCacheMetrics', () => {
    it('should track cache hits', () => {
      trackAbacusCacheMetrics('hit', testDatabasePrefix);
      const metrics = getAbacusCacheMetrics(testDatabasePrefix);
      
      expect(metrics.hits).toBe(1);
      expect(metrics.misses).toBe(0);
      expect(metrics.updates).toBe(0);
    });

    it('should track cache misses', () => {
      trackAbacusCacheMetrics('miss', testDatabasePrefix);
      const metrics = getAbacusCacheMetrics(testDatabasePrefix);
      
      expect(metrics.hits).toBe(0);
      expect(metrics.misses).toBe(1);
      expect(metrics.updates).toBe(0);
    });

    it('should track cache updates', () => {
      trackAbacusCacheMetrics('update', testDatabasePrefix);
      const metrics = getAbacusCacheMetrics(testDatabasePrefix);
      
      expect(metrics.hits).toBe(0);
      expect(metrics.misses).toBe(0);
      expect(metrics.updates).toBe(1);
    });

    it('should calculate hit rate correctly', () => {
      trackAbacusCacheMetrics('hit', testDatabasePrefix);
      trackAbacusCacheMetrics('hit', testDatabasePrefix);
      trackAbacusCacheMetrics('miss', testDatabasePrefix);
      
      const metrics = getAbacusCacheMetrics(testDatabasePrefix);
      expect(metrics.hitRate).toBe(66.7); // 2 hits out of 3 total
    });
  });

  describe('shouldRefreshAbacusCache', () => {
    it('should return true for non-existent cache', () => {
      const shouldRefresh = shouldRefreshAbacusCache('non_existent');
      expect(shouldRefresh).toBe(true);
    });

    it('should return false for fresh cache', () => {
      cacheAbacusDashboardData(testData, testDatabasePrefix);
      const shouldRefresh = shouldRefreshAbacusCache(testDatabasePrefix);
      expect(shouldRefresh).toBe(false);
    });

    it('should return true for old cache', () => {
      // Mock old timestamp
      const oldTimestamp = Date.now() - (5 * 60 * 1000); // 5 minutes ago
      localStorage.setItem(`abacus_dashboard_timestamp_${testDatabasePrefix}`, oldTimestamp.toString());
      
      const shouldRefresh = shouldRefreshAbacusCache(testDatabasePrefix);
      expect(shouldRefresh).toBe(true);
    });
  });
});
