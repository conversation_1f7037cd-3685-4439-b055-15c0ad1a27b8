import LineChart from "./LineChart";
import React, { useState } from 'react';
import SalesPurchases from "./SalesPurchases";
import { Card, Radio } from "antd";


const BusinessAnalysisCard = ({ pouchDatabase, databasePrefix }) => {
  const [activeChart, setActiveChart] = useState('analysis');

  return (
    <Card 
      title="Business Analysis / Sales vs Purchases"
      extra={
        <Radio.Group 
          value={activeChart} 
          onChange={e => setActiveChart(e.target.value)}
          buttonStyle="solid"
          size="small"
        >
          <Radio.Button value="analysis">Business Analysis</Radio.Button>
          <Radio.Button value="salesPurchases">Sales vs Purchases</Radio.Button>
        </Radio.Group>
      }
    >
      <div style={{ height: 300 }}>
        {activeChart === 'analysis' ? (
          <LineChart pouchDatabase={pouchDatabase} databasePrefix={databasePrefix} />
        ) : (
          <SalesPurchases pouchDatabase={pouchDatabase} databasePrefix={databasePrefix} />
        )}
      </div>
    </Card>
  );
};

export default BusinessAnalysisCard;