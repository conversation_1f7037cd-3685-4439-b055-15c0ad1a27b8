import React, { useState, useEffect, useCallback } from "react";
import moment from "moment";
import { Table, Radio, Spin, Empty, Typography } from "antd";
import { buffProducts } from "../../modulesProperties/utils";
import { filterByBranch } from "../../../../Utils/branchFiltering";
import { formatNumber } from "../../../../Utils/functions";


const { Text } = Typography;

const TopCategoriesTable = ({ pouchDatabase, databasePrefix, selectedMonth }) => {
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [metricType, setMetricType] = useState("quantity");

  // Format the month for display and debugging
  const monthLabel = selectedMonth ? selectedMonth.format('YYYY-MM') : 'current';

  // Define table columns based on metric type
  const columns = [
    {
      title: 'Rank',
      dataIndex: 'rank',
      key: 'rank',
      width: 60,
      render: (_, __, index) => index + 1,
      className: 'rank-column',
    },
    {
      title: 'Category',
      dataIndex: 'category',
      key: 'category',
      ellipsis: true,
      render: (text) => <span style={{ fontWeight: 500 }}>{text}</span>,
    },
    {
      title: metricType === 'quantity' ? 'Quantity' : 'Value',
      dataIndex: 'value',
      key: 'value',
      align: 'right',
      render: (value) => {
        const style = { fontWeight: 600 };
        return (
          <span style={style}>
            {metricType === 'quantity' ? value.toLocaleString() : value.toFixed(2)}
          </span>
        );
      },
    },
    {
      title: 'Share',
      dataIndex: 'percentage',
      key: 'percentage',
      align: 'right',
      width: 80,
      render: (value) => {
        const formattedValue = value.toFixed(1);
        return (
          <span style={{
            backgroundColor: value > 25 ? '#e6f7ff' : value > 15 ? '#f6ffed' : 'transparent',
            padding: '2px 8px',
            borderRadius: '10px',
            fontSize: '12px',
            fontWeight: 500
          }}>
            {formattedValue}%
          </span>
        );
      },
    }
  ];

  // Function to fetch and process data
  const fetchData = useCallback(async () => {
    console.log(`Fetching category table data for ${monthLabel} month, metric: ${metricType}`);
    setLoading(true);

    try {
      // Fetch data from databases
      const [invoicesData, productsData, categoriesData] = await Promise.all([
        pouchDatabase("invoices", databasePrefix).getAllData(),
        pouchDatabase("products", databasePrefix)
          .getAllData()
          .then(r => buffProducts(r, pouchDatabase, databasePrefix, "products", localStorage.getItem("SELECTED_BRANCH"))),
        pouchDatabase("categories", databasePrefix).getAllData()
      ]);

      // Get selected branch
      const SELECTED_BRANCH = localStorage.getItem("SELECTED_BRANCH");

      // Determine filter date
      const filterDate = selectedMonth ? moment(selectedMonth) : moment();
      const filterMonthYear = filterDate.format('YYYY-MM');

      // Filter invoices by branch and date
      const filteredInvoices = filterByBranch(invoicesData || [], SELECTED_BRANCH)
        .filter(invoice => {
          if (!invoice.date) return false;
          const invoiceDate = moment(invoice.date);
          return invoiceDate.format('YYYY-MM') === filterMonthYear;
        });

      // Create a map of products to their categories
      const productCategoryMap = new Map();
      (productsData || []).forEach(product => {
        if (product.category && product.category.value) {
          productCategoryMap.set(product._id, product.category.value);
        }
      });

      // Create a map of category IDs to names
      const categoryNameMap = new Map();
      (categoriesData || []).forEach(category => {
        categoryNameMap.set(category._id, category.name || 'Uncategorized');
      });

      // Process data for table
      const categoryMap = new Map();

      // Process invoices
      filteredInvoices.forEach(invoice => {
        if (!invoice.items || !Array.isArray(invoice.items)) return;

        invoice.items.forEach(item => {
          if (!item.product || !item.product.value) return;

          const productId = item.product.value;
          const categoryId = productCategoryMap.get(productId);

          if (!categoryId) return;

          const categoryName = categoryNameMap.get(categoryId) || 'Uncategorized';

          // Get or create category entry
          if (!categoryMap.has(categoryId)) {
            categoryMap.set(categoryId, {
              category: categoryName,
              quantity: 0,
              revenue: 0,
              cost: 0,
              profit: 0
            });
          }

          const categoryEntry = categoryMap.get(categoryId);

          // Find product for cost information
          const product = productsData.find(p => p._id === productId);
          const cost = product ? (product.cost || 0) : 0;
          const price = item.price || 0;
          const quantity = Number(item.quantity || 0);

          // Update all metrics
          categoryEntry.quantity += quantity;
          categoryEntry.revenue += price * quantity;
          categoryEntry.cost += cost * quantity;
          categoryEntry.profit += (price * quantity) - (cost * quantity);

          // Calculate stock value for the category
          categoryEntry.stock_value = categoryEntry.stock_value || 0;
          categoryEntry.stock_value += product ? (product.stock_value || 0) : 0;

          // Calculate revenue to stock value ratio (avoid division by zero)
          categoryEntry.revenue_to_stock_value = categoryEntry.stock_value > 0 ?
            categoryEntry.revenue / categoryEntry.stock_value : 0;
        });
      });

      // Convert map to array and filter out zero values
      let tableData = Array.from(categoryMap.values())
        .filter(entry => entry[metricType === 'quantity' ? 'quantity' : 'revenue_to_stock_value'] > 0)
        .map(entry => ({
          category: entry.category,
          value: metricType === 'quantity' ? entry.quantity : entry.revenue_to_stock_value
        }));

      // Sort data
      tableData = tableData.sort((a, b) => b.value - a.value).slice(0, 10);

      // Calculate percentages
      const totalValue = tableData.reduce((sum, item) => sum + item.value, 0);
      tableData = tableData.map(item => ({
        ...item,
        percentage: totalValue > 0 ? (item.value / totalValue) * 100 : 0
      }));

      // Update state
      setData(tableData);
    } catch (error) {
      console.error('Error fetching category table data:', error);
      setData([]);
    } finally {
      setLoading(false);
    }
  }, [pouchDatabase, databasePrefix, metricType, selectedMonth, monthLabel]);

  // Fetch data when dependencies change
  useEffect(() => {
    fetchData();
  }, [fetchData]);

  return (
    <div>
      <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Text strong>Top 10 Categories</Text>
        <Radio.Group
          value={metricType}
          onChange={e => setMetricType(e.target.value)}
          buttonStyle="solid"
          size="small"
        >
          <Radio.Button value="quantity">By Quantity</Radio.Button>
          <Radio.Button value="revenue_to_stock_value">Value</Radio.Button>
        </Radio.Group>
      </div>

      <Table
        columns={columns}
        dataSource={data}
        rowKey="category"
        pagination={false}
        size="small"
        loading={loading}
        rowClassName={(record, index) => {
          if (index === 0) return 'top-rank-row';
          if (index === 1) return 'second-rank-row';
          if (index === 2) return 'third-rank-row';
          return '';
        }}
        locale={{
          emptyText: <Empty description={`No data available for ${monthLabel}`} />
        }}
      />
      <style jsx="true">{`
        .top-rank-row td {
          background-color: rgba(255, 215, 0, 0.1) !important;
        }
        .second-rank-row td {
          background-color: rgba(192, 192, 192, 0.1) !important;
        }
        .third-rank-row td {
          background-color: rgba(205, 127, 50, 0.1) !important;
        }
        .rank-column {
          font-weight: bold;
        }
        .ant-table-row:hover td {
          background-color: #f5f5f5 !important;
        }
      `}</style>
    </div>
  );
};

export default TopCategoriesTable;