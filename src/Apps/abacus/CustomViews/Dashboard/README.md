# Abacus Dashboard with localStorage Caching

This implementation provides localStorage caching for the Abacus dashboards with background data updates, improving performance while maintaining data accuracy.

## Features

### 🚀 Performance Optimizations
- **Instant Loading**: Shows cached data immediately for sub-second load times
- **Background Updates**: Refreshes data in the background without blocking UI
- **Web Worker Support**: Heavy calculations run in separate threads
- **Memoized Components**: Prevents unnecessary re-renders
- **Smart Caching**: Aggressive 2-minute refresh for data accuracy

### 📊 Cache Management
- **localStorage Storage**: Persistent cache across browser sessions
- **Automatic Expiration**: 5-minute default cache expiration
- **Cache Metrics**: Hit rate tracking and performance monitoring
- **Branch-Aware**: Separate cache per database prefix
- **Conflict Resolution**: Graceful fallbacks when cache fails

### 🔧 Developer Experience
- **Performance Monitor**: Real-time metrics in development mode
- **Console Logging**: Detailed cache operation logs
- **Error Handling**: Robust error recovery and fallbacks
- **Testing**: Comprehensive test suite included

## Architecture

### Core Components

1. **AbacusDashboardCache.js** - Cache management utilities
2. **useAbacusDashboardWorker.js** - Web worker hook for calculations
3. **abacusDashboardCalculations.js** - Worker script for heavy computations
4. **AbacusDashboardPerformanceMonitor.js** - Development performance tracking

### Data Flow

```
1. Component Mount
   ↓
2. Check localStorage Cache
   ↓
3a. Cache Hit → Show Data Immediately
   ↓
3b. Cache Miss → Show Loading State
   ↓
4. Background Data Fetch
   ↓
5. Process with Web Worker (if available)
   ↓
6. Update UI & Cache
   ↓
7. Track Metrics
```

## Implementation Details

### Cache Strategy
- **Cache Key**: `abacus_dashboard_data_{databasePrefix}`
- **Timestamp Key**: `abacus_dashboard_timestamp_{databasePrefix}`
- **Metrics Key**: `abacus_cache_metrics_{databasePrefix}`
- **Expiration**: 5 minutes (configurable)
- **Refresh Threshold**: 2 minutes for background updates

### Data Structure
```javascript
{
  invoices: Array,
  expenses: Array,
  products: Array,
  purchases: Array,
  timestamp: Number
}
```

### Performance Metrics
```javascript
{
  hits: Number,
  misses: Number,
  updates: Number,
  hitRate: Number,
  lastAccess: Number
}
```

## Usage

### AdvancedDashboard
```jsx
import AdvancedDashboard from './AdvancedDashboard';

<AdvancedDashboard
  pouchDatabase={pouchDatabase}
  databasePrefix={databasePrefix}
  modulesProperties={modulesProperties}
/>
```

### StandardDashboard
```jsx
import StandardDashboard from './StandardDashboard';

<StandardDashboard
  pouchDatabase={pouchDatabase}
  databasePrefix={databasePrefix}
  currentUser={currentUser}
  modulesProperties={modulesProperties}
/>
```

## Configuration

### Cache Expiration
```javascript
// Default: 5 minutes
const DEFAULT_CACHE_EXPIRATION = 5 * 60 * 1000;

// Custom expiration
getCachedAbacusDashboardData(databasePrefix, 10 * 60 * 1000); // 10 minutes
```

### Refresh Threshold
```javascript
// Background refresh after 2 minutes
shouldRefreshAbacusCache(databasePrefix);
```

## API Reference

### Cache Functions

#### `cacheAbacusDashboardData(data, databasePrefix)`
Stores dashboard data in localStorage.

#### `getCachedAbacusDashboardData(databasePrefix, expirationTime?)`
Retrieves cached data if valid and not expired.

#### `hasAbacusDashboardCache(databasePrefix, expirationTime?)`
Checks if valid cache exists.

#### `clearAbacusDashboardCache(databasePrefix)`
Removes cached data and timestamp.

#### `trackAbacusCacheMetrics(event, databasePrefix)`
Tracks cache performance metrics ('hit', 'miss', 'update').

#### `getAbacusCacheMetrics(databasePrefix)`
Returns cache performance statistics.

#### `shouldRefreshAbacusCache(databasePrefix)`
Determines if cache should be refreshed in background.

### Worker Functions

#### `calculateAbacusStats(invoices, expenses, selectedBranch, callback)`
Calculates dashboard statistics using web worker.

#### `calculateDashboardInsights(invoices, expenses, products, purchases, callback)`
Processes dashboard insights data using web worker.

## Performance Benefits

### Before Implementation
- **Load Time**: 2-5 seconds on every visit
- **UI Blocking**: Heavy calculations freeze interface
- **Network Dependency**: Always requires fresh data fetch
- **User Experience**: Loading spinners on every navigation

### After Implementation
- **Load Time**: <100ms with cache hit
- **UI Responsiveness**: Non-blocking background updates
- **Offline Capability**: Works with cached data when offline
- **User Experience**: Instant data display with fresh updates

## Development Mode Features

### Performance Monitor
- Real-time cache hit rate
- Load time tracking
- Data freshness indicators
- Cache operation counters
- Worker status monitoring

### Console Logging
```
⚡ Loading from cache
⚡ Fetching fresh dashboard data
⚡ Fresh data loaded and cached
[Abacus Cache] HIT - Hit Rate: 85.7%
```

## Testing

Run the test suite:
```bash
npm test -- AbacusDashboardCache.test.js
```

### Test Coverage
- Cache storage and retrieval
- Expiration handling
- Metrics tracking
- Error scenarios
- Edge cases

## Browser Compatibility

- **localStorage**: All modern browsers
- **Web Workers**: IE10+, all modern browsers
- **AbortController**: Modern browsers (polyfill available)
- **Graceful Degradation**: Falls back to main thread calculations

## Memory Management

### localStorage Limits
- **Typical Limit**: 5-10MB per domain
- **Data Size**: ~50-100KB per cache entry
- **Cleanup**: Automatic expiration and cleanup
- **Monitoring**: Size tracking in development mode

### Memory Optimization
- Memoized components prevent re-renders
- Worker threads isolate heavy calculations
- Automatic cleanup of expired cache entries
- Efficient data structures and serialization

## Troubleshooting

### Common Issues

1. **Cache Not Working**
   - Check localStorage availability
   - Verify databasePrefix is consistent
   - Check browser storage limits

2. **Worker Errors**
   - Ensure worker file is accessible
   - Check browser Web Worker support
   - Verify data serialization

3. **Performance Issues**
   - Monitor cache hit rate
   - Check data size and complexity
   - Verify memoization is working

### Debug Mode
Set `NODE_ENV=development` to enable:
- Performance monitoring
- Detailed console logs
- Cache metrics display
- Worker status indicators
