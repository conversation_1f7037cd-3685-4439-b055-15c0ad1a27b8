import "./styles.css";
import PrintComponents from "react-print-components";
import React, { useEffect, useState, useMemo } from "react";
import moment from "moment";
import { Card, Row, Col, Typography, Descriptions, Table, Tag, Divider, Space, Button, Statistic, Timeline, Avatar, Empty, Skeleton, Flex, Badge, Tooltip } from "antd";
import { PageHeader } from "@ant-design/pro-components";
import { ShoppingCartOutlined, DollarOutlined, UserOutlined, CalendarOutlined, FileTextOutlined, PrinterOutlined, ShopOutlined, InfoCircleOutlined, HistoryOutlined, CheckCircleOutlined, ClockCircleOutlined } from "@ant-design/icons";
import { numberFormat } from "../../../../Utils/functions";


const { Title, Text, Paragraph } = Typography;

const StockPurchase = (props) => {
  const { data, singular, pouchDatabase, databasePrefix } = props;
  const [supplier, setSupplier] = useState(null);
  const [payments, setPayments] = useState([]);
  const [loading, setLoading] = useState(true);
  const [company, setCompany] = useState(null);

  // Fetch supplier and payment data
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        // Fetch supplier details
        if (data.supplier && data.supplier.value) {
          const supplierDB = pouchDatabase("suppliers", databasePrefix);
          const supplierDoc = await supplierDB.get(data.supplier.value);
          setSupplier(supplierDoc);
        }

        // Fetch payments related to this purchase
        const paymentsDB = pouchDatabase("stock_payments", databasePrefix);
        const allPayments = await paymentsDB.getAllData();
        const purchasePayments = allPayments
          .filter(payment => payment && !payment._id.startsWith('_') &&
                  payment.invoice && payment.invoice.value === data._id);
        setPayments(purchasePayments);

        // Fetch company info for printing
        const companyDB = pouchDatabase("company", databasePrefix);
        const allCompanies = await companyDB.getAllData();
        if (allCompanies.length > 0) {
          setCompany(allCompanies[0]);
        }
      } catch (error) {
        console.error("Error fetching data:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [data._id, data.supplier, pouchDatabase, databasePrefix]);

  // Calculate purchase statistics
  const statistics = useMemo(() => {
    const subtotal = data.items?.reduce((sum, item) => sum + (item.price * item.quantity), 0) || 0;
    const totalPaid = payments.reduce((sum, payment) => sum + (payment.amount || 0), 0);
    const balance = subtotal - totalPaid;

    return {
      subtotal,
      totalPaid,
      balance,
      itemCount: data.items?.length || 0,
      status: balance <= 0 ? "Paid" : "Pending"
    };
  }, [data.items, payments]);

  // Format date for display
  const formatDate = (dateString) => {
    return moment(dateString).format('MMM DD, YYYY');
  };

  // Printable purchase component
  const PrintablePurchase = () => (
    <div style={{ padding: "20px", maxWidth: "800px", margin: "0 auto" }}>
      {company && (
        <Flex align="center" justify="space-between" style={{ marginBottom: "20px" }}>
          <div>
            {company.logo && <img src={company.logo} alt="Company Logo" style={{ maxHeight: 100 }} />}
          </div>
          <div style={{ textAlign: "center" }}>
            <Title level={2}>PURCHASE ORDER</Title>
            <Text>Order #: {data._id}</Text>
          </div>
          <div style={{ textAlign: "right" }}>
            <Text>
              {company.address}
              <br />
              Tel: {company.phone}
              <br />
              Email: {company.email}
            </Text>
          </div>
        </Flex>
      )}

      <Divider />

      <Row gutter={24}>
        <Col span={12}>
          <Title level={5}>Supplier Information</Title>
          {supplier ? (
            <div>
              <Text strong>{supplier.name}</Text><br />
              {supplier.address && <><Text>{supplier.address}</Text><br /></>}
              {supplier.phone && <><Text>Phone: {supplier.phone}</Text><br /></>}
              {supplier.email && <><Text>Email: {supplier.email}</Text><br /></>}
            </div>
          ) : (
            <Text>No supplier information available</Text>
          )}
        </Col>
        <Col span={12}>
          <Title level={5}>Order Information</Title>
          <div>
            <Text>Date: {formatDate(data.date)}</Text><br />
            <Text>Order #: {data._id}</Text><br />
            <Text>Status: {statistics.status}</Text><br />
            {data.entrant && <Text>Created By: {data.entrant.label}</Text>}
          </div>
        </Col>
      </Row>

      <Divider />

      <Title level={5}>Order Items</Title>
      <Table
        dataSource={data.items}
        pagination={false}
        size="small"
        columns={[
          {
            title: 'Product',
            dataIndex: 'product',
            key: 'product',
            render: product => product.label
          },
          {
            title: 'Quantity',
            dataIndex: 'quantity',
            key: 'quantity',
            align: 'right'
          },
          {
            title: 'Unit Price',
            dataIndex: 'price',
            key: 'price',
            align: 'right',
            render: price => numberFormat(price)
          },
          {
            title: 'Total',
            key: 'total',
            align: 'right',
            render: (_, record) => numberFormat(record.quantity * record.price)
          }
        ]}
        summary={() => (
          <Table.Summary>
            <Table.Summary.Row>
              <Table.Summary.Cell colSpan={3} index={0}>
                <Text strong>Total</Text>
              </Table.Summary.Cell>
              <Table.Summary.Cell index={1} align="right">
                <Text strong>{numberFormat(statistics.subtotal)}</Text>
              </Table.Summary.Cell>
            </Table.Summary.Row>
            <Table.Summary.Row>
              <Table.Summary.Cell colSpan={3} index={0}>
                <Text strong>Paid</Text>
              </Table.Summary.Cell>
              <Table.Summary.Cell index={1} align="right">
                <Text strong>{numberFormat(statistics.totalPaid)}</Text>
              </Table.Summary.Cell>
            </Table.Summary.Row>
            <Table.Summary.Row>
              <Table.Summary.Cell colSpan={3} index={0}>
                <Text strong>Balance</Text>
              </Table.Summary.Cell>
              <Table.Summary.Cell index={1} align="right">
                <Text strong type={statistics.balance > 0 ? "danger" : "success"}>
                  {numberFormat(statistics.balance)}
                </Text>
              </Table.Summary.Cell>
            </Table.Summary.Row>
          </Table.Summary>
        )}
      />

      {payments.length > 0 && (
        <>
          <Divider />
          <Title level={5}>Payment History</Title>
          <Table
            dataSource={payments}
            pagination={false}
            size="small"
            columns={[
              {
                title: 'Date',
                dataIndex: 'date',
                key: 'date',
                render: date => formatDate(date)
              },
              {
                title: 'Description',
                dataIndex: 'description',
                key: 'description'
              },
              {
                title: 'Amount',
                dataIndex: 'amount',
                key: 'amount',
                align: 'right',
                render: amount => numberFormat(amount)
              }
            ]}
          />
        </>
      )}

      <div style={{ textAlign: 'right', marginTop: 20 }}>
        <Text type="secondary">Generated on {moment().format('MMMM Do YYYY, h:mm:ss a')}</Text>
      </div>
    </div>
  );

  return (
    <>
      <PageHeader
        className="site-page-header-responsive"
        onBack={() => window.history.back()}
        title={`Purchase Order #${data._id}`}
        subTitle={formatDate(data.date)}
        tags={[
          <Tag color={statistics.status === "Paid" ? "success" : "processing"} key="status">
            {statistics.status}
          </Tag>
        ]}
        extra={[
          <PrintComponents
            key="print"
            trigger={
              <Button icon={<PrinterOutlined />} type="primary">
                Print Purchase Order
              </Button>
            }
          >
            <PrintablePurchase />
          </PrintComponents>,
        ]}
      />

      <div style={{ padding: "0 24px 24px" }}>
        {loading ? (
          <Row gutter={[24, 24]}>
            <Col span={24}>
              <Skeleton active paragraph={{ rows: 6 }} />
            </Col>
          </Row>
        ) : (
          <Row gutter={[24, 24]}>
            <Col xs={24} lg={8}>
              <Card
                title={<><InfoCircleOutlined /> Order Information</>}
                className="stock-card"
              >
                <Descriptions column={1} size="small" bordered>
                  <Descriptions.Item label="Order Number">{data._id}</Descriptions.Item>
                  <Descriptions.Item label="Date">{formatDate(data.date)}</Descriptions.Item>
                  <Descriptions.Item label="Status">
                    <Badge
                      status={statistics.status === "Paid" ? "success" : "processing"}
                      text={statistics.status}
                    />
                  </Descriptions.Item>
                  {data.entrant && (
                    <Descriptions.Item label="Created By">{data.entrant.label}</Descriptions.Item>
                  )}
                </Descriptions>

                {supplier && (
                  <>
                    <Divider orientation="left">Supplier</Divider>
                    <div style={{ display: 'flex', alignItems: 'center', marginBottom: 16 }}>
                      <Avatar
                        icon={<ShopOutlined />}
                        style={{ backgroundColor: '#722ed1', marginRight: 8 }}
                      />
                      <Text strong>{supplier.name}</Text>
                    </div>
                    {supplier.phone && (
                      <div className="info-item">
                        <Text type="secondary">Phone:</Text> {supplier.phone}
                      </div>
                    )}
                    {supplier.email && (
                      <div className="info-item">
                        <Text type="secondary">Email:</Text> {supplier.email}
                      </div>
                    )}
                    {supplier.address && (
                      <div className="info-item">
                        <Text type="secondary">Address:</Text> {supplier.address}
                      </div>
                    )}
                    <div style={{ marginTop: 16 }}>
                      <Button
                        type="link"
                        onClick={() => window.location.href = `#/inventory/suppliers/${supplier._id}`}
                        style={{ padding: 0 }}
                      >
                        View Supplier Details
                      </Button>
                    </div>
                  </>
                )}
              </Card>

              <Card
                title={<><DollarOutlined /> Financial Summary</>}
                className="stock-card"
                style={{ marginTop: 16 }}
              >
                <Statistic
                  title="Total Amount"
                  value={numberFormat(statistics.subtotal)}
                  style={{ marginBottom: 16 }}
                />
                <Statistic
                  title="Paid Amount"
                  value={numberFormat(statistics.totalPaid)}
                  valueStyle={{ color: '#52c41a' }}
                  style={{ marginBottom: 16 }}
                />
                <Statistic
                  title="Balance"
                  value={numberFormat(statistics.balance)}
                  valueStyle={{ color: statistics.balance > 0 ? '#f5222d' : '#52c41a' }}
                />
              </Card>
            </Col>

            <Col xs={24} lg={16}>
              <Card
                title={<><ShoppingCartOutlined /> Order Items</>}
                className="stock-card"
              >
                <Table
                  dataSource={data.items}
                  rowKey={(record, index) => index}
                  pagination={false}
                  columns={[
                    {
                      title: 'Product',
                      dataIndex: 'product',
                      key: 'product',
                      render: product => (
                        <Space>
                          <Avatar
                            shape="square"
                            size="small"
                            icon={<ShoppingCartOutlined />}
                            style={{ backgroundColor: '#1890ff' }}
                          />
                          <Tooltip title={`View product details: ${product.label}`}>
                            <a href={`#/inventory/products/${product.value}`}>{product.label}</a>
                          </Tooltip>
                        </Space>
                      )
                    },
                    {
                      title: 'Quantity',
                      dataIndex: 'quantity',
                      key: 'quantity',
                      align: 'right',
                      width: '15%'
                    },
                    {
                      title: 'Unit Price',
                      dataIndex: 'price',
                      key: 'price',
                      align: 'right',
                      width: '20%',
                      render: price => numberFormat(price)
                    },
                    {
                      title: 'Total',
                      key: 'total',
                      align: 'right',
                      width: '20%',
                      render: (_, record) => numberFormat(record.quantity * record.price)
                    }
                  ]}
                  summary={() => (
                    <Table.Summary>
                      <Table.Summary.Row>
                        <Table.Summary.Cell colSpan={3} index={0}>
                          <Text strong>Total</Text>
                        </Table.Summary.Cell>
                        <Table.Summary.Cell index={1} align="right">
                          <Text strong>{numberFormat(statistics.subtotal)}</Text>
                        </Table.Summary.Cell>
                      </Table.Summary.Row>
                    </Table.Summary>
                  )}
                />
              </Card>

              {payments.length > 0 && (
                <Card
                  title={<><HistoryOutlined /> Payment History</>}
                  className="stock-card"
                  style={{ marginTop: 16 }}
                >
                  <Timeline>
                    {payments.map((payment, index) => (
                      <Timeline.Item
                        key={index}
                        color="green"
                        dot={<DollarOutlined style={{ fontSize: '16px' }} />}
                      >
                        <div className="timeline-item">
                          <div className="timeline-item-title">
                            <Text strong>{formatDate(payment.date)}</Text>
                            <Text type="success" strong>{numberFormat(payment.amount)}</Text>
                          </div>
                          <div className="timeline-item-description">
                            {payment.description || 'No description provided'}
                          </div>
                        </div>
                      </Timeline.Item>
                    ))}
                  </Timeline>
                </Card>
              )}
            </Col>
          </Row>
        )}
      </div>
    </>
  );
};

export default StockPurchase;