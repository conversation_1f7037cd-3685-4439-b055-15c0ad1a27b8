import React, { lazy, Suspense } from 'react';
import { ReportsProvider } from "./ReportsContext";
import { Tabs, Spin } from "antd";


const Sales = lazy(() => import('./Sales'));
const Expense = lazy(() => import('./Expense'));
const Profit = lazy(() => import('./Profit'));
const ProductList = lazy(() => import('./ProductList'));
const PriceList = lazy(() => import('./PriceList'));
const Purchases = lazy(() => import('./Purchases'));
const Inventory = lazy(() => import('./Inventory'));
const CategorySales = lazy(() => import('./CategorySales'));

const Reports = ({ pouchDatabase, databasePrefix }) => {
  const items = [
    {
      key: "1",
      label: `Product List`,
      children: (
        <Suspense fallback={<Spin />}>
          <ProductList pouchDatabase={pouchDatabase} databasePrefix={databasePrefix} />
        </Suspense>
      ),
    },
    {
      key: "9",
      label: `Price List`,
      children: (
        <Suspense fallback={<Spin />}>
          <PriceList pouchDatabase={pouchDatabase} databasePrefix={databasePrefix} />
        </Suspense>
      ),
    },
    {
      key: "8",
      label: `Inventory Report`,
      children: (
        <Suspense fallback={<Spin />}>
          <Inventory pouchDatabase={pouchDatabase} databasePrefix={databasePrefix} />
        </Suspense>
      ),
    },
    {
      key: "2",
      label: `Profit/Loss Ledger`,
      children: (
        <Suspense fallback={<Spin />}>
          <Profit pouchDatabase={pouchDatabase} databasePrefix={databasePrefix} />
        </Suspense>
      ),
    },
    {
      key: "3",
      label: `Sales Ledger`,
      children: (
        <Suspense fallback={<Spin />}>
          <Sales pouchDatabase={pouchDatabase} databasePrefix={databasePrefix} />
        </Suspense>
      ),
    },
    {
      key: "6",
      label: `Purchase Ledger`,
      children: (
        <Suspense fallback={<Spin />}>
          <Purchases pouchDatabase={pouchDatabase} databasePrefix={databasePrefix} />
        </Suspense>
      ),
    },
    {
      key: "11",
      label: `Category Sales Report`,
      children: (
        <Suspense fallback={<Spin />}>
          <CategorySales pouchDatabase={pouchDatabase} databasePrefix={databasePrefix} />
        </Suspense>
      ),
    },
    {
      key: "4",
      label: `Expense Ledger`,
      children: (
        <Suspense fallback={<Spin />}>
          <Expense pouchDatabase={pouchDatabase} databasePrefix={databasePrefix} />
        </Suspense>
      ),
    },
  ];

  return (
    <ReportsProvider pouchDatabase={pouchDatabase} databasePrefix={databasePrefix}>
      <Tabs defaultActiveKey="1" items={items} />
    </ReportsProvider>
  );
};

export default Reports;
