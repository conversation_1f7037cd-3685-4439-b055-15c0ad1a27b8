import React, { createContext, useContext, useEffect, useState } from 'react';


const ReportsContext = createContext();

export const useReportsData = () => useContext(ReportsContext);

export const ReportsProvider = ({ children, pouchDatabase, databasePrefix }) => {
  const [loading, setLoading] = useState(true);
  const [data, setData] = useState({
    products: [],
    sales: [],
    prices: [],
    stocking: [],
    expense: [],
    returned: [],
    purchases: [],
    categories: [],
    customers: [],
    suppliers: [],
    receipts: [],
    stock_payments: [],  // Add stock_payments to initial state
    company: null
  });

  // Memoized data transformations
  const memoizedData = React.useMemo(() => ({
    ...data,
    // Pre-calculate common aggregations
    salesByCustomer: new Map(
      data.sales.reduce((acc, sale) => {
        const customerId = sale.customer?.value;
        if (customerId) {
          const existing = acc.get(customerId) || [];
          acc.set(customerId, [...existing, sale]);
        }
        return acc;
      }, new Map())
    ),
    productTotals: new Map(
      data.products.map(product => [
        product._id,
        {
          totalSales: data.sales.reduce((sum, sale) =>
            sum + (sale.items?.filter(item => item.product?.value === product._id)
              .reduce((itemSum, item) => itemSum + (item.quantity * item.price), 0) || 0), 0),
          totalPurchases: data.purchases.reduce((sum, purchase) =>
            sum + (purchase.items?.filter(item => item.product?.value === product._id)
              .reduce((itemSum, item) => itemSum + (item.quantity * item.price), 0) || 0), 0)
        }
      ])
    ),
    // Add helper for stock payments
    purchasePayments: new Map(
      data.purchases.map(purchase => [
        purchase._id,
        {
          totalPaid: data.stock_payments
            .filter(payment => payment.order?._id === purchase._id)
            .reduce((sum, payment) => sum + (parseFloat(payment.amount) || 0), 0)
        }
      ])
    )
  }), [data]);

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        const [
          products,
          sales,
          prices,
          stocking,
          expense,
          returned,
          purchases,
          categories,
          customers,
          suppliers,
          nonPurchased,
          receipts,
          stock_payments,  // Add stock_payments to Promise.all
          organizations
        ] = await Promise.all([
          pouchDatabase("products", databasePrefix).getAllData(),
          pouchDatabase("invoices", databasePrefix).getAllData(),
          pouchDatabase("product_prices", databasePrefix).getAllData(),
          pouchDatabase("stock_purchasing", databasePrefix).getAllData(),
          pouchDatabase("expenses", databasePrefix).getAllData(),
          pouchDatabase("returned_products", databasePrefix).getAllData(),
          pouchDatabase("stock_purchasing", databasePrefix).getAllData(),
          pouchDatabase("categories", databasePrefix).getAllData(),
          pouchDatabase("customers", databasePrefix).getAllData(),
          pouchDatabase("suppliers", databasePrefix).getAllData(),
          pouchDatabase("non_purchased_stock", databasePrefix).getAllData(),
          pouchDatabase("receipts", databasePrefix).getAllData(),
          pouchDatabase("stock_payments", databasePrefix).getAllData(),  // Add this line
          pouchDatabase("organizations", databasePrefix).getAllData()
        ]);

        // Process organization data with logo
        let company = null;
        if (organizations && organizations.length > 0) {
          const org = organizations[0];
          if (org._attachments && org._attachments.logo) {
            try {
              const logoBlob = await pouchDatabase("organizations", databasePrefix).getAttachment(org._id, "logo");
              company = { ...org, orgLogo: logoBlob };
            } catch (error) {
              console.error("Error fetching organization logo:", error);
              company = org;
            }
          } else {
            company = org;
          }
        }

        setData({
          products,
          sales,
          prices,
          stocking: [...nonPurchased, ...stocking],
          expense,
          returned,
          purchases,
          categories,
          customers,
          suppliers,
          receipts,
          stock_payments  // Add stock_payments to setData
        });
      } catch (error) {
        console.error('Error fetching reports data:', error);
      }
      setLoading(false);
    };

    fetchData();
  }, [pouchDatabase, databasePrefix]);

  return (
    <ReportsContext.Provider value={{ loading, ...memoizedData }}>
      {children}
    </ReportsContext.Provider>
  );
};
