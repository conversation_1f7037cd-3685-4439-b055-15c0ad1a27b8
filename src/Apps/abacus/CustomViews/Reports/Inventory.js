import React, { useState, useMemo, useEffect } from 'react';
import './print.css';
import DocumentHead from "../../../Universal/CustomViews/Components/DocumentHead";
import PrintComponents from "react-print-components";
import StatsGrid from "../../../../Components/Stats/StatsGrid";
import { Column, Pie } from "@ant-design/plots";
import { PrinterOutlined, FilterOutlined, WarningOutlined, ExclamationCircleOutlined, InfoCircleOutlined, DollarOutlined, ShoppingOutlined, AppstoreOutlined, ArrowUpOutlined, ArrowDownOutlined, MinusOutlined, DownloadOutlined } from "@ant-design/icons";
import { Statistic } from "@ant-design/pro-components";
import { Table, Button, Row, Col, Input, Typography, Switch, Space, Select, Radio, Card, Badge, Tabs, Tooltip, Divider, Tag } from "antd";
import { buffProducts } from "../../modulesProperties/utils";
import { exportToCSV } from "./utils/csvExport";
import { formatNumber } from "../../../../Utils/functions";
import { useReportsData } from "./ReportsContext";


const Inventory = ({ pouchDatabase, databasePrefix }) => {
  const { products, stock, prices, categories, loading, company } = useReportsData();
  const [keyword, setKeyword] = useState(null);
  const [selectedCategory, setSelectedCategory] = useState(null);
  const [selectedBrand, setSelectedBrand] = useState(null);
  const [stockFilter, setStockFilter] = useState(null); // null = all, 'out' = out of stock, 'low' = low stock
  // Initialize showExternal state from localStorage or default to false
  const [showExternal, setShowExternal] = useState(
    localStorage.getItem("SHOW_EXTERNAL_PRODUCTS") === "true"
  );

  // State for processed products
  const [processedProducts, setProcessedProducts] = useState([]);
  const [processingProducts, setProcessingProducts] = useState(false);
  const [organization, setOrganization] = useState(null);

  // Process products using buffProducts to get accurate stock calculations
  useEffect(() => {
    const processProductsData = async () => {
      if (!products?.length || loading) {
        setProcessedProducts([]);
        return;
      }

      setProcessingProducts(true);
      try {
        // Get database prefix and branch from localStorage
        const databasePrefix = localStorage.getItem("DB_PREFIX") || "";
        const SELECTED_BRANCH = localStorage.getItem("SELECTED_BRANCH");

        console.log('INVENTORY REPORT: Processing products with buffProducts...');

        // Process products using the same buffProducts function used elsewhere
        const buffedProducts = await buffProducts(
          products.map((r) => ({ key: r._id, ...r })),
          null, // pouchDatabase not needed here since we're in reports context
          databasePrefix,
          "products",
          SELECTED_BRANCH,
          showExternal
        );

        console.log('INVENTORY REPORT: Finished processing products with buffProducts');
        setProcessedProducts(buffedProducts);
      } catch (error) {
        console.error('Error processing products with buffProducts:', error);
        // Fallback to raw products if buffProducts fails
        setProcessedProducts(products);
      } finally {
        setProcessingProducts(false);
      }
    };

    processProductsData();
  }, [products, loading, showExternal]);

  // Set up organization data for consistent document headers
  useEffect(() => {
    // Default organization structure
    const defaultOrg = {
      name: "Your Company Name",
      alternative_name: "",
      address: "Your Company Address",
      phone: "Your Phone Number",
      email: "<EMAIL>",
      website: "www.yourwebsite.com",
    };

    // If pouchDatabase is not available, use default organization
    if (!pouchDatabase) {
      console.log("PouchDatabase not available, using default organization");
      setOrganization(defaultOrg);
      return;
    }

    // Attempt to fetch organization data
    if (company) {
      setOrganization(company);
    } else if (pouchDatabase) {
      pouchDatabase("organizations", databasePrefix)
        .getAllData()
        .then((data) => {
          if (data && data.length > 0) {
            // Get the first organization or a specific one if needed
            const org = data[0];

            // If organization has logo attachment, fetch it
            if (org._attachments && org._attachments.logo) {
              pouchDatabase("organizations", databasePrefix)
                .getAttachment(org._id, "logo")
                .then((logoBlob) => {
                  setOrganization({ ...org, orgLogo: logoBlob });
                })
                .catch((err) => {
                  console.error("Error fetching organization logo:", err);
                  setOrganization(org);
                });
            } else {
              setOrganization(org);
            }
          } else {
            // No organization data found, use default
            console.log("No organization data found, using default");
            setOrganization(defaultOrg);
          }
        })
        .catch((err) => {
          console.error("Error fetching organization data:", err);
          // On error, use default organization
          setOrganization(defaultOrg);
        });
    }
  }, [company, pouchDatabase, databasePrefix]);

  // Extract unique brands from processed products
  const brands = useMemo(() => {
    if (!processedProducts?.length) return [];

    const brandMap = new Map();
    processedProducts.forEach(product => {
      if (product.brand && product.brand.value && product.brand.label) {
        brandMap.set(product.brand.value, product.brand.label);
      }
    });

    return Array.from(brandMap.entries()).map(([value, label]) => ({ value, label }));
  }, [processedProducts]);

  const processedData = useMemo(() => {
    if (!processedProducts?.length) return [];

    // Create a price lookup map for faster access
    const priceMap = new Map();
    if (prices?.length) {
      prices.forEach(price => {
        const productId = price.product?.value;
        if (productId) {
          // If multiple prices exist for a product, keep the latest one
          if (!priceMap.has(productId) ||
            new Date(price.date) > new Date(priceMap.get(productId).date)) {
            priceMap.set(productId, price);
          }
        }
      });
    }

    return processedProducts.map(product => {
      // Use the currentStock already calculated by buffProducts
      const currentStock = product.currentStock || 0;

      // Log detailed calculation for debugging - now using buffProducts result
      console.log(`INVENTORY REPORT stock calculation for ${product.name || product._id} (${product._id}):`, {
        stoked: product.stoked,
        sold: product.sold,
        returned: product.returned,
        returned_stock: product.returned_stock,
        adjustments: product.adjustments,
        currentStock: currentStock,
        source: 'buffProducts'
      });

      // Get latest price using the map for faster lookup
      const latestPrice = priceMap.get(product._id)?.price || product.price || 0;

      // Calculate value
      const value = currentStock * latestPrice;

      // Pre-compute stock value for summary calculations
      const stock_value = currentStock * latestPrice;

      return {
        ...product,
        currentStock,
        value,
        latestPrice,
        stock_value
      };
    });
  }, [processedProducts, prices]);

  const columns = useMemo(() => [
    {
      title: "No",
      dataIndex: "no",
      render: (_, __, index) => index + 1,
    },
    {
      valueType: "text",
      dataIndex: "name",
      title: "Name",
      render: (text, record) => {
        if (!record.units || record.units.toLowerCase() == "pc") {
          return text;
        } else {
          return `${record.name} - ${record.measurements} ${record.units}`;
        }
      },
    },
    {
      dataIndex: "sku",
      title: "SKU/Item No",
      // render: (v) => v?v.label:"",
    },
    {
      dataIndex: "brand",
      title: "Brand",
      render: (v) => (v ? v.label : ""),
      sorter: (a, b) => {
        const brandA = a.brand?.label || "";
        const brandB = b.brand?.label || "";
        return brandA.localeCompare(brandB);
      },
    },
    {
      dataIndex: "category",
      title: "Category",
      render: (v) => (v ? v.label : ""),
    },
    {
      dataIndex: "bin_location",
      title: "Bin",
      // render: (v) => v?v.label:"",
    },
    {
      title: "Stock Status",
      dataIndex: "stock_status",
      width: 140,
      render: (_, record) => {
        const currentStock = record.currentStock || 0;
        const minStock = record.stock_alert || 0;

        if (currentStock <= 0) {
          return (
            <Tooltip title={`This product is out of stock. Consider reordering.`}>
              <Badge
                status="error"
                text={
                  <Typography.Text type="danger" strong>
                    <ExclamationCircleOutlined /> Out of Stock
                  </Typography.Text>
                }
              />
            </Tooltip>
          );
        } else if (minStock > 0 && currentStock <= minStock) {
          return (
            <Tooltip title={`Current stock (${currentStock}) is below or equal to minimum stock level (${minStock}). Consider reordering.`}>
              <Badge
                status="warning"
                text={
                  <Typography.Text type="warning" strong>
                    <WarningOutlined /> Low Stock
                  </Typography.Text>
                }
              />
            </Tooltip>
          );
        } else {
          const stockLevel = currentStock > minStock * 3 ? 'High' : 'Adequate';

          return (
            <Tooltip title={`Current stock (${currentStock}) is above minimum stock level (${minStock || 'Not set'})`}>
              <Badge
                status="success"
                text={
                  <Typography.Text type="success" strong>
                    {stockLevel} Stock
                  </Typography.Text>
                }
              />
            </Tooltip>
          );
        }
      },
      filters: [
        { text: 'Out of Stock', value: 'out' },
        { text: 'Low Stock', value: 'low' },
        { text: 'Adequate', value: 'adequate' },
        { text: 'High Stock', value: 'high' },
      ],
      onFilter: (value, record) => {
        const currentStock = record.currentStock || 0;
        const minStock = record.stock_alert || 0;

        if (value === 'out') {
          return currentStock <= 0;
        } else if (value === 'low') {
          return currentStock > 0 && minStock > 0 && currentStock <= minStock;
        } else if (value === 'adequate') {
          return currentStock > minStock && currentStock <= minStock * 3;
        } else if (value === 'high') {
          return currentStock > minStock * 3;
        }
        return true;
      },
    },
    {
      valueType: "digit",
      dataIndex: "currentStock",
      title: "Current Stock",
      hideInForm: true,
      align: 'right',
      render: (value, record) => {
        const currentStock = value || 0;
        const minStock = record.stock_alert || 0;

        // Add visual indicators for stock levels
        if (currentStock <= 0) {
          return <Typography.Text type="danger" strong>{currentStock}</Typography.Text>;
        } else if (minStock > 0 && currentStock <= minStock) {
          return <Typography.Text type="warning" strong>{currentStock}</Typography.Text>;
        } else {
          return <Typography.Text>{currentStock}</Typography.Text>;
        }
      },
    },
    {
      valueType: "digit",
      dataIndex: "stock_alert",
      title: "Min Stock",
      hideInForm: true,
      align: 'right',
      render: (value) => value || <Typography.Text type="secondary">Not set</Typography.Text>,
    },
    {
      valueType: "digit",
      dataIndex: "stock_value",
      title: "Stock Value",
      hideInForm: true,
      render: (value) => formatNumber(value),
      align: 'right',
    },

    {
      valueType: "digit",
      dataIndex: "cost",
      title: "Buying Price",
      hideInForm: true,
      render: (value) => formatNumber(value),
      align: 'right',
    },
    {
      valueType: "digit",
      dataIndex: "sell",
      title: "Selling Price",
      hideInForm: true,
      render: (value) => formatNumber(value),
      align: 'right',
    },

    {
      valueType: "digit",
      dataIndex: "cost",
      title: "Total Buying Price",
      hideInForm: true,
      align: 'right',
      render: (_, record) => {
        // Use the pre-calculated currentStock value for consistency
        return formatNumber(record.currentStock * record.cost);
      },
    },
    {
      valueType: "digit",
      dataIndex: "sell",
      title: "Total Selling Price",
      hideInForm: true,
      align: 'right',
      render: (_, record) => {
        // Use the pre-calculated currentStock value for consistency
        return formatNumber(record.currentStock * record.sell);
      },
    },
    {
      valueType: "digit",
      dataIndex: "profit",
      title: "Profit",
      hideInForm: true,
      align: 'right',
      render: (_, record) => {
        // Use the pre-calculated currentStock value for consistency
        return formatNumber(record.currentStock * (record.sell - record.cost));
      },
    },
  ], []);

  const filteredData = useMemo(() => {
    if (!processedData?.length) return [];

    // First filter by external status
    let filtered = showExternal
      ? processedData
      : processedData.filter(record => !record.external);

    // Then filter by category if selected
    if (selectedCategory) {
      filtered = filtered.filter(record =>
        record.category && record.category.value === selectedCategory
      );
    }

    // Then filter by brand if selected
    if (selectedBrand) {
      filtered = filtered.filter(record =>
        record.brand && record.brand.value === selectedBrand
      );
    }

    // Then filter by stock status if selected
    if (stockFilter) {
      filtered = filtered.filter(record => {
        const currentStock = record.currentStock || 0;
        const minStock = record.stock_alert || 0;

        if (stockFilter === 'out') {
          // Out of stock: currentStock <= 0
          return currentStock <= 0;
        } else if (stockFilter === 'low') {
          // Low stock: currentStock > 0 && currentStock <= minStock
          return currentStock > 0 && minStock > 0 && currentStock <= minStock;
        }
        return true;
      });
    }

    // Then filter by keyword if provided
    if (keyword) {
      const searchLower = keyword.toLowerCase();
      filtered = filtered.filter(record => {
        // Check specific fields instead of converting the entire record to a string
        return (
          (typeof record.name === 'string' && record.name.toLowerCase().includes(searchLower)) ||
          (typeof record.sku === 'string' && record.sku.toLowerCase().includes(searchLower)) ||
          (typeof record.bin_location === 'string' && record.bin_location.toLowerCase().includes(searchLower)) ||
          (record.category?.label && typeof record.category.label === 'string' && record.category.label.toLowerCase().includes(searchLower)) ||
          (record.brand?.label && typeof record.brand.label === 'string' && record.brand.label.toLowerCase().includes(searchLower))
        );
      });
    }

    // Use a more efficient sorting approach
    return filtered.sort((a, b) => {
      const nameA = a.name || '';
      const nameB = b.name || '';
      return nameA.localeCompare(nameB);
    });
  }, [processedData, keyword, showExternal, selectedCategory, selectedBrand, stockFilter]);

  // Calculate inventory statistics for the StatsGrid
  const inventoryStats = useMemo(() => {
    if (!filteredData?.length) return {
      totalProducts: 0,
      totalStockValue: 0,
      outOfStockCount: 0,
      lowStockCount: 0,
      inStockCount: 0,
      totalBuyingValue: 0,
      totalSellingValue: 0,
      potentialProfit: 0
    };

    return filteredData.reduce((stats, product) => {
      const currentStock = product.currentStock || 0;
      const minStock = product.stock_alert || 0;
      const cost = product.cost || 0;
      const sell = product.sell || 0;
      const stockValue = product.stock_value || 0;

      // Update counts based on stock status
      if (currentStock <= 0) {
        stats.outOfStockCount++;
      } else if (minStock > 0 && currentStock <= minStock) {
        stats.lowStockCount++;
      } else {
        stats.inStockCount++;
      }

      // Update financial values
      stats.totalStockValue += stockValue;
      stats.totalBuyingValue += cost * currentStock;
      stats.totalSellingValue += sell * currentStock;
      stats.potentialProfit += (sell - cost) * currentStock;

      return stats;
    }, {
      totalProducts: filteredData.length,
      totalStockValue: 0,
      outOfStockCount: 0,
      lowStockCount: 0,
      inStockCount: 0,
      totalBuyingValue: 0,
      totalSellingValue: 0,
      potentialProfit: 0
    });
  }, [filteredData]);

  // Prepare data for StatsGrid
  const statsGridData = useMemo(() => [
    {
      title: 'Total Products',
      value: inventoryStats.totalProducts,
      icon: 'chart',
      theme: 'primary'
    },
    {
      title: 'Stock Value',
      value: inventoryStats.totalStockValue,
      icon: 'money',
      theme: 'success'
    },
    {
      title: 'Out of Stock',
      value: inventoryStats.outOfStockCount,
      icon: 'cart',
      theme: 'danger',
      diff: inventoryStats.totalProducts ? (inventoryStats.outOfStockCount / inventoryStats.totalProducts) * 100 : 0,
      diffLabel: 'of total products'
    },
    {
      title: 'Low Stock',
      value: inventoryStats.lowStockCount,
      icon: 'chart',
      theme: 'warning',
      diff: inventoryStats.totalProducts ? (inventoryStats.lowStockCount / inventoryStats.totalProducts) * 100 : 0,
      diffLabel: 'of total products'
    }
  ], [inventoryStats]);

  // Prepare data for category chart
  const categoryChartData = useMemo(() => {
    if (!filteredData?.length) return [];

    const categoryData = {};

    filteredData.forEach(product => {
      const categoryLabel = product.category?.label || 'Uncategorized';

      if (!categoryData[categoryLabel]) {
        categoryData[categoryLabel] = {
          category: categoryLabel,
          value: 0
        };
      }

      categoryData[categoryLabel].value += product.stock_value || 0;
    });

    return Object.values(categoryData)
      .sort((a, b) => b.value - a.value)
      .slice(0, 10); // Top 10 categories by value
  }, [filteredData]);

  // Prepare data for brand chart
  const brandChartData = useMemo(() => {
    if (!filteredData?.length) return [];

    const brandData = {};

    filteredData.forEach(product => {
      const brandLabel = product.brand?.label || 'Unbranded';

      if (!brandData[brandLabel]) {
        brandData[brandLabel] = {
          brand: brandLabel,
          value: 0
        };
      }

      brandData[brandLabel].value += product.stock_value || 0;
    });

    return Object.values(brandData)
      .sort((a, b) => b.value - a.value)
      .slice(0, 10); // Top 10 brands by value
  }, [filteredData]);

  const tableProps = useMemo(() => ({
    size: 'small',
    columns,
    dataSource: filteredData,
    loading: loading || processingProducts,
    pagination: {
      defaultPageSize: 20,
      showSizeChanger: true,
      pageSizeOptions: ['10', '20', '50', '100'],
      showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`
    },
    scroll: { y: 'calc(100vh - 350px)' },
    summary: (pageData) => {
      // Use reduce for more efficient calculations
      const totals = pageData.reduce((acc, item) => {
        // Use the pre-computed currentStock value
        const stock_in = item.currentStock || 0;

        // Use the pre-computed stock_value
        acc.total_stock_value += item.stock_value || 0;

        // Calculate other totals
        const cost = item.cost || 0;
        const sell = item.sell || 0;

        acc.total_bought += cost;
        acc.grand_total_bought += cost * stock_in;
        acc.total_if_sold += sell;
        acc.grand_total_if_sold += sell * stock_in;
        acc.total_profit += (sell - cost) * stock_in;

        return acc;
      }, {
        total_bought: 0,
        grand_total_bought: 0,
        total_if_sold: 0,
        grand_total_if_sold: 0,
        total_profit: 0,
        total_stock_value: 0
      });

      // Destructure for easier access
      const {
        total_bought,
        grand_total_bought,
        total_if_sold,
        grand_total_if_sold,
        total_profit,
        total_stock_value
      } = totals;


      return (
        <Table.Summary fixed>
          <Table.Summary.Row>

            <Table.Summary.Cell index={0} colSpan={10}>
              Total
            </Table.Summary.Cell>
            <Table.Summary.Cell index={5}>
              {formatNumber(total_stock_value)}
            </Table.Summary.Cell>
            <Table.Summary.Cell index={2}>
              {formatNumber(total_bought)}
            </Table.Summary.Cell>
            <Table.Summary.Cell index={3}>
              {formatNumber(total_if_sold)}
            </Table.Summary.Cell>
            <Table.Summary.Cell index={2}>
              {formatNumber(grand_total_bought)}
            </Table.Summary.Cell>
            <Table.Summary.Cell index={3}>
              {formatNumber(grand_total_if_sold)}
            </Table.Summary.Cell>
            <Table.Summary.Cell index={4}>
              {formatNumber(total_profit)}
            </Table.Summary.Cell>
          </Table.Summary.Row>
        </Table.Summary>
      );
    }
  }), [columns, filteredData, loading, processingProducts]);

  // Configure chart for category distribution
  const categoryChartConfig = {
    data: categoryChartData,
    xField: 'value',
    yField: 'category',
    seriesField: 'category',
    legend: false,
    meta: {
      value: {
        formatter: (v) => formatNumber(v),
      },
    },
    label: {
      position: 'right',
      formatter: (v) => formatNumber(v.value),
    },
    tooltip: {
      formatter: (datum) => {
        return { name: datum.category, value: formatNumber(datum.value) };
      },
    },
  };

  // Configure chart for brand distribution
  const brandChartConfig = {
    data: brandChartData,
    angleField: 'value',
    colorField: 'brand',
    radius: 0.8,
    label: {
      type: 'outer',
      formatter: (datum) => `${datum.brand}: ${formatNumber(datum.value)}`,
    },
    tooltip: {
      formatter: (datum) => {
        return { name: datum.brand, value: formatNumber(datum.value) };
      },
    },
    interactions: [{ type: 'element-active' }],
  };

  // Define tabs for the inventory report
  const items = [
    {
      key: 'inventory',
      label: 'Inventory List',
      children: (
        <Table
          {...tableProps}
          title={() => (
            <Row justify="end">
              <Col>
                <Space>
                  <Button
                    icon={<DownloadOutlined />}
                    onClick={() => exportToCSV(filteredData, columns, 'Inventory_Report')}
                    disabled={!filteredData || filteredData.length === 0}
                    style={{ marginBottom: 16 }}
                  >
                    Export CSV
                  </Button>
                  <PrintComponents
                    trigger={
                      <Button
                        icon={<PrinterOutlined />}
                        type="primary"
                        style={{ marginBottom: 16 }}
                      >
                        Print
                      </Button>
                    }
                  >
                  <div style={{ margin: 20 }}>
                    {organization && <DocumentHead company={organization} />}
                    <Typography.Title level={3}>
                      Inventory Report
                      {!showExternal && <Typography.Text type="secondary" style={{ fontSize: '16px', marginLeft: '10px' }}>
                        (External products excluded)
                      </Typography.Text>}
                    </Typography.Title>

                    {/* Include statistics in print output */}
                    <Row gutter={[16, 16]} style={{ marginBottom: 20 }}>
                      <Col span={6}>
                        <Card>
                          <Statistic
                            title="Total Products"
                            value={inventoryStats.totalProducts}
                            valueStyle={{ color: '#1677ff' }}
                          />
                        </Card>
                      </Col>
                      <Col span={6}>
                        <Card>
                          <Statistic
                            title="Stock Value"
                            value={formatNumber(inventoryStats.totalStockValue)}
                            valueStyle={{ color: '#52c41a' }}
                          />
                        </Card>
                      </Col>
                      <Col span={6}>
                        <Card>
                          <Statistic
                            title="Out of Stock"
                            value={inventoryStats.outOfStockCount}
                            valueStyle={{ color: '#ff4d4f' }}
                          />
                        </Card>
                      </Col>
                      <Col span={6}>
                        <Card>
                          <Statistic
                            title="Low Stock"
                            value={inventoryStats.lowStockCount}
                            valueStyle={{ color: '#faad14' }}
                          />
                        </Card>
                      </Col>
                    </Row>

                    {(selectedCategory || selectedBrand || stockFilter) && (
                      <Typography.Text type="secondary" style={{ display: 'block', marginBottom: '10px' }}>
                        {selectedCategory && (
                          <>Filtered by Category: {categories?.find(c => c._id === selectedCategory)?.name || 'Unknown'}</>
                        )}
                        {selectedCategory && (selectedBrand || stockFilter) && <> | </>}
                        {selectedBrand && (
                          <>Filtered by Brand: {brands.find(b => b.value === selectedBrand)?.label || 'Unknown'}</>
                        )}
                        {selectedBrand && stockFilter && <> | </>}
                        {stockFilter && (
                          <>Stock Status: {stockFilter === 'out' ? 'Out of Stock' : 'Low Stock'}</>
                        )}
                      </Typography.Text>
                    )}
                    <Table
                      {...tableProps}
                      pagination={false}
                      scroll={false}
                    />
                  </div>
                </PrintComponents>
                </Space>
              </Col>
            </Row>
          )}
        />
      ),
    },
    {
      key: 'alerts',
      label: 'Stock Alerts',
      children: (
        <div>
          <Row gutter={[16, 16]}>
            <Col span={24}>
              <Card
                title={
                  <Space>
                    <ExclamationCircleOutlined style={{ color: 'red' }} />
                    <span>Out of Stock Products</span>
                    <Tag color="red">{inventoryStats.outOfStockCount}</Tag>
                  </Space>
                }
              >
                <Table
                  size="small"
                  columns={columns.filter(col =>
                    ['no', 'name', 'sku', 'brand', 'category', 'bin_location', 'stock_status', 'sell'].includes(col.dataIndex)
                  )}
                  dataSource={filteredData.filter(product => (product.currentStock || 0) <= 0)}
                  pagination={false}
                />
              </Card>
            </Col>
            <Col span={24}>
              <Card
                title={
                  <Space>
                    <WarningOutlined style={{ color: 'orange' }} />
                    <span>Low Stock Products</span>
                    <Tag color="orange">{inventoryStats.lowStockCount}</Tag>
                  </Space>
                }
              >
                <Table
                  size="small"
                  columns={columns.filter(col =>
                    ['no', 'name', 'sku', 'brand', 'category', 'bin_location', 'stock_status', 'currentStock', 'stock_alert'].includes(col.dataIndex)
                  ).concat([
                    {
                      title: 'Stock Alert',
                      dataIndex: 'stock_alert',
                      render: value => value || 'Not set',
                      align: 'right'
                    }
                  ])}
                  dataSource={filteredData.filter(product => {
                    const currentStock = product.currentStock || 0;
                    const minStock = product.stock_alert || 0;
                    return currentStock > 0 && minStock > 0 && currentStock <= minStock;
                  })}
                  pagination={false}
                />
              </Card>
            </Col>
          </Row>
        </div>
      ),
    },
    {
      key: 'analysis',
      label: 'Inventory Analysis',
      children: (
        <div>
          <Row gutter={[16, 16]}>
            <Col span={12}>
              <Card title="Stock Value by Category">
                <div style={{ height: 400 }}>
                  <Column {...categoryChartConfig} />
                </div>
              </Card>
            </Col>
            <Col span={12}>
              <Card title="Stock Value by Brand">
                <div style={{ height: 400 }}>
                  <Pie {...brandChartConfig} />
                </div>
              </Card>
            </Col>
          </Row>
        </div>
      ),
    },
  ];

  return (
    <div>
      {/* Stats Grid at the top */}
      <div style={{ marginBottom: 16 }}>
        <StatsGrid data={statsGridData} />
      </div>

      {/* New Filter Card */}
      <Card title="Filters" style={{ marginBottom: 16 }}>
        <Space direction="vertical" style={{ width: '100%' }}>
          <Row gutter={[16, 8]} align="middle">
            <Col flex="auto">
              <Input.Search
                placeholder="Search products..."
                onChange={(e) => setKeyword(e.target.value)}
                allowClear
              />
            </Col>
            <Col>
              <Space>
                <Typography.Text>Show External:</Typography.Text>
                <Switch
                  checked={showExternal}
                  onChange={(checked) => {
                    setShowExternal(checked);
                    localStorage.setItem("SHOW_EXTERNAL_PRODUCTS", checked);
                  }}
                  checkedChildren="Yes"
                  unCheckedChildren="No"
                />
              </Space>
            </Col>
          </Row>
          <Row gutter={[16, 8]} align="middle">
            <Col xs={24} sm={12} md={8}>
              <Select
                placeholder="Filter by Category"
                style={{ width: '100%' }}
                allowClear
                value={selectedCategory}
                onChange={(value) => setSelectedCategory(value)}
                options={categories?.map(category => ({
                  value: category._id,
                  label: category.name
                }))}
              />
            </Col>
            <Col xs={24} sm={12} md={8}>
              <Select
                placeholder="Filter by Brand"
                style={{ width: '100%' }}
                allowClear
                value={selectedBrand}
                onChange={(value) => setSelectedBrand(value)}
                options={brands}
              />
            </Col>
            <Col xs={24} sm={24} md={8}>
              <Radio.Group
                value={stockFilter}
                onChange={(e) => setStockFilter(e.target.value)}
                buttonStyle="solid"
                style={{ width: '100%' }}
              >
                <Radio.Button value={null} style={{ flexGrow: 1, textAlign: 'center' }}>All</Radio.Button>
                <Radio.Button value="out" style={{ flexGrow: 1, textAlign: 'center' }}>
                  <ExclamationCircleOutlined /> Out
                </Radio.Button>
                <Radio.Button value="low" style={{ flexGrow: 1, textAlign: 'center' }}>
                  <WarningOutlined /> Low
                </Radio.Button>
              </Radio.Group>
            </Col>
          </Row>
        </Space>
      </Card>

      {/* Main content with tabs */}
      <Tabs defaultActiveKey="inventory" items={items} />
    </div>
  );
};

export default Inventory;