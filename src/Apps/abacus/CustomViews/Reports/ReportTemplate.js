import './print.css';
import DocumentHead from "../../../Universal/CustomViews/Components/DocumentHead";
import PrintComponents from "react-print-components";
import React, { useState, useMemo } from 'react';
import { PrinterOutlined, DownloadOutlined } from "@ant-design/icons";
import { Table, Button, Row, Col, Input, Typography, Space } from "antd";
import { exportToCSV } from "./utils/csvExport";
import { useReportsData } from "./ReportsContext";


const ReportTemplate = () => {
  const {
    loading,
    // Add required data from context:
    products,
    sales,
    prices,
    stocking,
    expense,
    returned,
    purchases,
    categories,
    customers,
    suppliers
  } = useReportsData();

  const [keyword, setKeyword] = useState(null);

  // Define your columns
  const columns = useMemo(() => [
    // Your column definitions
  ], []);

  // Process and filter your data
  const processedData = useMemo(() => {
    if (!yourRequiredData?.length) return [];

    // Your data processing logic
    return [];
  }, [/* your dependencies */]);

  const filteredData = useMemo(() => {
    if (!processedData?.length) return [];

    let filtered = processedData;
    if (keyword) {
      const searchLower = keyword.toLowerCase();
      filtered = filtered.filter((record) =>
        JSON.stringify(record).toLowerCase().includes(searchLower)
      );
    }
    return filtered;
  }, [processedData, keyword]);

  const tableProps = useMemo(() => ({
    size: 'small',
    columns,
    dataSource: filteredData,
    pagination: {
      defaultPageSize: 20,
      showSizeChanger: true,
      pageSizeOptions: ['10', '20', '50', '100'],
      showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`
    },
    loading,
    scroll: { y: 'calc(100vh - 350px)' },
  }), [columns, filteredData, loading]);

  return (
    <div>
      <Table
        {...tableProps}
        title={() => (
          <Row justify="space-between">
            <Col span={12}>
              <Input.Search
                placeholder="Search..."
                onSearch={(value) => setKeyword(value)}
                style={{ width: 304 }}
                allowClear
              />
            </Col>
            <Col span={12} style={{ textAlign: 'end' }}>
              <Space>
                <Button
                  icon={<DownloadOutlined />}
                  onClick={() => exportToCSV(filteredData, columns, 'Report')}
                  disabled={!filteredData || filteredData.length === 0}
                  style={{ marginBottom: 16 }}
                >
                  Export CSV
                </Button>
                <PrintComponents
                  trigger={
                    <Button
                      icon={<PrinterOutlined />}
                      type="primary"
                      style={{ marginBottom: 16 }}
                    >
                      Print
                    </Button>
                  }
                >
                  <div style={{ margin: 20 }}>
                    <DocumentHead />
                    <Typography.Title level={3}>Report Title</Typography.Title>
                    <Table
                      {...tableProps}
                      pagination={false}
                      scroll={false}
                    />
                  </div>
                </PrintComponents>
              </Space>
            </Col>
          </Row>
        )}
      />
    </div>
  );
};

export default ReportTemplate;