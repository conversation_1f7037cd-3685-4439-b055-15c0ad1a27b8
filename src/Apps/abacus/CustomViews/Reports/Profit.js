import React, { useState, useMemo, useEffect } from "react";
import './print.css';
import DocumentHead from "../../../Universal/CustomViews/Components/DocumentHead";
import PrintComponents from "react-print-components";
import StatsGrid from "../../../../Components/Stats/StatsGrid";
import dayjs from "dayjs";
import isSameOrAfter from "dayjs/plugin/isSameOrAfter";
import isSameOrBefore from "dayjs/plugin/isSameOrBefore";
import { Button, Col, DatePicker, Input, Row, Table, Typography, Tabs, Card, Select, Space } from "antd";
import { Column, Pie } from "@ant-design/plots";
import { PrinterOutlined, BarChartOutlined, UnorderedListOutlined, DownloadOutlined } from "@ant-design/icons";
import { exportToCSV } from "./utils/csvExport";
import { useReportsData } from "./ReportsContext";


dayjs.extend(isSameOrAfter);
dayjs.extend(isSameOrBefore);

const { TabPane } = Tabs;

const Profit = (props) => {
  const { sales = [], returned = [], products = [], stocking = [], loading } = useReportsData();
  const stock = stocking;
  const [dateRange, setDateRange] = useState([
    dayjs().startOf('day'),
    dayjs().endOf('day')
  ]);
  const [keyword, setKeyword] = useState(null);
  const [selectedCategory, setSelectedCategory] = useState(null);
  const [activeTab, setActiveTab] = useState("ledger");
  const [organization, setOrganization] = useState(null);

  // Fetch organization information
  useEffect(() => {
    const { pouchDatabase, databasePrefix } = props;

    // Set a default organization to prevent loading state from getting stuck
    const defaultOrg = {
      name: "Your Company",
      address: "",
      phone: "",
      email: ""
    };

    // If pouchDatabase is not available, use default organization
    if (!pouchDatabase) {
      console.log("PouchDatabase not available, using default organization");
      setOrganization(defaultOrg);
      return;
    }

    // Attempt to fetch organization data
    pouchDatabase("organizations", databasePrefix)
      .getAllData()
      .then((data) => {
        if (data && data.length > 0) {
          // Get the first organization or a specific one if needed
          const org = data[0];

          // If the organization has a logo attachment, fetch it
          if (org._attachments && org._attachments.logo) {
            pouchDatabase("organizations", databasePrefix)
              .getAttachment(org._id, "logo")
              .then((logoBlob) => {
                setOrganization({ ...org, orgLogo: logoBlob });
              })
              .catch((err) => {
                console.error("Error fetching logo:", err);
                setOrganization(org);
              });
          } else {
            setOrganization(org);
          }
        } else {
          // No organization data found, use default
          console.log("No organization data found, using default");
          setOrganization(defaultOrg);
        }
      })
      .catch((err) => {
        console.error("Error fetching organization data:", err);
        // On error, use default organization
        setOrganization(defaultOrg);
      });
  }, [props.pouchDatabase, props.databasePrefix]);

  // Create a product-to-stock map for faster lookups
  const productStockMap = useMemo(() => {
    const map = new Map();

    if (stock?.length) {
      // Process all stock records once
      const stockRecords = [];

      stock.forEach((invoice) => {
        if (invoice?.items) {
          invoice.items.forEach((item) => {
            if (item.product?.value) {
              stockRecords.push({
                date: invoice.date,
                ...item,
                category: item.product?.category
              });
            }
          });
        } else if (invoice.product?.value) {
          stockRecords.push({
            price: invoice.unit_cost,
            ...invoice,
            category: invoice.product?.category
          });
        }
      });

      // Group stock records by product
      stockRecords.forEach(record => {
        if (record.product?.value) {
          if (!map.has(record.product.value)) {
            map.set(record.product.value, []);
          }
          map.get(record.product.value).push(record);
        }
      });

      // Sort each product's stock records by date
      map.forEach((records, productId) => {
        map.set(productId, records.sort((a, b) =>
          dayjs(a.date).unix() - dayjs(b.date).unix()
        ));
      });
    }

    return map;
  }, [stock]);

  const profitData = useMemo(() => {
    let data = [];

    // Helper function to find the appropriate stock record
    const findStockRecord = (productId, date) => {
      if (!productStockMap.has(productId)) return null;

      const records = productStockMap.get(productId);

      // Find records before the given date
      const recordsBeforeDate = records.filter(rec =>
        dayjs(date).isSameOrAfter(dayjs(rec.date))
      );

      // Return the most recent record before the date
      return recordsBeforeDate.length > 0
        ? recordsBeforeDate[recordsBeforeDate.length - 1]
        : records[0];
    };

    // Process returned products - limit to 1000 for performance
    if (returned?.length) {
      const limitedReturns = returned.slice(0, 1000);

      limitedReturns.forEach((element) => {
        if (element?.items) {
          element.items.forEach((item) => {
            if (!item.product?.value) return;

            const stockRow = findStockRecord(item.product.value, element.date);

            const costPrice = -(
              stockRow ? (
                stockRow.price
                  ? stockRow.price
                  : stockRow.total_cost / stockRow.quantity
              ) : 0
            );

            const rPrice = -(item.unit_cost
              ? item.unit_cost
              : item.total_cost / item.quantity);

            data.push({
              ...item,
              date: element.date,
              id: element._id,
              cost: costPrice,
              price: rPrice,
              bought: costPrice * item.quantity,
              sold: rPrice * item.quantity,
              profit: rPrice * item.quantity - costPrice * item.quantity,
              quantity: -item.quantity,
              category: item.product?.category
            });
          });
        }
      });
    }

    // Process sales - limit to 1000 for performance
    if (sales?.length) {
      const limitedSales = sales.slice(0, 1000);

      limitedSales.forEach((sale) => {
        if (sale?.items) {
          sale.items.forEach((item) => {
            if (!item.product?.value) return;

            const stockRow = findStockRecord(item.product.value, sale.date);

            const costPrice = stockRow
              ? stockRow.price
                ? stockRow.price
                : stockRow.total_cost
                  ? stockRow.total_cost / stockRow.quantity
                  : 0
              : 0;

            data.push({
              ...item,
              date: sale.date,
              id: sale._id,
              cost: costPrice,
              price: Number(item.price || 0),
              quantity: Number(item.quantity || 0),
              bought: costPrice * Number(item.quantity || 0),
              sold: Number(item.price || 0) * Number(item.quantity || 0),
              profit:
                Number(item.price || 0) * Number(item.quantity || 0) -
                costPrice * Number(item.quantity || 0),
              category: item.product?.category
            });
          });
        }
      });
    }

    return data.map((d) => ({
      ...d,
      category: products.find((p) => p._id === d.product.value)?.category,
    }));
  }, [sales, returned, productStockMap, products]);

  const columns = useMemo(() => [
    {
      dataIndex: "date",
      title: "Date",
      render: (text) => dayjs(text).format("DD-MM-YYYY"),
      sorter: (a, b) => dayjs(a.date).unix() - dayjs(b.date).unix(),
    },
    {
      dataIndex: "product",
      title: "Product",
      render: (v) => v?.label,
      sorter: (a, b) => {
        const aLabel = a.product?.label || '';
        const bLabel = b.product?.label || '';
        return aLabel.localeCompare(bLabel);
      },
    },
    {
      dataIndex: "category",
      title: "Category",
      render: (v) => v?.label,
      sorter: (a, b) => {
        const aLabel = a.category?.label || '';
        const bLabel = b.category?.label || '';
        return aLabel.localeCompare(bLabel);
      },
    },
    {
      dataIndex: "quantity",
      title: "Quantity",
      sorter: (a, b) => a.quantity - b.quantity,
    },
    {
      dataIndex: "bought",
      title: "Cost",
      render: (v) => v.toLocaleString(undefined, { maximumFractionDigits: 2 }),
      sorter: (a, b) => a.bought - b.bought,
    },
    {
      dataIndex: "sold",
      title: "Sold",
      render: (v) => v.toLocaleString(undefined, { maximumFractionDigits: 2 }),
      sorter: (a, b) => a.sold - b.sold,
    },
    {
      dataIndex: "profit",
      title: "Profit",
      render: (_, row) => {
        const profitPercent = row.bought !== 0 ? (row.profit / row.bought) * 100 : 0;
        const color = profitPercent >= 0 ? 'green' : 'red';
        return (
          <span style={{ color }}>
            {row.profit.toLocaleString(undefined, { maximumFractionDigits: 2 })}
            ({profitPercent.toLocaleString(undefined, { maximumFractionDigits: 1 })}%)
          </span>
        );
      },
      sorter: (a, b) => a.profit - b.profit,
    },
  ], []);

  const filteredData = useMemo(() => {
    if (!profitData?.length) return [];

    // Limit the initial data size for better performance
    const limitedData = profitData.length > 5000 ? profitData.slice(0, 5000) : profitData;

    let filtered = limitedData
      .sort((a, b) => dayjs(a.date).unix() - dayjs(b.date).unix());

    if (dateRange?.[0] && dateRange?.[1]) {
      filtered = filtered.filter(
        (r) =>
          r.product && r.id &&
          dayjs(r.date).isSameOrAfter(dayjs(dateRange[0]), "day") &&
          dayjs(r.date).isSameOrBefore(dayjs(dateRange[1]), "day")
      );
    } else {
      // If no date range, just show recent data
      filtered = filtered.slice(-100);
    }

    // Apply category filter if selected
    if (selectedCategory) {
      filtered = filtered.filter(
        (r) => r.category && r.category.value === selectedCategory
      );
    }

    if (keyword) {
      const searchLower = keyword.toLowerCase();
      filtered = filtered.filter((r) => {
        // Optimize search by checking most common fields first
        const productLabel = r.product?.label?.toLowerCase() || '';
        if (productLabel.includes(searchLower)) return true;

        const categoryLabel = r.category?.label?.toLowerCase() || '';
        if (categoryLabel.includes(searchLower)) return true;

        // Only do the expensive JSON.stringify as a last resort
        return JSON.stringify({
          product: r.product?.label,
          category: r.category?.label,
          quantity: r.quantity,
          profit: r.profit
        }).toLowerCase().includes(searchLower);
      });
    }

    // Limit the final result size
    return filtered.length > 1000 ? filtered.slice(0, 1000).reverse() : filtered.reverse();
  }, [profitData, dateRange, keyword, selectedCategory]);

  // Calculate summary statistics
  const summaryStats = useMemo(() => {
    if (!filteredData?.length) return {
      totalProfit: 0,
      totalSales: 0,
      totalCost: 0,
      profitMargin: 0,
      productCount: 0
    };

    const totalProfit = filteredData.reduce((sum, item) => sum + (item.profit || 0), 0);
    const totalSales = filteredData.reduce((sum, item) => sum + (item.sold || 0), 0);
    const totalCost = filteredData.reduce((sum, item) => sum + (item.bought || 0), 0);
    const profitMargin = totalSales > 0 ? (totalProfit / totalSales) * 100 : 0;

    // Count unique products
    const uniqueProducts = new Set(filteredData.map(item => item.product?.value)).size;

    return {
      totalProfit,
      totalSales,
      totalCost,
      profitMargin,
      productCount: uniqueProducts
    };
  }, [filteredData]);

  // Prepare chart data for monthly profit trend
  const monthlyProfitData = useMemo(() => {
    if (!filteredData?.length) return [];

    const monthlyData = {};

    filteredData.forEach(item => {
      const monthYear = dayjs(item.date).format('MMM YY');

      if (!monthlyData[monthYear]) {
        monthlyData[monthYear] = {
          month: monthYear,
          profit: 0,
          sales: 0,
          cost: 0
        };
      }

      monthlyData[monthYear].profit += item.profit || 0;
      monthlyData[monthYear].sales += item.sold || 0;
      monthlyData[monthYear].cost += item.bought || 0;
    });

    // Convert to array and sort by date
    return Object.values(monthlyData)
      .sort((a, b) => {
        const aDate = dayjs(a.month, 'MMM YY');
        const bDate = dayjs(b.month, 'MMM YY');
        return aDate.unix() - bDate.unix();
      })
      .flatMap(item => [
        {
          month: item.month,
          value: item.sales,
          category: 'Sales'
        },
        {
          month: item.month,
          value: item.cost,
          category: 'Cost'
        },
        {
          month: item.month,
          value: item.profit,
          category: 'Profit'
        }
      ]);
  }, [filteredData]);

  // Extract unique categories for the filter dropdown
  const categoryOptions = useMemo(() => {
    if (!profitData?.length) return [];

    const uniqueCategories = new Map();

    profitData.forEach(item => {
      if (item.category?.value && item.category?.label) {
        uniqueCategories.set(item.category.value, {
          value: item.category.value,
          label: item.category.label
        });
      }
    });

    // Convert Map to array and sort alphabetically by label
    return Array.from(uniqueCategories.values())
      .sort((a, b) => a.label.localeCompare(b.label));
  }, [profitData]);

  // Prepare chart data for product category profitability
  const categoryProfitData = useMemo(() => {
    if (!filteredData?.length) return [];

    const categoryData = {};

    filteredData.forEach(item => {
      const categoryLabel = item.category?.label || 'Uncategorized';

      if (!categoryData[categoryLabel]) {
        categoryData[categoryLabel] = {
          category: categoryLabel,
          profit: 0,
          sales: 0,
          cost: 0
        };
      }

      categoryData[categoryLabel].profit += item.profit || 0;
      categoryData[categoryLabel].sales += item.sold || 0;
      categoryData[categoryLabel].cost += item.bought || 0;
    });

    // Convert to array and sort by profit
    return Object.values(categoryData)
      .sort((a, b) => b.profit - a.profit)
      .slice(0, 10); // Top 10 categories
  }, [filteredData]);

  const tableProps = useMemo(() => ({
    size: "small",
    columns,
    dataSource: filteredData,
    loading,
    pagination: false,
    // scroll: { y: "calc(100vh - 350px)" },
    summary: (pageData) => {
      const total_bought = pageData.reduce((a, b) => a + (b.bought || 0), 0);
      const total_sold = pageData.reduce((a, b) => a + (b.sold || 0), 0);
      const total_profit = pageData.reduce((a, b) => a + (b.profit || 0), 0);
      const percent_profit = total_bought ? (total_profit / total_bought) * 100 : 0;
      const color = percent_profit >= 0 ? 'green' : 'red';

      return (
        <Table.Summary fixed style={{ fontSize: 20 }}>
          <Table.Summary.Row>
            <Table.Summary.Cell colSpan={4}>Total</Table.Summary.Cell>
            <Table.Summary.Cell>
              {total_bought.toLocaleString(undefined, { maximumFractionDigits: 2 })}
            </Table.Summary.Cell>
            <Table.Summary.Cell>
              {total_sold.toLocaleString(undefined, { maximumFractionDigits: 2 })}
            </Table.Summary.Cell>
            <Table.Summary.Cell style={{ color }}>
              {total_profit.toLocaleString(undefined, { maximumFractionDigits: 2 })}
              ({percent_profit.toLocaleString(undefined, { maximumFractionDigits: 1 })}%)
            </Table.Summary.Cell>
          </Table.Summary.Row>
        </Table.Summary>
      );
    },
  }), [columns, filteredData, loading]);

  // Stats grid data
  const statsGridData = useMemo(() => [
    {
      title: 'Total Profit',
      value: summaryStats.totalProfit,
      icon: 'money',
      diff: summaryStats.profitMargin,
      diffLabel: 'profit margin',
      theme: summaryStats.totalProfit >= 0 ? 'success' : 'danger'
    },
    {
      title: 'Total Sales',
      value: summaryStats.totalSales,
      icon: 'chart',
      theme: 'primary'
    },
    {
      title: 'Total Cost',
      value: summaryStats.totalCost,
      icon: 'cash',
      theme: 'info'
    },
    {
      title: 'Products Sold',
      value: summaryStats.productCount,
      icon: 'cart',
      theme: 'warning'
    }
  ], [summaryStats]);

  // Chart configurations
  const monthlyChartConfig = {
    data: monthlyProfitData,
    isGroup: true,
    xField: 'month',
    yField: 'value',
    seriesField: 'category',
    color: ['#1677ff', '#fadb14', '#52c41a'],
    tooltip: {
      formatter: (datum) => {
        return {
          name: datum.category,
          value: datum.value.toLocaleString(undefined, { maximumFractionDigits: 2 })
        };
      }
    }
  };

  const categoryPieConfig = {
    data: categoryProfitData,
    angleField: 'profit',
    colorField: 'category',
    radius: 0.8,
    tooltip: {
      formatter: (datum) => {
        return {
          name: datum.category,
          value: datum.profit.toLocaleString(undefined, { maximumFractionDigits: 2 })
        };
      }
    }
  };

  // Add a loading state component
  const renderLoading = () => (
    <div style={{ padding: '50px', textAlign: 'center' }}>
      <div style={{ marginBottom: '20px' }}>Loading profit data...</div>
      <div className="loading-spinner" style={{
        display: 'inline-block',
        width: '50px',
        height: '50px',
        border: '5px solid rgba(0, 0, 0, 0.1)',
        borderRadius: '50%',
        borderTop: '5px solid #1890ff',
        animation: 'spin 1s linear infinite'
      }}></div>
      <style>{`
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      `}</style>
    </div>
  );

  // Add debug logging
  useEffect(() => {
    console.log("Loading state:", loading);
    console.log("Organization state:", organization);
  }, [loading, organization]);

  // Render loading state if data is still loading or organization is not loaded
  // Add a timeout to prevent infinite loading
  const [loadingTimeout, setLoadingTimeout] = useState(false);

  useEffect(() => {
    // Set a timeout to force rendering after 5 seconds even if data isn't fully loaded
    const timer = setTimeout(() => {
      setLoadingTimeout(true);
    }, 5000);

    return () => clearTimeout(timer);
  }, []);

  if (loading && !loadingTimeout) {
    return renderLoading();
  }

  return (
    <div>
      {/* Stats Grid at the top */}
      <div style={{ marginBottom: 16 }}>
        <StatsGrid data={statsGridData} />
      </div>

      {/* Date Range and Search Controls */}
      <Row justify="space-between" style={{ marginBottom: 16 }}>
        <Col span={12}>
          <DatePicker.RangePicker
            value={dateRange}
            onChange={setDateRange}
            style={{ marginRight: 16 }}
            presets={[
              {
                label: 'Today',
                value: [dayjs().startOf('day'), dayjs().endOf('day')],
              },
              {
                label: 'Yesterday',
                value: [
                  dayjs().subtract(1, 'day').startOf('day'),
                  dayjs().subtract(1, 'day').endOf('day'),
                ],
              },
              {
                label: 'This Week',
                value: [dayjs().startOf('week'), dayjs().endOf('day')],
              },
              {
                label: 'Last Week',
                value: [
                  dayjs().subtract(1, 'week').startOf('week'),
                  dayjs().subtract(1, 'week').endOf('week'),
                ],
              },
              {
                label: 'This Month',
                value: [dayjs().startOf('month'), dayjs().endOf('day')],
              },
              {
                label: 'Last Month',
                value: [
                  dayjs().subtract(1, 'month').startOf('month'),
                  dayjs().subtract(1, 'month').endOf('month'),
                ],
              },
              {
                label: 'This Year',
                value: [dayjs().startOf('year'), dayjs().endOf('day')],
              },
              {
                label: 'Last Year',
                value: [
                  dayjs().subtract(1, 'year').startOf('year'),
                  dayjs().subtract(1, 'year').endOf('year'),
                ],
              },
            ]}
          />
          <Select
            allowClear
            placeholder="Select Category"
            value={selectedCategory}
            onChange={setSelectedCategory}
            style={{ width: 200, marginRight: 16 }}
            options={categoryOptions}
          />
          <Input.Search
            placeholder="Search..."
            onChange={(e) => setKeyword(e.target.value)}
            style={{ width: 304 }}
            allowClear
          />
        </Col>
        <Col span={12} style={{ textAlign: "end" }}>
          <Space>
            <Button
              icon={<DownloadOutlined />}
              onClick={() => exportToCSV(filteredData, columns, 'Profit_Loss_Report')}
              disabled={!filteredData || filteredData.length === 0}
            >
              Export CSV
            </Button>
            <PrintComponents
              trigger={
                <Button
                  icon={<PrinterOutlined />}
                  type="primary"
                >
                  Print
                </Button>
              }
            >
            <div className="print-container" style={{ margin: 20, background: 'white' }}>
              {organization && <DocumentHead company={organization} />}

              {/* Company Information Header */}
              <div style={{ marginBottom: 20 }}>
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
                  <div>
                    <Typography.Title level={3} style={{ margin: 0 }}>Profit/Loss Ledger</Typography.Title>
                    <Typography.Text type="secondary">Financial Performance Report</Typography.Text>
                  </div>
                  <div style={{ textAlign: 'right' }}>
                    <Typography.Text type="secondary" style={{ display: 'block' }}>
                      Generated: {dayjs().format('DD-MM-YYYY HH:mm')}
                    </Typography.Text>
                    <Typography.Text type="secondary" style={{ display: 'block' }}>
                      Prepared by: {(() => {
                        try {
                          const user = JSON.parse(localStorage.getItem('LOCAL_STORAGE_USER'));
                          return user ? `${user.first_name} ${user.last_name}` : 'System User';
                        } catch (e) {
                          return 'System User';
                        }
                      })()}
                    </Typography.Text>
                  </div>
                </div>
              </div>

              {/* Add a divider */}
              <div style={{ height: 2, background: '#f0f0f0', marginBottom: 20 }}></div>

              {/* Print Stats Summary */}
              <div style={{ marginBottom: 20 }}>
                <Row gutter={[16, 16]}>
                  <Col span={6}>
                    <Card>
                      <div style={{ textAlign: 'center' }}>
                        <Typography.Title level={4}>Total Profit</Typography.Title>
                        <Typography.Title level={3} style={{
                          color: summaryStats.totalProfit >= 0 ? 'green' : 'red',
                          margin: 0
                        }}>
                          {summaryStats.totalProfit.toLocaleString(undefined, { maximumFractionDigits: 2 })}
                        </Typography.Title>
                      </div>
                    </Card>
                  </Col>
                  <Col span={6}>
                    <Card>
                      <div style={{ textAlign: 'center' }}>
                        <Typography.Title level={4}>Total Sales</Typography.Title>
                        <Typography.Title level={3} style={{ margin: 0 }}>
                          {summaryStats.totalSales.toLocaleString(undefined, { maximumFractionDigits: 2 })}
                        </Typography.Title>
                      </div>
                    </Card>
                  </Col>
                  <Col span={6}>
                    <Card>
                      <div style={{ textAlign: 'center' }}>
                        <Typography.Title level={4}>Total Cost</Typography.Title>
                        <Typography.Title level={3} style={{ margin: 0 }}>
                          {summaryStats.totalCost.toLocaleString(undefined, { maximumFractionDigits: 2 })}
                        </Typography.Title>
                      </div>
                    </Card>
                  </Col>
                  <Col span={6}>
                    <Card>
                      <div style={{ textAlign: 'center' }}>
                        <Typography.Title level={4}>Profit Margin</Typography.Title>
                        <Typography.Title level={3} style={{
                          margin: 0,
                          color: summaryStats.profitMargin >= 0 ? 'green' : 'red'
                        }}>
                          {summaryStats.profitMargin.toLocaleString(undefined, { maximumFractionDigits: 1 })}%
                        </Typography.Title>
                      </div>
                    </Card>
                  </Col>
                </Row>
              </div>

              {/* Print Date Range and Filters */}
              <div style={{ marginBottom: 10, display: 'flex', justifyContent: 'space-between' }}>
                <div>
                  <Typography.Text strong>
                    Period: {dateRange?.[0] ? dayjs(dateRange[0]).format('DD-MM-YYYY') : 'All'} to {dateRange?.[1] ? dayjs(dateRange[1]).format('DD-MM-YYYY') : 'All'}
                  </Typography.Text>
                  {selectedCategory && (
                    <Typography.Text strong style={{ marginLeft: 16 }}>
                      Category: {categoryOptions.find(c => c.value === selectedCategory)?.label || 'Unknown'}
                    </Typography.Text>
                  )}
                </div>
              </div>

              <Table
                {...tableProps}
                pagination={false}
                scroll={false}
              />
            </div>
          </PrintComponents>
          </Space>
        </Col>
      </Row>

      {/* Tabbed Interface */}
      <Tabs
        activeKey={activeTab}
        onChange={setActiveTab}
        type="card"
      >
        <TabPane
          tab={<span><UnorderedListOutlined /> Profit/Loss Ledger</span>}
          key="ledger"
        >
          <Table {...tableProps} />
        </TabPane>

        <TabPane
          tab={<span><BarChartOutlined /> Profit Analysis</span>}
          key="analysis"
        >
          <Row gutter={[16, 16]}>
            {/* Monthly Profit Trend Chart */}
            <Col span={24}>
              <Card title="Monthly Profit Trend">
                {monthlyProfitData.length > 0 ? (
                  <div style={{ height: 400 }}>
                    <Column {...monthlyChartConfig} />
                  </div>
                ) : (
                  <div style={{ height: 400, display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
                    <p>No data available for the selected period</p>
                  </div>
                )}
              </Card>
            </Col>

            {/* Category Profitability Chart */}
            <Col span={12}>
              <Card title="Profit by Category">
                {categoryProfitData.length > 0 ? (
                  <div style={{ height: 400 }}>
                    <Pie {...categoryPieConfig} />
                  </div>
                ) : (
                  <div style={{ height: 400, display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
                    <p>No data available for the selected period</p>
                  </div>
                )}
              </Card>
            </Col>

            {/* Top Products Table */}
            <Col span={12}>
              <Card title="Top Products by Profit">
                <Table
                  dataSource={filteredData
                    .sort((a, b) => b.profit - a.profit)
                    .slice(0, 50)
                  }
                  columns={columns}
                  pagination={{
                    defaultPageSize: 10,
                    showSizeChanger: true,
                    pageSizeOptions: ['5', '10', '20', '50'],
                    showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`
                  }}
                  size="small"
                />
              </Card>
            </Col>
          </Row>
        </TabPane>
      </Tabs>
    </div>
  );
};

export default Profit;