import React, { useEffect, useState, useMemo } from "react";
import "./print.css";
import DocumentHead from "../../../Universal/CustomViews/Components/DocumentHead";
import PrintComponents from "react-print-components";
import StatsGrid from "../../../../Components/Stats/StatsGrid";
import dayjs from "dayjs";
import { Button, Col, Input, Row, Table, Typography, Tabs, Card, Select, Tag, Statistic, Space, Divider, } from "antd";
import { Pie, Column } from "@ant-design/charts";
import { PrinterOutlined, BarChartOutlined, UnorderedListOutlined, FilterOutlined, DownloadOutlined, } from "@ant-design/icons";
import { buffProducts } from "../../modulesProperties/utils";
import { exportToCSV } from "./utils/csvExport";
import { useReportsData } from "./ReportsContext";


const { TabPane } = Tabs;

const ProductList = ({ pouchDatabase, databasePrefix }) => {
  const {
    products,
    loading,
    prices,
    categories = [],
    company,
  } = useReportsData();
  const [data, setData] = useState([]);
  const [keyword, setKeyword] = useState(null);
  const [selectedCategory, setSelectedCategory] = useState(null);
  const [activeTab, setActiveTab] = useState("list");
  const [organization, setOrganization] = useState(null);
  const [stockFilter, setStockFilter] = useState("all"); // 'all', 'in-stock', 'low-stock', 'out-of-stock'

  useEffect(() => {
    if (products?.length) {
      buffProducts(products).then((res) => setData(res));
    }
  }, [products]);

  // Fetch organization information
  useEffect(() => {

    // Set a default organization to prevent loading state from getting stuck
    const defaultOrg = {
      name: "Your Company",
      address: "",
      phone: "",
      email: "",
    };

    // If pouchDatabase is not available, use default organization
    if (!pouchDatabase) {
      console.log("PouchDatabase not available, using default organization");
      setOrganization(defaultOrg);
      return;
    }

    // Attempt to fetch organization data
    if (company) {
      setOrganization(company);
    } else if (pouchDatabase) {
      pouchDatabase("organizations", databasePrefix)
        .getAllData()
        .then((data) => {
          if (data && data.length > 0) {
            // Get the first organization or a specific one if needed
            const org = data[0];
            setOrganization(org);
          } else {
            // No organization data found, use default
            console.log("No organization data found, using default");
            setOrganization(defaultOrg);
          }
        })
        .catch((err) => {
          console.error("Error fetching organization data:", err);
          // On error, use default organization
          setOrganization(defaultOrg);
        });
    }
  }, [company, pouchDatabase, databasePrefix]);

  const columns = useMemo(
    () => [
      {
        title: "No",
        dataIndex: "no",
        render: (_, __, index) => index + 1,
      },
      {
        valueType: "text",
        dataIndex: "name",
        title: "Name",
        render: (text, record) => {
          if (!record.units || (typeof record.units === 'string' && record.units.toLowerCase() === "pc")) {
            return text;
          } else {
            return `${record.name} - ${record.measurements || ''} ${record.units || ''}`;
          }
        },
      },
      {
        dataIndex: "description",
        title: "Description",
        width: 300,
        render: (text) => text && text.split(",").join(", "),
      },
      {
        valueType: "text",
        dataIndex: "sku",
        title: "SKU/Item No",
      },
      {
        dataIndex: "category",
        title: "Category",
        render: (v) => v?.label,
      },
      {
        title: "Current Stock",
        dataIndex: "stock",
        render: (_, record) => {
          const currentStock =
            (record.stoked || 0) -
            (record.sold || 0) +
            (record.returned || 0) -
            (record.returned_stock || 0) +
            (record.adjustments || 0);
          return currentStock;
        },
      },
      {
        title: "Selling Price",
        dataIndex: "sell",
        render: (_, record) => {
          const latestPrice =
            prices?.find((p) => p.product?.value === record._id)?.price ||
            record.sell ||
            0;
          return latestPrice.toLocaleString();
        },
      },
    ],
    [prices],
  );

  const filteredData = useMemo(() => {
    if (!data?.length) return [];

    let filtered = data;

    // Apply category filter
    if (selectedCategory) {
      filtered = filtered.filter(
        (record) => record.category?.value === selectedCategory,
      );
    }

    // Apply stock filter
    if (stockFilter !== "all") {
      filtered = filtered.filter((record) => {
        const currentStock =
          (record.stoked || 0) -
          (record.sold || 0) +
          (record.returned || 0) -
          (record.returned_stock || 0) +
          (record.adjustments || 0);

        switch (stockFilter) {
          case "in-stock":
            return currentStock > 0;
          case "low-stock":
            return currentStock > 0 && currentStock <= 5;
          case "out-of-stock":
            return currentStock <= 0;
          default:
            return true;
        }
      });
    }

    // Apply keyword search
    if (keyword) {
      const searchLower = keyword.toLowerCase();
      filtered = filtered.filter((record) => {
        // Optimize search by checking most common fields first
        const name = record.name?.toLowerCase() || "";
        if (name.includes(searchLower)) return true;

        // Ensure sku exists and is a string before calling toLowerCase
        const sku = typeof record.sku === 'string' ? record.sku.toLowerCase() : "";
        if (sku.includes(searchLower)) return true;

        const description = record.description?.toLowerCase() || "";
        if (description.includes(searchLower)) return true;

        const categoryLabel = record.category?.label?.toLowerCase() || "";
        if (categoryLabel.includes(searchLower)) return true;

        // Only do the expensive JSON.stringify as a last resort
        return JSON.stringify({
          name: record.name || "",
          sku: record.sku || "",
          description: record.description || "",
          category: record.category?.label || "",
        })
          .toLowerCase()
          .includes(searchLower);
      });
    }

    return filtered.sort((a, b) =>
      (a?.name || "").localeCompare(b?.name || ""),
    );
  }, [data, keyword, selectedCategory, stockFilter]);

  // Calculate summary statistics
  const summaryStats = useMemo(() => {
    if (!filteredData?.length)
      return {
        totalProducts: 0,
        totalValue: 0,
        avgPrice: 0,
        inStockCount: 0,
        lowStockCount: 0,
        outOfStockCount: 0,
      };

    const totalProducts = filteredData.length;

    let totalValue = 0;
    let inStockCount = 0;
    let lowStockCount = 0;
    let outOfStockCount = 0;

    filteredData.forEach((product) => {
      const currentStock =
        (product.stoked || 0) -
        (product.sold || 0) +
        (product.returned || 0) -
        (product.returned_stock || 0) +
        (product.adjustments || 0);

      const price =
        prices?.find((p) => p.product?.value === product._id)?.price ||
        product.sell ||
        0;

      if (currentStock > 0) {
        totalValue += currentStock * price;

        if (currentStock <= 5) {
          lowStockCount++;
        } else {
          inStockCount++;
        }
      } else {
        outOfStockCount++;
      }
    });

    const avgPrice =
      totalProducts > 0
        ? filteredData.reduce((sum, product) => {
            const price =
              prices?.find((p) => p.product?.value === product._id)?.price ||
              product.sell ||
              0;
            return sum + price;
          }, 0) / totalProducts
        : 0;

    return {
      totalProducts,
      totalValue,
      avgPrice,
      inStockCount,
      lowStockCount,
      outOfStockCount,
    };
  }, [filteredData, prices]);

  // Prepare chart data for category breakdown
  const categoryChartData = useMemo(() => {
    if (!filteredData?.length) return [];

    const categoryData = {};

    filteredData.forEach((product) => {
      const categoryLabel = product.category?.label || "Uncategorized";

      if (!categoryData[categoryLabel]) {
        categoryData[categoryLabel] = {
          category: categoryLabel,
          count: 0,
          value: 0,
        };
      }

      const currentStock =
        (product.stoked || 0) -
        (product.sold || 0) +
        (product.returned || 0) -
        (product.returned_stock || 0) +
        (product.adjustments || 0);

      const price =
        prices?.find((p) => p.product?.value === product._id)?.price ||
        product.sell ||
        0;

      categoryData[categoryLabel].count += 1;
      categoryData[categoryLabel].value +=
        currentStock > 0 ? currentStock * price : 0;
    });

    // Convert to array and sort by count
    return Object.values(categoryData)
      .sort((a, b) => b.count - a.count)
      .slice(0, 10); // Top 10 categories
  }, [filteredData, prices]);

  // Prepare chart data for stock status
  const stockStatusData = useMemo(() => {
    return [
      { status: "In Stock", value: summaryStats.inStockCount },
      { status: "Low Stock", value: summaryStats.lowStockCount },
      { status: "Out of Stock", value: summaryStats.outOfStockCount },
    ];
  }, [summaryStats]);

  // Stats grid data
  const statsGridData = useMemo(
    () => [
      {
        title: "Total Products",
        value: summaryStats.totalProducts,
        icon: "shopping",
        theme: "primary",
      },
      {
        title: "Inventory Value",
        value: summaryStats.totalValue,
        icon: "money",
        theme: "success",
        valueType: "money",
      },
      {
        title: "Average Price",
        value: summaryStats.avgPrice,
        icon: "tag",
        theme: "info",
        valueType: "money",
      },
      {
        title: "Out of Stock",
        value: summaryStats.outOfStockCount,
        icon: "warning",
        theme: "danger",
        diff:
          summaryStats.totalProducts > 0
            ? (summaryStats.outOfStockCount / summaryStats.totalProducts) * 100
            : 0,
        diffLabel: "of total",
      },
    ],
    [summaryStats],
  );

  // Chart configurations
  const categoryPieConfig = {
    data: categoryChartData,
    angleField: "count",
    colorField: "category",
    radius: 0.8,
    tooltip: {
      formatter: (datum) => {
        return {
          name: datum.category,
          value: `${datum.count} products (${datum.value.toLocaleString(undefined, { maximumFractionDigits: 2 })} value)`,
        };
      },
    },
  };

  const stockStatusPieConfig = {
    data: stockStatusData,
    angleField: "value",
    colorField: "status",
    radius: 0.8,
    colors: ["#52c41a", "#faad14", "#ff4d4f"],
    tooltip: {
      formatter: (datum) => {
        return {
          name: datum.status,
          value: datum.value,
        };
      },
    },
  };

  const tableProps = useMemo(
    () => ({
      size: "small",
      columns,
      pagination: false,
      // {
      //   defaultPageSize: 20,
      //   showSizeChanger: true,
      //   pageSizeOptions: ['10', '20', '50', '100'],
      //   showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`
      // },
      dataSource: filteredData,
      loading,
      scroll: { y: "calc(100vh - 350px)" },
    }),
    [columns, filteredData, loading],
  );

  return (
    <div>
      {/* Stats Grid at the top */}
      <div style={{ marginBottom: 16 }}>
        <StatsGrid data={statsGridData} />
      </div>

      {/* Filters and Search Controls */}
      <Row justify="space-between" style={{ marginBottom: 16 }}>
        <Col span={16}>
          <Row gutter={[16, 16]}>
            <Col>
              <Select
                allowClear
                style={{ width: 200, marginRight: 16 }}
                placeholder="Select Category"
                options={categories?.map((cat) => ({
                  label: cat.name,
                  value: cat._id,
                }))}
                onChange={setSelectedCategory}
                value={selectedCategory}
              />
            </Col>
            <Col>
              <Select
                style={{ width: 150, marginRight: 16 }}
                placeholder="Stock Status"
                options={[
                  { label: "All Products", value: "all" },
                  { label: "In Stock", value: "in-stock" },
                  { label: "Low Stock", value: "low-stock" },
                  { label: "Out of Stock", value: "out-of-stock" },
                ]}
                onChange={setStockFilter}
                value={stockFilter}
              />
            </Col>
            <Col>
              <Input.Search
                placeholder="Search products..."
                onSearch={(value) => setKeyword(value)}
                style={{ width: 250 }}
                allowClear
              />
            </Col>
          </Row>
        </Col>
        <Col span={8} style={{ textAlign: "end" }}>
          <Space>
            <Button
              icon={<DownloadOutlined />}
              onClick={() => exportToCSV(filteredData, columns, 'Product_List')}
              disabled={!filteredData || filteredData.length === 0}
            >
              Export CSV
            </Button>
            <PrintComponents
              trigger={
                <Button icon={<PrinterOutlined />} type="primary">
                  Print
                </Button>
              }
            >
            <div
              className="print-container"
              style={{ margin: 20, background: "white" }}
            >
              {organization && <DocumentHead company={organization} />}

              {/* Company Information Header */}
              <div style={{ marginBottom: 20 }}>
                <div
                  style={{
                    display: "flex",
                    justifyContent: "space-between",
                    alignItems: "flex-start",
                  }}
                >
                  <div>
                    <Typography.Title level={3} style={{ margin: 0 }}>
                      Product Inventory
                    </Typography.Title>
                    <Typography.Text type="secondary">
                      Inventory Report
                    </Typography.Text>
                  </div>
                  <div style={{ textAlign: "right" }}>
                    <Typography.Text
                      type="secondary"
                      style={{ display: "block" }}
                    >
                      Generated: {dayjs().format("DD-MM-YYYY HH:mm")}
                    </Typography.Text>
                    <Typography.Text
                      type="secondary"
                      style={{ display: "block" }}
                    >
                      Prepared by:{" "}
                      {(() => {
                        try {
                          const user = JSON.parse(
                            localStorage.getItem("LOCAL_STORAGE_USER"),
                          );
                          return user
                            ? `${user.first_name} ${user.last_name}`
                            : "System User";
                        } catch (e) {
                          return "System User";
                        }
                      })()}
                    </Typography.Text>
                  </div>
                </div>
              </div>

              {/* Add a divider */}
              <div
                style={{ height: 2, background: "#f0f0f0", marginBottom: 20 }}
              ></div>

              {/* Print Stats Summary */}
              <div style={{ marginBottom: 20 }}>
                <Row gutter={[16, 16]}>
                  <Col span={6}>
                    <Card>
                      <div style={{ textAlign: "center" }}>
                        <Typography.Title level={4}>
                          Total Products
                        </Typography.Title>
                        <Typography.Title level={3} style={{ margin: 0 }}>
                          {summaryStats.totalProducts}
                        </Typography.Title>
                      </div>
                    </Card>
                  </Col>
                  <Col span={6}>
                    <Card>
                      <div style={{ textAlign: "center" }}>
                        <Typography.Title level={4}>
                          Inventory Value
                        </Typography.Title>
                        <Typography.Title level={3} style={{ margin: 0 }}>
                          {summaryStats.totalValue.toLocaleString(undefined, {
                            maximumFractionDigits: 2,
                          })}
                        </Typography.Title>
                      </div>
                    </Card>
                  </Col>
                  <Col span={6}>
                    <Card>
                      <div style={{ textAlign: "center" }}>
                        <Typography.Title level={4}>In Stock</Typography.Title>
                        <Typography.Title level={3} style={{ margin: 0 }}>
                          {summaryStats.inStockCount}
                        </Typography.Title>
                      </div>
                    </Card>
                  </Col>
                  <Col span={6}>
                    <Card>
                      <div style={{ textAlign: "center" }}>
                        <Typography.Title level={4}>
                          Out of Stock
                        </Typography.Title>
                        <Typography.Title
                          level={3}
                          style={{ margin: 0, color: "red" }}
                        >
                          {summaryStats.outOfStockCount}
                        </Typography.Title>
                      </div>
                    </Card>
                  </Col>
                </Row>
              </div>

              {/* Print Filters */}
              <div style={{ marginBottom: 10 }}>
                {selectedCategory && (
                  <Typography.Text strong style={{ marginRight: 16 }}>
                    Category:{" "}
                    {categories.find((c) => c._id === selectedCategory)?.name ||
                      "Unknown"}
                  </Typography.Text>
                )}
                {stockFilter !== "all" && (
                  <Typography.Text strong>
                    Stock Status:{" "}
                    {stockFilter === "in-stock"
                      ? "In Stock"
                      : stockFilter === "low-stock"
                        ? "Low Stock"
                        : stockFilter === "out-of-stock"
                          ? "Out of Stock"
                          : "All"}
                  </Typography.Text>
                )}
              </div>

              <Table {...tableProps} pagination={false} scroll={false} />
            </div>
          </PrintComponents>
          </Space>
        </Col>
      </Row>

      {/* Tabbed Interface */}
      <Tabs activeKey={activeTab} onChange={setActiveTab} type="card">
        <TabPane
          tab={
            <span>
              <UnorderedListOutlined /> Product List
            </span>
          }
          key="list"
        >
          <Table {...tableProps} />
        </TabPane>

        <TabPane
          tab={
            <span>
              <BarChartOutlined /> Inventory Analysis
            </span>
          }
          key="analysis"
        >
          <Row gutter={[16, 16]}>
            {/* Stock Status Chart */}
            <Col span={12}>
              <Card title="Stock Status">
                {stockStatusData.some((item) => item.value > 0) ? (
                  <div style={{ height: 400 }}>
                    <Pie {...stockStatusPieConfig} />
                  </div>
                ) : (
                  <div
                    style={{
                      height: 400,
                      display: "flex",
                      justifyContent: "center",
                      alignItems: "center",
                    }}
                  >
                    <p>No data available</p>
                  </div>
                )}
              </Card>
            </Col>

            {/* Category Breakdown Chart */}
            <Col span={12}>
              <Card title="Products by Category">
                {categoryChartData.length > 0 ? (
                  <div style={{ height: 400 }}>
                    <Pie {...categoryPieConfig} />
                  </div>
                ) : (
                  <div
                    style={{
                      height: 400,
                      display: "flex",
                      justifyContent: "center",
                      alignItems: "center",
                    }}
                  >
                    <p>No data available</p>
                  </div>
                )}
              </Card>
            </Col>

            {/* Low Stock Products Table */}
            <Col span={24}>
              <Card title="Low Stock Products (5 or fewer in stock)">
                <Table
                  dataSource={filteredData
                    .filter((product) => {
                      const currentStock =
                        (product.stoked || 0) -
                        (product.sold || 0) +
                        (product.returned || 0) -
                        (product.returned_stock || 0) +
                        (product.adjustments || 0);
                      return currentStock > 0 && currentStock <= 5;
                    })
                    .sort((a, b) => {
                      const stockA =
                        (a.stoked || 0) -
                        (a.sold || 0) +
                        (a.returned || 0) -
                        (a.returned_stock || 0) +
                        (a.adjustments || 0);
                      const stockB =
                        (b.stoked || 0) -
                        (b.sold || 0) +
                        (b.returned || 0) -
                        (b.returned_stock || 0) +
                        (b.adjustments || 0);
                      return stockA - stockB;
                    })}
                  columns={columns}
                  pagination={{
                    defaultPageSize: 10,
                    showSizeChanger: true,
                    pageSizeOptions: ["5", "10", "20", "50"],
                    showTotal: (total, range) =>
                      `${range[0]}-${range[1]} of ${total} items`,
                  }}
                  size="small"
                />
              </Card>
            </Col>
          </Row>
        </TabPane>
      </Tabs>
    </div>
  );
};

export default ProductList;