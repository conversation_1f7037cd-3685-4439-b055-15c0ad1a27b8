import './print.css';
import DocumentHead from "../../../Universal/CustomViews/Components/DocumentHead";
import PrintComponents from "react-print-components";
import React, { useState, useMemo } from 'react';
import dayjs from "dayjs";
import moment from "moment";
import { PrinterOutlined, DownloadOutlined } from "@ant-design/icons";
import { Table, Button, Row, Col, Input, Typography, DatePicker, Space } from "antd";
import { exportToCSV } from "./utils/csvExport";
import { formatNumber } from "../../../../Utils/functions";
import { useReportsData } from "./ReportsContext";


const { RangePicker } = DatePicker;

const ProductSales = () => {
  const { sales: invoices, products, loading, company } = useReportsData();
  const [keyword, setKeyword] = useState(null);
  const [dateRange, setDateRange] = useState([
    dayjs().startOf('month'),
    dayjs().endOf('month')
  ]);

  console.log("invoicesinvoicesinvoices", invoices);


  const processedData = useMemo(() => {
    if (!invoices?.length || !products?.length) return [];

    // Filter invoices by date range
    const filteredInvoices = invoices.filter(invoice => {
      return dateRange && moment(invoice.date).isSameOrAfter(moment(dateRange[0].$d), "day") &&
        moment(invoice.date).isSameOrBefore(moment(dateRange[1].$d), "day")
    })

    // Create a map of product sales
    const salesMap = new Map();

    filteredInvoices.forEach(invoice => {
      invoice.items?.forEach(item => {
        const product = products.find(p => p._id === item.product?.value);
        if (!product) return;

        const existing = salesMap.get(product._id) || {
          product: product.name,
          sku: product.sku,
          category: product.category?.label,
          quantity: 0,
          amount: 0
        };

        existing.quantity += item.quantity || 0;
        existing.amount += (item.quantity * item.price) || 0;
        salesMap.set(product._id, existing);
      });
    });

    return Array.from(salesMap.values());
  }, [invoices, products, dateRange]);

  const columns = [
    {
      title: 'No',
      dataIndex: 'no',
      width: 50,
      render: (_, __, index) => index + 1,
    },
    {
      title: 'Product',
      dataIndex: 'product',
    },
    {
      title: 'SKU',
      dataIndex: 'sku',
    },
    {
      title: 'Category',
      dataIndex: 'category',
    },
    {
      title: 'Quantity',
      dataIndex: 'quantity',
      align: 'right',
      render: value => formatNumber(value),
    },
    {
      title: 'Amount',
      dataIndex: 'amount',
      align: 'right',
      render: value => formatNumber(value),
    }
  ];

  const filteredData = useMemo(() => {
    if (!processedData?.length) return [];

    let filtered = processedData;
    if (keyword) {
      const searchLower = keyword.toLowerCase();
      filtered = filtered.filter(record =>
        JSON.stringify(record).toLowerCase().includes(searchLower)
      );
    }
    return filtered.sort((a, b) => b.amount - a.amount);
  }, [processedData, keyword]);

  const tableProps = {
    size: 'small',
    columns,
    dataSource: filteredData,
    loading,
    pagination: {
      defaultPageSize: 20,
      showSizeChanger: true,
      pageSizeOptions: ['10', '20', '50', '100'],
      showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`
    },
    scroll: { y: 'calc(100vh - 350px)' },
    summary: (pageData) => {
      const totalQuantity = pageData.reduce((sum, row) => sum + (row.quantity || 0), 0);
      const totalAmount = pageData.reduce((sum, row) => sum + (row.amount || 0), 0);

      return (
        <Table.Summary fixed>
          <Table.Summary.Row>
            <Table.Summary.Cell colSpan={4}>Total</Table.Summary.Cell>
            <Table.Summary.Cell align="right">{formatNumber(totalQuantity)}</Table.Summary.Cell>
            <Table.Summary.Cell align="right">{formatNumber(totalAmount)}</Table.Summary.Cell>
          </Table.Summary.Row>
        </Table.Summary>
      );
    }
  };

  return (
    <div>
      <Table
        {...tableProps}
        title={() => (
          <Row gutter={[16, 16]}>
            <Col xs={24} sm={8}>
              <Input.Search
                placeholder="Search..."
                onChange={(e) => setKeyword(e.target.value)}
                style={{ width: '100%' }}
                allowClear
              />
            </Col>
            <Col xs={24} sm={8}>
              <RangePicker
                value={dateRange}
                onChange={setDateRange}
                style={{ width: '100%' }}
              />
            </Col>
            <Col xs={24} sm={8} style={{ textAlign: 'right' }}>
              <Space>
                <Button
                  icon={<DownloadOutlined />}
                  onClick={() => exportToCSV(filteredData, columns, 'Product_Sales_Report')}
                  disabled={!filteredData || filteredData.length === 0}
                >
                  Export CSV
                </Button>
                <PrintComponents
                  trigger={
                    <Button
                      icon={<PrinterOutlined />}
                      type="primary"
                    >
                      Print
                    </Button>
                  }
                >
                <div style={{ margin: 20, background: 'white' }}>
                  {company && <DocumentHead company={company} />}
                  <Typography.Title level={3}>Product Sales Report</Typography.Title>
                  {dateRange && dateRange[0] &&
                    <Typography.Text>
                      Period: {dateRange && dateRange[0].format('DD/MM/YYYY')} - {dateRange[1].format('DD/MM/YYYY')}
                    </Typography.Text>
                  }
                  <Table
                    {...tableProps}
                    pagination={false}
                    scroll={false}
                  />
                </div>
              </PrintComponents>
              </Space>
            </Col>
          </Row>
        )}
      />
    </div>
  );
};

export default ProductSales;