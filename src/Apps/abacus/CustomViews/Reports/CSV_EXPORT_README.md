# Abacus Reports CSV Export Implementation

## Overview

This document describes the CSV export functionality that has been implemented for all Abacus reports. Each report now includes a well-implemented CSV export feature alongside the existing print functionality.

## Features Implemented

### 1. CSV Export Utility (`utils/csvExport.js`)

A comprehensive utility module that provides:

- **`escapeCsvValue(value)`**: Properly escapes CSV values to handle commas, quotes, and newlines
- **`exportToCSV(data, columns, filename)`**: Main export function that converts table data to CSV format
- **`prepareDataForCSV(data, columns)`**: Prepares complex data structures for CSV export
- **`createCSVExportConfig(data, columns, reportName)`**: Creates standardized button configurations

### 2. Enhanced Data Handling

The CSV export handles various data types intelligently:

- **Dates**: Formatted as DD/MM/YYYY
- **Numbers**: Currency fields formatted with 2 decimal places
- **Objects**: Extracts `label`, `name`, or `value` properties
- **Calculated Fields**: Handles computed values like current stock
- **Product Names**: Includes measurements and units when applicable

### 3. Reports Updated

All major Abacus reports now include CSV export:

1. **Product List** (`ProductList.js`)
   - Exports product inventory with stock levels, prices, and categories
   - Filename: `Product_List_YYYY-MM-DD.csv`

2. **Sales Report** (`Sales.js`)
   - Exports detailed sales transactions by item
   - Includes date range filtering
   - Filename: `Sales_Report_YYYY-MM-DD.csv`

3. **Purchase Report** (`Purchases.js`)
   - Exports purchase transactions and supplier information
   - Includes date range filtering
   - Filename: `Purchase_Report_YYYY-MM-DD.csv`

4. **Profit/Loss Report** (`Profit.js`)
   - Exports profitability analysis by product
   - Includes profit margins and cost analysis
   - Filename: `Profit_Loss_Report_YYYY-MM-DD.csv`

5. **Inventory Report** (`Inventory.js`)
   - Exports current inventory status with stock alerts
   - Includes valuation and stock levels
   - Filename: `Inventory_Report_YYYY-MM-DD.csv`

6. **Product Sales Report** (`ProductSales.js`)
   - Exports product-specific sales performance
   - Filename: `Product_Sales_Report_YYYY-MM-DD.csv`

7. **Product Report** (`Product.js`)
   - Exports individual product transaction history
   - Filename: `Product_Report_YYYY-MM-DD.csv`

## User Interface

### Button Placement
- CSV export buttons are placed alongside existing print buttons
- Consistent styling and positioning across all reports
- Buttons are disabled when no data is available

### User Experience
- One-click export functionality
- Automatic filename generation with timestamps
- Success/error messages for user feedback
- No data validation warnings

## Technical Implementation

### Dependencies
- `dayjs` for date formatting
- `antd` for UI components and messaging
- No additional external libraries required

### File Structure
```
src/Apps/abacus/CustomViews/Reports/
├── utils/
│   ├── csvExport.js          # Main CSV export utility
│   ├── csvExport.test.js     # Test functions
│   └── CSV_EXPORT_README.md  # This documentation
├── ProductList.js            # Updated with CSV export
├── Sales.js                  # Updated with CSV export
├── Purchases.js              # Updated with CSV export
├── Profit.js                 # Updated with CSV export
├── Inventory.js              # Updated with CSV export
├── ProductSales.js           # Updated with CSV export
├── Product.js                # Updated with CSV export
└── ReportTemplate.js         # Template with CSV export
```

## Usage Examples

### Basic Usage
```javascript
import { exportToCSV } from './utils/csvExport';

// Export data with columns
exportToCSV(filteredData, columns, 'My_Report');
```

### Button Implementation
```javascript
<Button
  icon={<DownloadOutlined />}
  onClick={() => exportToCSV(filteredData, columns, 'Report_Name')}
  disabled={!filteredData || filteredData.length === 0}
>
  Export CSV
</Button>
```

## Data Format Examples

### Product List Export
```csv
No,Name,Description,SKU/Item No,Category,Current Stock,Selling Price
1,"Product A - 500 ml","High quality product","SKU001","Electronics",150,"1,250.00"
2,"Product B","Standard product","SKU002","Books",75,"850.50"
```

### Sales Report Export
```csv
No,Date,Invoice No,Customer,Item Name,SKU,Category,Qty,Price,Amount
1,"15/01/2024","INV001","Customer A","Product A","SKU001","Electronics",2,"1,250.00","2,500.00"
```

## Testing

A test file (`csvExport.test.js`) is included with functions to verify:
- CSV value escaping
- Data preparation
- Export functionality

Run tests in browser console:
```javascript
import csvTests from './utils/csvExport.test.js';
csvTests.runAllTests();
```

## Browser Compatibility

The CSV export functionality works in all modern browsers:
- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## Security Considerations

- All user data is processed client-side
- No data is sent to external servers
- CSV files are generated and downloaded locally
- Proper escaping prevents CSV injection attacks

## Future Enhancements

Potential improvements for future versions:
1. Custom column selection for export
2. Multiple file format support (Excel, PDF)
3. Scheduled/automated exports
4. Export templates and presets
5. Data compression for large exports

## Support

For issues or questions regarding CSV export functionality:
1. Check browser console for error messages
2. Verify data availability before export
3. Ensure proper column definitions
4. Test with sample data using provided test functions
