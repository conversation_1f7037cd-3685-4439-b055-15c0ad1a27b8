import React, { useState, useMemo, useEffect } from 'react';
import './print.css';
import DocumentHead from "../../../Universal/CustomViews/Components/DocumentHead";
import PrintComponents from "react-print-components";
import StatsGrid from "../../../../Components/Stats/StatsGrid";
import dayjs from "dayjs";
import isSameOrAfter from "dayjs/plugin/isSameOrAfter";
import isSameOrBefore from "dayjs/plugin/isSameOrBefore";
import { Column, Pie } from "@ant-design/plots";
import { PrinterOutlined, BarChartOutlined, UnorderedListOutlined } from "@ant-design/icons";
import { Table, Button, Row, Col, DatePicker, Typography, Select, Tabs, Card, Input } from "antd";
import { formatNumber } from "../../../../Utils/functions";
import { useReportsData } from "./ReportsContext";


dayjs.extend(isSameOrAfter);
dayjs.extend(isSameOrBefore);

const { TabPane } = Tabs;

const Expense = () => {
  const { expense: expenses = [], company, expenseCategories = [] } = useReportsData();
  const [dateRange, setDateRange] = useState([
    dayjs().startOf('month'),
    dayjs().endOf('month')
  ]);
  const [keyword, setKeyword] = useState(null);
  const [selectedCategory, setSelectedCategory] = useState(null);
  const [activeTab, setActiveTab] = useState("ledger");
  const [organization, setOrganization] = useState(null);

  // Fetch organization information
  useEffect(() => {
    const { pouchDatabase, databasePrefix } = { pouchDatabase: window.pouchDatabase, databasePrefix: window.databasePrefix };

    // Set a default organization to prevent loading state from getting stuck
    const defaultOrg = {
      name: "Your Company",
      address: "",
      phone: "",
      email: ""
    };

    // If pouchDatabase is not available, use default organization
    if (!pouchDatabase) {
      console.log("PouchDatabase not available, using default organization");
      setOrganization(defaultOrg);
      return;
    }

    // Attempt to fetch organization data
    if (company) {
      setOrganization(company);
    } else if (pouchDatabase) {
      pouchDatabase("organizations", databasePrefix)
        .getAllData()
        .then((data) => {
          if (data && data.length > 0) {
            // Get the first organization or a specific one if needed
            const org = data[0];

            // If organization has logo attachment, fetch it
            if (org._attachments && org._attachments.logo) {
              pouchDatabase("organizations", databasePrefix)
                .getAttachment(org._id, "logo")
                .then((logoBlob) => {
                  setOrganization({ ...org, orgLogo: logoBlob });
                })
                .catch((err) => {
                  console.error("Error fetching organization logo:", err);
                  setOrganization(org);
                });
            } else {
              setOrganization(org);
            }
          } else {
            // No organization data found, use default
            console.log("No organization data found, using default");
            setOrganization(defaultOrg);
          }
        })
        .catch((err) => {
          console.error("Error fetching organization data:", err);
          // On error, use default organization
          setOrganization(defaultOrg);
        });
    }
  }, [company]);

  const columns = [
    {
      title: 'Date',
      dataIndex: 'date',
      render: (text) => dayjs(text).format('DD MMM YY'),
      sorter: (a, b) => dayjs(a.date).unix() - dayjs(b.date).unix(),
    },
    {
      title: 'Supplier',
      dataIndex: 'supplier',
      render: (supplier) => supplier?.label || '-',
      sorter: (a, b) => {
        const aLabel = a.supplier?.label || '';
        const bLabel = b.supplier?.label || '';
        return aLabel.localeCompare(bLabel);
      },
    },
    {
      title: 'Category',
      dataIndex: 'expense-category',
      render: (category) => category?.label || 'Uncategorized',
      sorter: (a, b) => {
        const aLabel = a['expense-category']?.label || '';
        const bLabel = b['expense-category']?.label || '';
        return aLabel.localeCompare(bLabel);
      },
    },
    {
      title: 'Description',
      dataIndex: 'description',
    },
    {
      title: 'Amount',
      dataIndex: 'amount',
      align: 'right',
      render: (value) => value.toLocaleString(undefined, { maximumFractionDigits: 2 }),
      sorter: (a, b) => a.amount - b.amount,
    },
  ];

  const filteredData = useMemo(() => {
    if (!expenses?.length) return [];

    let filtered = expenses;

    // Filter by date range
    if (dateRange?.length === 2) {
      filtered = filtered.filter((expense) => {
        const expenseDate = dayjs(expense.date);
        return (
          expenseDate.isSameOrAfter(dayjs(dateRange[0]), 'day') &&
          expenseDate.isSameOrBefore(dayjs(dateRange[1]), 'day')
        );
      });
    }

    // Filter by category if selected
    if (selectedCategory) {
      filtered = filtered.filter(
        (expense) => expense['expense-category']?.value === selectedCategory
      );
    }

    // Filter by keyword if provided
    if (keyword) {
      const searchLower = keyword.toLowerCase();
      filtered = filtered.filter((expense) => {
        // Optimize search by checking most common fields first
        const supplierLabel = expense.supplier?.label?.toLowerCase() || '';
        if (supplierLabel.includes(searchLower)) return true;

        const categoryLabel = expense['expense-category']?.label?.toLowerCase() || '';
        if (categoryLabel.includes(searchLower)) return true;

        const description = expense.description?.toLowerCase() || '';
        if (description.includes(searchLower)) return true;

        // Only do the expensive JSON.stringify as a last resort
        return JSON.stringify({
          supplier: expense.supplier?.label,
          category: expense['expense-category']?.label,
          description: expense.description,
          amount: expense.amount
        }).toLowerCase().includes(searchLower);
      });
    }

    return filtered.sort((a, b) => dayjs(b.date).unix() - dayjs(a.date).unix());
  }, [expenses, dateRange, selectedCategory, keyword]);

  // Calculate summary statistics
  const summaryStats = useMemo(() => {
    if (!filteredData?.length) return {
      totalExpenses: 0,
      avgExpense: 0,
      maxExpense: 0,
      expenseCount: 0
    };

    const totalExpenses = filteredData.reduce((sum, item) => sum + (Number(item.amount) || 0), 0);
    const expenseCount = filteredData.length;
    const avgExpense = expenseCount > 0 ? totalExpenses / expenseCount : 0;
    const maxExpense = Math.max(...filteredData.map(item => Number(item.amount) || 0));

    return {
      totalExpenses,
      avgExpense,
      maxExpense,
      expenseCount
    };
  }, [filteredData]);

  // Prepare chart data for monthly expense trend
  const monthlyExpenseData = useMemo(() => {
    if (!filteredData?.length) return [];

    const monthlyData = {};

    filteredData.forEach(item => {
      const monthYear = dayjs(item.date).format('MMM YY');

      if (!monthlyData[monthYear]) {
        monthlyData[monthYear] = {
          month: monthYear,
          amount: 0,
          count: 0
        };
      }

      monthlyData[monthYear].amount += Number(item.amount) || 0;
      monthlyData[monthYear].count += 1;
    });

    // Convert to array and sort by date
    return Object.values(monthlyData)
      .sort((a, b) => {
        const aDate = dayjs(a.month, 'MMM YY');
        const bDate = dayjs(b.month, 'MMM YY');
        return aDate.unix() - bDate.unix();
      })
      .flatMap(item => [
        {
          month: item.month,
          value: item.amount,
          category: 'Amount'
        },
        {
          month: item.month,
          value: item.count,
          category: 'Count'
        }
      ]);
  }, [filteredData]);

  // Prepare chart data for expense category breakdown
  const categoryExpenseData = useMemo(() => {
    if (!filteredData?.length) return [];

    const categoryData = {};

    filteredData.forEach(item => {
      const categoryLabel = item['expense-category']?.label || 'Uncategorized';

      if (!categoryData[categoryLabel]) {
        categoryData[categoryLabel] = {
          category: categoryLabel,
          amount: 0,
          count: 0
        };
      }

      categoryData[categoryLabel].amount += Number(item.amount) || 0;
      categoryData[categoryLabel].count += 1;
    });

    // Convert to array and sort by amount
    return Object.values(categoryData)
      .sort((a, b) => b.amount - a.amount)
      .slice(0, 10); // Top 10 categories
  }, [filteredData]);

  const tableProps = {
    size: 'small',
    columns,
    dataSource: filteredData,
    pagination: {
      defaultPageSize: 20,
      showSizeChanger: true,
      pageSizeOptions: ['10', '20', '50', '100'],
      showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`
    },
    summary: (pageData) => {
      const total = pageData.reduce((sum, record) => sum + (Number(record.amount) || 0), 0);

      return (
        <Table.Summary fixed style={{ fontSize: 16 }}>
          <Table.Summary.Row>
            <Table.Summary.Cell colSpan={columns.length - 1}>
              <Typography.Text strong>Total</Typography.Text>
            </Table.Summary.Cell>
            <Table.Summary.Cell align="right">
              <Typography.Text strong>{total.toLocaleString(undefined, { maximumFractionDigits: 2 })}</Typography.Text>
            </Table.Summary.Cell>
          </Table.Summary.Row>
        </Table.Summary>
      );
    },
  };

  // Stats grid data
  const statsGridData = useMemo(() => [
    {
      title: 'Total Expenses',
      value: summaryStats.totalExpenses,
      icon: 'money',
      theme: 'danger'
    },
    {
      title: 'Average Expense',
      value: summaryStats.avgExpense,
      icon: 'chart',
      theme: 'primary'
    },
    {
      title: 'Highest Expense',
      value: summaryStats.maxExpense,
      icon: 'cash',
      theme: 'warning'
    },
    {
      title: 'Number of Expenses',
      value: summaryStats.expenseCount,
      icon: 'receipt',
      theme: 'info'
    }
  ], [summaryStats]);

  // Chart configurations
  const monthlyChartConfig = {
    data: monthlyExpenseData,
    isGroup: true,
    xField: 'month',
    yField: 'value',
    seriesField: 'category',
    color: ['#ff4d4f', '#1677ff'],
    tooltip: {
      formatter: (datum) => {
        return {
          name: datum.category,
          value: datum.value.toLocaleString(undefined, { maximumFractionDigits: 2 })
        };
      }
    }
  };

  const categoryPieConfig = {
    data: categoryExpenseData,
    angleField: 'amount',
    colorField: 'category',
    radius: 0.8,
    tooltip: {
      formatter: (datum) => {
        return {
          name: datum.category,
          value: datum.amount.toLocaleString(undefined, { maximumFractionDigits: 2 })
        };
      }
    }
  };

  return (
    <div>
      {/* Stats Grid at the top */}
      <div style={{ marginBottom: 16 }}>
        <StatsGrid data={statsGridData} />
      </div>

      {/* Date Range and Search Controls */}
      <Row justify="space-between" style={{ marginBottom: 16 }}>
        <Col span={16}>
          <Row gutter={[16, 16]}>
            <Col>
              <DatePicker.RangePicker
                value={dateRange}
                onChange={setDateRange}
                style={{ marginRight: 16 }}
                presets={[
                  {
                    label: 'Today',
                    value: [dayjs().startOf('day'), dayjs().endOf('day')],
                  },
                  {
                    label: 'Yesterday',
                    value: [
                      dayjs().subtract(1, 'day').startOf('day'),
                      dayjs().subtract(1, 'day').endOf('day'),
                    ],
                  },
                  {
                    label: 'This Week',
                    value: [dayjs().startOf('week'), dayjs().endOf('day')],
                  },
                  {
                    label: 'Last Week',
                    value: [
                      dayjs().subtract(1, 'week').startOf('week'),
                      dayjs().subtract(1, 'week').endOf('week'),
                    ],
                  },
                  {
                    label: 'This Month',
                    value: [dayjs().startOf('month'), dayjs().endOf('day')],
                  },
                  {
                    label: 'Last Month',
                    value: [
                      dayjs().subtract(1, 'month').startOf('month'),
                      dayjs().subtract(1, 'month').endOf('month'),
                    ],
                  },
                  {
                    label: 'This Year',
                    value: [dayjs().startOf('year'), dayjs().endOf('day')],
                  },
                  {
                    label: 'Last Year',
                    value: [
                      dayjs().subtract(1, 'year').startOf('year'),
                      dayjs().subtract(1, 'year').endOf('year'),
                    ],
                  },
                ]}
              />
            </Col>
            <Col>
              <Select
                allowClear
                style={{ width: 200, marginRight: 16 }}
                placeholder="Select Category"
                options={expenseCategories?.map(cat => ({
                  label: cat.name,
                  value: cat._id
                }))}
                onChange={setSelectedCategory}
              />
            </Col>
            <Col>
              <Input.Search
                placeholder="Search..."
                onChange={(e) => setKeyword(e.target.value)}
                style={{ width: 200 }}
                allowClear
              />
            </Col>
          </Row>
        </Col>
        <Col span={8} style={{ textAlign: "end" }}>
          <PrintComponents
            trigger={
              <Button
                icon={<PrinterOutlined />}
                type="primary"
              >
                Print
              </Button>
            }
          >
            <div className="print-container" style={{ margin: 20, background: 'white' }}>
              {organization && <DocumentHead company={organization} />}

              {/* Company Information Header */}
              <div style={{ marginBottom: 20 }}>
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
                  <div>
                    <Typography.Title level={3} style={{ margin: 0 }}>Expense Ledger</Typography.Title>
                    <Typography.Text type="secondary">Financial Expense Report</Typography.Text>
                  </div>
                  <div style={{ textAlign: 'right' }}>
                    <Typography.Text type="secondary" style={{ display: 'block' }}>
                      Generated: {dayjs().format('DD-MM-YYYY HH:mm')}
                    </Typography.Text>
                    <Typography.Text type="secondary" style={{ display: 'block' }}>
                      Prepared by: {(() => {
                        try {
                          const user = JSON.parse(localStorage.getItem('LOCAL_STORAGE_USER'));
                          return user ? `${user.first_name} ${user.last_name}` : 'System User';
                        } catch (e) {
                          return 'System User';
                        }
                      })()}
                    </Typography.Text>
                  </div>
                </div>
              </div>

              {/* Add a divider */}
              <div style={{ height: 2, background: '#f0f0f0', marginBottom: 20 }}></div>

              {/* Print Stats Summary */}
              <div style={{ marginBottom: 20 }}>
                <Row gutter={[16, 16]}>
                  <Col span={8}>
                    <Card>
                      <div style={{ textAlign: 'center' }}>
                        <Typography.Title level={4}>Total Expenses</Typography.Title>
                        <Typography.Title level={3} style={{ margin: 0 }}>
                          {summaryStats.totalExpenses.toLocaleString(undefined, { maximumFractionDigits: 2 })}
                        </Typography.Title>
                      </div>
                    </Card>
                  </Col>
                  <Col span={8}>
                    <Card>
                      <div style={{ textAlign: 'center' }}>
                        <Typography.Title level={4}>Average Expense</Typography.Title>
                        <Typography.Title level={3} style={{ margin: 0 }}>
                          {summaryStats.avgExpense.toLocaleString(undefined, { maximumFractionDigits: 2 })}
                        </Typography.Title>
                      </div>
                    </Card>
                  </Col>
                  <Col span={8}>
                    <Card>
                      <div style={{ textAlign: 'center' }}>
                        <Typography.Title level={4}>Number of Expenses</Typography.Title>
                        <Typography.Title level={3} style={{ margin: 0 }}>
                          {summaryStats.expenseCount}
                        </Typography.Title>
                      </div>
                    </Card>
                  </Col>
                </Row>
              </div>

              {/* Print Date Range and Filters */}
              <div style={{ marginBottom: 10 }}>
                <Typography.Text strong>
                  Period: {dateRange?.[0] ? dayjs(dateRange[0]).format('DD-MM-YYYY') : 'All'} to {dateRange?.[1] ? dayjs(dateRange[1]).format('DD-MM-YYYY') : 'All'}
                </Typography.Text>
                {selectedCategory && (
                  <Typography.Text strong style={{ marginLeft: 16 }}>
                    Category: {expenseCategories.find(c => c._id === selectedCategory)?.name || 'Unknown'}
                  </Typography.Text>
                )}
              </div>

              <Table
                {...tableProps}
                pagination={false}
                scroll={false}
              />
            </div>
          </PrintComponents>
        </Col>
      </Row>

      {/* Tabbed Interface */}
      <Tabs
        activeKey={activeTab}
        onChange={setActiveTab}
        type="card"
      >
        <TabPane
          tab={<span><UnorderedListOutlined /> Expense Ledger</span>}
          key="ledger"
        >
          <Table {...tableProps} />
        </TabPane>

        <TabPane
          tab={<span><BarChartOutlined /> Expense Analysis</span>}
          key="analysis"
        >
          <Row gutter={[16, 16]}>
            {/* Monthly Expense Trend Chart */}
            <Col span={24}>
              <Card title="Monthly Expense Trend">
                {monthlyExpenseData.length > 0 ? (
                  <div style={{ height: 400 }}>
                    <Column {...monthlyChartConfig} />
                  </div>
                ) : (
                  <div style={{ height: 400, display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
                    <p>No data available for the selected period</p>
                  </div>
                )}
              </Card>
            </Col>

            {/* Category Breakdown Chart */}
            <Col span={12}>
              <Card title="Expenses by Category">
                {categoryExpenseData.length > 0 ? (
                  <div style={{ height: 400 }}>
                    <Pie {...categoryPieConfig} />
                  </div>
                ) : (
                  <div style={{ height: 400, display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
                    <p>No data available for the selected period</p>
                  </div>
                )}
              </Card>
            </Col>

            {/* Top Expenses Table */}
            <Col span={12}>
              <Card title="Top Expenses by Amount">
                <Table
                  dataSource={filteredData
                    .sort((a, b) => b.amount - a.amount)
                    .slice(0, 50)
                  }
                  columns={columns}
                  pagination={{
                    defaultPageSize: 10,
                    showSizeChanger: true,
                    pageSizeOptions: ['5', '10', '20', '50'],
                    showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`
                  }}
                  size="small"
                />
              </Card>
            </Col>
          </Row>
        </TabPane>
      </Tabs>
    </div>
  );
};

export default Expense;