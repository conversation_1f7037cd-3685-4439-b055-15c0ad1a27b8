import dayjs from "dayjs";
import { message } from "antd";


 /**
  * Utility functions for CSV export in Abacus reports
 */

/**
 * Escapes CSV field values to handle commas, quotes, and newlines
 * @param {string|number} value - The value to escape
 * @returns {string} - The escaped value
 */
export const escapeCsvValue = (value) => {
  if (value === null || value === undefined) return '';

  const stringValue = String(value);

  // If the value contains comma, quote, or newline, wrap it in quotes and escape internal quotes
  if (stringValue.includes(',') || stringValue.includes('"') || stringValue.includes('\n')) {
    return `"${stringValue.replace(/"/g, '""')}"`;
  }

  return stringValue;
};

/**
 * Converts array of objects to CSV format
 * @param {Array} data - Array of data objects
 * @param {Array} columns - Array of column definitions with title and dataIndex
 * @param {string} filename - Name for the CSV file
 * @returns {void}
 */
export const exportToCSV = (data, columns, filename) => {
  try {
    if (!data || data.length === 0) {
      message.warning('No data available to export');
      return;
    }

    // Create CSV headers
    const headers = columns
      .filter(col => col.title && col.dataIndex) // Only include columns with title and dataIndex
      .map(col => escapeCsvValue(col.title));

    // Create CSV rows
    const rows = data.map(row => {
      return columns
        .filter(col => col.title && col.dataIndex) // Only include columns with title and dataIndex
        .map(col => {
          let value = row[col.dataIndex];

          // Handle special data types and formatting
          if (value === null || value === undefined) {
            return '';
          }

          // Handle date fields
          if (col.dataIndex === 'date' || col.dataIndex === 'invoice_date' || col.dataIndex === 'purchase_date') {
            return dayjs(value).isValid() ? dayjs(value).format('DD/MM/YYYY') : '';
          }

          // Handle object values with label or name properties
          if (typeof value === 'object' && value !== null) {
            if (value.label) return value.label;
            if (value.name) return value.name;
            if (value.value) return value.value;
            return JSON.stringify(value);
          }

          // Handle numeric values
          if (typeof value === 'number') {
            // Check if this is a currency/amount field
            if (col.title.toLowerCase().includes('price') ||
                col.title.toLowerCase().includes('amount') ||
                col.title.toLowerCase().includes('cost') ||
                col.title.toLowerCase().includes('total') ||
                col.title.toLowerCase().includes('value') ||
                col.title.toLowerCase().includes('profit')) {
              return value.toFixed(2);
            }
            return value.toString();
          }

          // Handle calculated fields (like current stock)
          if (col.dataIndex === 'currentStock' || col.dataIndex === 'stock') {
            const stock = (row.stoked || 0) - (row.sold || 0) + (row.returned || 0) - (row.returned_stock || 0) + (row.adjustments || 0);
            return stock.toString();
          }

          // Handle product names with measurements
          if (col.dataIndex === 'name' && row.measurements && row.units && row.units.toLowerCase() !== 'pc') {
            return `${value} - ${row.measurements} ${row.units}`;
          }

          // Handle selling price from prices array
          if (col.dataIndex === 'sell' && row._id) {
            // This would need access to prices array - simplified for now
            return (value || 0).toString();
          }

          return escapeCsvValue(value);
        });
    });

    // Combine headers and rows
    const csvContent = [headers, ...rows]
      .map(row => row.join(','))
      .join('\n');

    // Create and download the file
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `${filename}_${dayjs().format('YYYY-MM-DD')}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);

    message.success('CSV file exported successfully');
  } catch (error) {
    console.error('CSV export error:', error);
    message.error('Failed to export CSV file');
  }
};

/**
 * Prepares data for CSV export by flattening complex objects
 * @param {Array} data - Raw data array
 * @param {Array} columns - Column definitions
 * @returns {Array} - Processed data ready for CSV export
 */
export const prepareDataForCSV = (data, columns) => {
  return data.map(row => {
    const processedRow = {};

    columns.forEach(col => {
      let value = row[col.dataIndex];

      // Handle complex object values
      if (typeof value === 'object' && value !== null) {
        if (value.label) {
          processedRow[col.dataIndex] = value.label;
        } else if (value.name) {
          processedRow[col.dataIndex] = value.name;
        } else {
          processedRow[col.dataIndex] = JSON.stringify(value);
        }
      } else {
        processedRow[col.dataIndex] = value;
      }
    });

    return processedRow;
  });
};

/**
 * Creates a standardized CSV export button configuration
 * @param {Array} data - Data to export
 * @param {Array} columns - Column definitions
 * @param {string} reportName - Name of the report for filename
 * @returns {Object} - Button configuration object
 */
export const createCSVExportConfig = (data, columns, reportName) => {
  return {
    onClick: () => exportToCSV(data, columns, reportName),
    disabled: !data || data.length === 0,
    title: data && data.length > 0 ? 'Export to CSV' : 'No data to export'
  };
};
