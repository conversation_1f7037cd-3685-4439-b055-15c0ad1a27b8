import React, { useState, useMemo } from 'react';
import './print.css';
import DocumentHead from "../../../Universal/CustomViews/Components/DocumentHead";
import PrintComponents from "react-print-components";
import dayjs from "dayjs";
import moment from "moment";
import { PrinterOutlined, DownloadOutlined } from "@ant-design/icons";
import { Table, Button, Row, Col, Input, Typography, DatePicker, Select, Space } from "antd";
import { exportToCSV } from "./utils/csvExport";
import { formatNumber } from "../../../../Utils/functions";
import { useReportsData } from "./ReportsContext";


const { RangePicker } = DatePicker;

const Sales = () => {
  const { sales: invoices, products, categories, loading, company } = useReportsData();  // Add products and categories
  const [keyword, setKeyword] = useState(null);
  const [selectedCategory, setSelectedCategory] = useState(null);
  const [dateRange, setDateRange] = useState([
    dayjs().startOf('month'),
    dayjs().endOf('month')
  ]);


  const processedData = useMemo(() => {
    if (!invoices?.length) return [];

    // Create a map of products by ID for faster lookup
    const productsMap = new Map();
    products?.forEach(product => {
      productsMap.set(product._id, product);
    });

    // Create a map of categories by ID for faster lookup
    const categoriesMap = new Map();
    categories?.forEach(category => {
      categoriesMap.set(category._id, category);
    });

    const itemsList = [];
    invoices
      .filter(invoice => {
        // Ensure dateRange and its elements are valid dayjs objects before calling $d
        const startDate = dateRange && dateRange[0] ? dateRange[0] : dayjs('1970-01-01'); // Or some other default
        const endDate = dateRange && dateRange[1] ? dateRange[1] : dayjs(); // Or some other default

        return moment(invoice.date).isSameOrAfter(moment(startDate.$d || startDate.toDate()), "day") &&
          moment(invoice.date).isSameOrBefore(moment(endDate.$d || endDate.toDate()), "day");
      })
      .forEach(invoice => {
        invoice.items?.forEach((item, index) => {
          // Look up the full product information
          const productId = item.product?.value;
          const product = productId ? productsMap.get(productId) : null;

          // Get category information if available
          const categoryId = product?.category?.value;
          const category = categoryId ? categoriesMap.get(categoryId) : null;

          itemsList.push({
            key: `${invoice._id}-${index}`, // Unique key for each item row
            invoice_date: invoice.date,
            invoice_no: invoice.key || invoice._id,
            customer: invoice.customer?.label,
            item_name: item.product.label || 'N/A', // Assuming item.name, provide fallback
            item_sku: product?.sku || 'N/A', // Get SKU from the full product object
            item_category: category?.name || product?.category?.label || 'N/A', // Get category from the full category object
            item_quantity: item.quantity,
            item_price: item.price,
            item_total_amount: (item.quantity || 0) * (item.price || 0),
          });
        });
      });
    return itemsList;
  }, [invoices, products, categories, dateRange]);

  // console.log("Processed Item List:", processedData); // For debugging if needed

  const columns = [
    {
      title: 'No',
      width: 50,
      render: (_, __, index) => index + 1,
    },
    {
      title: 'Date',
      dataIndex: 'invoice_date',
      render: date => dayjs(date).format('DD/MM/YYYY'),
    },
    {
      title: 'Invoice No',
      dataIndex: 'invoice_no',
    },
    {
      title: 'Customer',
      dataIndex: 'customer',
    },
    {
      title: 'Item Name',
      dataIndex: 'item_name',
    },
    {
      title: 'SKU',
      dataIndex: 'item_sku',
    },
    {
      title: 'Category',
      dataIndex: 'item_category',
    },
    {
      title: 'Qty',
      dataIndex: 'item_quantity',
      align: 'right',
    },
    {
      title: 'Price',
      dataIndex: 'item_price',
      align: 'right',
      render: value => formatNumber(value),
    },
    {
      title: 'Amount',
      dataIndex: 'item_total_amount',
      align: 'right',
      render: value => formatNumber(value),
    }
  ];

  const filteredData = useMemo(() => {
    if (!processedData?.length) return [];

    let filtered = processedData;

    // Apply keyword search filter
    if (keyword) {
      const searchLower = keyword.toLowerCase();
      filtered = filtered.filter(record =>
        JSON.stringify(record).toLowerCase().includes(searchLower)
      );
    }

    // Apply category filter
    if (selectedCategory) {
      filtered = filtered.filter(record =>
        record.item_category === selectedCategory
      );
    }

    // Sort by invoice date, then by item name within the same invoice (optional, but good for consistency)
    return filtered.sort((a, b) => {
      const dateComparison = dayjs(b.invoice_date).unix() - dayjs(a.invoice_date).unix();
      if (dateComparison !== 0) {
        return dateComparison;
      }
      // If dates are the same, sort by invoice number, then item name
      if (a.invoice_no < b.invoice_no) return -1;
      if (a.invoice_no > b.invoice_no) return 1;
      if (a.item_name < b.item_name) return -1;
      if (a.item_name > b.item_name) return 1;
      return 0;
    });
  }, [processedData, keyword, selectedCategory]);

  // Prepare category options for the dropdown
  const categoryOptions = useMemo(() => {
    // Get unique categories from the processed data
    const uniqueCategories = new Set();
    processedData.forEach(item => {
      if (item.item_category && item.item_category !== 'N/A') {
        uniqueCategories.add(item.item_category);
      }
    });

    // Convert to array and sort alphabetically
    return Array.from(uniqueCategories).sort().map(category => ({
      label: category,
      value: category
    }));
  }, [processedData]);

  const tableProps = {
    size: 'small',
    columns,
    dataSource: filteredData,
    loading,
    pagination: {
      defaultPageSize: 20,
      showSizeChanger: true,
      pageSizeOptions: ['10', '20', '50', '100'],
      showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`
    },
    scroll: { y: 'calc(100vh - 350px)' },
    summary: (pageData) => {
      const totalItemAmount = pageData.reduce((sum, row) => sum + (row.item_total_amount || 0), 0);

      return (
        <Table.Summary fixed>
          <Table.Summary.Row>
            {/* Columns: No, Date, Inv No, Cust, Item, SKU, Category, Qty, Price, Amount (10 cols) */}
            <Table.Summary.Cell colSpan={9}>Total</Table.Summary.Cell>
            <Table.Summary.Cell align="right">{formatNumber(totalItemAmount)}</Table.Summary.Cell>
          </Table.Summary.Row>
        </Table.Summary>
      );
    }
  };

  return (
    <div>
      <Table
        {...tableProps}
        title={() => (
          <Row gutter={[16, 16]}>
            <Col xs={24} sm={8}>
              <Input.Search
                placeholder="Search..."
                onChange={(e) => setKeyword(e.target.value)}
                style={{ width: '100%' }}
                allowClear
              />
            </Col>
            <Col xs={24} sm={8}>
              <Row gutter={[8, 8]}>
                <Col span={12}>
                  <Select
                    placeholder="Filter by Category"
                    style={{ width: '100%' }}
                    allowClear
                    options={categoryOptions}
                    onChange={setSelectedCategory}
                    value={selectedCategory}
                  />
                </Col>
                <Col span={12}>
                  <RangePicker
                    value={dateRange}
                    onChange={setDateRange}
                    style={{ width: '100%' }}
                  />
                </Col>
              </Row>
            </Col>
            <Col xs={24} sm={8} style={{ textAlign: 'right' }}>
              <Space>
                <Button
                  icon={<DownloadOutlined />}
                  onClick={() => exportToCSV(filteredData, columns, 'Sales_Report')}
                  disabled={!filteredData || filteredData.length === 0}
                >
                  Export CSV
                </Button>
                <PrintComponents
                  trigger={
                    <Button
                      icon={<PrinterOutlined />}
                      type="primary"
                    >
                      Print
                    </Button>
                  }
                >
                <div style={{ margin: 20 }}>
                  {company && <DocumentHead company={company} />}
                  <Typography.Title level={3}>Sales Report</Typography.Title>
                  {dateRange && dateRange[0] &&
                    <Typography.Text>
                      Period: {dateRange && dateRange[0].format('DD/MM/YYYY')} - {dateRange[1].format('DD/MM/YYYY')}
                    </Typography.Text>
                  }
                  <Table
                    {...tableProps}
                    pagination={false}
                    scroll={false}
                  />
                </div>
              </PrintComponents>
              </Space>
            </Col>
          </Row>
        )}
      />
    </div>
  );
};

export default Sales;