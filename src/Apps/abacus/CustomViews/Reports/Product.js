import './print.css';
import DocumentHead from "../../../../Components/Reports/DocumentHead";
import PrintComponents from "react-print-components";
import React, { useState } from "react";
import moment from "moment";
import { Button, Col, DatePicker, Row, Select, Table, Typography, Space } from "antd";
import { PrinterOutlined, DownloadOutlined } from "@ant-design/icons";
import { exportToCSV } from "./utils/csvExport";
import { formatNumber } from "../../../../Utils/functions";


const Product = (props) => {
  const [dateRange, setDateRange] = useState(null);
  const [category, setCategory] = useState(null);
  const [product, setProduct] = useState(null);

  const { sales, categories, products, company } = props;

  const columns = [
    {
      dataIndex: "date",
      title: "Date",
      render: (text) => moment(text).format("DD MMM YY"),
    },
    {
      dataIndex: "product",
      title: "Product",
      render: (text, record) => `${text.name}${text.sku ? ` - ${text.sku}` : ""} - ${text.measurements} ${text.units}`,
      //   render: (text) => text && text.label && text.label,
    },
    {
      dataIndex: "quantity",
      title: "Qty",
      //   render: (text) => text && text.label && text.label,
    },
    {
      dataIndex: "total",
      title: "Price",
      render: (text, record) => formatNumber(record.price * record.quantity),
    },
  ];

  const dataSource = () => {
    const salesData = dateRange
      ? sales.filter(
        (r) =>
          moment(r.date).isSameOrAfter(moment(dateRange[0].$d), "day") &&
          moment(r.date).isSameOrBefore(moment(dateRange[1].$d), "day")
      )
      : sales.reverse().slice(0, 10).reverse();

    let newData = [];

    salesData.forEach((sale) => {
      sale.items.forEach((item) => {
        newData.push({
          ...item,
          date: sale.date,
          id: sale._id,
          price: Number(item.price),
          quantity: Number(item.quantity),
          sold: Number(item.price) * Number(item.quantity),
          product: products.find((p) => p._id === item.product.value),
        });
      });
    });

    if (category && product && product === "all") {
      newData = newData.filter((d) => d.product.category && d.product.category.value === category);
    } else if (
      category &&
      product &&
      product !== "all" &&
      product !== "select"
    ) {
      newData = newData.filter(
        (d) =>
          d.product._id === product && d.product.category && d.product.category.value === category
      );
    } else {
      newData = newData;
    }

    return newData;
  };
  const tProps = {
    size: "small",
    columns: columns,
    pagination: {
      defaultPageSize: 20,
      showSizeChanger: true,
      pageSizeOptions: ['10', '20', '50', '100'],
      showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`
    },
    dataSource: dataSource(),
    summary: (pageData) => {
      console.log("pageData", pageData);

      return (
        <Table.Summary fixed style={{ fontSize: 20 }}>
          <Table.Summary.Row>
            <Table.Summary.Cell colSpan={columns.length - 2}>
              Total
            </Table.Summary.Cell>
            <Table.Summary.Cell>
              {pageData.reduce((a, b) => a + Number(b.quantity), 0)}
            </Table.Summary.Cell>
            <Table.Summary.Cell>
              {formatNumber(
                pageData.reduce(
                  (a, b) => a + Number(b.price) * Number(b.quantity),
                  0
                )
              )}
            </Table.Summary.Cell>
          </Table.Summary.Row>
        </Table.Summary>
      );
    },
  };

  const handleCategoryChange = (value) => {
    setCategory(value);
  };
  const handleProductChange = (value) => {
    setProduct(value);
  };

  return (
    <div>
      <Table
        {...tProps}
        scroll={{ y: "calc(100vh - 300px)" }}
        title={() => (
          <Row justify="space-between">
            <Col span={12}>
              <DatePicker.RangePicker
                onChange={(dates) => {
                  console.log("date picker dates", dates);
                  setDateRange(dates);
                }}
              />

              <Select
                defaultValue="select"
                style={{
                  width: 180,
                  marginLeft: 5,
                }}
                onChange={handleCategoryChange}
                showSearch
                filterOption={(input, option) =>
                  (option?.label ?? "")
                    .toLowerCase()
                    .includes(input.toLowerCase())
                }
                filterSort={(optionA, optionB) =>
                  (optionA?.label ?? "")
                    .toLowerCase()
                    .localeCompare((optionB?.label ?? "").toLowerCase())
                }
                options={[
                  {
                    value: "select",
                    label: "Select a Category",
                  },
                  ...categories.map((c) => ({
                    value: c._id,
                    label: c.name,
                  })),
                ]}
              />

              <Select
                defaultValue="select"
                style={{
                  width: 180,
                  marginLeft: 5,
                }}
                onChange={handleProductChange}
                showSearch
                filterOption={(input, option) =>
                  (option?.label ?? "")
                    .toLowerCase()
                    .includes(input.toLowerCase())
                }
                filterSort={(optionA, optionB) =>
                  (optionA?.label ?? "")
                    .toLowerCase()
                    .localeCompare((optionB?.label ?? "").toLowerCase())
                }
                options={[
                  {
                    value: "select",
                    label: "Select a Product",
                  },
                  {
                    value: "all",
                    label: "All",
                  },
                  ...(category ? products
                    .filter((p) => p.category && p.category.value === category) : products)
                    .map((c) => ({
                      value: c._id,
                      label: `${c.name} ${c.measurements}${c.units}`,
                    })),
                ]}
              />
            </Col>
            <Col span={12} style={{ textAlign: "end" }}>
              <Space>
                <Button
                  icon={<DownloadOutlined />}
                  onClick={() => exportToCSV(data, columns, 'Product_Report')}
                  disabled={!data || data.length === 0}
                  style={{ marginBottom: 16 }}
                >
                  Export CSV
                </Button>
                <PrintComponents
                  trigger={
                    <Button
                      icon={<PrinterOutlined />}
                      type="primary"
                      style={{ marginBottom: 16 }}
                    >
                      Print
                    </Button>
                  }
                >
                <div style={{ margin: 20 }}>
                  {company && <DocumentHead company={company} />}
                  <Typography.Title level={3}>Product Report</Typography.Title>
                  <Table
                    {...tProps}
                    pagination={false}
                    scroll={false}
                  />
                </div>
              </PrintComponents>
              </Space>
            </Col>
          </Row>
        )}
      />
    </div>
  );
};

export default Product;