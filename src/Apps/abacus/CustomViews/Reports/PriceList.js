import React, { useEffect, useState, useMemo } from "react";
import "./print.css";
import "jspdf-autotable";
import DocumentHead from "../../../Universal/CustomViews/Components/DocumentHead";
import PrintComponents from "react-print-components";
import jsPDF from "jspdf";
import { Button, Col, Input, Row, Table, Typography, Card, Select, Space, message, } from "antd";
import { PrinterOutlined, DownloadOutlined, FilterOutlined, FilePdfOutlined, } from "@ant-design/icons";
import { buffProducts } from "../../modulesProperties/utils";
import { exportToCSV } from "./utils/csvExport";
import { fetchOrganizationWithLogo } from "../../../../Utils/organizationUtils";
import { useReportsData } from "./ReportsContext";


const { Search } = Input;
const { Option } = Select;

const PriceList = ({ pouchDatabase, databasePrefix }) => {
  // Price List Report Component
  const {
    products,
    loading,
    prices,
    categories = [],
    company,
  } = useReportsData();
  const [data, setData] = useState([]);
  const [keyword, setKeyword] = useState(null);
  const [selectedCategory, setSelectedCategory] = useState(null);
  const [selectedProducts, setSelectedProducts] = useState([]);
  const [organization, setOrganization] = useState(null);

  useEffect(() => {
    if (products?.length) {
      buffProducts(products).then((res) => setData(res));
    }
  }, [products]);

  // Set up organization data for consistent document headers
  useEffect(() => {
    // Default organization structure
    const defaultOrg = {
      name: "Your Company Name",
      alternative_name: "",
      address: "Your Company Address",
      phone: "Your Phone Number",
      email: "<EMAIL>",
      website: "www.yourwebsite.com",
    };

    // If pouchDatabase is not available, use default organization
    if (!pouchDatabase) {
      console.log("PouchDatabase not available, using default organization");
      setOrganization(defaultOrg);
      return;
    }

    // Attempt to fetch organization data
    if (company) {
      setOrganization(company);
    } else if (pouchDatabase) {
      fetchOrganizationWithLogo(pouchDatabase, "organizations", databasePrefix)
        .then((organizationWithLogo) => {
          if (organizationWithLogo) {
            // Convert logo property to orgLogo for compatibility with DocumentHead
            setOrganization({
              ...organizationWithLogo,
              orgLogo: organizationWithLogo.logo
            });
          } else {
            // No organization data found, use default
            console.log("No organization data found, using default");
            setOrganization(defaultOrg);
          }
        })
        .catch((err) => {
          console.error("Error fetching organization data:", err);
          // On error, use default organization
          setOrganization(defaultOrg);
        });
    }
  }, [company, pouchDatabase, databasePrefix]);

  const filteredData = useMemo(() => {
    let filtered = data;

    // Filter by selected products (if any are selected, show only those)
    if (selectedProducts.length > 0) {
      filtered = filtered.filter((item) => selectedProducts.includes(item._id));
    }

    // Filter by keyword
    if (keyword) {
      filtered = filtered.filter(
        (item) =>
          item.name?.toLowerCase().includes(keyword.toLowerCase()) ||
          item.description?.toLowerCase().includes(keyword.toLowerCase()) ||
          item.sku?.toLowerCase().includes(keyword.toLowerCase())
      );
    }

    // Filter by category
    if (selectedCategory) {
      filtered = filtered.filter(
        (item) => item.category?.value === selectedCategory
      );
    }

    return filtered.sort((a, b) => (a.name || "").localeCompare(b.name || ""));
  }, [data, keyword, selectedCategory, selectedProducts]);

  const columns = useMemo(
    () => [
      {
        title: "No",
        dataIndex: "no",
        width: 60,
        render: (_, __, index) => index + 1,
      },
      {
        valueType: "text",
        dataIndex: "name",
        title: "Name",
        width: 200,
        render: (text, record) => {
          if (!record.units || (typeof record.units === 'string' && record.units.toLowerCase() === "pc")) {
            return text;
          } else {
            return `${record.name} - ${record.measurements || ''} ${record.units || ''}`;
          }
        },
      },
      {
        dataIndex: "description",
        title: "Description",
        width: 300,
        ellipsis: false,
        render: (text) => (
          <div style={{ whiteSpace: 'normal', wordWrap: 'break-word' }}>
            {text && text.split(",").join(", ")}
          </div>
        ),
      },
      {
        valueType: "text",
        dataIndex: "sku",
        title: "SKU/Item No",
        width: 150,
      },
      {
        title: "Selling Price",
        dataIndex: "sell",
        width: 120,
        align: 'right',
        render: (_, record) => {
          const latestPrice =
            prices?.find((p) => p.product?.value === record._id)?.price ||
            record.sell ||
            0;
          return latestPrice.toLocaleString();
        },
      },
    ],
    [prices],
  );

  const handleExportCSV = () => {
    const csvData = filteredData.map((item, index) => ({
      'No': index + 1,
      'Name': item.units && item.units.toLowerCase() !== "pc"
        ? `${item.name} - ${item.measurements || ''} ${item.units || ''}`
        : item.name,
      'Description': item.description ? item.description.split(",").join(", ") : '',
      'SKU/Item No': item.sku || '',
      'Selling Price': prices?.find((p) => p.product?.value === item._id)?.price || item.sell || 0,
    }));

    const fileName = selectedProducts.length > 0
      ? `custom-price-list-${new Date().toISOString().split('T')[0]}`
      : `price-list-${new Date().toISOString().split('T')[0]}`;

    exportToCSV(csvData, fileName);
  };

  const handleProductSelection = (values) => {
    setSelectedProducts(values);
  };

  const handleSelectAll = () => {
    const allProductIds = data.map(item => item._id);
    setSelectedProducts(allProductIds);
  };

  const handleClearSelection = () => {
    setSelectedProducts([]);
  };

  // Prepare product options for the select dropdown
  const productOptions = useMemo(() => {
    return data.map(item => ({
      value: item._id,
      label: `${item.name}${item.sku ? ` (${item.sku})` : ''}`,
      key: item._id
    })).sort((a, b) => a.label.localeCompare(b.label));
  }, [data]);

  // Custom PDF generation function using jsPDF autoTable
  const generatePDF = async () => {
    try {
      message.loading({ content: "Generating PDF...", key: "pdf-generation" });

      const doc = new jsPDF();

      // Add company header if available
      if (organization) {
        doc.setFontSize(16);
        doc.setFont(undefined, 'bold');
        doc.text(organization.name || 'Company Name', 20, 20);

        if (organization.alternative_name) {
          doc.setFontSize(12);
          doc.setFont(undefined, 'normal');
          doc.text(organization.alternative_name, 20, 30);
        }

        if (organization.address) {
          doc.setFontSize(10);
          doc.text(organization.address, 20, 40);
        }

        if (organization.phone) {
          doc.text(`Phone: ${organization.phone}`, 20, 50);
        }

        if (organization.email) {
          doc.text(`Email: ${organization.email}`, 20, 60);
        }
      }

      // Add document title
      doc.setFontSize(18);
      doc.setFont(undefined, 'bold');
      const title = selectedProducts.length > 0 ? 'Custom Price List' : 'Price List';
      doc.text(title, 20, organization ? 80 : 20);

      // Add generation info
      doc.setFontSize(10);
      doc.setFont(undefined, 'normal');
      const generatedText = `Generated: ${new Date().toLocaleDateString()}`;
      doc.text(generatedText, 20, organization ? 90 : 30);

      // Get user info
      let preparedBy = "System User";
      try {
        const user = JSON.parse(localStorage.getItem("LOCAL_STORAGE_USER"));
        if (user) {
          preparedBy = `${user.first_name} ${user.last_name}`;
        }
      } catch (e) {
        // Use default
      }
      doc.text(`Prepared by: ${preparedBy}`, 20, organization ? 100 : 40);

      if (selectedProducts.length > 0) {
        doc.text(`Selected Products: ${selectedProducts.length}`, 20, organization ? 110 : 50);
      }

      // Prepare table data
      const tableHeaders = ['No', 'Name', 'Description', 'SKU/Item No', 'Selling Price'];
      const tableData = filteredData.map((item, index) => {
        const name = !item.units || (typeof item.units === 'string' && item.units.toLowerCase() === "pc")
          ? item.name
          : `${item.name} - ${item.measurements || ''} ${item.units || ''}`;

        const description = item.description ? item.description.split(",").join(", ") : '';
        const sku = item.sku || '';
        const price = (prices?.find((p) => p.product?.value === item._id)?.price || item.sell || 0).toLocaleString();

        return [
          (index + 1).toString(),
          name,
          description,
          sku,
          price
        ];
      });

      // Add table using autoTable
      doc.autoTable({
        head: [tableHeaders],
        body: tableData,
        startY: organization ? 120 : 60,
        styles: {
          fontSize: 9,
          cellPadding: 3,
        },
        headStyles: {
          fillColor: [245, 245, 245],
          textColor: [0, 0, 0],
          fontStyle: 'bold',
        },
        columnStyles: {
          0: { cellWidth: 15, halign: 'center' }, // No
          1: { cellWidth: 45 }, // Name
          2: { cellWidth: 60 }, // Description
          3: { cellWidth: 30 }, // SKU
          4: { cellWidth: 30, halign: 'right' }, // Price
        },
        margin: { left: 20, right: 20 },
      });

      // Add footer
      const finalY = doc.lastAutoTable.finalY + 10;
      doc.setFontSize(10);
      doc.setFont(undefined, 'bold');
      doc.text(`Total Products: ${filteredData.length}`, 20, finalY);

      if (selectedProducts.length > 0) {
        doc.text(`Custom Selection: ${selectedProducts.length} products`, 120, finalY);
      }

      // Save the PDF
      const filename = selectedProducts.length > 0
        ? `Custom_Price_List_${new Date().toISOString().split('T')[0]}.pdf`
        : `Price_List_${new Date().toISOString().split('T')[0]}.pdf`;

      doc.save(filename);
      message.success({ content: "PDF downloaded successfully!", key: "pdf-generation" });
    } catch (error) {
      console.error("Error generating PDF:", error);
      message.error({ content: "Failed to generate PDF. Please try again.", key: "pdf-generation" });
    }
  };

  const PrintButton = () => (
    <PrintComponents
      trigger={
        <Button type="primary" icon={<PrinterOutlined />}>
          Print
        </Button>
      }
    >
      <div
        className="print-container"
        style={{ margin: 20, background: "white" }}
      >
        {organization && <DocumentHead company={organization} />}

        {/* Company Information Header */}
        <div style={{ marginBottom: 20 }}>
          <div
            style={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "flex-start",
            }}
          >
            <div>
              <Typography.Title level={3} style={{ margin: 0 }}>
                {selectedProducts.length > 0 ? 'Custom Price List' : 'Price List'}
              </Typography.Title>
              <Typography.Text type="secondary">
                Product Pricing Report
              </Typography.Text>
            </div>
            <div style={{ textAlign: "right" }}>
              <Typography.Text
                type="secondary"
                style={{ display: "block" }}
              >
                Generated: {new Date().toLocaleDateString()}
              </Typography.Text>
              <Typography.Text
                type="secondary"
                style={{ display: "block" }}
              >
                Prepared by:{" "}
                {(() => {
                  try {
                    const user = JSON.parse(
                      localStorage.getItem("LOCAL_STORAGE_USER"),
                    );
                    return user
                      ? `${user.first_name} ${user.last_name}`
                      : "System User";
                  } catch (e) {
                    return "System User";
                  }
                })()}
              </Typography.Text>
              {selectedProducts.length > 0 && (
                <Typography.Text
                  type="secondary"
                  style={{ display: "block" }}
                >
                  Selected Products: {selectedProducts.length}
                </Typography.Text>
              )}
            </div>
          </div>
        </div>

        <Table
          columns={columns}
          dataSource={filteredData}
          pagination={false}
          size="small"
          bordered
        />
      </div>
    </PrintComponents>
  );



  const tableProps = useMemo(
    () => ({
      size: "small",
      columns,
      pagination: {
        defaultPageSize: 50,
        showSizeChanger: true,
        pageSizeOptions: ['25', '50', '100', '200'],
        showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`
      },
      dataSource: filteredData,
      loading,
      scroll: { y: "calc(100vh - 350px)" },
      bordered: true,
    }),
    [columns, filteredData, loading],
  );

  return (
    <div>
      <Card>
        <Row gutter={16} style={{ marginBottom: 16 }}>
          <Col span={6}>
            <Search
              placeholder="Search by name, description, or SKU"
              allowClear
              onSearch={(value) => setKeyword(value || null)}
              onChange={(e) => !e.target.value && setKeyword(null)}
            />
          </Col>
          <Col span={5}>
            <Select
              placeholder="Filter by category"
              allowClear
              style={{ width: "100%" }}
              onChange={(value) => setSelectedCategory(value || null)}
            >
              {categories.map((category) => (
                <Option key={category.value} value={category.value}>
                  {category.label}
                </Option>
              ))}
            </Select>
          </Col>
          <Col span={8}>
            <Select
              mode="multiple"
              placeholder="Select specific products (optional)"
              allowClear
              style={{ width: "100%" }}
              value={selectedProducts}
              onChange={handleProductSelection}
              options={productOptions}
              maxTagCount="responsive"
              showSearch
              filterOption={(input, option) =>
                option?.label?.toLowerCase().includes(input.toLowerCase())
              }
              dropdownRender={(menu) => (
                <div>
                  <div style={{ padding: '8px', borderBottom: '1px solid #f0f0f0' }}>
                    <Space>
                      <Button size="small" type="link" onClick={handleSelectAll}>
                        Select All
                      </Button>
                      <Button size="small" type="link" onClick={handleClearSelection}>
                        Clear All
                      </Button>
                    </Space>
                  </div>
                  {menu}
                </div>
              )}
            />
          </Col>
          <Col span={5} style={{ textAlign: "right" }}>
            <Space>
              <Button
                icon={<DownloadOutlined />}
                onClick={handleExportCSV}
                disabled={!filteredData.length}
              >
                Export CSV
              </Button>
              <Button
                icon={<FilePdfOutlined />}
                disabled={!filteredData.length}
                type="default"
                onClick={generatePDF}
              >
                Download PDF
              </Button>
              <PrintButton />
            </Space>
          </Col>
        </Row>

        {selectedProducts.length > 0 && (
          <Row style={{ marginBottom: 16 }}>
            <Col span={24}>
              <div style={{
                padding: '8px 12px',
                backgroundColor: '#f6ffed',
                border: '1px solid #b7eb8f',
                borderRadius: '6px',
                fontSize: '14px'
              }}>
                <Space>
                  <span style={{ color: '#52c41a', fontWeight: 'medium' }}>
                    Custom Price List: {selectedProducts.length} products selected
                  </span>
                  <Button
                    size="small"
                    type="link"
                    onClick={handleClearSelection}
                    style={{ padding: 0, height: 'auto' }}
                  >
                    Clear Selection
                  </Button>
                </Space>
              </div>
            </Col>
          </Row>
        )}

        <Table
          {...tableProps}
          title={() => (
            <Row justify="space-between" align="middle">
              <Col>
                <Typography.Title level={4} style={{ margin: 0 }}>
                  {selectedProducts.length > 0 ? 'Custom Price List' : 'Price List'}
                </Typography.Title>
                <Typography.Text type="secondary">
                  {filteredData.length} products
                  {selectedProducts.length > 0 && ` (${selectedProducts.length} selected)`}
                </Typography.Text>
              </Col>
            </Row>
          )}
        />
      </Card>
    </div>
  );
};

export default PriceList;