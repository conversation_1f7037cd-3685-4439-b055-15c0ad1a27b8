import DocumentHead from "../../../Universal/CustomViews/Components/DocumentHead";
import PrintComponents from "react-print-components";
import React, { useEffect, useState } from "react";
import moment from "moment";
import { <PERSON><PERSON>, Col, DatePicker, Row, Table, Typography } from "antd";
import { PrinterOutlined } from "@ant-design/icons";
import { buffProducts } from "../../modulesProperties/utils";
import { formatNumber, numberFormat } from "../../../../Utils/functions";


const Statement = (props) => {
  const { products, company, pouchDatabase, databasePrefix } = props;
  const [data, setData] = useState(null);
  const [dateRange, setDateRange] = useState(null);
  const [openingBalance, setOpeningBalance] = useState(0);
  const [filteredData, setFilteredData] = useState(null);

  useEffect(() => {
    const invoicesDB = pouchDatabase("invoices", databasePrefix);
    const returnedDB = pouchDatabase("returned", databasePrefix);
    const receiptsDB = pouchDatabase("receipts", databasePrefix);

    Promise.all([
      invoicesDB.getAllData({ include_docs: true }),
      returnedDB.getAllData({ include_docs: true }),
      receiptsDB.getAllData({ include_docs: true }),
    ]).then((res) => {
      const [allInvoices, allReturned, allReceipts] = res;
      
      const statementData = [];

      allInvoices
        .filter((invoice) => invoice.customer.value === props.filterID.id)
        .forEach((invoice) => {
          invoice.items.forEach((item) => {
            statementData.push({
              ...item,
              date: invoice.date,
              type: "invoice",
              code: "INV-" + invoice._id,
              amount: item.price * item.quantity,
              description: item.product.label,
            });
          });

          !invoice.not_paid &&
            statementData.push({
              date: invoice.date,
              type: "receipt",
              code: "REC-SYS GEN",
              amount:
                invoice.items.reduce((a, b) => a + b.price * b.quantity, 0) *
                -1,
              description: "Payment for " + "INV-" + invoice._id,
            });

          allReceipts
            .filter((receipt) => receipt.invoice.value === invoice._id)
            .forEach((receipt) => {
              statementData.push({
                ...receipt,
                date: receipt.date,
                type: "receipt",
                code: "REC-" + receipt._id,
                amount: receipt.amount * -1,
                description: `Receipt for ${invoice.customer.label} (INV${invoice._id})`,
              });
            });
        });

      allReturned.forEach((returned) => {
        returned.items.forEach((item) => {
          statementData.push({
            ...item,
            date: returned.date,
            type: "returned",
            code: "RET-" + returned._id,
            amount: item.unit_price * item.quantity * -1,
            description: item.product.label,
          });
        });
      });

      setData(statementData);
      setFilteredData(statementData);
    });
  }, []);

  useEffect(() => {
    if (!data || !dateRange) {
      setFilteredData(data);
      setOpeningBalance(0);
      return;
    }

    const [startDate, endDate] = dateRange;

    // Calculate opening balance from transactions before start date
    const openingBal = data
      .filter((item) =>
        moment(item.date).isBefore(moment(startDate.$d).startOf('day'), 'day')
      )
      .reduce((acc, curr) => acc + (curr.amount || 0), 0);
    setOpeningBalance(openingBal);

    // Filter transactions within date range
    const filtered = data.filter(
      (item) =>
        moment(item.date).isSameOrAfter(moment(startDate.$d), 'day') &&
        moment(item.date).isSameOrBefore(moment(endDate.$d), 'day')
    );
    setFilteredData(filtered);
  }, [data, dateRange]);

  const columns = [
    {
      valueType: "date",
      dataIndex: "date",
      title: "Date",
      hideInForm: true,
    },
    {
      valueType: "text",
      dataIndex: "code",
      title: "Document ID",
      hideInForm: true,
    },
    {
      valueType: "text",
      dataIndex: "type",
      title: "Type",
      render: (v) => v.charAt(0).toUpperCase() + v.slice(1),
    },
    {
      valueType: "text",
      dataIndex: "description",
      title: "Description",
    },
    {
      valueType: "money",
      dataIndex: "amount",
      title: "Invoice",
      render: (v) => v > 0 && formatNumber(v ? v : 0),
    },
    {
      valueType: "money",
      dataIndex: "amount",
      title: "Payment",
      render: (v) => v < 0 && formatNumber(v ? v : 0),
    },
    {
      valueType: "money",
      dataIndex: "balance",
      title: "Balance",
      render: (v) => formatNumber(v),
    },
  ];


  let runningBalance;

  const tProps = {
    size: "small",
    columns: columns,
    pagination: false,
    dataSource:
      filteredData &&
      [
        // Add opening balance as first row when date range is selected
        ...(dateRange ? [{
          date: moment(dateRange[0].$d).format('YYYY-MM-DD'),
          code: 'OPENING',
          type: 'balance',
          description: 'Opening Balance',
          amount: openingBalance,
          balance: openingBalance
        }] : []),
        // Map the rest of the transactions with running balance
        ...filteredData
      ]
        .sort((a, b) => moment(a.date).valueOf() - moment(b.date).valueOf()) // Sort by date first
        .map((transaction, index, array) => {
          // Calculate running balance

          runningBalance = array.slice(0, index + 1).reduce((acc, curr) => acc + curr.amount, 0);
          // if (dateRange) {
          //   // With date range: include opening balance
          // } else {
          //   // Without date range: simple running sum
          //   runningBalance = index === 0
          //     ? transaction.amount
          //     : array.slice(0, index + 1).reduce((acc, curr) => acc + curr.amount, 0);
          // }

          return {
            ...transaction,
            balance: runningBalance
          };
        }),
    summary: (pageData) => {
      // Calculate totals excluding opening balance row
      const transactionData = pageData.filter(row => row.type !== 'balance');
      const finalBalance = pageData[pageData.length - 1]?.balance || 0;

      return (
        <Table.Summary fixed style={{ fontSize: 20 }}>
          <Table.Summary.Row>
            <Table.Summary.Cell colSpan={4}>Total</Table.Summary.Cell>
            <Table.Summary.Cell>
              {formatNumber(
                transactionData.reduce((a, b) => a + (b.amount > 0 ? b.amount : 0), 0)
              )}
            </Table.Summary.Cell>
            <Table.Summary.Cell>
              {formatNumber(
                transactionData.reduce((a, b) => a + (b.amount < 0 ? b.amount : 0), 0)
              )}
            </Table.Summary.Cell>
            <Table.Summary.Cell>
              <Typography.Text
                style={{
                  color: finalBalance >= 0 ? "orange" : "green",
                  fontWeight: "bold",
                  fontSize: 16
                }}
              >
                {formatNumber(finalBalance)}
              </Typography.Text>
            </Table.Summary.Cell>
          </Table.Summary.Row>
        </Table.Summary>
      );
    },
  };

  return (
    <div>
      <Table
        {...tProps}
        title={() => (
          <Row justify="space-between">
            <Col span={12}>
              <DatePicker.RangePicker
                onChange={(dates) => {
                  setDateRange(dates);
                }}
              />
            </Col>
            <Col span={12} style={{ textAlign: "end" }}>
              <PrintComponents
                trigger={
                  <Button
                    icon={<PrinterOutlined />}
                    type="primary"
                    style={{ marginBottom: 16 }}
                  >
                    Print
                  </Button>
                }
              >
                <div style={{ margin: 20 }}>
                  <DocumentHead />
                  <Typography.Title level={3}>
                    Customer Statement
                  </Typography.Title>
                  <Table {...tProps} />
                </div>
              </PrintComponents>
            </Col>
          </Row>
        )}
      />
    </div>
  );
};

export default Statement;