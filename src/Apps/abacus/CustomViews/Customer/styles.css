/* Customer Custom View Styles */

.customer-card {
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  height: 100%;
  margin-bottom: 16px;
}

.customer-card .ant-card-head {
  background-color: #fafafa;
  border-bottom: 1px solid #f0f0f0;
}

.customer-card .ant-card-head-title {
  font-weight: 600;
}

.customer-avatar {
  background-color: #1890ff;
  font-size: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.customer-info-item {
  margin-bottom: 8px;
}

.customer-info-label {
  font-weight: 500;
  color: rgba(0, 0, 0, 0.65);
  margin-right: 8px;
}

.customer-info-value {
  color: rgba(0, 0, 0, 0.85);
}

.customer-tabs {
  width: 100%;
}

.customer-tabs .ant-tabs-nav {
  margin-bottom: 16px;
}

.customer-statistic-card {
  text-align: center;
}

.customer-statistic-card .ant-statistic-title {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.65);
}

.customer-statistic-card .ant-statistic-content {
  font-size: 20px;
  font-weight: 600;
}

.customer-contact-icon {
  margin-right: 8px;
  color: #1890ff;
}

.customer-timeline-item {
  padding-bottom: 16px;
}

.customer-timeline-date {
  font-size: 12px;
  color: rgba(0, 0, 0, 0.45);
}

.customer-timeline-title {
  font-weight: 500;
  margin-bottom: 4px;
}

.customer-timeline-description {
  color: rgba(0, 0, 0, 0.65);
}

.customer-action-button {
  margin-right: 8px;
}

.customer-print-button {
  margin-left: auto;
}

.customer-balance-positive {
  color: #52c41a;
}

.customer-balance-negative {
  color: #f5222d;
}

.customer-badge {
  margin-left: 8px;
}

.customer-tag {
  margin: 4px;
}
