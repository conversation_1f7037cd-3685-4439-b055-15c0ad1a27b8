import "./styles.css";
import PrintComponents from "react-print-components";
import React, { useEffect, useState, useMemo } from "react";
import Statement from "./Statement";
import ViewTable from "../../../../Components/ViewTable";
import moment from "moment";
import { Descriptions, Divider, Space, Typography, Button, Tabs, Row, Col, Image, Card, Avatar, Tag, Timeline, Badge, Tooltip, FloatButton, Empty, Skeleton, Table, Flex, } from "antd";
import { PageHeader } from "@ant-design/pro-layout";
import { ShopOutlined, PhoneOutlined, MailOutlined, HomeOutlined, IdcardOutlined, PrinterOutlined, FileTextOutlined, ShoppingCartOutlined, DollarOutlined, HistoryOutlined, RiseOutlined, FallOutlined, CalendarOutlined, BankOutlined, InboxOutlined, } from "@ant-design/icons";
import { StatisticCard } from "@ant-design/pro-components";
import { numberFormat } from "../../../../Utils/functions";


const { Text, Link, Title, Paragraph } = Typography;
const { Statistic } = StatisticCard;

const Supplier = (props) => {
  const supplier = props.data;
  const { pouchDatabase, databasePrefix } = props;
  const [dp, setDp] = useState(null);
  const [stockPurchases, setStockPurchases] = useState([]);
  const [requisitions, setRequisitions] = useState([]);
  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [company, setCompany] = useState(null);

  const sharedProps = {
    modules: props.modules,
    modulesProperties: props.modulesProperties,
    databasePrefix: props.databasePrefix,
    pouchDatabase: props.pouchDatabase,
    userPermissions: props.userPermissions,
    filterID: { column: "supplier", id: props.data._id },
  };

  // Fetch supplier data, stock purchases, and requisitions
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        // Fetch stock purchases
        const stockPurchasesDB = pouchDatabase("stock_purchasing", databasePrefix);
        const allStockPurchases = await stockPurchasesDB.getAllData();
        const supplierStockPurchases = allStockPurchases
          .filter(purchase => purchase && !purchase._id.startsWith('_') &&
            purchase.supplier && purchase.supplier.value === supplier._id);
        setStockPurchases(supplierStockPurchases);

        // Fetch requisitions
        const requisitionsDB = pouchDatabase("requisitions", databasePrefix);
        const allRequisitions = await requisitionsDB.getAllData();
        const supplierRequisitions = allRequisitions
          .filter(requisition => requisition && !requisition._id.startsWith('_') &&
            requisition.supplier && requisition.supplier.value === supplier._id);
        setRequisitions(supplierRequisitions);

        // Fetch products
        const productsDB = pouchDatabase("products", databasePrefix);
        const allProducts = await productsDB.getAllData();
        const filteredProducts = allProducts.filter(product => !product._id.startsWith('_'));
        setProducts(filteredProducts);

        // Fetch company info for printing
        const companyDB = pouchDatabase("company", databasePrefix);
        const allCompanies = await companyDB.getAllData();
        if (allCompanies.length > 0) {
          setCompany(allCompanies[0]);
        }
      } catch (error) {
        console.error("Error fetching data:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [supplier._id, pouchDatabase, databasePrefix]);

  // Calculate supplier statistics
  const statistics = useMemo(() => {
    if (!stockPurchases.length) return { totalPurchased: 0, totalItems: 0, purchaseCount: 0, avgOrderValue: 0 };

    const totalPurchased = stockPurchases.reduce((sum, purchase) => {
      const purchaseTotal = purchase.items?.reduce((total, item) => {
        return total + (item.price * item.quantity);
      }, 0) || 0;
      return sum + purchaseTotal;
    }, 0);

    const totalItems = stockPurchases.reduce((sum, purchase) => {
      const itemCount = purchase.items?.reduce((count, item) => count + item.quantity, 0) || 0;
      return sum + itemCount;
    }, 0);

    return {
      totalPurchased,
      totalItems,
      purchaseCount: stockPurchases.length,
      avgOrderValue: stockPurchases.length > 0 ? totalPurchased / stockPurchases.length : 0
    };
  }, [stockPurchases]);

  // Get top purchased products
  const topProducts = useMemo(() => {
    if (!stockPurchases.length || !products.length) return [];

    // Create a map of product IDs to quantities
    const productQuantities = {};

    stockPurchases.forEach(purchase => {
      purchase.items?.forEach(item => {
        if (item.product && item.product.value) {
          if (!productQuantities[item.product.value]) {
            productQuantities[item.product.value] = {
              quantity: 0,
              value: 0,
            };
          }
          productQuantities[item.product.value].quantity += item.quantity;
          productQuantities[item.product.value].value += (item.price * item.quantity);
        }
      });
    });

    // Convert to array and add product details
    const productArray = Object.keys(productQuantities).map(productId => {
      const product = products.find(p => p._id === productId);
      return {
        id: productId,
        name: product ? product.name : 'Unknown Product',
        sku: product ? product.sku : 'N/A',
        quantity: productQuantities[productId].quantity,
        value: productQuantities[productId].value
      };
    });

    // Sort by quantity and take top 5
    return productArray
      .sort((a, b) => b.quantity - a.quantity)
      .slice(0, 5);
  }, [stockPurchases, products]);

  // Get recent transactions for timeline
  const recentTransactions = useMemo(() => {
    const transactions = [
      ...stockPurchases.map(purchase => ({
        type: 'purchase',
        date: purchase.date,
        id: purchase._id,
        amount: purchase.items?.reduce((total, item) => total + (item.price * item.quantity), 0) || 0
      })),
      ...requisitions.map(requisition => ({
        type: 'requisition',
        date: requisition.date,
        id: requisition._id,
        amount: requisition.items?.reduce((total, item) => total + (item.price * item.quantity), 0) || 0
      }))
    ];

    return transactions
      .sort((a, b) => new Date(b.date) - new Date(a.date))
      .slice(0, 5);
  }, [stockPurchases, requisitions]);

  const { TabPane } = Tabs;

  // Format date for display
  const formatDate = (dateString) => {
    return moment(dateString).format('MMM DD, YYYY');
  };

  // Printable supplier component
  const PrintableSupplier = () => (
    <div style={{ padding: "20px", maxWidth: "800px", margin: "0 auto" }}>
      {company && (
        <Flex align="center" justify="space-between" style={{ marginBottom: "20px" }}>
          <div>
            {company.logo && <img src={company.logo} alt="Company Logo" style={{ maxHeight: 100 }} />}
          </div>
          <div style={{ textAlign: "center" }}>
            <Title level={2}>SUPPLIER PROFILE</Title>
            <Text>Supplier: {supplier.name}</Text>
          </div>
          <div style={{ textAlign: "right" }}>
            <Text>
              {company.address}
              <br />
              Tel: {company.phone}
              <br />
              Email: {company.email}
            </Text>
          </div>
        </Flex>
      )}

      <Divider />

      <Title level={4}>Supplier Information</Title>
      <Descriptions column={2} bordered>
        <Descriptions.Item label="Name">{supplier.name}</Descriptions.Item>
        <Descriptions.Item label="Supplier ID">{supplier._id}</Descriptions.Item>
        {supplier.email && <Descriptions.Item label="Email">{supplier.email}</Descriptions.Item>}
        {supplier.phone && <Descriptions.Item label="Phone">{supplier.phone}</Descriptions.Item>}
        {supplier.mobile && <Descriptions.Item label="Mobile">{supplier.mobile}</Descriptions.Item>}
        {supplier.address && <Descriptions.Item label="Address">{supplier.address}</Descriptions.Item>}
        {supplier.tin_number && <Descriptions.Item label="TIN Number">{supplier.tin_number}</Descriptions.Item>}
      </Descriptions>

      <Divider />

      <Title level={4}>Purchase Summary</Title>
      <Descriptions column={2} bordered>
        <Descriptions.Item label="Total Purchased">{numberFormat(statistics.totalPurchased)}</Descriptions.Item>
        <Descriptions.Item label="Total Items">{statistics.totalItems}</Descriptions.Item>
        <Descriptions.Item label="Purchase Count">{statistics.purchaseCount}</Descriptions.Item>
        <Descriptions.Item label="Average Order Value">{numberFormat(statistics.avgOrderValue)}</Descriptions.Item>
      </Descriptions>

      {topProducts.length > 0 && (
        <>
          <Divider />
          <Title level={4}>Top Purchased Products</Title>
          <Table
            dataSource={topProducts}
            pagination={false}
            size="small"
            columns={[
              {
                title: 'Product',
                dataIndex: 'name',
                key: 'name',
              },
              {
                title: 'SKU',
                dataIndex: 'sku',
                key: 'sku',
              },
              {
                title: 'Quantity',
                dataIndex: 'quantity',
                key: 'quantity',
              },
              {
                title: 'Value',
                dataIndex: 'value',
                key: 'value',
                render: value => numberFormat(value)
              }
            ]}
          />
        </>
      )}

      <div style={{ textAlign: 'right', marginTop: 20 }}>
        <Text type="secondary">Generated on {moment().format('MMMM Do YYYY, h:mm:ss a')}</Text>
      </div>
    </div>
  );

  // Main content render
  const renderContent = () => (
    <Row gutter={[24, 24]}>
      <Col xs={24} lg={8}>
        <Card className="supplier-card">
          <div style={{ textAlign: 'center', marginBottom: 24 }}>
            {dp ? (
              <Avatar size={100} src={dp} />
            ) : (
              <Avatar size={100} icon={<ShopOutlined />} className="supplier-avatar" />
            )}
            <Title level={4} style={{ marginTop: 16, marginBottom: 0 }}>
              {supplier.name}
            </Title>
            <Text type="secondary">
              Supplier ID: {supplier._id}
            </Text>
          </div>

          <Divider style={{ margin: '16px 0' }} />

          <div>
            {supplier.phone && (
              <div className="supplier-info-item">
                <PhoneOutlined className="supplier-contact-icon" />
                <Text className="supplier-info-label">Phone:</Text>
                <Text copyable className="supplier-info-value">{supplier.phone}</Text>
              </div>
            )}

            {supplier.mobile && (
              <div className="supplier-info-item">
                <PhoneOutlined className="supplier-contact-icon" />
                <Text className="supplier-info-label">Mobile:</Text>
                <Text copyable className="supplier-info-value">{supplier.mobile}</Text>
              </div>
            )}

            {supplier.email && (
              <div className="supplier-info-item">
                <MailOutlined className="supplier-contact-icon" />
                <Text className="supplier-info-label">Email:</Text>
                <Text copyable className="supplier-info-value">{supplier.email}</Text>
              </div>
            )}

            {supplier.address && (
              <div className="supplier-info-item">
                <HomeOutlined className="supplier-contact-icon" />
                <Text className="supplier-info-label">Address:</Text>
                <Text className="supplier-info-value">{supplier.address}</Text>
              </div>
            )}

            {supplier.tin_number && (
              <div className="supplier-info-item">
                <IdcardOutlined className="supplier-contact-icon" />
                <Text className="supplier-info-label">TIN Number:</Text>
                <Text copyable className="supplier-info-value">{supplier.tin_number}</Text>
              </div>
            )}
          </div>
        </Card>

        <Card
          title={<><InboxOutlined /> Top Purchased Products</>}
          className="supplier-card"
          style={{ marginTop: 16 }}
        >
          {loading ? (
            <Skeleton active paragraph={{ rows: 5 }} />
          ) : topProducts.length > 0 ? (
            <ul style={{ paddingLeft: 20 }}>
              {topProducts.map((product, index) => (
                <li key={index} style={{ marginBottom: 12 }}>
                  <div>
                    <Text strong>{product.name}</Text>
                    {product.sku && <Text type="secondary"> ({product.sku})</Text>}
                  </div>
                  <div>
                    <Text type="secondary">Quantity: {product.quantity}</Text>
                    <Divider type="vertical" />
                    <Text type="secondary">Value: {numberFormat(product.value)}</Text>
                  </div>
                </li>
              ))}
            </ul>
          ) : (
            <Empty description="No product data available" />
          )}
        </Card>
      </Col>

      <Col xs={24} lg={16}>
        <Row gutter={[16, 16]}>
          <Col xs={24} sm={12} md={6}>
            <Card className="supplier-statistic-card">
              <Statistic
                title="Total Purchased"
                value={numberFormat(statistics.totalPurchased)}
                prefix={<DollarOutlined />}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} md={6}>
            <Card className="supplier-statistic-card">
              <Statistic
                title="Total Items"
                value={statistics.totalItems}
                prefix={<InboxOutlined />}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} md={6}>
            <Card className="supplier-statistic-card">
              <Statistic
                title="Purchase Count"
                value={statistics.purchaseCount}
                prefix={<ShoppingCartOutlined />}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} md={6}>
            <Card className="supplier-statistic-card">
              <Statistic
                title="Avg Order Value"
                value={numberFormat(statistics.avgOrderValue)}
                prefix={<BankOutlined />}
              />
            </Card>
          </Col>
        </Row>

        <Card
          title={<><HistoryOutlined /> Recent Transactions</>}
          className="supplier-card"
          style={{ marginTop: 16 }}
        >
          {loading ? (
            <Skeleton active paragraph={{ rows: 5 }} />
          ) : recentTransactions.length > 0 ? (
            <Timeline>
              {recentTransactions.map((transaction, index) => (
                <Timeline.Item
                  key={index}
                  color={transaction.type === 'purchase' ? 'purple' : 'blue'}
                  dot={transaction.type === 'purchase' ? <ShoppingCartOutlined /> : <FileTextOutlined />}
                >
                  <div className="supplier-timeline-item">
                    <div className="supplier-timeline-date">{formatDate(transaction.date)}</div>
                    <div className="supplier-timeline-title">
                      {transaction.type === 'purchase' ? 'Stock Purchase' : 'Requisition'} #{transaction.id}
                    </div>
                    <div className="supplier-timeline-description">
                      Amount: {numberFormat(transaction.amount)}
                    </div>
                  </div>
                </Timeline.Item>
              ))}
            </Timeline>
          ) : (
            <Empty description="No recent transactions" />
          )}
        </Card>
      </Col>
    </Row>
  );

  return (
    <>
      <PageHeader
        className="site-page-header-responsive"
        onBack={() => window.history.back()}
        title={`${supplier.name}`}
        subTitle={supplier.phone}
        tags={[
          <Tag color="purple" key="supplier">
            Supplier
          </Tag>
        ]}
        extra={[
          <PrintComponents
            key="print"
            trigger={
              <Button icon={<PrinterOutlined />} type="primary">
                Print Supplier Profile
              </Button>
            }
          >
            <PrintableSupplier />
          </PrintComponents>,
        ]}
      />

      <div style={{ padding: "0 24px 24px" }}>
        {loading ? (
          <Row gutter={[24, 24]}>
            <Col xs={24} lg={8}>
              <Card>
                <Skeleton avatar active paragraph={{ rows: 6 }} />
              </Card>
            </Col>
            <Col xs={24} lg={16}>
              <Row gutter={[16, 16]}>
                <Col span={6}><Skeleton.Button active block style={{ height: 100 }} /></Col>
                <Col span={6}><Skeleton.Button active block style={{ height: 100 }} /></Col>
                <Col span={6}><Skeleton.Button active block style={{ height: 100 }} /></Col>
                <Col span={6}><Skeleton.Button active block style={{ height: 100 }} /></Col>
              </Row>
              <Card style={{ marginTop: 16 }}>
                <Skeleton active paragraph={{ rows: 5 }} />
              </Card>
            </Col>
          </Row>
        ) : (
          renderContent()
        )}

        <Tabs
          defaultActiveKey="statement"
          type="card"
          className="supplier-tabs"
          style={{ marginTop: 24 }}
          size="large"
        >
          <TabPane
            tab={<span><FileTextOutlined /> Statement</span>}
            key="statement"
          >
            <Statement {...sharedProps} />
          </TabPane>

          <TabPane
            tab={<span><ShoppingCartOutlined /> Stock Purchases</span>}
            key="purchases"
          >
            <ViewTable
              {...sharedProps}
              removeColumns={["items", "createdAt"]}
              {...props.modules.stock_purchasing}
              {...props.modulesProperties.stock_purchasing}
              organization={props.organization}
            />
          </TabPane>

          <TabPane
            tab={<span><FileTextOutlined /> Requisitions</span>}
            key="requisitions"
          >
            <ViewTable
              {...sharedProps}
              {...props.modules.requisitions}
              {...props.modulesProperties.requisitions}
              organization={props.organization}
            />
          </TabPane>
        </Tabs>
      </div>

      <FloatButton
        icon={<PrinterOutlined />}
        type="primary"
        tooltip="Print Supplier Profile"
        onClick={() => document.getElementById("print-button").click()}
        style={{ right: 94 }}
      />

      {/* Hidden print button for FloatButton to trigger */}
      <div style={{ display: "none" }}>
        <PrintComponents
          trigger={<Button id="print-button">Print</Button>}
        >
          <PrintableSupplier />
        </PrintComponents>
      </div>
    </>
  );
};

export default Supplier;