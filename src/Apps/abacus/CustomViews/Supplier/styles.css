/* Supplier Custom View Styles */

.supplier-card {
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  height: 100%;
  margin-bottom: 16px;
}

.supplier-card .ant-card-head {
  background-color: #fafafa;
  border-bottom: 1px solid #f0f0f0;
}

.supplier-card .ant-card-head-title {
  font-weight: 600;
}

.supplier-avatar {
  background-color: #722ed1;
  font-size: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.supplier-info-item {
  margin-bottom: 8px;
}

.supplier-info-label {
  font-weight: 500;
  color: rgba(0, 0, 0, 0.65);
  margin-right: 8px;
}

.supplier-info-value {
  color: rgba(0, 0, 0, 0.85);
}

.supplier-tabs {
  width: 100%;
}

.supplier-tabs .ant-tabs-nav {
  margin-bottom: 16px;
}

.supplier-statistic-card {
  text-align: center;
}

.supplier-statistic-card .ant-statistic-title {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.65);
}

.supplier-statistic-card .ant-statistic-content {
  font-size: 20px;
  font-weight: 600;
}

.supplier-contact-icon {
  margin-right: 8px;
  color: #722ed1;
}

.supplier-timeline-item {
  padding-bottom: 16px;
}

.supplier-timeline-date {
  font-size: 12px;
  color: rgba(0, 0, 0, 0.45);
}

.supplier-timeline-title {
  font-weight: 500;
  margin-bottom: 4px;
}

.supplier-timeline-description {
  color: rgba(0, 0, 0, 0.65);
}

.supplier-action-button {
  margin-right: 8px;
}

.supplier-print-button {
  margin-left: auto;
}

.supplier-balance-positive {
  color: #52c41a;
}

.supplier-balance-negative {
  color: #f5222d;
}

.supplier-badge {
  margin-left: 8px;
}

.supplier-tag {
  margin: 4px;
}
