import DocumentHead from "../../../Universal/CustomViews/Components/DocumentHead";
import PrintComponents from "react-print-components";
import React, { useEffect, useState } from "react";
import moment from "moment";
import { <PERSON><PERSON>, Col, DatePicker, Input, Row, Table, Typography } from "antd";
import { PrinterOutlined, SearchOutlined } from "@ant-design/icons";
import { buffProducts } from "../../modulesProperties/utils";
import { formatNumber, numberFormat } from "../../../../Utils/functions";


const Statement = (props) => {
  const { products, company, pouchDatabase, databasePrefix } = props;
  const [data, setData] = useState(null);
  const [filteredData, setFilteredData] = useState(null);
  const [dateRange, setDateRange] = useState([null, null]);
  const [searchText, setSearchText] = useState("");
  const [openingBalance, setOpeningBalance] = useState(0);

  useEffect(() => {
    const invoicesDB = pouchDatabase("stock_purchasing", databasePrefix);
    const returnedDB = pouchDatabase("returned_stock", databasePrefix);
    const receiptsDB = pouchDatabase("stock_payments", databasePrefix);

    Promise.all([
      invoicesDB.getAllData({ include_docs: true }),
      returnedDB.getAllData({ include_docs: true }),
      receiptsDB.getAllData({ include_docs: true }),
    ]).then((res) => {
      const [allInvoices, allReturned, allReceipts] = res;
      const statementData = [];

      allInvoices
        .filter(
          (invoice) =>
            invoice.supplier && invoice.supplier.value === props.filterID.id
        )
        .forEach((invoice) => {
          invoice.items
            ? invoice.items.forEach((item) => {
              statementData.push({
                ...item,
                date: invoice.date,
                type: "invoice",
                code: "INV-" + invoice._id,
                amount: item.price * item.quantity,
                description: item.product.label,
              });
            })
            : statementData.push({
              date: invoice.date,
              type: "invoice",
              code: "INV-" + invoice._id,
              amount: invoice.unit_cost
                ? invoice.unit_cost * invoice.quantity
                : invoice.total_cost,
              description: invoice.product.label,
            });

          !invoice.not_paid &&
            statementData.push({
              date: invoice.date,
              type: "receipt",
              code: "REC-SYS GEN",
              amount: invoice.items
                ? invoice.items.reduce((a, b) => a + b.price * b.quantity, 0) *
                -1
                : (invoice.unit_cost
                  ? invoice.unit_cost * invoice.quantity
                  : invoice.total_cost) * -1,
              description: "Payment for " + "INV-" + invoice._id,
            });

          allReceipts
            .filter((receipt) => receipt.invoice._id === invoice._id)
            .forEach((receipt) => {
              statementData.push({
                ...receipt,
                date: receipt.date,
                type: "receipt",
                code: "REC-" + receipt._id,
                amount: Number(receipt.amount) * -1,
                description: `Receipt for ${invoice.supplier.label} (INV${invoice._id})`,
              });
            });
        });

      allReturned
        .filter((returned) => returned.supplier.value === props.filterID.id)
        .forEach((returned) => {
          returned.items.forEach((item) => {
            statementData.push({
              ...item,
              date: returned.date,
              type: "returned",
              code: "RET-" + returned._id,
              amount: item.price * item.quantity * -1,
              description: item.product.label,
            });
          });
        });

      setData(statementData);
      setFilteredData(statementData);
    });
  }, []);

  useEffect(() => {
    if (!data) return;

    let filtered = [...data];
    const [startDate, endDate] = dateRange;

    if (startDate && endDate) {
      // Calculate opening balance from transactions before start date
      const openingBal = data
        .filter((item) => moment(item.date).isBefore(moment(startDate.$d).startOf('day'), 'day'))
        .reduce((acc, curr) => acc + (curr.amount || 0), 0);
      setOpeningBalance(openingBal);

      // Filter transactions within date range (inclusive)
      filtered = filtered.filter(
        (item) =>
          moment(item.date).isSameOrAfter(moment(startDate.$d), 'day') &&
          moment(item.date).isSameOrBefore(moment(endDate.$d), 'day')
      );
    }

    // Filter by search text
    if (searchText) {
      const searchLower = searchText.toLowerCase();
      filtered = filtered.filter(
        (item) =>
          item.description.toLowerCase().includes(searchLower) ||
          item.code.toLowerCase().includes(searchLower) ||
          item.type.toLowerCase().includes(searchLower)
      );
    }

    setFilteredData(filtered);
  }, [data, dateRange, searchText]);

  const columns = [
    {
      valueType: "date",
      dataIndex: "date",
      title: "Date",
      hideInForm: true,
    },
    {
      valueType: "text",
      dataIndex: "code",
      title: "Document ID",
      hideInForm: true,
    },
    {
      valueType: "text",
      dataIndex: "type",
      title: "Type",
      render: (v) => v.charAt(0).toUpperCase() + v.slice(1),
    },
    {
      valueType: "text",
      dataIndex: "description",
      title: "Description",
    },
    {
      valueType: "money",
      dataIndex: "amount",
      title: "Invoice",
      render: (v) => v > 0 && formatNumber(v ? v : 0),
    },
    {
      valueType: "money",
      dataIndex: "amount",
      title: "Payment",
      render: (v) => v < 0 && formatNumber(v ? v : 0),
    },
    {
      valueType: "money",
      dataIndex: "balance",
      title: "Balance",
      render: (_, record, index) => {
        let runningBalance = 0;
        for (let i = 0; i <= index; i++) {
          runningBalance += (filteredData[i] && (filteredData[i].amount ? filteredData[i].amount : 0)) || 0;
        }
        return formatNumber(runningBalance);
      },
    },
  ];

  let balance = openingBalance;

  const tProps = {
    size: "small",
    columns: columns,
    pagination: false,
    dataSource:
      filteredData &&
      [
        // Add opening balance as first row when date range is selected
        ...(dateRange && dateRange[0] && dateRange[1] ? [{
          date: moment(dateRange[0].$d).format('YYYY-MM-DD'),
          code: 'OPENING',
          type: 'balance',
          description: 'Opening Balance',
          amount: openingBalance,
          balance: openingBalance
        }] : []),
        // Map the rest of the transactions
        ...filteredData.map((d, i) => {
          const prevBalance = i === 0 ? (dateRange[0] && dateRange[1] ? openingBalance : 0) : filteredData[i - 1].balance || 0;
          return {
            ...d,
            balance: prevBalance + d.amount,
          };
        })
      ],
    summary: (pageData) => {
      return (
        <Table.Summary fixed style={{ fontSize: 20 }}>
          <Table.Summary.Row>
            <Table.Summary.Cell colSpan={4}>Total</Table.Summary.Cell>
            <Table.Summary.Cell>
              {formatNumber(
                pageData.reduce((a, b) => a + (b.amount > 0 ? b.amount : 0), 0)
              )}
            </Table.Summary.Cell>
            <Table.Summary.Cell>
              {formatNumber(
                pageData.reduce((a, b) => a + (b.amount < 0 ? b.amount : 0), 0)
              )}
            </Table.Summary.Cell>
            <Table.Summary.Cell>
              <Typography.Text
                style={{ color: "red", fontWeight: "bold", fontSize: 16 }}
              >
                {formatNumber(pageData.reduce((a, b) => a + b.amount, 0))}
              </Typography.Text>
            </Table.Summary.Cell>
          </Table.Summary.Row>
        </Table.Summary>
      );
    },
  };

  return (
    <div>
      <Table
        {...tProps}
        title={() => (
          <Row gutter={[16, 16]}>
            <Col span={8}>
              <DatePicker.RangePicker
                style={{ width: '100%' }}
                value={dateRange}
                onChange={setDateRange}
                format="YYYY-MM-DD"
              />
            </Col>
            <Col span={8}>
              <Input
                placeholder="Search by description, document ID, or type"
                prefix={<SearchOutlined />}
                value={searchText}
                onChange={(e) => setSearchText(e.target.value)}
              />
            </Col>
            <Col span={8} style={{ textAlign: "end" }}>
              <PrintComponents
                trigger={
                  <Button
                    icon={<PrinterOutlined />}
                    type="primary"
                    style={{ marginBottom: 16 }}
                  >
                    Print
                  </Button>
                }
              >
                <div style={{ margin: 20 }}>
                  <DocumentHead />
                  <Typography.Title level={3}>
                    Supplier Statement
                  </Typography.Title>
                  {dateRange[0] && dateRange[1] && (
                    <Typography.Text strong>
                      Opening Balance: {formatNumber(openingBalance)}
                    </Typography.Text>
                  )}
                  <Table {...tProps} />
                </div>
              </PrintComponents>
            </Col>
          </Row>
        )}
      />
    </div>
  );
};

export default Statement;