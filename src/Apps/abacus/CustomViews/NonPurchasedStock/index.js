import "./styles.css";
import AppDatabase from "../../../../Utils/AppDatabase";
import PrintComponents from "react-print-components";
import React, { useEffect, useState, useMemo } from "react";
import moment from "moment";
import { Card, Row, Col, Typography, Descriptions, Table, Tag, Divider, Space, Button, Statistic, Avatar, Empty, Skeleton, Flex, Badge, Tooltip } from "antd";
import { InboxOutlined, ShoppingCartOutlined, CalendarOutlined, FileTextOutlined, PrinterOutlined, InfoCircleOutlined, DollarOutlined, GiftOutlined } from "@ant-design/icons";
import { PageHeader } from "@ant-design/pro-components";
import { numberFormat } from "../../../../Utils/functions";


const { Title, Text, Paragraph } = Typography;

const NonPurchasedStock = (props) => {
  const { data, singular } = props;
  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [company, setCompany] = useState(null);

  // Fetch product data
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        const databasePrefix = localStorage.getItem("DB_PREFIX") || "";

        // Fetch products for items in non-purchased stock
        if (data.items && data.items.length > 0) {
          const productsDB = AppDatabase("products", databasePrefix);
          const allProducts = await productsDB.getAllData();
          setProducts(allProducts);
        }

        // Fetch company info for printing
        const companyDB = AppDatabase("company", databasePrefix);
        const companyData = await companyDB.getAllData();
        if (companyData.length > 0) {
          setCompany(companyData[0]);
        }
      } catch (error) {
        console.error("Error fetching data:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [data.items]);

  // Prepare stock items with product details
  const stockItems = useMemo(() => {
    if (!data.items || !products.length) return [];

    return data.items.map(item => {
      const product = products.find(p => p._id === item.product.value);
      return {
        ...item,
        productDetails: product || null
      };
    });
  }, [data.items, products]);

  // Calculate stock statistics
  const statistics = useMemo(() => {
    if (!stockItems.length) return { totalItems: 0, totalValue: 0 };

    const totalItems = stockItems.reduce((sum, item) => sum + (item.quantity || 0), 0);
    const totalValue = stockItems.reduce((sum, item) => {
      const cost = item.productDetails ? item.productDetails.cost || 0 : 0;
      return sum + (cost * (item.quantity || 0));
    }, 0);

    return {
      totalItems,
      totalValue
    };
  }, [stockItems]);

  // Format date for display
  const formatDate = (dateString) => {
    return moment(dateString).format('MMM DD, YYYY');
  };

  // Printable stock component
  const PrintableStock = () => (
    <div style={{ padding: "20px", maxWidth: "800px", margin: "0 auto" }}>
      {company && (
        <Flex align="center" justify="space-between" style={{ marginBottom: "20px" }}>
          <div>
            {company.logo && <img src={company.logo} alt="Company Logo" style={{ maxHeight: 100 }} />}
          </div>
          <div style={{ textAlign: "center" }}>
            <Title level={2}>NON-PURCHASED STOCK</Title>
            <Text>Reference #: {data._id}</Text>
          </div>
          <div style={{ textAlign: "right" }}>
            <Text>
              {company.address}
              <br />
              Tel: {company.phone}
              <br />
              Email: {company.email}
            </Text>
          </div>
        </Flex>
      )}

      <Divider />

      <Title level={5}>Stock Information</Title>
      <Descriptions bordered column={2}>
        <Descriptions.Item label="Reference ID">{data._id}</Descriptions.Item>
        <Descriptions.Item label="Date">{formatDate(data.date)}</Descriptions.Item>
        <Descriptions.Item label="Total Items">{statistics.totalItems}</Descriptions.Item>
        <Descriptions.Item label="Total Value">{numberFormat(statistics.totalValue)}</Descriptions.Item>
        {data.source && (
          <Descriptions.Item label="Source" span={2}>{data.source}</Descriptions.Item>
        )}
        {data.description && (
          <Descriptions.Item label="Description" span={2}>{data.description}</Descriptions.Item>
        )}
      </Descriptions>

      <Divider />

      <Title level={5}>Stock Items</Title>
      <Table
        dataSource={stockItems}
        pagination={false}
        size="small"
        columns={[
          {
            title: 'Product',
            dataIndex: 'product',
            key: 'product',
            render: product => product.label
          },
          {
            title: 'Quantity',
            dataIndex: 'quantity',
            key: 'quantity',
            align: 'right'
          },
          {
            title: 'Unit Cost',
            key: 'cost',
            align: 'right',
            render: (_, record) => numberFormat(record.productDetails ? record.productDetails.cost || 0 : 0)
          },
          {
            title: 'Total Value',
            key: 'value',
            align: 'right',
            render: (_, record) => {
              const cost = record.productDetails ? record.productDetails.cost || 0 : 0;
              return numberFormat(cost * (record.quantity || 0));
            }
          }
        ]}
        summary={() => (
          <Table.Summary>
            <Table.Summary.Row>
              <Table.Summary.Cell colSpan={3} index={0}>
                <Text strong>Total Value</Text>
              </Table.Summary.Cell>
              <Table.Summary.Cell index={1} align="right">
                <Text strong>{numberFormat(statistics.totalValue)}</Text>
              </Table.Summary.Cell>
            </Table.Summary.Row>
          </Table.Summary>
        )}
      />

      <div style={{ textAlign: 'right', marginTop: 20 }}>
        <Text type="secondary">Generated on {moment().format('MMMM Do YYYY, h:mm:ss a')}</Text>
      </div>
    </div>
  );

  return (
    <>
      <PageHeader
        className="site-page-header-responsive"
        onBack={() => window.history.back()}
        title={`Non-Purchased Stock #${data._id}`}
        subTitle={formatDate(data.date)}
        tags={[
          <Tag color="geekblue" key="non-purchased">
            <GiftOutlined /> Non-Purchased Stock
          </Tag>
        ]}
        extra={[
          <PrintComponents
            key="print"
            trigger={
              <Button icon={<PrinterOutlined />} type="primary">
                Print Stock Record
              </Button>
            }
          >
            <PrintableStock />
          </PrintComponents>,
        ]}
      />

      <div style={{ padding: "0 24px 24px" }}>
        {loading ? (
          <Row gutter={[24, 24]}>
            <Col span={24}>
              <Skeleton active paragraph={{ rows: 6 }} />
            </Col>
          </Row>
        ) : (
          <Row gutter={[24, 24]}>
            <Col xs={24} lg={8}>
              <Card
                title={<><InfoCircleOutlined /> Stock Information</>}
                className="stock-card"
              >
                <Descriptions column={1} size="small" bordered>
                  <Descriptions.Item label="Reference ID">{data._id}</Descriptions.Item>
                  <Descriptions.Item label="Date">{formatDate(data.date)}</Descriptions.Item>
                  {data.source && (
                    <Descriptions.Item label="Source">{data.source}</Descriptions.Item>
                  )}
                  {data.entrant && (
                    <Descriptions.Item label="Created By">{data.entrant.label}</Descriptions.Item>
                  )}
                </Descriptions>

                {data.description && (
                  <>
                    <Divider orientation="left">Description</Divider>
                    <Paragraph>{data.description}</Paragraph>
                  </>
                )}
              </Card>

              <Card
                title={<><InboxOutlined /> Stock Summary</>}
                className="stock-card"
                style={{ marginTop: 16 }}
              >
                <Statistic
                  title="Total Items"
                  value={statistics.totalItems}
                  prefix={<ShoppingCartOutlined />}
                  style={{ marginBottom: 16 }}
                />
                <Statistic
                  title="Total Value"
                  value={numberFormat(statistics.totalValue)}
                  prefix={<DollarOutlined />}
                />
              </Card>
            </Col>

            <Col xs={24} lg={16}>
              <Card
                title={<><ShoppingCartOutlined /> Stock Items</>}
                className="stock-card"
              >
                {stockItems.length > 0 ? (
                  <Table
                    dataSource={stockItems}
                    rowKey={(record, index) => index}
                    pagination={false}
                    columns={[
                      {
                        title: 'Product',
                        dataIndex: 'product',
                        key: 'product',
                        render: product => (
                          <Space>
                            <Avatar
                              shape="square"
                              size="small"
                              icon={<ShoppingCartOutlined />}
                              style={{ backgroundColor: '#1890ff' }}
                            />
                            <Tooltip title={`View product details: ${product.label}`}>
                              <a href={`#/inventory/products/${product.value}`}>{product.label}</a>
                            </Tooltip>
                          </Space>
                        )
                      },
                      {
                        title: 'Quantity',
                        dataIndex: 'quantity',
                        key: 'quantity',
                        align: 'right',
                        width: '15%'
                      },
                      {
                        title: 'Unit Cost',
                        key: 'cost',
                        align: 'right',
                        width: '20%',
                        render: (_, record) => numberFormat(record.productDetails ? record.productDetails.cost || 0 : 0)
                      },
                      {
                        title: 'Total Value',
                        key: 'value',
                        align: 'right',
                        width: '20%',
                        render: (_, record) => {
                          const cost = record.productDetails ? record.productDetails.cost || 0 : 0;
                          return numberFormat(cost * (record.quantity || 0));
                        }
                      }
                    ]}
                    summary={() => (
                      <Table.Summary>
                        <Table.Summary.Row>
                          <Table.Summary.Cell colSpan={3} index={0}>
                            <Text strong>Total Value</Text>
                          </Table.Summary.Cell>
                          <Table.Summary.Cell index={1} align="right">
                            <Text strong>{numberFormat(statistics.totalValue)}</Text>
                          </Table.Summary.Cell>
                        </Table.Summary.Row>
                      </Table.Summary>
                    )}
                  />
                ) : (
                  <Empty description="No items in this stock record" />
                )}
              </Card>
            </Col>
          </Row>
        )}
      </div>
    </>
  );
};

export default NonPurchasedStock;