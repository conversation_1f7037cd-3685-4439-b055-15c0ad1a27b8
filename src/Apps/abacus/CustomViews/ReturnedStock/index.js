import "./styles.css";
import PrintComponents from "react-print-components";
import React, { useEffect, useState, useMemo } from "react";
import moment from "moment";
import { Card, Row, Col, Typography, Descriptions, Table, Tag, Divider, Space, Button, Statistic, Avatar, Empty, Skeleton, Flex, Badge, Tooltip } from "antd";
import { PageHeader } from "@ant-design/pro-components";
import { RollbackOutlined, ShoppingCartOutlined, ShopOutlined, CalendarOutlined, FileTextOutlined, PrinterOutlined, InfoCircleOutlined, DollarOutlined } from "@ant-design/icons";
import { numberFormat } from "../../../../Utils/functions";


const { Title, Text, Paragraph } = Typography;

const ReturnedStock = (props) => {
  const { data, singular, pouchDatabase, databasePrefix } = props;
  const [supplier, setSupplier] = useState(null);
  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [company, setCompany] = useState(null);

  // Fetch supplier and product data
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        // Fetch supplier details
        if (data.supplier && data.supplier.value) {
          const supplierDB = pouchDatabase("suppliers", databasePrefix);
          const supplierDoc = await supplierDB.get(data.supplier.value);
          setSupplier(supplierDoc);
        }

        // Fetch products for items in return
        if (data.items && data.items.length > 0) {
          const productsDB = pouchDatabase("products", databasePrefix);
          const allProducts = await productsDB.getAllData();
          const filteredProducts = allProducts.filter(product => !product._id.startsWith('_'));
          setProducts(filteredProducts);
        }

        // Fetch company info for printing
        const companyDB = pouchDatabase("company", databasePrefix);
        const allCompanies = await companyDB.getAllData();
        if (allCompanies.length > 0) {
          setCompany(allCompanies[0]);
        }
      } catch (error) {
        console.error("Error fetching data:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [data.supplier, data.items, pouchDatabase, databasePrefix]);

  // Calculate return statistics
  const statistics = useMemo(() => {
    if (!data.items || !data.items.length) return { totalItems: 0, totalValue: 0 };

    const totalItems = data.items.reduce((sum, item) => sum + (item.quantity || 0), 0);
    const totalValue = data.items.reduce((sum, item) => sum + ((item.price || 0) * (item.quantity || 0)), 0);

    return {
      totalItems,
      totalValue
    };
  }, [data.items]);

  // Format date for display
  const formatDate = (dateString) => {
    return moment(dateString).format('MMM DD, YYYY');
  };

  // Printable return component
  const PrintableReturn = () => (
    <div style={{ padding: "20px", maxWidth: "800px", margin: "0 auto" }}>
      {company && (
        <Flex align="center" justify="space-between" style={{ marginBottom: "20px" }}>
          <div>
            {company.logo && <img src={company.logo} alt="Company Logo" style={{ maxHeight: 100 }} />}
          </div>
          <div style={{ textAlign: "center" }}>
            <Title level={2}>RETURNED STOCK</Title>
            <Text>Return #: {data._id}</Text>
          </div>
          <div style={{ textAlign: "right" }}>
            <Text>
              {company.address}
              <br />
              Tel: {company.phone}
              <br />
              Email: {company.email}
            </Text>
          </div>
        </Flex>
      )}

      <Divider />

      <Row gutter={24}>
        <Col span={12}>
          <Title level={5}>Supplier Information</Title>
          {supplier ? (
            <div>
              <Text strong>{supplier.name}</Text><br />
              {supplier.address && <><Text>{supplier.address}</Text><br /></>}
              {supplier.phone && <><Text>Phone: {supplier.phone}</Text><br /></>}
              {supplier.email && <><Text>Email: {supplier.email}</Text><br /></>}
            </div>
          ) : (
            <Text>No supplier information available</Text>
          )}
        </Col>
        <Col span={12}>
          <Title level={5}>Return Information</Title>
          <div>
            <Text>Date: {formatDate(data.date)}</Text><br />
            <Text>Return #: {data._id}</Text><br />
            {data.entrant && <Text>Created By: {data.entrant.label}</Text>}
          </div>
        </Col>
      </Row>

      <Divider />

      <Title level={5}>Returned Items</Title>
      <Table
        dataSource={data.items}
        pagination={false}
        size="small"
        columns={[
          {
            title: 'Product',
            dataIndex: 'product',
            key: 'product',
            render: product => product.label
          },
          {
            title: 'Quantity',
            dataIndex: 'quantity',
            key: 'quantity',
            align: 'right'
          },
          {
            title: 'Unit Price',
            dataIndex: 'price',
            key: 'price',
            align: 'right',
            render: price => numberFormat(price)
          },
          {
            title: 'Total',
            key: 'total',
            align: 'right',
            render: (_, record) => numberFormat((record.price || 0) * (record.quantity || 0))
          }
        ]}
        summary={() => (
          <Table.Summary>
            <Table.Summary.Row>
              <Table.Summary.Cell colSpan={3} index={0}>
                <Text strong>Total</Text>
              </Table.Summary.Cell>
              <Table.Summary.Cell index={1} align="right">
                <Text strong>{numberFormat(statistics.totalValue)}</Text>
              </Table.Summary.Cell>
            </Table.Summary.Row>
          </Table.Summary>
        )}
      />

      {data.reason && (
        <>
          <Divider />
          <Title level={5}>Return Reason</Title>
          <Paragraph>{data.reason}</Paragraph>
        </>
      )}

      <div style={{ textAlign: 'right', marginTop: 20 }}>
        <Text type="secondary">Generated on {moment().format('MMMM Do YYYY, h:mm:ss a')}</Text>
      </div>
    </div>
  );

  return (
    <>
      <PageHeader
        className="site-page-header-responsive"
        onBack={() => window.history.back()}
        title={`Stock Return #${data._id}`}
        subTitle={formatDate(data.date)}
        tags={[
          <Tag color="volcano" key="return">
            <RollbackOutlined /> Stock Return
          </Tag>
        ]}
        extra={[
          <PrintComponents
            key="print"
            trigger={
              <Button icon={<PrinterOutlined />} type="primary">
                Print Stock Return
              </Button>
            }
          >
            <PrintableReturn />
          </PrintComponents>,
        ]}
      />

      <div style={{ padding: "0 24px 24px" }}>
        {loading ? (
          <Row gutter={[24, 24]}>
            <Col span={24}>
              <Skeleton active paragraph={{ rows: 6 }} />
            </Col>
          </Row>
        ) : (
          <Row gutter={[24, 24]}>
            <Col xs={24} lg={8}>
              <Card
                title={<><InfoCircleOutlined /> Return Information</>}
                className="stock-card"
              >
                <Descriptions column={1} size="small" bordered>
                  <Descriptions.Item label="Return ID">{data._id}</Descriptions.Item>
                  <Descriptions.Item label="Date">{formatDate(data.date)}</Descriptions.Item>
                  {data.entrant && (
                    <Descriptions.Item label="Created By">{data.entrant.label}</Descriptions.Item>
                  )}
                </Descriptions>

                {supplier && (
                  <>
                    <Divider orientation="left">Supplier</Divider>
                    <div style={{ display: 'flex', alignItems: 'center', marginBottom: 16 }}>
                      <Avatar
                        icon={<ShopOutlined />}
                        style={{ backgroundColor: '#722ed1', marginRight: 8 }}
                      />
                      <Text strong>{supplier.name}</Text>
                    </div>
                    {supplier.phone && (
                      <div className="info-item">
                        <Text type="secondary">Phone:</Text> {supplier.phone}
                      </div>
                    )}
                    {supplier.email && (
                      <div className="info-item">
                        <Text type="secondary">Email:</Text> {supplier.email}
                      </div>
                    )}
                    {supplier.address && (
                      <div className="info-item">
                        <Text type="secondary">Address:</Text> {supplier.address}
                      </div>
                    )}
                    <div style={{ marginTop: 16 }}>
                      <Button
                        type="link"
                        onClick={() => window.location.href = `#/inventory/suppliers/${supplier._id}`}
                        style={{ padding: 0 }}
                      >
                        View Supplier Details
                      </Button>
                    </div>
                  </>
                )}

                {data.reason && (
                  <>
                    <Divider orientation="left">Return Reason</Divider>
                    <Paragraph>{data.reason}</Paragraph>
                  </>
                )}
              </Card>

              <Card
                title={<><DollarOutlined /> Return Summary</>}
                className="stock-card"
                style={{ marginTop: 16 }}
              >
                <Statistic
                  title="Total Items"
                  value={statistics.totalItems}
                  prefix={<ShoppingCartOutlined />}
                  style={{ marginBottom: 16 }}
                />
                <Statistic
                  title="Total Value"
                  value={numberFormat(statistics.totalValue)}
                  prefix={<DollarOutlined />}
                />
              </Card>
            </Col>

            <Col xs={24} lg={16}>
              <Card
                title={<><ShoppingCartOutlined /> Returned Items</>}
                className="stock-card"
              >
                {data.items && data.items.length > 0 ? (
                  <Table
                    dataSource={data.items}
                    rowKey={(record, index) => index}
                    pagination={false}
                    columns={[
                      {
                        title: 'Product',
                        dataIndex: 'product',
                        key: 'product',
                        render: product => (
                          <Space>
                            <Avatar
                              shape="square"
                              size="small"
                              icon={<ShoppingCartOutlined />}
                              style={{ backgroundColor: '#1890ff' }}
                            />
                            <Tooltip title={`View product details: ${product.label}`}>
                              <a href={`#/inventory/products/${product.value}`}>{product.label}</a>
                            </Tooltip>
                          </Space>
                        )
                      },
                      {
                        title: 'Quantity',
                        dataIndex: 'quantity',
                        key: 'quantity',
                        align: 'right',
                        width: '15%'
                      },
                      {
                        title: 'Unit Price',
                        dataIndex: 'price',
                        key: 'price',
                        align: 'right',
                        width: '20%',
                        render: price => numberFormat(price || 0)
                      },
                      {
                        title: 'Total',
                        key: 'total',
                        align: 'right',
                        width: '20%',
                        render: (_, record) => numberFormat((record.price || 0) * (record.quantity || 0))
                      }
                    ]}
                    summary={() => (
                      <Table.Summary>
                        <Table.Summary.Row>
                          <Table.Summary.Cell colSpan={3} index={0}>
                            <Text strong>Total</Text>
                          </Table.Summary.Cell>
                          <Table.Summary.Cell index={1} align="right">
                            <Text strong>{numberFormat(statistics.totalValue)}</Text>
                          </Table.Summary.Cell>
                        </Table.Summary.Row>
                      </Table.Summary>
                    )}
                  />
                ) : (
                  <Empty description="No items in this return" />
                )}
              </Card>
            </Col>
          </Row>
        )}
      </div>
    </>
  );
};

export default ReturnedStock;