import "./a4.css";
import PrintComponents from "react-print-components";
import React from "react";
import RenderBlob from "../../../../../Components/RenderBlob";
import moment from "moment";
import { FloatButton } from "antd";
import { PrinterOutlined } from "@ant-design/icons";
import { numberFormat } from "../../../../../Utils/functions";


const Receipt = ({ company, data, documentTitle }) => {
  const Subtotal = data.invoice.items.reduce((acc, item) => acc + item.price * item.quantity, 0);
  const amountReceived = data.amountReceived || 0;
  const balance = Subtotal - amountReceived;

  return (
    <div className="tm_invoice_wrap">
      <div className="tm_invoice tm_style1">
        <div className="tm_invoice_in">
          <center>
            <RenderBlob blob={company.logo} size={150} />
            <p className="tm_f12">
              {company.address}<br />
              Tel: {company.phone}<br />
              Email: {company.email}
            </p>
          </center>

          <div className="tm_invoice_info">
            <div>
              <strong style={{ fontSize: "30px" }}>{documentTitle}</strong>
              <p className="tm_invoice_number">
                Receipt No: <b>{data.id}</b>
              </p>
              <p className="tm_invoice_date">
                Date: <b>{moment(data.date).format("DD MMM YYYY")}</b>
              </p>
            </div>
          </div>

          <div className="tm_table tm_style">
            <table>
              <thead>
                <tr>
                  <th>Item</th>
                  <th>Price</th>
                  <th>Qty</th>
                  <th style={{ textAlign: "right" }}>Total</th>
                </tr>
              </thead>
              <tbody>
                {data.invoice.items.map((item, index) => (
                  <tr key={index}>
                    <td>{item.product.label.split("-")[0]}</td>
                    <td>{item.quantity}</td>
                    <td>{numberFormat(item.price)}</td>
                    <td style={{ textAlign: "right" }}>{numberFormat(item.price * item.quantity)}</td>
                  </tr>
                ))}
              </tbody>
            </table>

            <div className="tm_invoice_footer">
              <div className="tm_right_footer">
                <table>
                  <tbody>
                    <tr>
                      <td>Subtotal:</td>
                      <td>{numberFormat(Subtotal)}</td>
                    </tr>
                    <tr>
                      <td>Amount Received:</td>
                      <td>{numberFormat(amountReceived)}</td>
                    </tr>
                    <tr style={{ fontWeight: "bold" }}>
                      <td>Balance Due:</td>
                      <td>{numberFormat(balance)}</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

const A4 = (props) => {
  return (
    <>
      <PrintComponents
        trigger={
          <FloatButton icon={<PrinterOutlined />} tooltip={<div>Print</div>} />
        }
      >
        <Receipt {...props} />
      </PrintComponents>
      <Receipt {...props} />
    </>
  );
};

export default A4;