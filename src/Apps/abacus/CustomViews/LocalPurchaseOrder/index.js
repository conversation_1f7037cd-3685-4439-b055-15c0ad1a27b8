import "../Invoice/css.css"; // Import consistent styling
import "./styles.css";
import DocumentFooter from "../DocumentFooter";
import PrintComponents from "react-print-components";
import React, { useState, useEffect } from "react";
import RenderBlob from "../../../../Components/RenderBlob";
import moment from "moment";
import { Card, Row, Col, Typography, Descriptions, Table, Tag, Button, Space, Avatar, Skeleton, Badge, Tooltip, Statistic } from "antd";
import { Flex, Divider } from "antd";
import { PageHeader } from "@ant-design/pro-components";
import { ShoppingCartOutlined, DollarOutlined, UserOutlined, CalendarOutlined, PrinterOutlined, ShopOutlined, InfoCircleOutlined, FileTextOutlined } from "@ant-design/icons";
import { numberFormat } from "../../../../Utils/functions";


const { Title, Text } = Typography;

const LocalPurchaseOrder = ({ data, documentTitle = "Local Purchase Order", pouchDatabase, databasePrefix }) => {
  const [loading, setLoading] = useState(true);
  const [supplier, setSupplier] = useState(null);
  const [company, setCompany] = useState(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        // Fetch supplier data
        if (data && data.supplier) {
          try {
            const supplierDoc = await pouchDatabase("suppliers", databasePrefix).getDocument(data.supplier.value);
            setSupplier(supplierDoc);
          } catch (error) {
            console.error("Error fetching supplier:", error);
          }
        }

        // Fetch company data
        try {
          const organizationsData = await pouchDatabase("organizations", databasePrefix).getAllData();
          if (organizationsData && organizationsData.length > 0) {
            let comp = organizationsData[0];

            if (comp._attachments && comp._attachments.logo) {
              try {
                const logoBlob = await pouchDatabase("organizations", databasePrefix).getAttachment(comp._id, "logo");
                comp.logo = logoBlob;
                setCompany(comp);
              } catch (attachmentError) {
                console.error("Error fetching logo attachment:", attachmentError);
                setCompany(comp);
              }
            } else {
              setCompany(comp);
            }
          }
        } catch (error) {
          console.error("Error fetching organization:", error);
        }
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [data, pouchDatabase, databasePrefix]);

  if (!data || loading) {
    return <Skeleton active />;
  }

  const getStatusTag = (status) => {
    const statusMap = {
      draft: { color: "default", text: "Draft" },
      approved: { color: "processing", text: "Approved" },
      pending: { color: "warning", text: "Pending" },
      converted: { color: "success", text: "Converted to Stock" },
      cancelled: { color: "error", text: "Cancelled" }
    };

    const { color, text } = statusMap[status] || { color: "default", text: status };
    return <Tag color={color}>{text}</Tag>;
  };

  // Calculate totals
  const subtotal = data.items?.reduce((sum, item) => sum + (item.quantity * item.price), 0) || 0;

  const columns = [
    {
      title: "Product",
      dataIndex: "product",
      key: "product",
      render: (product) => product?.label || "-",
    },
    {
      title: "Quantity",
      dataIndex: "quantity",
      key: "quantity",
      align: "right",
    },
    {
      title: "Unit Price",
      dataIndex: "price",
      key: "price",
      align: "right",
      render: (price) => numberFormat(price),
    },
    {
      // Expected delivery date moved to general fields
    },
    {
      title: "Total",
      key: "total",
      align: "right",
      render: (_, record) => numberFormat((record.quantity || 0) * (record.price || 0)),
    },
  ];

  const PrintableContent = () => {
    if (!company || !supplier) return null;

    // Calculate empty rows for consistent table height
    const emptyRows = [];
    const minRows = 10;
    const currentRows = data.items ? data.items.length : 0;
    for (let i = currentRows; i < minRows; i++) {
      emptyRows.push({});
    }

    return (
      // <div className="tm_invoice_wrap" style={{ padding: 0, margin: 0 }}>
      //   <div className="tm_invoice tm_style1" id="tm_download_section" style={{ padding: 20, margin: 0, border: 'none', borderRadius: 0 }}>
      //     <div className="tm_invoice_in">
            <div>
            {/* Header with Logo and Company Info - Same as Invoice */}
            <Flex vertical={false} justify="space-between" align="flex-start" style={{ marginBottom: 20 }}>
              <div style={{ flex: 1 }}>
                <RenderBlob blob={company.logo} size={200} />
              </div>
              <div style={{ flex: 1, textAlign: 'right' }}>
                <p className="tm_f12">
                <strong className="tm_f15">
                  {company.name}
                </strong>
                <br />
                  {company.address}
                  <br />
                  Tel: {company.phone} / {company.alternative_phone} <br />
                  Email: {company.email} <br />
                  {company.website && company.website}
                </p>
              </div>
            </Flex>

            {/* Supplier Info and Document Title - Same layout as Invoice */}
            <Flex vertical={false} justify="space-between" style={{ marginBottom: 20 }}>
              <div className="tm_mb2 tm_f10">
                <p className="tm_mb2">
                  <b className="tm_primary_color">To:</b>
                </p>
                <p>
                  <b style={{ fontSize: 15 }}>{supplier.name}</b>
                  <br />
                  {supplier.phone}{" "}
                  {supplier.alternative_phone &&
                    `/ ${supplier.alternative_phone}`}{" "}
                  <br />
                  {supplier.email && (
                    <>
                      {supplier.email} <br />
                    </>
                  )}
                  {supplier.address && (
                    <>
                      {supplier.address} <br />
                    </>
                  )}
                  {supplier._id && (
                    <>
                      Supplier ID : <strong>{supplier._id} <br /></strong>
                    </>
                  )}
                </p>
              </div>
              <div style={{ textAlign: 'right' }}>
                <strong style={{ fontSize: 30, fontWeight: "bold" }}>
                  LOCAL PURCHASE ORDER
                </strong>
                <p className="tm_invoice_number tm_m0 tm_f12">
                  LPO No: <b className="tm_primary_color">{data.lpo_number}</b>
                </p>
                <p className="tm_invoice_date tm_m0">
                  Date:{" "}
                  <b className="tm_primary_color">
                    {moment(data.date).format("DD MMM YYYY")}
                  </b>
                </p>
                {data.expected_delivery_date && (
                  <p className="tm_invoice_date tm_m0">
                    Expected Delivery:{" "}
                    <b className="tm_primary_color">
                      {moment(data.expected_delivery_date).format("DD MMM YYYY")}
                    </b>
                  </p>
                )}
              </div>
            </Flex>

            {/* Items Table */}
            <div className="tm_table tm_style" style={{ marginBottom: 20 }}>
              <div className="tm_border">
                <div className="main">
                  <table style={{ width: '100%' }}>
                    <thead>
                      <tr>
                        <th className="tm_width_1 tm_semi_bold tm_primary_color tm_gray_bg tm_text_center">
                          S/N
                        </th>
                        <th className="tm_width_3 tm_semi_bold tm_primary_color tm_gray_bg">
                          Description
                        </th>
                        <th className="tm_width_1 tm_semi_bold tm_primary_color tm_gray_bg tm_text_left">
                          Qty
                        </th>
                        <th className="tm_width_1 tm_semi_bold tm_primary_color tm_gray_bg tm_text_right">
                          Rate
                        </th>
                        <th className="tm_width_1 tm_semi_bold tm_primary_color tm_gray_bg tm_text_right">
                          Total
                        </th>
                      </tr>
                    </thead>
                    <tbody className="tm_border_none" style={{ border: "none", fontSize: 10 }}>
                      {data.items && data.items.map((item, index) => (
                        <tr className="main" key={`item-${index}`}>
                          <td className="tm_width_1 tm_text_center">
                            {index + 1}
                          </td>
                          <td className="tm_width_3">
                            {item.product?.label || item.description || 'Item'}
                          </td>
                          <td className="tm_width_1 tm_text_left">
                            {item.quantity}
                          </td>
                          <td className="tm_width_1 tm_text_right">
                            {numberFormat(item.price)}
                          </td>
                          <td className="tm_width_1 tm_text_right">
                            {numberFormat(item.price * item.quantity)}
                          </td>
                        </tr>
                      ))}
                      {emptyRows.map((_, index) => (
                        <tr className="main" key={`empty-row-${index}`}>
                          <td className="tm_width_1 tm_text_center">
                            &nbsp;
                          </td>
                          <td className="tm_width_3"></td>
                          <td className="tm_width_1 tm_text_left"></td>
                          <td className="tm_width_1 tm_text_right"></td>
                          <td className="tm_width_1 tm_text_right"></td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>

            {/* Totals */}
            <Flex vertical={false} justify="space-between" style={{ marginTop: 20 }}>
              <div>
                {data.notes && (
                  <div>
                    <p className="tm_mb2 tm_f11">
                      <b className="tm_primary_color">Notes:</b>
                    </p>
                    <p className="tm_f10">{data.notes}</p>
                  </div>
                )}
              </div>
              <div style={{ minWidth: 200 }}>
                <table className="tm_border">
                  <tbody>
                    <tr>
                      <td className="tm_width_3 tm_primary_color tm_border_none tm_bold">
                        Subtotal
                      </td>
                      <td className="tm_width_3 tm_primary_color tm_text_right tm_border_none tm_bold">
                        {numberFormat(subtotal)}
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </Flex>

            {/* Signatures */}
            <div style={{ marginTop: 40 }}>
              <Flex justify="space-between">
                <div style={{ textAlign: 'center', minWidth: 200 }}>
                  <div style={{ borderBottom: '1px solid #000', marginBottom: 5, height: 40 }}></div>
                  <p className="tm_f10">Prepared By: {data.preparedBy?.label || 'N/A'}</p>
                </div>
                <div style={{ textAlign: 'center', minWidth: 200 }}>
                  <div style={{ borderBottom: '1px solid #000', marginBottom: 5, height: 40 }}></div>
                  <p className="tm_f10">Approved By: {data.approvedBy?.label || 'N/A'}</p>
                </div>
              </Flex>
            </div>

            {/* Document Footer */}
            <DocumentFooter />
            </div>
      //     </div>
      //   </div>
      // </div>
    );
  };

  return (
    <div className="local-purchase-order-view">
      <PageHeader
        title={
          <Space>
            <FileTextOutlined /> {documentTitle}
          </Space>
        }
        subTitle={`#${data.lpo_number}`}
        extra={[
          <PrintComponents
            key="print"
            trigger={
              <Button icon={<PrinterOutlined />} type="primary">
                Print
              </Button>
            }
          >
            <PrintableContent />
          </PrintComponents>,
        ]}
      />

      <Card>
        <Row gutter={[24, 24]}>
          <Col xs={24} sm={12}>
            <Descriptions title="LPO Information" column={1} bordered>
              <Descriptions.Item label="LPO Number">{data.lpo_number}</Descriptions.Item>
              <Descriptions.Item label="Date">
                {moment(data.date).format("DD MMM YYYY")}
              </Descriptions.Item>
              <Descriptions.Item label="Expected Delivery">
                {data.expected_delivery_date ? moment(data.expected_delivery_date).format("DD MMM YYYY") : "Not specified"}
              </Descriptions.Item>
              <Descriptions.Item label="Status">
                {getStatusTag(data.status)}
              </Descriptions.Item>
              {data.branch && (
                <Descriptions.Item label="Branch">{data.branch}</Descriptions.Item>
              )}
            </Descriptions>
          </Col>
          <Col xs={24} sm={12}>
            <Descriptions title="Supplier Information" column={1} bordered>
              <Descriptions.Item label="Supplier">
                {supplier?.name || data.supplier?.label || "N/A"}
              </Descriptions.Item>
              {supplier?.phone && (
                <Descriptions.Item label="Phone">{supplier.phone}</Descriptions.Item>
              )}
              {supplier?.email && (
                <Descriptions.Item label="Email">{supplier.email}</Descriptions.Item>
              )}
              {supplier?.address && (
                <Descriptions.Item label="Address">{supplier.address}</Descriptions.Item>
              )}
            </Descriptions>
          </Col>
        </Row>

        <Row gutter={[24, 24]} style={{ marginTop: 24 }}>
          <Col span={24}>
            <Card title="Items" bordered={false}>
              <Table
                columns={columns}
                dataSource={data.items || []}
                pagination={false}
                rowKey={(record) => record.product?.value || Math.random().toString()}
                summary={() => (
                  <Table.Summary>
                    <Table.Summary.Row>
                      <Table.Summary.Cell index={0} colSpan={4} align="right">
                        <strong>Subtotal:</strong>
                      </Table.Summary.Cell>
                      <Table.Summary.Cell index={1} align="right">
                        <strong>{numberFormat(subtotal)}</strong>
                      </Table.Summary.Cell>
                    </Table.Summary.Row>
                  </Table.Summary>
                )}
              />
            </Card>
          </Col>
        </Row>

        {data.notes && (
          <Row gutter={[24, 24]} style={{ marginTop: 24 }}>
            <Col span={24}>
              <Card title="Notes" bordered={false}>
                <p>{data.notes}</p>
              </Card>
            </Col>
          </Row>
        )}

        <Row gutter={[24, 24]} style={{ marginTop: 24 }}>
          <Col span={24}>
            <Card title="Approval Information" bordered={false}>
              <Row gutter={16}>
                <Col span={12}>
                  <Descriptions column={1} bordered>
                    <Descriptions.Item label="Prepared By">
                      {data.preparedBy?.label || "N/A"}
                    </Descriptions.Item>
                    {data.preparedAt && (
                      <Descriptions.Item label="Prepared At">
                        {moment(data.preparedAt).format("DD MMM YYYY HH:mm")}
                      </Descriptions.Item>
                    )}
                  </Descriptions>
                </Col>
                <Col span={12}>
                  <Descriptions column={1} bordered>
                    <Descriptions.Item label="Approved By">
                      {data.approvedBy?.label || "N/A"}
                    </Descriptions.Item>
                    {data.approvedAt && (
                      <Descriptions.Item label="Approved At">
                        {moment(data.approvedAt).format("DD MMM YYYY HH:mm")}
                      </Descriptions.Item>
                    )}
                  </Descriptions>
                </Col>
              </Row>
            </Card>
          </Col>
        </Row>
      </Card>
    </div>
  );
};

export default LocalPurchaseOrder;