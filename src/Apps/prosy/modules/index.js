import peopleModule from "../modulesProperties/people";
import { buffPeople } from "../modulesProperties/utils";
import { complaints } from "./complaints";
import { expense_categories } from "./expense_categories";
import { expenses } from "./expenses";
import { follow_ups } from "./follow_ups";
import { invoices } from "./invoices";
import { mobile_money_in } from "./mobile_money_in";
import { mobile_money_out } from "./mobile_money_out";
import { occupancy_invoices } from "./occupancy_invoices";
import { occupancy_receipts } from "./occupancy_receipts";
import { occupations } from "./occupations";
import { people } from "./people";
import { petty_cash_in } from "./petty_cash_in";
import { petty_cash_out } from "./petty_cash_out";
import { properties } from "./properties";
import { receipts } from "./receipts";
import { special_receipts } from "./special_receipts";
import { tenancy_history } from "./tenancy_history";
import { units } from "./units";


export const modules = {
  occupations: occupations,
  tenancy_history: tenancy_history,
  invoices: invoices,
  receipts: receipts,
  special_receipts: special_receipts,
  people: people,
  landlords: {
    name: "Landlords",
    description: "Landlords",
    icon: "UserOutlined",
    path: "/landlords",
    collection: "people",
    singular: "Landlord",
    columns: [
      {
        initialValue: "landlord",
        title: "Category",
        dataIndex: "category",
        valueEnum: {
          tenant: {
            text: "Tenant",
          },
          landlord: {
            text: "Landlord",
          },
          broker: {
            text: "Broker",
          },
          police_officer: {
            text: "Police Officer",
          },
          customer: {
            text: "Customer",
          },
          supplier: {
            text: "Supplier",
          },
          other: {
            text: "Other",
          },
        },
        valueType: "select",
        sorter: true,
        isRequired: true,
        filters: true,
        onFilter: true,
      },
      {
        title: "Name",
        dataIndex: "name",
        valueType: "text",
        sorter: true,
        isRequired: true,
      },

      {
        title: "Email",
        dataIndex: "email",
        copyable: true,
        type: "email",
        hideInTable: true,
      },
      {
        title: "Phone",
        dataIndex: "phone",
        sorter: true,
        type: "phone",
        isRequired: true,
      },
      {
        title: "Mobile",
        dataIndex: "mobile",
        sorter: true,
        type: "phone",
        hideInTable: true,
      },
      {
        title: "ID Type",
        dataIndex: "id_type",
        initialValue: "National ID",
        filters: true,
        onFilter: true,
        valueType: "select",
        hideInTable: true,
        valueEnum: {
          "National ID": {
            text: "National ID",
          },
          "Driving Permit": {
            text: "Driving Permit",
          },
          Passport: {
            text: "Passport",
          },
        },
      },
      {
        title: "ID Number",
        dataIndex: "id_number",
        sorter: true,
        valueType: "text",
        hideInTable: true,
      },
      {
        title: "Address",
        dataIndex: "address",
        sorter: true,
        valueType: "text",
      },
      {
        title: "Balance",
        dataIndex: "currentBalance",
        valueType: "money",
        hideInForm: true,
        sorter: true,
        render: (_, record) => {
          const balance = record.currentBalance || 0;
          return (
            <span style={{
              color: balance >= 0 ? '#52c41a' : '#f5222d',
              fontWeight: 'bold'
            }}>
              {balance.toLocaleString()}
            </span>
          );
        },
      },
      // Bank Details Group
      {
        title: "Bank Details",
        valueType: "group",
        hideInTable: true,
        colProps: {
          md: 24,
        },
        columns: [
          {
            title: "Bank Name",
            dataIndex: "bank_name",
            valueType: "text",
            hideInTable: true,
            colProps: {
              md: 8,
            },
          },
          {
            title: "Account Name",
            dataIndex: "account_name",
            hideInTable: true,
            valueType: "text",
            colProps: {
              md: 8,
            },
          },
          {
            title: "Account Number",
            dataIndex: "account_number",
            hideInTable: true,
            valueType: "text",
            copyable: true,
            colProps: {
              md: 8,
            },
          },
          {
            title: "Status",
            dataIndex: "status",
            valueType: "select",
            hideInTable: true,
            valueEnum: {
              Active: {
                text: "Active",
              },
              Inactive: {
                text: "Inactive",
              },
            },
            colProps: {
              md: 8,
            },
          },
        ],
      },
    ],
    filterOptions: {
      dataIndex: "category",
      direction: "in",
      value: ["landlord"],
    },
    // Add landlord-specific statistics
    statistics: async function landlordsStatistics(pouchDatabase, databasePrefix = '', SELECTED_BRANCH) {
      console.log('[landlords.statistics] Calculating landlord-specific statistics');
      try {
        if (!pouchDatabase) {
          console.error("pouchDatabase is required for landlord statistics");
          return [
            { title: "Total Landlords", value: 0 },
            { title: "Total Advance", value: 0, prefix: "UGX " },
            { title: "Total Balance", value: 0, prefix: "UGX " }
          ];
        }

        // Load all required data
        const [peopleData] = await Promise.all([pouchDatabase("people", databasePrefix).getAllData()]);

        return buffPeople(peopleData.filter(person => person.category === 'landlord'), pouchDatabase, databasePrefix, 'people', SELECTED_BRANCH)
          .then(landlords => {
            if (landlords.length === 0) {
              return [
                { title: "Total Landlords", value: 0 },
                { title: "Total Advance", value: 0, prefix: "UGX " },
                { title: "Total Balance", value: 0, prefix: "UGX " }
              ];
            }
            else {
              return [
                { title: "Total Landlords", value: landlords.length, valueStyle: { color: '#722ed1' } },
                { title: "Total Advance", value: landlords.reduce((acc, landlord) => acc + (landlord.currentBalance < 0 ? landlord.currentBalance : 0), 0), prefix: "UGX " },
                { title: "Total Balance", value: landlords.reduce((acc, landlord) => acc + (landlord.currentBalance > 0 ? landlord.currentBalance : 0), 0), prefix: "UGX " }
              ]
            }

          });
      } catch (error) {
        console.error('[landlords.statistics] Error:', error);
        return [
          { title: "Total Landlords", value: 0 },
          { title: "Total Advance", value: 0, prefix: "UGX " },
          { title: "Total Balance", value: 0, prefix: "UGX " }
        ];
      }
    },
    // Use the same buffResults as people module for landlord balance calculations
    buffResults: peopleModule.buffResults,
  },
  properties: properties,
  units: units,
  expenses: expenses,
  expense_categories: expense_categories,
  complaints: complaints,
  follow_ups: follow_ups,
  occupancy_invoices: occupancy_invoices,
  occupancy_receipts: occupancy_receipts,
  mobile_money_in: mobile_money_in,
  mobile_money_out: mobile_money_out,
  petty_cash_in: petty_cash_in,
  petty_cash_out: petty_cash_out,

  // Add a finance parent menu if it doesn't exist elsewhere
  expenditure: {
    name: "Expenditure",
    icon: "DollarOutlined",
    path: "/expenditure",
    columns: [],
  },
  mobile_money: {
    name: "Mobile Money Tracker",
    icon: "BankOutlined",
    path: "/mobile_money",
    columns: [],
  },
  petty_cash: {
    name: "Petty Cash Tracker",
    icon: "WalletOutlined",
    path: "/petty_cash",
    columns: [],
  },
  finance: {
    name: "Finance",
    icon: "BankOutlined",
    path: "/finance",
    columns: [],
  },
};
