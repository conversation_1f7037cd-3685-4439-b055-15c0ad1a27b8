import AppDatabase from "../../../Utils/AppDatabase";
import numeral from "numeral";
import performanceMonitor, { trackDataProcessing, trackDatabaseQuery, endTracking } from "../utils/PerformanceMonitor";


const dataCache = new Map();
const CACHE_TTL = 30000; // 30 seconds cache

const getCachedData = async (key, fetcher) => {
  const cached = dataCache.get(key);
  if (cached && Date.now() - cached.timestamp < CACHE_TTL) {
    return cached.data;
  }

  const data = await fetcher();
  dataCache.set(key, { data, timestamp: Date.now() });

  // Limit cache size to prevent memory leaks
  if (dataCache.size > 20) {
    const oldestKey = dataCache.keys().next().value;
    dataCache.delete(oldestKey);
  }

  return data;
};

export const buffUnits = async (results, database, databasePrefix, collection, SELECTED_BRANCH) => {
  // PERFORMANCE FIX: Early return for empty results
  if (!results || results.length === 0) {
    return [];
  }

  console.log(`🔧 [buffUnits] Processing ${results.length} units with optimized caching`);

  try {
    // PERFORMANCE FIX: Use cached data with optimized queries
    const cacheKey = `units_data_${databasePrefix}`;
    const data = await getCachedData(cacheKey, async () => {
      // CRITICAL FIX: Handle different database parameter types
      let dbConnection;

      if (typeof database === 'function') {
        // database is a function like AppDatabase or pouchDatabase
        console.log('[buffUnits] Using provided database function');
        dbConnection = database;
      } else if (database && typeof database === 'object') {
        // database is already a database instance - use AppDatabase function instead
        console.log('[buffUnits] Received database instance, using AppDatabase function');
        dbConnection = AppDatabase;
      } else {
        // Fallback to AppDatabase
        console.log('[buffUnits] Using AppDatabase fallback');
        dbConnection = AppDatabase;
      }

      // Additional safety check
      if (typeof dbConnection !== 'function') {
        console.error('[buffUnits] dbConnection is not a function, using AppDatabase');
        dbConnection = AppDatabase;
      }

      const [tenants, properties, occupations] = await Promise.all([
        dbConnection("people", databasePrefix).getAllData(),
        dbConnection("properties", databasePrefix).getAllData(),
        dbConnection("occupations", databasePrefix).getAllData()
      ]);

      // Pre-filter active occupations to reduce processing
      const activeOccupations = occupations.filter(o => !o.dateOut);

      return { tenants, properties, occupations: activeOccupations };
    });

    // PERFORMANCE FIX: Use Map for O(1) lookups instead of find()
    const propertiesMap = new Map(data.properties.map(p => [p._id, p]));
    const occupationsMap = new Map();

    // Build occupations map by unit ID for faster lookups
    data.occupations.forEach(o => {
      if (o.unit && o.unit.value) {
        occupationsMap.set(o.unit.value, o);
      }
    });

    return results.map((unit) => {
      const property = propertiesMap.get(unit.property?.value);
      const occupation = occupationsMap.get(unit._id);

      return {
        ...unit,
        property,
        occupation,
        occupied: !!occupation
      };
    });

  } catch (error) {
    console.error('🚨 [buffUnits] Error:', error);
    return results; // Return original results on error
  }
}


/**
 * PERFORMANCE OPTIMIZED: buffOccupations function with connection reuse and batch processing
 /**
  * Implements efficient data fetching with proper async/await patterns
 */

export const buffOccupations = async (results, database, databasePrefix, collection, SELECTED_BRANCH) => {
  // PERFORMANCE FIX: Early return for empty results
  if (!results || results.length === 0) {
    return [];
  }

  console.log(`🔧 [buffOccupations] Processing ${results.length} occupations with database prefix: ${databasePrefix}`);

  // PERFORMANCE FIX: Use connection pooling and limit data fetching
  const allTenants = AppDatabase("people", databasePrefix);
  const allUnits = AppDatabase("units", databasePrefix);
  const allProperties = AppDatabase("properties", databasePrefix);
  const allInvoices = AppDatabase("invoices", databasePrefix);
  const allReceipts = AppDatabase("receipts", databasePrefix);
  const allExpenses = AppDatabase("expenses", databasePrefix);

  console.log(`🔍 buffOccupations - Processing ${results.length} occupations`);

  // PERFORMANCE FIX: Fetch data with error handling to prevent crashes
  const fetchDataSafely = async (db, name) => {
    try {
      return await db.getAllData();
    } catch (error) {
      console.warn(`⚠️ Error fetching ${name} data:`, error);
      return [];
    }
  };

  // PERFORMANCE FIX: Enhanced data fetching with error handling for buffUnits
  const fetchUnitsWithBuffing = async () => {
    try {
      const unitsData = await fetchDataSafely(allUnits, 'units');
      return await buffUnits(unitsData, AppDatabase, databasePrefix, 'units', SELECTED_BRANCH);
    } catch (error) {
      console.error('🚨 [buffOccupations] Error in buffUnits:', error);
      // Return raw units data if buffing fails
      return await fetchDataSafely(allUnits, 'units');
    }
  };

  const ddata = await Promise.all([
    fetchDataSafely(allTenants, 'people'),
    fetchUnitsWithBuffing(),
    fetchDataSafely(allProperties, 'properties'),
    fetchDataSafely(allInvoices, 'invoices'),
    fetchDataSafely(allReceipts, 'receipts'),
    fetchDataSafely(allExpenses, 'expenses'),
  ]);

  let totalRent = 0,
    totalAdvance = 0,
    totalArrears = 0;

  const [
    dbPeople,
    units,
    properties,
    dbInvoices,
    dbReceipts,
    expenses,
  ] = ddata;

  const occupations = results;

  // CRITICAL PERFORMANCE FIX: Create lookup maps for O(1) access instead of O(n) find operations
  console.log('🔧 Creating lookup maps for performance optimization...');
  const peopleMap = new Map(dbPeople.map(person => [person._id, person]));
  const unitsMap = new Map(units.map(unit => [unit._id, unit]));
  const propertiesMap = new Map(properties.map(property => [property._id, property]));

  // Create invoice and receipt lookup maps by occupancy
  const invoicesByOccupancy = new Map();
  const receiptsByOccupancy = new Map();

  dbInvoices.forEach(invoice => {
    if (invoice.occupancy?.value) {
      if (!invoicesByOccupancy.has(invoice.occupancy.value)) {
        invoicesByOccupancy.set(invoice.occupancy.value, []);
      }
      invoicesByOccupancy.get(invoice.occupancy.value).push(invoice);
    }
  });

  dbReceipts.forEach(receipt => {
    if (receipt.occupancy?.value) {
      if (!receiptsByOccupancy.has(receipt.occupancy.value)) {
        receiptsByOccupancy.set(receipt.occupancy.value, []);
      }
      receiptsByOccupancy.get(receipt.occupancy.value).push(receipt);
    }
  });

  console.log(`🚀 Processing ${occupations.length} occupations with optimized lookup maps...`);

  const TheData =
    occupations &&
    occupations.map((doc, index) => {
      // Enhanced ID extraction with fallback strategies
      const tenantId = doc?.tenant?.value || doc?.tenant?._id || doc?.tenant?.key || doc?.tenant;
      const unitId = doc?.unit?.value || doc?.unit?._id || doc?.unit?.key || doc?.unit;

      // PERFORMANCE FIX: Use Map.get() instead of Array.find() for O(1) lookup
      const tenant = tenantId ? peopleMap.get(tenantId) : null;
      const unit = unitId ? unitsMap.get(unitId) : null;

      if (!tenant || !unit) {
        console.warn(`⚠️ buffOccupations - Missing data for occupation ${doc._id}:`, {
          tenantFound: !!tenant,
          unitFound: !!unit,
          tenantId,
          unitId
        });
        // Return the original document if we can't find tenant or unit
        return doc;
      }

      const propertyId = unit?.property?.value || unit?.property?._id || unit?.property?.key || unit?.property;
      const property = propertyId ? propertiesMap.get(propertyId) : null;

      const landlordId = property?.landlord?.value || property?.landlord?._id || property?.landlord?.key || property?.landlord;
      const landlord = landlordId ? peopleMap.get(landlordId) : null;

      // PERFORMANCE FIX: Use lookup maps instead of filter operations
      const invoices = invoicesByOccupancy.get(doc._id) || [];
      const receipts = receiptsByOccupancy.get(doc._id) || [];




      const totalInvoices = invoices.reduce(
        (total, row) => total + row.amount,
        0
      );
      const totalRentInvoices = invoices
        .filter((i) => i.invoice_type === "Rent")
        .reduce((total, row) => total + row.amount, 0);
      const totalReceipts = receipts.reduce(
        (total, row) => total + row.amount,
        0
      );
      const totalRentReceipts = receipts
        .filter((i) => i.receipt_type === "Rent")
        .reduce((total, row) => total + row.amount, 0);

      totalRent += unit && unit.rent ? unit.rent : 0;
      totalArrears +=
        totalInvoices - totalReceipts > 0 ? totalInvoices - totalReceipts : 0;
      totalAdvance +=
        totalInvoices - totalReceipts <= 0 ? totalInvoices - totalReceipts : 0;

      // Ensure tenant has proper reference structure
      const tenantReference = tenant ? {
        value: tenant._id,
        label: tenant.name,
        name: tenant.name,
        _id: tenant._id,
        ...tenant
      } : (typeof doc.tenant === 'string' ? {
        value: doc.tenant,
        label: doc.tenant,
        name: doc.tenant
      } : doc.tenant);

      let rowToPush = {
        ...doc,
        unit: { ...doc.unit, ...unit }, //unit,
        landlord: landlord && landlord.name,
        tenant: tenantReference,
        arrears:
          totalInvoices - totalReceipts > 0 ? totalInvoices - totalReceipts : 0,
        advance:
          totalInvoices - totalReceipts <= 0
            ? totalInvoices - totalReceipts
            : 0,
        key: unit && unit._id,
        landlord_id: landlord && landlord._id,
        property_id: property && property._id,
        manager: property && property.manager && property.manager.label,
        receipts: receipts,
        invoices: invoices,
        rent: unit ? unit.rent : 0,
        phone_numbers: tenant && `${tenant.phone} ${tenant.mobile ? ` | ${tenant.mobile}` : ""}`
      };
      // if (category === "Rent") {
      rowToPush.months = numeral(
        (totalRentInvoices - totalRentReceipts) / (unit && unit.rent ? unit.rent : 1)
      ).format("0.00");
      rowToPush.balance = totalInvoices - totalReceipts;
      rowToPush.rent_balance = totalRentInvoices - totalRentReceipts;
      // }
      return rowToPush;
    });

  const rentCollection = (ddata[6] ? ddata[6] : []).map((receipt) => {
    const receiptOccupancy = occupations.find((u) => u._id === receipt.tenant);
    const receiptUnit = ddata[2].find((u) => u._id === receiptOccupancy.unit);
    const receiptProperty = ddata[3].find(
      (u) => u._id === receiptUnit.property_id
    );
    const receiptLandlord = ddata[4].find(
      (u) => u._id === receiptProperty.landlord
    );

    return {
      ...receipt,
      landlord: receiptProperty.landlord,
      property: receiptProperty._id,
    };
  });

  console.log("thebuffeddata", TheData);

  return TheData;
};

/**
 * LEGACY FALLBACK: Keep original implementation as fallback
 * This will be removed once the optimized version is stable
 */
const buffOccupationsLegacy = async (results, database, databasePrefix, collection, SELECTED_BRANCH) => {
  const allTenants = AppDatabase("people", databasePrefix);
  const allUnits = AppDatabase("units", databasePrefix);
  const allProperties = AppDatabase("properties", databasePrefix);
  const allInvoices = AppDatabase("invoices", databasePrefix);
  const allReceipts = AppDatabase("receipts", databasePrefix);
  const allExpenses = AppDatabase("expenses", databasePrefix);

  // PERFORMANCE FIX: Fetch data with error handling to prevent crashes
  const fetchDataSafely = async (db, name) => {
    try {
      return await db.getAllData();
    } catch (error) {
      console.warn(`⚠️ Error fetching ${name} data:`, error);
      return [];
    }
  };

  const ddata = await Promise.all([
    fetchDataSafely(allTenants, 'people'),
    buffUnits(await fetchDataSafely(allUnits, 'units'), database, databasePrefix, 'units', SELECTED_BRANCH),
    fetchDataSafely(allProperties, 'properties'),
    fetchDataSafely(allInvoices, 'invoices'),
    fetchDataSafely(allReceipts, 'receipts'),
    fetchDataSafely(allExpenses, 'expenses'),
  ]);

  let totalRent = 0,
    totalAdvance = 0,
    totalArrears = 0;

  const [
    dbPeople,
    units,
    properties,
    dbInvoices,
    dbReceipts,
    expenses,
  ] = ddata;

  const occupations = results;

  // CRITICAL PERFORMANCE FIX: Create lookup maps for O(1) access instead of O(n) find operations
  console.log('🔧 Creating lookup maps for performance optimization...');
  const peopleMap = new Map(dbPeople.map(person => [person._id, person]));
  const unitsMap = new Map(units.map(unit => [unit._id, unit]));
  const propertiesMap = new Map(properties.map(property => [property._id, property]));

  // Create invoice and receipt lookup maps by occupancy
  const invoicesByOccupancy = new Map();
  const receiptsByOccupancy = new Map();

  dbInvoices.forEach(invoice => {
    if (invoice.occupancy?.value) {
      if (!invoicesByOccupancy.has(invoice.occupancy.value)) {
        invoicesByOccupancy.set(invoice.occupancy.value, []);
      }
      invoicesByOccupancy.get(invoice.occupancy.value).push(invoice);
    }
  });

  dbReceipts.forEach(receipt => {
    if (receipt.occupancy?.value) {
      if (!receiptsByOccupancy.has(receipt.occupancy.value)) {
        receiptsByOccupancy.set(receipt.occupancy.value, []);
      }
      receiptsByOccupancy.get(receipt.occupancy.value).push(receipt);
    }
  });

  console.log(`🚀 Processing ${occupations.length} occupations with optimized lookup maps...`);

  // PERFORMANCE FIX: Process in smaller chunks to prevent UI blocking
  const processOccupationChunk = (doc, index) => {
    // Enhanced ID extraction with fallback strategies
    const tenantId = doc?.tenant?.value || doc?.tenant?._id || doc?.tenant?.key || doc?.tenant;
    const unitId = doc?.unit?.value || doc?.unit?._id || doc?.unit?.key || doc?.unit;

    // PERFORMANCE FIX: Use Map.get() instead of Array.find() for O(1) lookup
    const tenant = tenantId ? peopleMap.get(tenantId) : null;
    const unit = unitId ? unitsMap.get(unitId) : null;

    if (!tenant || !unit) {
      console.warn(`⚠️ buffOccupations - Missing data for occupation ${doc._id}:`, {
        tenantFound: !!tenant,
        unitFound: !!unit,
        tenantId,
        unitId
      });
      // Return the original document if we can't find tenant or unit
      return doc;
    }

    const propertyId = unit?.property?.value || unit?.property?._id || unit?.property?.key || unit?.property;
    const property = propertyId ? propertiesMap.get(propertyId) : null;

    const landlordId = property?.landlord?.value || property?.landlord?._id || property?.landlord?.key || property?.landlord;
    const landlord = landlordId ? peopleMap.get(landlordId) : null;

    // PERFORMANCE FIX: Use lookup maps instead of filter operations
    const invoices = invoicesByOccupancy.get(doc._id) || [];
    const receipts = receiptsByOccupancy.get(doc._id) || [];

    const totalInvoices = invoices.reduce(
      (total, row) => total + row.amount,
      0
    );
    const totalRentInvoices = invoices
      .filter((i) => i.invoice_type === "Rent")
      .reduce((total, row) => total + row.amount, 0);
    const totalReceipts = receipts.reduce(
      (total, row) => total + row.amount,
      0
    );
    const totalRentReceipts = receipts
      .filter((i) => i.receipt_type === "Rent")
      .reduce((total, row) => total + row.amount, 0);

    totalRent += unit && unit.rent ? unit.rent : 0;
    totalArrears +=
      totalInvoices - totalReceipts > 0 ? totalInvoices - totalReceipts : 0;
    totalAdvance +=
      totalInvoices - totalReceipts <= 0 ? totalInvoices - totalReceipts : 0;

    // Ensure tenant has proper reference structure
    const tenantReference = tenant ? {
      value: tenant._id,
      label: tenant.name,
      name: tenant.name,
      _id: tenant._id,
      ...tenant
    } : (typeof doc.tenant === 'string' ? {
      value: doc.tenant,
      label: doc.tenant,
      name: doc.tenant
    } : doc.tenant);

    let rowToPush = {
      ...doc,
      unit: { ...doc.unit, ...unit }, //unit,
      landlord: landlord && landlord.name,
      tenant: tenantReference,
      arrears:
        totalInvoices - totalReceipts > 0 ? totalInvoices - totalReceipts : 0,
      advance:
        totalInvoices - totalReceipts <= 0
          ? totalInvoices - totalReceipts
          : 0,
      key: unit && unit._id,
      landlord_id: landlord && landlord._id,
      property_id: property && property._id,
      manager: property && property.manager && property.manager.label,
      receipts: receipts,
      invoices: invoices,
      rent: unit ? unit.rent : 0,
      phone_numbers: tenant && `${tenant.phone} ${tenant.mobile ? ` | ${tenant.mobile}` : ""}`
    };
    // if (category === "Rent") {
    rowToPush.months = numeral(
      (totalRentInvoices - totalRentReceipts) / (unit && unit.rent ? unit.rent : 1)
    ).format("0.00");
    rowToPush.balance = totalInvoices - totalReceipts;
    rowToPush.rent_balance = totalRentInvoices - totalRentReceipts;
    // }
    return rowToPush;
  };

  // PERFORMANCE FIX: Process occupations in chunks to prevent UI blocking
  const TheData = [];
  const CHUNK_SIZE = 20; // Process 20 records at a time

  for (let i = 0; i < occupations.length; i += CHUNK_SIZE) {
    const chunk = occupations.slice(i, i + CHUNK_SIZE);
    const processedChunk = chunk.map(processOccupationChunk);
    TheData.push(...processedChunk);

    // Yield control every chunk to prevent UI blocking
    if (i + CHUNK_SIZE < occupations.length) {
      await new Promise(resolve => setTimeout(resolve, 0));
    }
  }

  console.log(`✅ [buffOccupations] Processed ${TheData.length} occupations in chunks`);

  return TheData;
};


/**
 * Process a batch of occupation records
 */
const processBatch = async (batch, getConnection) => {
  try {
    // Batch data fetching using PouchDB
    const fetchDataFromPouchDB = async (dbName) => {
      try {
        const pouchDb = getConnection(dbName);
        const result = await pouchDb.allDocs({ include_docs: true, binary: true });
        return result.rows
          .filter(r => r.doc && !r.doc._id.startsWith('_'))
          .map(r => r.doc);
      } catch (error) {
        console.warn(`[buffOccupations] Error fetching data from ${dbName}:`, error);
        return [];
      }
    };

    // Parallel data fetching for better performance
    const [
      dbPeople,
      rawUnits,
      properties,
      rawOccupations,
      dbInvoices,
      dbReceipts
    ] = await Promise.all([
      fetchDataFromPouchDB("people"),
      fetchDataFromPouchDB("units"),
      fetchDataFromPouchDB("properties"),
      fetchDataFromPouchDB("occupations"),
      fetchDataFromPouchDB("invoices"),
      fetchDataFromPouchDB("receipts")
    ]);

    // Create lookup maps for O(1) access instead of O(n) find operations
    const peopleMap = new Map(dbPeople.map(person => [person._id, person]));
    const propertiesMap = new Map(properties.map(property => [property._id, property]));
    const occupationsMap = new Map(rawOccupations.filter(o => !o.dateOut).map(occ => [occ.unit?.value || occ.unit?._id, occ]));

    // Optimize units with occupancy data using lookup maps
    const units = rawUnits.map(unit => {
      const occupation = occupationsMap.get(unit._id);
      const property = propertiesMap.get(unit.property?.value || unit.property?._id);

      return {
        ...unit,
        property: property || unit.property,
        occupation: occupation || null
      };
    });

    const unitsMap = new Map(units.map(unit => [unit._id, unit]));

    // Create invoice and receipt lookup maps by occupancy
    const invoicesByOccupancy = new Map();
    const receiptsByOccupancy = new Map();

    // Batch process invoices and receipts
    dbInvoices.forEach(invoice => {
      if (invoice.occupancy?.value) {
        if (!invoicesByOccupancy.has(invoice.occupancy.value)) {
          invoicesByOccupancy.set(invoice.occupancy.value, []);
        }
        invoicesByOccupancy.get(invoice.occupancy.value).push(invoice);
      }
    });

    dbReceipts.forEach(receipt => {
      if (receipt.occupancy?.value) {
        if (!receiptsByOccupancy.has(receipt.occupancy.value)) {
          receiptsByOccupancy.set(receipt.occupancy.value, []);
        }
        receiptsByOccupancy.get(receipt.occupancy.value).push(receipt);
      }
    });

    // Process occupations with optimized lookups
    const processedData = batch?.map((doc) => {
      // Use lookup maps for O(1) access
      const tenant = doc?.tenant?.value ? peopleMap.get(doc.tenant.value) : null;
      const unit = doc?.unit?.value ? unitsMap.get(doc.unit.value) : null;

      const property = unit?.property?.value ?
        propertiesMap.get(unit.property.value) :
        (unit?.property?._id ? propertiesMap.get(unit.property._id) : null);

      const landlord = property?.landlord?.value ?
        peopleMap.get(property.landlord.value) :
        (property?.landlord?._id ? peopleMap.get(property.landlord._id) : null);

      // Get invoices and receipts using lookup maps
      const invoices = invoicesByOccupancy.get(doc._id) || [];
      const receipts = receiptsByOccupancy.get(doc._id) || [];

      // Calculate totals efficiently
      const totalInvoices = invoices.reduce((total, row) => total + (Number(row.amount) || 0), 0);
      const totalRentInvoices = invoices
        .filter(i => i.invoice_type === "Rent")
        .reduce((total, row) => total + (Number(row.amount) || 0), 0);
      const totalReceipts = receipts.reduce((total, row) => total + (Number(row.amount) || 0), 0);
      const totalRentReceipts = receipts
        .filter(i => i.receipt_type === "Rent")
        .reduce((total, row) => total + (Number(row.amount) || 0), 0);

      const balance = totalInvoices - totalReceipts;
      const rentBalance = totalRentInvoices - totalRentReceipts;

      // Ensure tenant has proper reference structure
      const tenantReference = tenant ? {
        value: tenant._id,
        label: tenant.name,
        name: tenant.name,
        _id: tenant._id,
        ...tenant
      } : (typeof doc.tenant === 'string' ? {
        value: doc.tenant,
        label: doc.tenant,
        name: doc.tenant
      } : doc.tenant);

      return {
        ...doc,
        unit: { ...doc.unit, ...unit },
        landlord: landlord?.name,
        tenant: tenantReference,
        arrears: balance > 0 ? balance : 0,
        advance: balance <= 0 ? balance : 0,
        key: unit?._id,
        landlord_id: landlord?._id,
        property_id: property?._id,
        manager: property?.manager?.label,
        receipts: receipts,
        invoices: invoices,
        rent: unit?.rent,
        months: numeral(rentBalance / (unit?.rent || 1)).format("0.00"),
        balance: balance,
        rent_balance: rentBalance
      };
    });

    return processedData || [];
  } catch (error) {
    console.error('[buffOccupations] Error processing batch:', error);
    return batch; // Return original batch on error
  }
};

/**
 * Fallback implementation using PouchDB (original logic)
 */
const buffOccupationsFallback = async (results, database, databasePrefix, collection, SELECTED_BRANCH) => {
  const allTenants = AppDatabase("people", databasePrefix);
  const allUnits = AppDatabase("units", databasePrefix);
  const allProperties = AppDatabase("properties", databasePrefix);
  const allInvoices = AppDatabase("invoices", databasePrefix);
  const allReceipts = AppDatabase("receipts", databasePrefix);

  const ddata = await Promise.all([
    await allTenants.getAllData(),
    buffUnits(await allUnits.getAllData(), database, databasePrefix, 'units', SELECTED_BRANCH),
    await allProperties.getAllData(),
    await allInvoices.getAllData(),
    await allReceipts.getAllData()
  ]);

  const [dbPeople, units, properties, dbInvoices, dbReceipts] = ddata;
  const occupations = results;

  return occupations?.map((doc) => {
    const tenant = doc?.tenant && dbPeople.find(t => t._id === doc.tenant.value);
    const unit = units.find(u => u._id === (doc.unit?.value));
    const property = properties.find(p => {
      if (unit?.property && (p._id === unit.property.value || p._id === unit.property._id)) {
        return true;
      }
    });
    const landlord = dbPeople.find(l => {
      if (property && (l._id === property.landlord.value || l._id === property.landlord._id)) {
        return true;
      }
    });

    const invoices = dbInvoices.filter(i => i.occupancy?.value === doc._id);
    const receipts = dbReceipts.filter(i => i.occupancy?.value === doc._id);

    const totalInvoices = invoices.reduce((total, row) => total + (Number(row.amount) || 0), 0);
    const totalRentInvoices = invoices
      .filter(i => i.invoice_type === "Rent")
      .reduce((total, row) => total + (Number(row.amount) || 0), 0);
    const totalReceipts = receipts.reduce((total, row) => total + (Number(row.amount) || 0), 0);
    const totalRentReceipts = receipts
      .filter(i => i.receipt_type === "Rent")
      .reduce((total, row) => total + (Number(row.amount) || 0), 0);

    const balance = totalInvoices - totalReceipts;
    const rentBalance = totalRentInvoices - totalRentReceipts;

    // Ensure tenant has proper reference structure
    const tenantReference = tenant ? {
      value: tenant._id,
      label: tenant.name,
      name: tenant.name,
      _id: tenant._id,
      ...tenant
    } : (typeof doc.tenant === 'string' ? {
      value: doc.tenant,
      label: doc.tenant,
      name: doc.tenant
    } : doc.tenant);

    return {
      ...doc,
      unit: { ...doc.unit, ...unit },
      landlord: landlord?.name,
      tenant: tenantReference,
      arrears: balance > 0 ? balance : 0,
      advance: balance <= 0 ? balance : 0,
      key: unit?._id,
      landlord_id: landlord?._id,
      property_id: property?._id,
      manager: property?.manager?.label,
      receipts: receipts,
      invoices: invoices,
      rent: unit?.rent,
      months: numeral(rentBalance / (unit?.rent || 1)).format("0.00"),
      balance: balance,
      rent_balance: rentBalance
    };
  }) || [];
};

export const buffProperties = async (data, database, databasePrefix, collection, SELECTED_BRANCH) => {
  // CRITICAL FIX: Use the passed database parameter (pouchDatabase) instead of AppDatabase
  const dbConnection = database || AppDatabase;

  const propertiesDb = dbConnection("properties", databasePrefix);
  const unitsDb = dbConnection("units", databasePrefix);
  const occupancyDb = dbConnection("occupations", databasePrefix);

  const [allProperties, allUnits, occupancy] = await Promise.all([
    await propertiesDb.getAllData(),
    await unitsDb.getAllData(),
    (await occupancyDb.getAllData()).filter((h) => !h.dateOut),
  ]);

  return data.map((p) => {
    //total rent
    const totals = allUnits
      .filter((el) => el.property.value === p._id)
      .reduce((p, c) => [p[0] + c.rent, p[1] + 1], [0, 0]);

    return {
      ...p,
      totalRent: totals[0],
      MGTFees: totals[0] * (p.management_percentage / 100),
      totalUnits: totals[1],
    };
  });
};


// Optimized helper function to calculate landlord balance
const calculateLandlordBalance = (landlord, context) => {
  const {
    landlordPropertiesMap,
    landlordUnitsMap,
    landlordOccupationsMap,
    landlordTenancyHistoryMap,
    validReceipts,
    validExpenses,
    validInvoices,
    propertyLookup,
    unitLookup,
    occupationLookup,
    calculateLandlordPortion
  } = context;

  const landlordId = landlord._id;
  const landlordProperties = landlordPropertiesMap.get(landlordId) || [];
  const landlordUnits = landlordUnitsMap.get(landlordId) || [];
  const landlordOccupations = landlordOccupationsMap.get(landlordId) || [];
  const landlordTenancyHistory = landlordTenancyHistoryMap.get(landlordId) || [];

  // Create efficient lookup sets
  const propertyIds = new Set(landlordProperties.map(p => p._id));
  const unitIds = new Set(landlordUnits.map(u => u._id));
  const activeOccupationIds = new Set(landlordOccupations.map(o => o._id));
  const historicalOccupationIds = new Set(
    landlordTenancyHistory.map(h => h.original_occupation_id).filter(Boolean)
  );

  let lifetimeIncome = 0;
  let lifetimeExpenses = 0;

  // Process receipts with better number validation - match statement logic
  for (const receipt of validReceipts) {
    if (activeOccupationIds.has(receipt.occupancy?.value) || historicalOccupationIds.has(receipt.occupancy?.value)) {
      const receiptAmount = Number(receipt.amount) || 0;
      if (!isNaN(receiptAmount)) {
        // Find the occupation to get the unit and property
        const occupation = occupationLookup.get(receipt.occupancy?.value);
        if (occupation) {
          const unit = unitLookup.get(occupation.unit?.value);
          if (unit) {
            const property = propertyLookup.get(unit.property?.value);
            if (property) {
              // Apply same logic as statement: security deposits don't have management fees
              const landlordPortion = receipt.receipt_type === 'Security Deposit'
                ? receiptAmount
                : calculateLandlordPortion(receiptAmount, property.management_percentage || 0);
              lifetimeIncome += landlordPortion;
            }
          }
        }
      }
    }
  }

  // Process expenses with better number validation  
  for (const expense of validExpenses) {
    const expenseAmount = Number(expense.amount) || 0;
    if (!isNaN(expenseAmount)) {
      if (expense.property?.value && propertyIds.has(expense.property.value)) {
        lifetimeExpenses += expenseAmount;
      } else if (expense.unit?.value && unitIds.has(expense.unit.value)) {
        lifetimeExpenses += expenseAmount;
      } else if (expense.supplier?.value === landlordId) {
        lifetimeExpenses += expenseAmount;
      }
    }
  }

  // Process invoices with better number validation
  for (const invoice of validInvoices) {
    if (invoice.client.value === landlordId) {
      const invoiceAmount = Number(invoice.amount) || 0;
      if (!isNaN(invoiceAmount)) {
        if (invoiceAmount < 0) {
          lifetimeIncome += Math.abs(invoiceAmount);
        } else if (invoiceAmount > 0) {
          lifetimeExpenses += invoiceAmount;
        }
      }
    }
  }

  // Ensure final values are valid numbers
  lifetimeIncome = Number(lifetimeIncome) || 0;
  lifetimeExpenses = Number(lifetimeExpenses) || 0;
  const currentBalance = lifetimeIncome - lifetimeExpenses;

  return {
    ...landlord,
    lifetimeIncome: Math.round(lifetimeIncome),
    lifetimeExpenses: Math.round(lifetimeExpenses),
    currentBalance: Math.round(currentBalance),
    totalProperties: landlordProperties.length,
    totalUnits: landlordUnits.length,
    activeOccupations: landlordOccupations.filter(o => !o.dateOut).length
  };
};


export const buffPeople = async (results, database, databasePrefix, collection, SELECTED_BRANCH) => {
  const timerId = trackDataProcessing('buffPeople', {
    count: results.length,
    databasePrefix
  });

  try {
    // Early return if no landlords to process
    const landlords = results.filter(person => person.category === 'landlord');
    if (landlords.length === 0) {
      endTracking(timerId, { landlordCount: 0, skipped: true });
      console.log('[people.buffResults] No landlords found, returning original results');
      return results;
    }

    console.log(`[people.buffResults] Found ${landlords.length} landlords to process with optimized caching`);

    // PERFORMANCE FIX: Use cached data for all collections
    const cacheKey = `people_data_${databasePrefix}`;
    const allData = await getCachedData(cacheKey, async () => {
      console.log(`📊 [buffPeople] Loading fresh data from database...`);

      // Use AppDatabase function directly
      const [receiptsData, expensesData, invoicesData, propertiesData, unitsData, occupationsData, tenancyHistoryData] = await Promise.all([
        AppDatabase("receipts", databasePrefix).getAllData(),
        AppDatabase("expenses", databasePrefix).getAllData(),
        AppDatabase("invoices", databasePrefix).getAllData(),
        AppDatabase("properties", databasePrefix).getAllData(),
        AppDatabase("units", databasePrefix).getAllData(),
        AppDatabase("occupations", databasePrefix).getAllData(),
        AppDatabase("tenancy_history", databasePrefix).getAllData()
      ]);

      return {
        receipts: receiptsData,
        expenses: expensesData,
        invoices: invoicesData,
        properties: propertiesData,
        units: unitsData,
        occupations: occupationsData,
        tenancyHistory: tenancyHistoryData
      };
    });

    console.log(`[people.buffResults] Loaded data: ${allData.receipts.length} receipts, ${allData.expenses.length} expenses, ${allData.invoices.length} invoices`);

    // Pre-filter and index data for better performance
    const validReceipts = allData.receipts.filter(r =>
      r.date && r.occupancy?.value && !r.is_reversed && !r.is_reversal && r.show_on_landlord_statement !== false && r.receipt_type !== 'Utility'
    );

    const validExpenses = allData.expenses.filter(e =>
      (e.expense_Date || e.date) && (e.property?.value || e.unit?.value || e.supplier?.value) && e.offset === true
    );

    const validInvoices = allData.invoices.filter(i =>
      i.client?.value && i.amount !== undefined
    );

    // Create lookup maps for O(1) access
    const propertyLookup = new Map(allData.properties.map(p => [p._id, p]));
    const unitLookup = new Map(allData.units.map(u => [u._id, u]));
    const occupationLookup = new Map(allData.occupations.map(o => [o._id, o]));

    // Group data by landlord for efficient processing
    const landlordPropertiesMap = new Map();
    const landlordUnitsMap = new Map();
    const landlordOccupationsMap = new Map();
    const landlordTenancyHistoryMap = new Map();

    // Pre-compute landlord data relationships
    landlords.forEach(landlord => {
      const landlordId = landlord._id;

      // Get landlord's properties
      const landlordProperties = allData.properties.filter(p => p.landlord?.value === landlordId);
      landlordPropertiesMap.set(landlordId, landlordProperties);

      // Get landlord's units
      const propertyIds = new Set(landlordProperties.map(p => p._id));
      const landlordUnits = allData.units.filter(u => u.property && propertyIds.has(u.property.value));
      landlordUnitsMap.set(landlordId, landlordUnits);

      // Get landlord's occupations
      const unitIds = new Set(landlordUnits.map(u => u._id));
      const landlordOccupations = allData.occupations.filter(o => o.unit && unitIds.has(o.unit.value));
      landlordOccupationsMap.set(landlordId, landlordOccupations);

      // Get landlord's tenancy history
      const landlordTenancyHistory = allData.tenancyHistory.filter(h => h.unit && unitIds.has(h.unit.value));
      landlordTenancyHistoryMap.set(landlordId, landlordTenancyHistory);
    });

    // Optimized landlord portion calculation
    const calculateLandlordPortion = (amount, managementPercentage = 0) =>
      amount * (1 - managementPercentage / 100);

    // Process each person (landlords get enhanced data, others pass through)
    const processedResults = results.map(person => {
      if (person.category !== 'landlord') {
        return person;
      }

      return calculateLandlordBalance(person, {
        landlordPropertiesMap,
        landlordUnitsMap,
        landlordOccupationsMap,
        landlordTenancyHistoryMap,
        validReceipts,
        validExpenses,
        validInvoices,
        propertyLookup,
        unitLookup,
        occupationLookup,
        calculateLandlordPortion
      });
    });

    endTracking(timerId, {
      landlordCount: landlords.length,
      processedCount: processedResults.length
    });

    console.log(`[people.buffResults] Successfully processed ${landlords.length} landlords`);

    return processedResults;

  } catch (error) {
    endTracking(timerId, { error: error.message });
    console.error('[people.buffResults] Error calculating landlord balances:', error);
    return results;
  }
}

const UtilsModuleProperties = {
  buffUnits,
  buffOccupations,
  buffProperties,
  buffPeople
};

export default UtilsModuleProperties;
