

 /**
  * Safely formats an account object or string into a displayable string
 * Handles various formats that might be used in the application
 * 
 * @param {Object|string} account - The account object or string
 * @returns {string} - A string representation of the account
 */
export const formatAccountName = (account) => {
  if (!account) return 'Unknown Account';
  
  // If account is a string, return it directly
  if (typeof account === 'string') return account;
  
  // If account is an object, try to extract a displayable name
  if (typeof account === 'object') {
    // First try common display properties
    if (account.label) return account.label;
    if (account.name) return account.name;
    
    // Then try to get the name from the value property if it's a string
    if (account.value && typeof account.value === 'string') return account.value;
    
    // If account has an _id, use that as a last resort
    if (account._id) return `Account #${account._id}`;
  }
  
  // If all else fails, return a generic label
  return 'Unknown Account';
};
