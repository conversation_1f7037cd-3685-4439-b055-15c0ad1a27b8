import "./css.css";
import PouchDB from "pouchdb";
import PrintComponents from "react-print-components";
import React, { useEffect, useState } from "react";
import RenderBlob from "../../../../Components/RenderBlob";
import moment from "moment";
import { Descriptions, Divider, Layout, Typography, Tag, Button, Flex, FloatButton, Card, Row, Col, Statistic, Space, } from "antd";
import { DollarOutlined, PrinterOutlined, ShopOutlined, CalendarOutlined, FileTextOutlined, BankOutlined, CarOutlined, } from "@ant-design/icons";
import { PageHeader } from "@ant-design/pro-components";
import { numberFormat } from "../../../../Utils/functions";


const { Content } = Layout;
const { Title, Text, Paragraph } = Typography;

const Expense = (props) => {
  const { data } = props;
  const [company, setCompany] = useState(null);
  const [supplier, setSupplier] = useState(null);
  const [category, setCategory] = useState(null);
  const [job, setJob] = useState(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        // Fetch company info
        const settingsDB = new PouchDB("settings");
        const settingsResults = await settingsDB.allDocs({
          include_docs: true,
          limit: 1,
        });
        if (settingsResults.rows.length > 0) {
          setCompany(settingsResults.rows[0].doc);
        }

        // Fetch supplier info if available
        if (data.supplier && data.supplier.value) {
          const suppliersDB = new PouchDB("suppliers");
          try {
            const supplierDoc = await suppliersDB.get(data.supplier.value);
            setSupplier(supplierDoc);
          } catch (error) {
            console.error("Error fetching supplier:", error);
          }
        }

        // Fetch category info if available
        if (data["expense-category"] && data["expense-category"].value) {
          const categoriesDB = new PouchDB("expense_categories");
          try {
            const categoryDoc = await categoriesDB.get(data["expense-category"].value);
            setCategory(categoryDoc);
          } catch (error) {
            console.error("Error fetching category:", error);
          }
        }

        // Fetch job info if available
        if (data.job && data.job.value) {
          const jobsDB = new PouchDB("jobs");
          try {
            const jobDoc = await jobsDB.get(data.job.value);
            setJob(jobDoc);
          } catch (error) {
            console.error("Error fetching job:", error);
          }
        }
      } catch (error) {
        console.error("Error fetching data:", error);
      }
    };

    fetchData();
  }, [data]);

  // Get color for payment method tag
  const getPaymentMethodColor = (method) => {
    switch (method) {
      case "Cash":
        return "green";
      case "Credit Card":
        return "blue";
      case "Cheque":
        return "purple";
      case "MTN Mobile Money":
        return "orange";
      case "Airtel Mobile Money":
        return "red";
      case "Bank Deposit":
        return "cyan";
      default:
        return "default";
    }
  };

  // Printable expense component
  const PrintableExpense = () => (
    <div style={{ padding: "20px", maxWidth: "800px", margin: "0 auto" }}>
      {company && (
        <Flex align="center" justify="space-between" style={{ marginBottom: "20px" }}>
          <div>
            <RenderBlob blob={company.logo} size={150} />
          </div>
          <div style={{ textAlign: "center" }}>
            <Title level={2}>EXPENSE VOUCHER</Title>
            <Text>Voucher ID: {data._id}</Text>
            <br />
            <Text>Date: {moment(data.date).format("DD MMM YYYY")}</Text>
          </div>
          <div style={{ textAlign: "right" }}>
            <Text>
              {company.address}
              <br />
              Tel: {company.phone}
              <br />
              Email: {company.email}
            </Text>
          </div>
        </Flex>
      )}

      <Divider />

      <Title level={4}>Expense Details</Title>
      <Descriptions column={2} bordered>
        <Descriptions.Item label="Date">
          {moment(data.date).format("DD MMM YYYY")}
        </Descriptions.Item>
        <Descriptions.Item label="Amount">
          {numberFormat(data.amount)}
        </Descriptions.Item>
        <Descriptions.Item label="Supplier">
          {supplier ? supplier.name : data.supplier?.label || "N/A"}
        </Descriptions.Item>
        <Descriptions.Item label="Payment Method">
          {data.method_of_payment || "N/A"}
        </Descriptions.Item>
        <Descriptions.Item label="Category" span={2}>
          {category ? category.name : data["expense-category"]?.label || "N/A"}
        </Descriptions.Item>
        <Descriptions.Item label="Description" span={2}>
          {data.description || "No description provided"}
        </Descriptions.Item>
      </Descriptions>

      {job && (
        <>
          <Divider />
          <Title level={4}>Related Job</Title>
          <Descriptions column={2} bordered>
            <Descriptions.Item label="Job ID">{job._id}</Descriptions.Item>
            <Descriptions.Item label="Customer">
              {job.customer ? job.customer.label : ""}
            </Descriptions.Item>
            <Descriptions.Item label="Vehicle">
              {job.vehicle ? job.vehicle.label : ""}
            </Descriptions.Item>
          </Descriptions>
        </>
      )}

      <Divider />

      <div className="expense-signature-section">
        <Row gutter={48}>
          <Col span={12}>
            <div style={{ textAlign: "center" }}>
              <div style={{ borderTop: "1px solid #000", marginTop: "50px", paddingTop: "5px" }}>
                Prepared By
              </div>
            </div>
          </Col>
          <Col span={12}>
            <div style={{ textAlign: "center" }}>
              <div style={{ borderTop: "1px solid #000", marginTop: "50px", paddingTop: "5px" }}>
                Approved By
              </div>
            </div>
          </Col>
        </Row>
      </div>
    </div>
  );

  return (
    <>
      <PageHeader
        backIcon={<DollarOutlined style={{ fontSize: 30 }} />}
        onBack={() => window.history.back()}
        title="Expense Voucher"
        subTitle={`#${data._id}`}
        tags={<Tag color={getPaymentMethodColor(data.method_of_payment)}>{data.method_of_payment}</Tag>}
        extra={[
          <PrintComponents
            key="print"
            trigger={
              <Button icon={<PrinterOutlined />} type="primary" id="print-button">
                Print Voucher
              </Button>
            }
          >
            <PrintableExpense />
          </PrintComponents>,
        ]}
      />

      <Content style={{ padding: "0 24px 24px" }}>
        <Row gutter={[24, 24]}>
          <Col xs={24} md={8}>
            <Card className="expense-card">
              <Statistic
                title="Amount"
                value={data.amount}
                precision={2}
                valueStyle={{ color: '#3f8600' }}
                prefix={<DollarOutlined />}
                suffix=""
              />
            </Card>
          </Col>
          <Col xs={24} md={8}>
            <Card className="expense-card">
              <Statistic
                title="Date"
                value={moment(data.date).format("DD MMM YYYY")}
                valueStyle={{ color: '#1890ff' }}
                prefix={<CalendarOutlined />}
              />
            </Card>
          </Col>
          <Col xs={24} md={8}>
            <Card className="expense-card">
              <Statistic
                title="Payment Method"
                value={data.method_of_payment}
                valueStyle={{ color: '#722ed1' }}
                prefix={<BankOutlined />}
              />
            </Card>
          </Col>
        </Row>

        <Row gutter={[24, 24]} style={{ marginTop: 24 }}>
          <Col xs={24} lg={12}>
            <Card
              title={<><ShopOutlined /> Supplier</>}
              className="expense-card"
            >
              <Descriptions column={1} bordered>
                <Descriptions.Item label="Name">
                  {supplier ? supplier.name : data.supplier?.label || "N/A"}
                </Descriptions.Item>
                {supplier && (
                  <>
                    <Descriptions.Item label="Contact">
                      {supplier.phone || "N/A"}
                    </Descriptions.Item>
                    <Descriptions.Item label="Email">
                      {supplier.email || "N/A"}
                    </Descriptions.Item>
                  </>
                )}
              </Descriptions>
            </Card>
          </Col>

          <Col xs={24} lg={12}>
            <Card
              title={<><FileTextOutlined /> Expense Details</>}
              className="expense-card"
            >
              <Descriptions column={1} bordered>
                <Descriptions.Item label="Category">
                  {category ? category.name : data["expense-category"]?.label || "N/A"}
                </Descriptions.Item>
                <Descriptions.Item label="Description">
                  {data.description || "No description provided"}
                </Descriptions.Item>
              </Descriptions>
            </Card>
          </Col>
        </Row>

        {job && (
          <Row gutter={[24, 24]} style={{ marginTop: 24 }}>
            <Col xs={24}>
              <Card
                title={<><CarOutlined /> Related Job</>}
                className="expense-card"
              >
                <Descriptions column={{ xxl: 3, xl: 3, lg: 3, md: 2, sm: 1, xs: 1 }} bordered>
                  <Descriptions.Item label="Job ID">{job._id}</Descriptions.Item>
                  <Descriptions.Item label="Customer">
                    {job.customer ? job.customer.label : ""}
                  </Descriptions.Item>
                  <Descriptions.Item label="Vehicle">
                    {job.vehicle ? job.vehicle.label : ""}
                  </Descriptions.Item>
                </Descriptions>
              </Card>
            </Col>
          </Row>
        )}
      </Content>

      <FloatButton
        icon={<PrinterOutlined />}
        type="primary"
        tooltip="Print Voucher"
        onClick={() => document.getElementById("print-button").click()}
        style={{ right: 94 }}
      />
    </>
  );
};

export default Expense;
