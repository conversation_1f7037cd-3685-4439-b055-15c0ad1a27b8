/* Expense Custom View Styles */

.expense-card {
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  height: 100%;
}

.expense-card .ant-card-head {
  background-color: #fafafa;
  border-bottom: 1px solid #f0f0f0;
}

.expense-card .ant-card-head-title {
  font-weight: 600;
}

.expense-statistic {
  padding: 20px;
  text-align: center;
}

.expense-statistic .ant-statistic-title {
  font-size: 16px;
  margin-bottom: 8px;
}

.expense-statistic .ant-statistic-content {
  font-size: 24px;
  font-weight: bold;
}

/* Print styles */
@media print {
  .expense-print-section {
    padding: 0;
  }
  
  .ant-descriptions-bordered .ant-descriptions-item-label,
  .ant-descriptions-bordered .ant-descriptions-item-content {
    padding: 12px 16px;
  }
  
  .ant-divider {
    margin: 16px 0;
  }
  
  .ant-typography {
    margin-bottom: 8px;
  }
  
  .expense-signature-section {
    margin-top: 40px;
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .ant-descriptions-item {
    padding-bottom: 8px;
  }
}
