import React from "react";
import moment from "moment";
import { BankOutlined, CalendarOutlined, ArrowUpOutlined, ArrowDownOutlined } from "@ant-design/icons";
import { Card, Descriptions, Tag, Row, Col, Divider, Typography, Alert } from "antd";
import { formatNumber } from "../../../../Utils/functions";


const { Title, Text } = Typography;

const Details = (reconciliation) => {

  

  const getStatusColor = (status) => {
    const colors = {
      completed: 'success',
      pending: 'warning',
      cancelled: 'error'
    };
    return colors[status] || 'default';
  };

  const isPositive = Number(reconciliation.amount) >= 0;

  return (
    reconciliation && <Card>
      <Row gutter={[24, 24]}>
        <Col span={24}>
          <div style={{ textAlign: 'center', marginBottom: 24 }}>
            <Title level={3}>Account Reconciliation</Title>
            <Text type="secondary">ID: {reconciliation._id}</Text>
          </div>
        </Col>

        <Col span={24}>
          <Card
            style={{
              background: '#f5f5f5',
              borderRadius: '8px',
              boxShadow: '0 2px 8px rgba(0, 0, 0, 0.09)'
            }}
          >
            <Row align="middle" justify="center">
              <Col span={24} style={{ textAlign: 'center' }}>
                <BankOutlined style={{ fontSize: '24px', color: '#1890ff' }} />
                <div style={{ marginTop: 8 }}>
                  <Text strong style={{ fontSize: '18px', display: 'block' }}>
                    {reconciliation.account?.label || 'Unknown Account'}
                  </Text>
                  <Text type="secondary">Account</Text>
                </div>
              </Col>
            </Row>
          </Card>
        </Col>

        <Col span={24}>
          <Card
            style={{
              background: isPositive ? '#f6ffed' : '#fff1f0',
              borderRadius: '8px',
              boxShadow: '0 2px 8px rgba(0, 0, 0, 0.09)'
            }}
          >
            <Row align="middle" justify="center">
              <Col span={24} style={{ textAlign: 'center' }}>
                {isPositive ? (
                  <ArrowUpOutlined style={{ fontSize: '24px', color: '#52c41a' }} />
                ) : (
                  <ArrowDownOutlined style={{ fontSize: '24px', color: '#f5222d' }} />
                )}
                <div style={{ marginTop: 8 }}>
                  <Text
                    strong
                    style={{
                      fontSize: '24px',
                      display: 'block',
                      color: isPositive ? '#52c41a' : '#f5222d'
                    }}
                  >
                    {formatNumber(reconciliation.amount)}
                  </Text>
                  <Text type="secondary">
                    {isPositive ? 'Credit Adjustment' : 'Debit Adjustment'}
                  </Text>
                </div>
              </Col>
            </Row>
          </Card>
        </Col>

        <Col span={24}>
          <Alert
            message="Reconciliation Reason"
            description={reconciliation.reason || 'No reason provided'}
            type={isPositive ? "success" : "warning"}
            showIcon
          />
        </Col>

        <Col span={24}>
          <Divider orientation="left">Reconciliation Details</Divider>
          <Descriptions bordered column={2}>
            <Descriptions.Item label="Date">
              <CalendarOutlined style={{ marginRight: 8 }} />
              {reconciliation.date ? moment(reconciliation.date).format('DD MMM YYYY') : 'No date'}
            </Descriptions.Item>
            <Descriptions.Item label="Status">
              <Tag color={getStatusColor(reconciliation.status)}>
                {reconciliation.status?.toUpperCase()}
              </Tag>
            </Descriptions.Item>
            <Descriptions.Item label="Created At">
              <CalendarOutlined style={{ marginRight: 8 }} />
              {reconciliation.createdAt ? moment(reconciliation.createdAt).format('DD MMM YYYY HH:mm') : 'Unknown'}
            </Descriptions.Item>
            <Descriptions.Item label="Created By">
              {reconciliation.createdBy ? reconciliation.createdBy.label : 'System'}
            </Descriptions.Item>
          </Descriptions>
        </Col>
      </Row>
    </Card>
  );
};

export default Details;