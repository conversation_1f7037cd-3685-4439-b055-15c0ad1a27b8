import A4 from "./A4";
import PDFDownload from "../../../../../Components/PDFDownload";
import PouchDb from "pouchdb-browser";
import PrintComponents from "react-print-components";
import React, { useEffect, useState } from "react";
import Thermal from "./Thermal";
import packageJson from "../../../../../../package.json";
import { FloatButton, Radio } from "antd";
import { PageHeader } from "@ant-design/pro-components";
import { PrinterOutlined } from "@ant-design/icons";


const appName = packageJson.name;

const InvoiceTemplate = ({ data, documentTitle }) => {
  const [company, setCompany] = useState(null);
  const [customer, setCustomer] = useState(null);
  const [value, setValue] = useState("Thermal Paper");


  


  useEffect(() => {
    const CompanyDB = new PouchDb("organizations");
    CompanyDB.allDocs({
      include_docs: true,
      attachments: true,
      binary: true,
    }).then((data) => {
      const filteredRows = data.rows.filter(r => r.doc && !r.doc._id.startsWith('_'));
      if (filteredRows.length > 0) {
        let comp =
          appName === "mission-control"
            ? filteredRows.find((row) => row.doc._id === "M8BDICHP")?.doc || filteredRows[0].doc
            : filteredRows[0].doc;

        
        if (comp._attachments && comp._attachments.logo) {
          CompanyDB.getAttachment(comp._id, "logo").then((res) => {
            
            comp.logo = res;
            setCompany(comp);
          });
        } else {
          setCompany(comp);
        }
      }
    });
  }, []);

  useEffect(() => {
    const customersDB = new PouchDb("organizations");
    
    data &&
      data.client && data.client.name &&
      customersDB
        .get(data.client.name._id, { binary: true })
        .then((res) => {
          
          setCustomer(res);
        });
  }, [data]);

  const options = [
    { label: "A4", value: "A4" },
    { label: "Thermal Paper", value: "Thermal Paper" },
  ];

  const onChange = ({ target: { value } }) => {
    
    setValue(value);
  };

  return (
    <>
      {company && customer && (
        <div>
          <PageHeader
            title={documentTitle}
            extra={
              <Radio.Group
                options={options}
                onChange={onChange}
                value={value}
                optionType="button"
                buttonStyle="solid"
              />
            }
            style={{ marginBottom: 24 }}
          ></PageHeader>

          <PrintComponents
            trigger={
              <FloatButton
                icon={<PrinterOutlined />}
                tooltip={<div>Print</div>}
              />
            }
          >
            {value === "Thermal Paper" ? (
              <Thermal
                company={company}
                data={{ ...data, client: customer }}
                documentTitle={documentTitle}
              />
            ) : (
              <A4
                company={company}
                data={{ ...data, client: customer }}
                documentTitle={documentTitle}
                taxable={data.taxable}
              />
            )}
          </PrintComponents>

          <PDFDownload
            filename={`${documentTitle.toLowerCase()}_${data?.invoiceNumber || 'document'}`}
            pageSize={value === "Thermal Paper" ? "Thermal" : "A4"}
            documentTitle={documentTitle}
          >
            {value === "Thermal Paper" ? (
              <Thermal
                company={company}
                data={{ ...data, client: customer }}
                documentTitle={documentTitle}
              />
            ) : (
              <A4
                company={company}
                data={{ ...data, client: customer }}
                documentTitle={documentTitle}
                taxable={data.taxable}
              />
            )}
          </PDFDownload>

          {value === "Thermal Paper" && (
            <Thermal
              company={company}
              data={{ ...data, client: customer }}
              documentTitle={documentTitle}
            />
          )}
          {value === "A4" && (
            <A4 company={company} data={{ ...data, client: customer }} documentTitle={documentTitle} />
          )}
        </div>
      )}
    </>
  );
};

export default InvoiceTemplate;