import React, { useEffect, useState } from "react";
import RenderBlob from "../../../../Components/RenderBlob";


const styles = {
  container: {
    display: "flex",
    alignItems: "center",
    justifyContent: "space-between",
    padding: "20px 24px 30px",
    marginBottom: "20px",
    minHeight: "200px",
    borderBottom: "2px solid #e8e8e8",
  },
  contact: {
    textAlign: "right",
    fontSize: "13px",
    color: "#333",
    minWidth: "250px",
  },
};

function DocumentHead({ logoSize = 300, pouchDatabase, databasePrefix, company: companyProp }) {
  const [company, setCompany] = useState(companyProp || null);

  useEffect(() => {
    // If company prop is provided, use it directly
    if (companyProp) {
      setCompany(companyProp);
      return;
    }

    // Otherwise, fetch from database
    const loadData = async () => {
      try {
        if (pouchDatabase && databasePrefix !== undefined) {
          const companyID = JSON.parse(localStorage.getItem("LOCAL_STORAGE_ORGANIZATION"))._id;
          const data = await pouchDatabase("organizations", databasePrefix).getDocument(companyID);
          if (data) {
            if (data._attachments?.logo) {
              try {
                const logo = await pouchDatabase("organizations", databasePrefix).getAttachment(data._id, "logo");
                setCompany({ ...data, orgLogo: logo });
              } catch {
                setCompany(data);
              }
            } else {
              setCompany(data);
            }
          }
        }
      } catch (error) {
        console.error("Error loading organization:", error);
      }
    };
    loadData();
  }, [pouchDatabase, databasePrefix, companyProp]);

  if (!company) {
    return (
      <div style={{...styles.container, backgroundColor: "#fff3cd", border: "1px dashed #ffc107"}}>
        <div style={{fontSize: '24px', color: "#856404"}}>[Your Company Name]</div>
        <div style={{...styles.contact, color: "#856404"}}>Please set up organization details in Settings</div>
      </div>
    );
  }

  return (
    <div style={styles.container}>
      <div>
        {company.orgLogo && <RenderBlob blob={company.orgLogo} size={logoSize} />}
      </div>
      <div style={styles.contact}>
        {company.address && <div>{company.address}</div>}
        {company.phone && <div><strong>Phone:</strong> {company.phone}</div>}
        {company.email && <div><strong>Email:</strong> {company.email}</div>}
        {company.website && <div><strong>Website:</strong> {company.website}</div>}
      </div>
    </div>
  );
}

export default DocumentHead;
