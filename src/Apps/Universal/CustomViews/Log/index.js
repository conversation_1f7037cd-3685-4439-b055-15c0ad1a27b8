import React, { useState } from 'react';
import moment from "moment";
import { Button, Descriptions, Card, Typography, Space, Tag, Divider, Skeleton, Collapse, Badge, Tooltip, Modal } from "antd";
import { UndoOutlined, RollbackOutlined, EditOutlined, DeleteOutlined, PlusOutlined, InfoCircleOutlined, UserOutlined, ClockCircleOutlined } from "@ant-design/icons";


const { Title, Text } = Typography;
const { Panel } = Collapse;

const CustomViewLog = ({ data, pouchDatabase, collection, databasePrefix, CRUD_USER }) => {
    const [loading, setLoading] = useState(false);

    // Handle revert or restore actions
    const handleAction = async (values, action, collection) => {
        try {
            setLoading(true);
            const db = pouchDatabase(collection, databasePrefix);

            const message = await import('antd').then(antd => antd.message);

            if (action === 'Restored') {
                // For restoring deleted documents
                try {
                    // Create a clean copy of the document to restore
                    const docToRestore = { ...values };

                    // Make sure we have the original ID
                    if (!docToRestore._id) {
                        throw new Error('Document ID not found in the log entry');
                    }

                    // Remove PouchDB metadata fields but keep the ID
                    delete docToRestore._rev;
                    delete docToRestore._deleted; // Make sure it's not marked as deleted

                    // Add metadata to indicate this is a restore
                    docToRestore.restoredAt = new Date().toISOString();
                    docToRestore.restoredBy = CRUD_USER;
                    docToRestore.restoredFrom = data._id; // Reference to the log entry
                    docToRestore.createdAt = new Date().toISOString();
                    docToRestore.updatedAt = new Date().toISOString();

                    // Try to save the document with its original ID
                    try {
                        // First check if a document with this ID already exists
                        try {
                            await db.db.get(docToRestore._id);
                            // If we get here, a document with this ID already exists
                            message.error(`Cannot restore: A document with ID ${docToRestore._id} already exists`);
                            return;
                        } catch (getError) {
                            // If error is 404 (not found), that's good - we can create a new doc with this ID
                            if (getError.status !== 404) {
                                throw getError; // Some other error occurred
                            }
                        }

                        // Use direct put to ensure we use the original ID
                        const result = await db.db.put(docToRestore);

                        // Log the restoration
                        await db.addLog({
                            description: `Restored document in ${collection}`,
                            details: { restored: docToRestore },
                            user: CRUD_USER,
                            action: "rollback",
                            type: "rollback",
                        });

                        if (result && result.ok) {
                            message.success(`Document successfully restored with original ID: ${docToRestore._id}`);
                        }
                    } catch (saveError) {
                        console.error('Error saving restored document:', saveError);
                        message.error(`Error restoring document: ${saveError.message || 'Unknown error'}`);
                    }
                } catch (restoreError) {
                    console.error('Error preparing document for restore:', restoreError);
                    message.error(`Error preparing document for restore: ${restoreError.message || 'Unknown error'}`);
                }
            } else if (action === 'reverted') {
                // For reverting edited documents
                try {
                    // Get the document ID from the original document
                    const docId = values._id;

                    if (!docId) {
                        throw new Error('Document ID not found in the log entry');
                    }

                    try {
                        // First try to get the current document to check if it exists
                        await db.db.get(docId);
                    } catch (getError) {
                        // If document doesn't exist, we can create a new one with the old values
                        if (getError.status === 404) {
                            // Document doesn't exist anymore, create a new one
                            const newDoc = {
                                ...values,
                                _id: docId, // Keep the same ID
                                revertedAt: new Date().toISOString(),
                                revertedBy: CRUD_USER,
                                revertedFrom: data._id
                            };
                            delete newDoc._rev; // Remove _rev for new document

                            await db.saveDocument(newDoc, CRUD_USER);
                            message.success('Document recreated successfully');
                            return;
                        } else {
                            // Some other error occurred
                            throw getError;
                        }
                    }

                    // Get the current document to get its _rev
                    const currentDoc = await db.db.get(docId);

                    // Start with a clean copy of the original values
                    const revertedDoc = { ...values };

                    // Keep the current document's metadata fields
                    const metadataFields = ['_id', '_rev', 'createdAt', 'createdBy', 'entrant'];
                    metadataFields.forEach(field => {
                        if (currentDoc[field]) {
                            revertedDoc[field] = currentDoc[field];
                        }
                    });

                    // Add revert metadata
                    revertedDoc.revertedAt = new Date().toISOString();
                    revertedDoc.revertedBy = CRUD_USER;
                    revertedDoc.revertedFrom = data._id;
                    revertedDoc.updatedAt = new Date().toISOString();
                    revertedDoc.updatedBy = CRUD_USER;

                    // Use standard put method to update the document
                    try {
                        const result = await db.db.put(revertedDoc);
                        if (result && result.ok) {
                            // Log the revert action
                            await db.addLog({
                                description: `Reverted changes to document in ${collection}`,
                                details: {
                                    from: currentDoc,
                                    to: revertedDoc,
                                    revertedFrom: data._id
                                },
                                user: CRUD_USER,
                                action: "rollback",
                                type: "rollback",
                            });

                            message.success('Changes successfully reverted');
                        }
                    } catch (putError) {
                        if (putError.name === 'conflict') {
                            // Handle conflict by getting the latest version and trying again
                            const latestDoc = await db.db.get(docId);
                            revertedDoc._rev = latestDoc._rev;
                            const retryResult = await db.db.put(revertedDoc);
                            if (retryResult && retryResult.ok) {
                                // Log the revert action after conflict resolution
                                await db.addLog({
                                    description: `Reverted deleted document in ${collection} (after conflict)`,
                                    details: {
                                        from: latestDoc,
                                        to: revertedDoc,
                                        revertedFrom: data._id
                                    },
                                    user: CRUD_USER,
                                    action: "revert",
                                    type: "revert",
                                });

                                message.success('Changes successfully reverted (after resolving conflict)');
                            }
                        } else {
                            throw putError;
                        }
                    }
                } catch (docError) {
                    console.error('Error getting or updating document:', docError);
                    message.error(`Error: ${docError.message || 'Failed to revert changes'}`);
                }
            }

            setLoading(false);
        } catch (error) {
            console.error('Error performing action:', error);

            // Show error message
            const message = await import('antd').then(antd => antd.message);
            message.error(`Error: ${error.message || 'Unknown error occurred'}`);

            setLoading(false);
        }
    };

    // Get log type icon and color
    const getLogTypeInfo = (type) => {
        switch (type) {
            case 'add':
                return { icon: <PlusOutlined />, color: 'success', text: 'Added' };
            case 'edit':
                return { icon: <EditOutlined />, color: 'warning', text: 'Edited' };
            case 'delete':
                return { icon: <DeleteOutlined />, color: 'error', text: 'Deleted' };
            case 'restore':
                return { icon: <RollbackOutlined />, color: 'processing', text: 'Restored' };
            case 'revert':
                return { icon: <UndoOutlined />, color: 'processing', text: 'Reverted' };
            default:
                return { icon: <InfoCircleOutlined />, color: 'default', text: type };
        }
    };



    // Render object recursively
    const renderObject = (objData) => {
        if (!objData || typeof objData !== 'object') return String(objData);

        // Create a clean copy without system fields
        const cleanData = { ...objData };
        delete cleanData._id;
        delete cleanData._rev;
        delete cleanData._attachments;
        delete cleanData.otherProps;

        return (
            <Descriptions
                bordered
                size="small"
                column={1}
                labelStyle={{ fontWeight: 600 }}
            >
                {Object.keys(cleanData).map(key => {
                    return <Descriptions.Item label={key} key={key}>
                        {typeof cleanData[key] === 'object' && cleanData[key] !== null ?
                            (cleanData[key].label ?
                                cleanData[key].label :
                                <Collapse ghost>
                                    <Panel header={Array.isArray(cleanData[key]) ?
                                        `Array (${cleanData[key].length} items)` :
                                        "View Object"} key="1">
                                        {renderObject(cleanData[key])}
                                    </Panel>
                                </Collapse>
                            ) :
                            String(cleanData[key])
                        }
                    </Descriptions.Item>;
                })}
            </Descriptions>
        );
    };

    // Render edit log with parallel comparison
    const renderEditLog = () => {
        if (!data.details || !data.details.from || !data.details.to) {
            return <Text type="warning">Invalid edit log format</Text>;
        }

        const fromData = { ...data.details.from };
        const toData = { ...data.details.to };

        // Combine all keys from both objects
        const allKeys = [...new Set([...Object.keys(fromData), ...Object.keys(toData)])];

        // Filter out system fields
        const filteredKeys = allKeys.filter(key =>
            !['_id', '_rev', '_attachments', 'otherProps'].includes(key));

        // Determine which fields have changes
        const changedFields = filteredKeys.filter(key => {
            return JSON.stringify(fromData[key]) !== JSON.stringify(toData[key]);
        });

        // Determine which fields are unchanged
        const unchangedFields = filteredKeys.filter(key => {
            return JSON.stringify(fromData[key]) === JSON.stringify(toData[key]);
        });

        return (
            <>
                {/* Changed fields */}
                <Card
                    title={
                        <Space>
                            <Badge status="warning" />
                            <span>Changed Fields ({changedFields.length})</span>
                        </Space>
                    }
                    style={{ marginBottom: 16 }}
                >
                    {changedFields.length === 0 ? (
                        <Text type="secondary">No fields were changed</Text>
                    ) : (
                        <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
                            {changedFields.map(key => (
                                <Card
                                    key={key}
                                    size="small"
                                    title={<Text strong>{key}</Text>}
                                    style={{ marginBottom: 8 }}
                                >
                                    <div style={{ display: 'flex', flexWrap: 'wrap', gap: '16px' }}>
                                        <div style={{ flex: 1, minWidth: '300px' }}>
                                            <div style={{ marginBottom: 8 }}>
                                                <Tag color="red">Previous Value</Tag>
                                            </div>
                                            {typeof fromData[key] === 'object' && fromData[key] !== null ? (
                                                fromData[key].label ? (
                                                    fromData[key].label
                                                ) : (
                                                    renderObject(fromData[key])
                                                )
                                            ) : (
                                                String(fromData[key])
                                            )}
                                        </div>
                                        <div style={{ flex: 1, minWidth: '300px' }}>
                                            <div style={{ marginBottom: 8 }}>
                                                <Tag color="green">New Value</Tag>
                                            </div>
                                            {typeof toData[key] === 'object' && toData[key] !== null ? (
                                                toData[key].label ? (
                                                    toData[key].label
                                                ) : (
                                                    renderObject(toData[key])
                                                )
                                            ) : (
                                                String(toData[key])
                                            )}
                                        </div>
                                    </div>
                                </Card>
                            ))}
                        </div>
                    )}
                </Card>

                {/* Unchanged fields */}
                <Collapse ghost>
                    <Panel
                        header={
                            <Space>
                                <span>Unchanged Fields ({unchangedFields.length})</span>
                            </Space>
                        }
                        key="unchanged"
                    >
                        <Descriptions
                            bordered
                            column={1}
                            size="small"
                            labelStyle={{ fontWeight: 600 }}
                        >
                            {unchangedFields.map(key => (
                                <Descriptions.Item label={key} key={key}>
                                    {typeof toData[key] === 'object' && toData[key] !== null ? (
                                        toData[key].label ? (
                                            toData[key].label
                                        ) : (
                                            <Collapse ghost>
                                                <Panel header="View Value" key="1">
                                                    {renderObject(toData[key])}
                                                </Panel>
                                            </Collapse>
                                        )
                                    ) : (
                                        String(toData[key])
                                    )}
                                </Descriptions.Item>
                            ))}
                        </Descriptions>
                    </Panel>
                </Collapse>
            </>
        );
    };

    // Render delete log
    const renderDeleteLog = () => {
        if (!data.details) {
            return <Text type="warning">Invalid delete log format</Text>;
        }

        return (
            <Card title="Deleted Data">
                {renderObject(data.details)}
            </Card>
        );
    };

    // Render add log
    const renderAddLog = () => {
        if (!data.details) {
            return <Text type="warning">Invalid add log format</Text>;
        }

        return (
            <Card title="Added Data">
                {renderObject(data.details)}
            </Card>
        );
    };

    // Main render function
    const renderLogContent = () => {
        if (loading) {
            return <Skeleton active paragraph={{ rows: 10 }} />;
        }

        switch (data.type) {
            case 'edit':
            case 'revert':
                return renderEditLog();
            case 'delete':
                return renderDeleteLog();
            case 'add':
            case 'restore':
                return renderAddLog();
            default:
                return renderObject(data);
        }
    };

    // Get log type information
    const logTypeInfo = getLogTypeInfo(data.type);

    return (
        <Space direction="vertical" style={{ width: '100%' }}>
            {/* Header with metadata */}
            <Card>
                <Space direction="vertical" style={{ width: '100%' }}>
                    <Space>
                        <Badge status={logTypeInfo.color} text={<Title level={4}>{data.description || `${logTypeInfo.text} Record`}</Title>} />
                    </Space>

                    <Space split={<Divider type="vertical" />}>
                        {data.user && (
                            <Tooltip title="User">
                                <Space>
                                    <UserOutlined />
                                    <Text>{data.user.label || 'Unknown User'}</Text>
                                </Space>
                            </Tooltip>
                        )}
                        {data.createdAt && (
                            <Tooltip title="Time">
                                <Space>
                                    <ClockCircleOutlined />
                                    <Text>{moment(data.createdAt).format('YYYY-MM-DD HH:mm:ss')} ({moment(data.createdAt).fromNow()})</Text>
                                </Space>
                            </Tooltip>
                        )}
                    </Space>

                    {/* Action buttons */}
                    <div style={{ marginTop: 16 }}>
                        {data.type === "edit" && (
                            <Tooltip title="Revert this record to its previous state before the edit">
                                <Button
                                    onClick={() => {
                                        // Add confirmation before reverting
                                        const { confirm } = Modal;
                                        confirm({
                                            title: 'Confirm Revert',
                                            icon: <UndoOutlined />,
                                            content: 'Are you sure you want to revert these changes? This will restore the record to its previous state.',
                                            okText: 'Yes, Revert',
                                            cancelText: 'Cancel',
                                            onOk() {
                                                //get last word from description
                                                const collection = data.description.split(' ')[data.description.split(' ').length - 1];

                                                return handleAction(data.details.from, 'reverted', collection);
                                            },
                                        });
                                    }}
                                    icon={<UndoOutlined />}
                                    type="primary"
                                    loading={loading}
                                >
                                    Revert Changes
                                </Button>
                            </Tooltip>
                        )}
                        {data.type === "delete" && (
                            <Tooltip title="Restore this deleted record">
                                <Button
                                    onClick={() => {
                                        // Add confirmation before restoring
                                        const { confirm } = Modal;
                                        confirm({
                                            title: 'Confirm Restore',
                                            icon: <RollbackOutlined />,
                                            content: 'Are you sure you want to restore this deleted record?',
                                            okText: 'Yes, Restore',
                                            cancelText: 'Cancel',
                                            onOk() {
                                                // Extract collection name from description
                                                const collection = data.description.split(' ')[data.description.split(' ').length - 1];
                                                return handleAction(data.details, 'Restored', collection);
                                            },
                                        });
                                    }}
                                    icon={<RollbackOutlined />}
                                    type="primary"
                                    loading={loading}
                                >
                                    Restore Record
                                </Button>
                            </Tooltip>
                        )}
                    </div>
                </Space>
            </Card>

            {/* Log content */}
            {renderLogContent()}
        </Space>
    )
}

export default CustomViewLog;