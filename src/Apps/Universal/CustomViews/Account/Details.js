import React from "react";
import { Card, Descriptions, Tag } from "antd";
import { formatNumber } from "../../../../Utils/functions";


const Details = (account) => {
  const getStatusColor = (status) => {
    const colors = {
      active: 'success',
      inactive: 'default',
      frozen: 'error'
    };
    return colors[status] || 'default';
  };

  return (
    <Card>
      <Descriptions bordered column={2}>
        <Descriptions.Item label="Account Name">{account.name}</Descriptions.Item>
        <Descriptions.Item label="Account Type">{account.type}</Descriptions.Item>
        <Descriptions.Item label="Currency">{account.currency}</Descriptions.Item>
        <Descriptions.Item label="Account Number">{account.account_number || 'N/A'}</Descriptions.Item>
        <Descriptions.Item label="Bank/Provider">{account.provider || 'N/A'}</Descriptions.Item>
        <Descriptions.Item label="Branch">{account.branch || 'N/A'}</Descriptions.Item>
        <Descriptions.Item label="Current Balance">{formatNumber(account.balance)}</Descriptions.Item>
        <Descriptions.Item label="Status">
          <Tag color={getStatusColor(account.status)}>{account.status?.toUpperCase()}</Tag>
        </Descriptions.Item>
        <Descriptions.Item label="Description" span={2}>
          {account.description || 'No description provided'}
        </Descriptions.Item>
      </Descriptions>
    </Card>
  );
};

export default Details;