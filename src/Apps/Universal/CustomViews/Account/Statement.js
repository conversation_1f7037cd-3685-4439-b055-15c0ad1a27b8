import PouchDb from "pouchdb-browser";
import PrintComponents from "react-print-components";
import React, { useState, useEffect } from 'react';
import moment from "moment";
import { SearchOutlined, PrinterOutlined, CalendarOutlined } from "@ant-design/icons";
import { Table, DatePicker, Input, Button, Row, Col, Card, Space } from "antd";
import { formatNumber } from "../../../../Utils/functions";


const Statement = (account) => {
  const [data, setData] = useState([]);
  // Set default date range to just the current day to avoid too many selected days
  const [dateRange, setDateRange] = useState([
    moment().startOf('day'),
    moment().endOf('day')
  ]);
  const [searchText, setSearchText] = useState('');
  const [openingBalance, setOpeningBalance] = useState(0);
  const [loading, setLoading] = useState(true);

  // Predefined date ranges for quick selection
  const dateRangePresets = {
    'Today': [moment().startOf('day'), moment().endOf('day')],
    'Yesterday': [moment().subtract(1, 'day').startOf('day'), moment().subtract(1, 'day').endOf('day')],
    'Last 7 Days': [moment().subtract(6, 'days').startOf('day'), moment().endOf('day')],
    'Last 30 Days': [moment().subtract(29, 'days').startOf('day'), moment().endOf('day')],
    'This Month': [moment().startOf('month'), moment().endOf('day')],
    'Last Month': [
      moment().subtract(1, 'month').startOf('month'),
      moment().subtract(1, 'month').endOf('month')
    ],
    'This Year': [moment().startOf('year'), moment().endOf('day')],
  };

  const fetchTransactions = async () => {
    setLoading(true);
    try {
      const receiptsDB = new PouchDb('receipts');
      const expensesDB = new PouchDb('expenses');
      const transfersDB = new PouchDb('account_transfers');
      const reconciliationsDB = new PouchDb('account_reconciliations');

      const [receipts, expenses, transfers, reconciliations] = await Promise.all([
        (await receiptsDB.allDocs({ include_docs: true })).rows.filter(r => r.doc && !r.doc._id.startsWith('_')).map(r => r.doc),
        (await expensesDB.allDocs({ include_docs: true })).rows.filter(r => r.doc && !r.doc._id.startsWith('_')).map(r => r.doc),
        (await transfersDB.allDocs({ include_docs: true })).rows.filter(r => r.doc && !r.doc._id.startsWith('_')).map(r => r.doc),
        (await reconciliationsDB.allDocs({ include_docs: true })).rows.filter(r => r.doc && !r.doc._id.startsWith('_')).map(r => r.doc)
      ]);



      // Enhanced helper function to ensure we have a valid date
      const getValidDate = (dateField, createdAtField) => {
        // Try to parse the date field
        if (dateField) {
          const parsedDate = moment(dateField);
          if (parsedDate.isValid()) {
            // Return ISO string for consistent format
            return parsedDate.toISOString();
          }
        }

        // Try to parse the createdAt field as fallback
        if (createdAtField) {
          const parsedCreatedAt = moment(createdAtField);
          if (parsedCreatedAt.isValid()) {
            return parsedCreatedAt.toISOString();
          }
        }

        // Fallback to current date if both are invalid
        return moment().toISOString();
      };

      let transactions = [
        // Process receipts
        ...receipts
          .filter(r => r.account?.value === account._id)
          .map(r => ({
            date: getValidDate(r.date, r.createdAt),
            type: 'Receipt',
            reference: r._id,
            description: `Receipt from ${r.customer ? r.customer.label : r.invoice ? r.invoice.label : 'Unknown'}`,
            amount: Number(r.amount) || 0,
            balance: 0 // Will be calculated later
          })),

        // Process expenses
        ...expenses
          .filter(e => e.account?.value === account._id)
          .map(e => ({
            date: getValidDate(e.date, e.createdAt),
            type: 'Expense',
            reference: e._id,
            description: e.description || 'Expense payment',
            amount: -(Number(e.amount) || 0),
            balance: 0
          })),

        // Process transfers
        ...transfers
          .filter(t => t.status === 'completed' &&
            (t.from_account?.value === account._id || t.to_account?.value === account._id))
          .flatMap(t => {
            const transactions = [];
            const transferDate = getValidDate(t.date, t.createdAt);

            if (t.from_account?.value === account._id) {
              // Outgoing transfer
              transactions.push({
                date: transferDate,
                type: 'Transfer',
                reference: t._id,
                description: `Transfer to ${t.to_account?.label || 'another account'}`,
                amount: -Number(t.amount || 0),
                balance: 0
              });
              const charges = Number(t.charges) || 0;
              if (charges > 0) {
                transactions.push({
                  date: transferDate,
                  type: 'Charges', // Separate type for charges
                  reference: t._id,
                  description: `Charges for transfer to ${t.to_account?.label || 'another account'}`,
                  amount: -charges,
                  balance: 0
                });
              }
            } else {
              // Incoming transfer
              transactions.push({
                date: transferDate,
                type: 'Transfer',
                reference: t._id,
                description: `Transfer from ${t.from_account?.label || 'another account'}`,
                amount: Number(t.amount || 0),
                balance: 0
              });
            }
            return transactions;
          }),

        // Process reconciliations
        ...reconciliations
          .filter(r => r.status === 'completed' && r.account?.value === account._id)
          .map(r => ({
            date: getValidDate(r.date, r.createdAt),
            type: 'Reconciliation',
            reference: r._id,
            description: r.reason || 'Account reconciliation',
            amount: Number(r.amount) || 0,
            balance: 0
          }))
      ];

      // Sort by date
      transactions.sort((a, b) => moment(a.date).valueOf() - moment(b.date).valueOf());

      setData(transactions);
    } catch (error) {
      console.error('Error fetching transactions:', error);
    }
    setLoading(false);
  };

  useEffect(() => {
    fetchTransactions();
  }, [account._id]);

  useEffect(() => {
    if (!data || !dateRange || !dateRange[0] || !dateRange[1]) {
      setOpeningBalance(0);
      return;
    }

    const [startDate, endDate] = dateRange;

    // Ensure we have a valid start date
    if (!startDate) {
      setOpeningBalance(0);
      return;
    }

    // Calculate opening balance from transactions before start date
    // Use moment object directly if it's already a moment, otherwise create one
    const startMoment = moment.isMoment(startDate)
      ? startDate.clone().startOf('day')
      : moment(startDate).startOf('day');

    // Calculate opening balance from transactions before start date
    const openingBal = data
      .filter(item => {
        // Ensure item.date is valid before comparing
        if (!item.date) return false;

        const itemDate = moment(item.date);
        if (!itemDate.isValid()) return false;

        return itemDate.isBefore(startMoment);
      })
      .reduce((acc, curr) => acc + curr.amount, 0);

    setOpeningBalance(openingBal);
  }, [data, dateRange]);

  const getFilteredData = () => {
    let filtered = [...data];

    // Apply date range filter
    if (dateRange && dateRange[0] && dateRange[1]) {
      // Handle both moment objects and date objects with $d property
      const startDate = moment.isMoment(dateRange[0])
        ? dateRange[0].clone().startOf('day')
        : dateRange[0].$d
          ? moment(dateRange[0].$d).startOf('day')
          : moment(dateRange[0]).startOf('day');

      const endDate = moment.isMoment(dateRange[1])
        ? dateRange[1].clone().endOf('day')
        : dateRange[1].$d
          ? moment(dateRange[1].$d).endOf('day')
          : moment(dateRange[1]).endOf('day');

      filtered = filtered.filter(item => {
        // Skip items with invalid dates
        if (!item.date) return false;

        try {
          const itemDate = moment(item.date);
          if (!itemDate.isValid()) return false;

          // Check if the date is within the range (inclusive)
          return itemDate.isSameOrAfter(startDate) && itemDate.isSameOrBefore(endDate);
        } catch (error) {
          console.error('Error comparing dates:', error, item);
          return false;
        }
      });
    }

    // Apply search filter
    if (searchText) {
      const search = searchText.toLowerCase();
      filtered = filtered.filter(item =>
        (item.description && item.description.toLowerCase().includes(search)) ||
        (item.reference && item.reference.toLowerCase().includes(search)) ||
        (item.type && item.type.toLowerCase().includes(search))
      );
    }

    // Calculate running balance
    let runningBalance = openingBalance;
    return filtered.map(item => {
      runningBalance += item.amount;
      return { ...item, balance: runningBalance };
    });
  };

  const columns = [
    {
      title: 'Date',
      dataIndex: 'date',
      key: 'date',
      render: text => {
        const date = moment(text);
        return date.isValid() ? date.format('DD MMM YYYY') : 'Invalid Date';
      }
    },
    {
      title: 'Type',
      dataIndex: 'type',
      key: 'type'
    },
    {
      title: 'Reference',
      dataIndex: 'reference',
      key: 'reference'
    },
    {
      title: 'Description',
      dataIndex: 'description',
      key: 'description'
    },
    {
      title: 'In',
      dataIndex: 'amount',
      key: 'amount',
      align: 'right',
      render: amount => amount > 0 ? formatNumber(amount) : ''
    },
    {
      title: 'Out',
      dataIndex: 'amount',
      key: 'amount',
      align: 'right',
      render: amount => amount < 0 ? formatNumber(Math.abs(amount)) : ''
    },
    {
      title: 'Balance',
      dataIndex: 'balance',
      key: 'balance',
      align: 'right',
      render: balance => formatNumber(balance)
    }
  ];

  const filteredData = getFilteredData();

  return (
    <Card>
      <Row gutter={[16, 16]} style={{ marginBottom: 16 }}>
        <Col span={8}>
          <DatePicker.RangePicker
            style={{ width: '100%' }}
            value={dateRange}
            onChange={setDateRange}
            ranges={dateRangePresets}
            allowClear={false}
            format="DD MMM YYYY"
            placeholder={['Start Date', 'End Date']}
            separator="→"
          />
        </Col>
        <Col span={8}>
          <Input
            placeholder="Search by description, reference, or type"
            prefix={<SearchOutlined />}
            value={searchText}
            onChange={e => setSearchText(e.target.value)}
          />
        </Col>
        <Col span={8} style={{ textAlign: 'right' }}>
          <PrintComponents
            trigger={
              <Button icon={<PrinterOutlined />} type="primary">
                Print Statement
              </Button>
            }
          >
            <div style={{ padding: 24 }}>
              <h2>{account.name} - Account Statement</h2>
              <Table
                columns={columns}
                dataSource={[
                  // Add opening balance row when date range is selected
                  ...(dateRange && dateRange[0] ? [{
                    key: 'opening',
                    date: moment.isMoment(dateRange[0]) ? dateRange[0].toDate() : dateRange[0].$d || dateRange[0],
                    type: 'Opening',
                    reference: '-',
                    description: 'Opening Balance',
                    amount: openingBalance,
                    balance: openingBalance
                  }] : []),
                  ...filteredData
                ]}
                pagination={false}
                summary={pageData => {
                  // Calculate totals for In and Out columns separately
                  const totalIn = pageData.reduce((acc, curr) => acc + (curr.amount > 0 ? curr.amount : 0), 0);
                  const totalOut = pageData.reduce((acc, curr) => acc + (curr.amount < 0 ? Math.abs(curr.amount) : 0), 0);
                  const finalBalance = filteredData[filteredData.length - 1]?.balance || 0;

                  return (
                    <Table.Summary>
                      <Table.Summary.Row>
                        <Table.Summary.Cell index={0} colSpan={4}>
                          <strong>Total</strong>
                        </Table.Summary.Cell>
                        <Table.Summary.Cell index={4} align="right">
                          <strong>{formatNumber(totalIn)}</strong>
                        </Table.Summary.Cell>
                        <Table.Summary.Cell index={5} align="right">
                          <strong>{formatNumber(totalOut)}</strong>
                        </Table.Summary.Cell>
                        <Table.Summary.Cell index={6} align="right">
                          <strong>{formatNumber(finalBalance)}</strong>
                        </Table.Summary.Cell>
                      </Table.Summary.Row>
                    </Table.Summary>
                  );
                }}
              />
            </div>
          </PrintComponents>
        </Col>
      </Row>

      <Table
        loading={loading}
        columns={columns}
        dataSource={[
          // Add opening balance row when date range is selected
          ...(dateRange && dateRange[0] ? [{
            key: 'opening',
            date: moment.isMoment(dateRange[0]) ? dateRange[0].toDate() : dateRange[0].$d || dateRange[0],
            type: 'Opening',
            reference: '-',
            description: 'Opening Balance',
            amount: openingBalance,
            balance: openingBalance
          }] : []),
          ...filteredData
        ]}
        pagination={{ pageSize: 50 }}
        summary={pageData => {
          // Calculate totals for In and Out columns separately
          const totalIn = pageData.reduce((acc, curr) => acc + (curr.amount > 0 ? curr.amount : 0), 0);
          const totalOut = pageData.reduce((acc, curr) => acc + (curr.amount < 0 ? Math.abs(curr.amount) : 0), 0);
          const finalBalance = filteredData[filteredData.length - 1]?.balance || 0;

          return (
            <Table.Summary>
              <Table.Summary.Row>
                <Table.Summary.Cell index={0} colSpan={4}>
                  <strong>Total</strong>
                </Table.Summary.Cell>
                <Table.Summary.Cell index={4} align="right">
                  <strong>{formatNumber(totalIn)}</strong>
                </Table.Summary.Cell>
                <Table.Summary.Cell index={5} align="right">
                  <strong>{formatNumber(totalOut)}</strong>
                </Table.Summary.Cell>
                <Table.Summary.Cell index={6} align="right">
                  <strong>{formatNumber(finalBalance)}</strong>
                </Table.Summary.Cell>
              </Table.Summary.Row>
            </Table.Summary>
          );
        }}
      />
    </Card>
  );
};

export default Statement;