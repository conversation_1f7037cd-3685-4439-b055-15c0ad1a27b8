import PouchDb from "pouchdb-browser";
import React, { useState, useEffect, useRef } from 'react';
import moment from "moment";
import { BetaSchemaForm } from "@ant-design/pro-components";
import { SearchOutlined, FilterOutlined, PlusOutlined, ReconciliationOutlined } from "@ant-design/icons";
import { Table, Card, Tag, Typography, Space, DatePicker, Button, Input, message } from "antd";
import { formatNumber } from "../../../../Utils/functions";


const { Text, Title } = Typography;
const { RangePicker } = DatePicker;

const Reconciliations = (account) => {
  const formRef = useRef();
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [dateRange, setDateRange] = useState(null);
  const [searchText, setSearchText] = useState('');
  const [filteredData, setFilteredData] = useState([]);

  // Function to get the current user for the CRUD operations
  const getCurrentUser = () => {
    try {
      return JSON.parse(localStorage.getItem('LOCAL_STORAGE_USER')) || { _id: 'system' };
    } catch (error) {
      console.error('Error getting current user:', error);
      return { _id: 'system' };
    }
  };

  const fetchReconciliations = async () => {
    setLoading(true);
    try {
      const reconciliationsDB = new PouchDb('account_reconciliations');

      const reconciliations = (await reconciliationsDB.allDocs({ include_docs: true }))
        .rows
        .filter(r => r.doc && !r.doc._id.startsWith('_'))
        .map(r => r.doc)
        .filter(doc => {
          // Check if the account matches the current account
          if (!doc.account) return false;

          // Handle different account reference formats
          if (typeof doc.account === 'string') {
            return doc.account === account._id;
          } else if (doc.account.value) {
            return doc.account.value === account._id;
          } else if (doc.account._id) {
            return doc.account._id === account._id;
          }
          return false;
        });

      setData(reconciliations);
      setFilteredData(reconciliations);
    } catch (error) {
      console.error('Error fetching reconciliations:', error);
    }
    setLoading(false);
  };

  useEffect(() => {
    fetchReconciliations();
  }, [account._id]);

  useEffect(() => {
    // Apply filters when data, search text, or date range changes
    let filtered = [...data];

    // Apply search filter
    if (searchText) {
      const search = searchText.toLowerCase();
      filtered = filtered.filter(item =>
        (item.reason && item.reason.toLowerCase().includes(search)) ||
        (item._id && item._id.toLowerCase().includes(search))
      );
    }

    // Apply date range filter
    if (dateRange && dateRange[0] && dateRange[1]) {
      filtered = filtered.filter(item =>
        moment(item.date).isBetween(dateRange[0], dateRange[1], 'day', '[]')
      );
    }

    // Sort by date (newest first)
    filtered.sort((a, b) => moment(b.date).valueOf() - moment(a.date).valueOf());

    setFilteredData(filtered);
  }, [data, searchText, dateRange]);

  const getStatusColor = (status) => {
    const colors = {
      completed: 'success',
      pending: 'warning',
      cancelled: 'error'
    };
    return colors[status] || 'default';
  };

  const columns = [
    {
      title: 'Date',
      dataIndex: 'date',
      key: 'date',
      render: text => text ? moment(text).format('DD MMM YYYY') : 'No date',
      sorter: (a, b) => moment(a.date).valueOf() - moment(b.date).valueOf(),
    },
    {
      title: 'Reference',
      dataIndex: '_id',
      key: '_id',
      render: text => <Text copyable>{text}</Text>,
    },
    {
      title: 'Reason',
      dataIndex: 'reason',
      key: 'reason',
      ellipsis: true,
    },
    {
      title: 'Amount',
      dataIndex: 'amount',
      key: 'amount',
      align: 'right',
      render: amount => {
        const isPositive = Number(amount) >= 0;
        return (
          <Text style={{ color: isPositive ? '#52c41a' : '#f5222d' }}>
            {formatNumber(amount)}
          </Text>
        );
      },
      sorter: (a, b) => a.amount - b.amount,
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: status => (
        <Tag color={getStatusColor(status)}>
          {status ? status.toUpperCase() : 'UNKNOWN'}
        </Tag>
      ),
      filters: [
        { text: 'Completed', value: 'completed' },
        { text: 'Pending', value: 'pending' },
        { text: 'Cancelled', value: 'cancelled' },
      ],
      onFilter: (value, record) => record.status === value,
    },
  ];

  return (
    <Card>
      <Space direction="vertical" style={{ width: '100%' }} size="large">
        <Title level={4}>Account Reconciliations</Title>

        <Space style={{ marginBottom: 16 }} wrap>
          <RangePicker
            onChange={setDateRange}
            value={dateRange}
            placeholder={['Start Date', 'End Date']}
          />
          <Input
            placeholder="Search by reason or reference"
            prefix={<SearchOutlined />}
            value={searchText}
            onChange={e => setSearchText(e.target.value)}
            style={{ width: 250 }}
          />
          <Button
            type="primary"
            icon={<FilterOutlined />}
            onClick={() => {
              setDateRange(null);
              setSearchText('');
            }}
          >
            Clear Filters
          </Button>
          <Button
            onClick={fetchReconciliations}
          >
            Refresh
          </Button>

          <BetaSchemaForm
            formRef={formRef}
            submitter={{
              searchConfig: {
                resetText: "Cancel",
                submitText: "Save",
              },
            }}
            modalProps={{ centered: true }}
            grid={true}
            trigger={
              <Button type="primary" icon={<PlusOutlined />}>
                Add Reconciliation
              </Button>
            }
            title="Add Account Reconciliation"
            destroyOnClose={true}
            layoutType="ModalForm"
            onFinish={async (values) => {
              try {
                const reconciliationsDB = new PouchDb('account_reconciliations');
                const CRUD_USER = getCurrentUser();

                await reconciliationsDB.put({
                  _id: `reconciliation_${Date.now()}`,
                  ...values,
                  account: {
                    value: account._id,
                    label: account.name,
                  },
                  status: values.status || 'completed',
                  createdAt: new Date().toISOString(),
                  createdBy: CRUD_USER._id
                });

                message.success(`Account reconciliation created successfully`);
                fetchReconciliations(); // Refresh the list
                return true;
              } catch (error) {
                message.error(`Failed to create reconciliation: ${error.message}`);
                return false;
              }
            }}
            columns={[
              {
                title: "Date",
                dataIndex: "date",
                valueType: "date",
                isRequired: true,
                initialValue: new Date(),
              },
              {
                title: "Amount",
                dataIndex: "amount",
                valueType: "money",
                isRequired: true,
                tooltip: "Can be positive (credit) or negative (debit)"
              },
              {
                title: "Reason",
                dataIndex: "reason",
                valueType: "textarea",
                isRequired: true,
                colProps: {
                  span: 24,
                },
              },
              {
                title: "Status",
                dataIndex: "status",
                valueType: "select",
                valueEnum: {
                  completed: { text: "Completed", status: "Success" },
                  pending: { text: "Pending", status: "Warning" },
                  cancelled: { text: "Cancelled", status: "Error" }
                },
                initialValue: "completed",
              }
            ]}
          />
        </Space>

        <Table
          columns={columns}
          dataSource={filteredData}
          rowKey="_id"
          loading={loading}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            pageSizeOptions: ['10', '20', '50'],
          }}
          summary={pageData => {
            const total = pageData.reduce((sum, item) => sum + Number(item.amount || 0), 0);
            const positiveTotal = pageData.reduce((sum, item) => {
              const amount = Number(item.amount || 0);
              return sum + (amount > 0 ? amount : 0);
            }, 0);
            const negativeTotal = pageData.reduce((sum, item) => {
              const amount = Number(item.amount || 0);
              return sum + (amount < 0 ? Math.abs(amount) : 0);
            }, 0);

            return (
              <Table.Summary>
                <Table.Summary.Row>
                  <Table.Summary.Cell index={0} colSpan={3}>
                    <strong>Total</strong>
                  </Table.Summary.Cell>
                  <Table.Summary.Cell index={3} align="right">
                    <Space direction="vertical">
                      <Text type="success">Credits: {formatNumber(positiveTotal)}</Text>
                      <Text type="danger">Debits: {formatNumber(negativeTotal)}</Text>
                      <Text strong>Net: {formatNumber(total)}</Text>
                    </Space>
                  </Table.Summary.Cell>
                  <Table.Summary.Cell index={4}>
                    <Text>{filteredData.length} reconciliation(s)</Text>
                  </Table.Summary.Cell>
                </Table.Summary.Row>
              </Table.Summary>
            );
          }}
        />
      </Space>
    </Card>
  );
};

export default Reconciliations;