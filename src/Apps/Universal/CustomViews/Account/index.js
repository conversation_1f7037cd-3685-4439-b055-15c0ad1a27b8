import Details from "./Details";
import React from "react";
import Reconciliations from "./Reconciliations";
import Statement from "./Statement";
import { InfoCircleOutlined, BankOutlined, ReconciliationOutlined } from "@ant-design/icons";
import { PageHeader } from "@ant-design/pro-components";
import { Tabs } from "antd";


const { TabPane } = Tabs;

const Account = (props) => {
  const { data } = props;

  

  if (!data) return null;

  return (
    <PageHeader
      className="site-page-header-responsive"
      onBack={() => window.history.back()}
      title={data.name}
      subTitle={`${data.type} - ${data.currency}`}
      footer={
        <Tabs defaultActiveKey="1" type="card">
          <TabPane tab={<span><InfoCircleOutlined /> Details</span>} key="1">
            <Details {...data} />
          </TabPane>
          <TabPane tab={<span><BankOutlined /> Statement</span>} key="2">
            <Statement {...data} />
          </TabPane>
          <TabPane tab={<span><ReconciliationOutlined /> Reconciliations</span>} key="3">
            <Reconciliations {...data} />
          </TabPane>
        </Tabs>
      }
    />
  );
};

export default Account;