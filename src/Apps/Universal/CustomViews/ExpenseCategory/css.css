/* Expense Category Custom View Styles */

.expense-category-card {
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  height: 100%;
}

.expense-category-card .ant-card-head {
  background-color: #fafafa;
  border-bottom: 1px solid #f0f0f0;
}

.expense-category-card .ant-card-head-title {
  font-weight: 600;
}

.child-category-card {
  text-align: center;
  padding: 16px;
  transition: all 0.3s;
}

.child-category-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(-5px);
}

.category-expenses-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 16px;
}

.category-expenses-table th,
.category-expenses-table td {
  border: 1px solid #f0f0f0;
  padding: 12px 16px;
  text-align: left;
}

.category-expenses-table th {
  background-color: #fafafa;
  font-weight: 600;
}

.category-expenses-table tr:nth-child(even) {
  background-color: #fafafa;
}

.category-expenses-table tfoot tr {
  background-color: #f0f0f0;
  font-weight: bold;
}

/* Highlight current category in tree */
.current-category {
  color: #1890ff;
  font-weight: bold;
}

/* Print styles */
@media print {
  .expense-category-print-section {
    padding: 0;
  }
  
  .ant-descriptions-bordered .ant-descriptions-item-label,
  .ant-descriptions-bordered .ant-descriptions-item-content {
    padding: 12px 16px;
  }
  
  .ant-divider {
    margin: 16px 0;
  }
  
  .ant-typography {
    margin-bottom: 8px;
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .ant-descriptions-item {
    padding-bottom: 8px;
  }
}
