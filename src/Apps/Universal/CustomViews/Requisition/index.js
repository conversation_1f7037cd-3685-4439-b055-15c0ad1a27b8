import "./css.css";
import PrintComponents from "react-print-components";
import React, { useEffect, useState } from "react";
import moment from "moment";
import { Descriptions, Tabs, Table, Tag, Flex, FloatButton, Card, Divider } from "antd";
import { PrinterOutlined, CheckCircleOutlined, CloseCircleOutlined, DollarOutlined } from "@ant-design/icons";
import { fetchCurrentOrganization } from "../../../../Utils/organizationUtils";
import { numberFormat } from "../../../../Utils/functions";


const { TabPane } = Tabs;

const RequisitionView = (props) => {
  const { data, pouchDatabase, databasePrefix } = props;
  const [company, setCompany] = useState(null);
  const [expenseCategory, setExpenseCategory] = useState(null);
  const [supplier, setSupplier] = useState(null);

  useEffect(() => {
    const loadCompanyData = async () => {
      try {
        // Use fetchCurrentOrganization utility for efficient organization fetching
        const organization = await fetchCurrentOrganization(pouchDatabase, "organizations", databasePrefix);

        if (organization) {
          let comp = organization;

          // Fetch logo attachment if it exists
          if (comp._attachments && comp._attachments.logo && pouchDatabase) {
            try {
              const logoBlob = await pouchDatabase("organizations", databasePrefix).getAttachment(comp._id, "logo");
              comp.logo = logoBlob;
              setCompany(comp);
            } catch (logoError) {
              console.warn("Error fetching logo:", logoError);
              setCompany(comp);
            }
          } else {
            setCompany(comp);
          }
        } else {
          console.warn("No organization data found, using fallback");
          setCompany({
            name: "Your Company",
            phone: "",
            alternative_phone: "",
            email: "",
            website: "",
            address: ""
          });
        }
      } catch (error) {
        console.error("Error fetching organization:", error);
        setCompany({
          name: "Your Company",
          phone: "",
          alternative_phone: "",
          email: "",
          website: "",
          address: ""
        });
      }
    };

    const loadOtherData = async () => {
      try {
        // Fetch expense category if available
        if (data && data["expense-category"] && data["expense-category"].value && pouchDatabase) {
          try {
            const result = await pouchDatabase("expense_categories", databasePrefix).getDocument(data["expense-category"].value);
            setExpenseCategory(result);
          } catch (err) {
            console.error("Error fetching expense category:", err);
          }
        }

        // Fetch supplier if available
        if (data && data.supplier && data.supplier.value && pouchDatabase) {
          try {
            const result = await pouchDatabase("suppliers", databasePrefix).getDocument(data.supplier.value);
            setSupplier(result);
          } catch (err) {
            console.error("Error fetching supplier:", err);
          }
        }
      } catch (error) {
        console.error("Error fetching additional data:", error);
      }
    };

    loadCompanyData();
    loadOtherData();
  }, [data, pouchDatabase, databasePrefix]);

  const getStatusTag = () => {
    if (data.released) {
      return <Tag color="green">Funds Released</Tag>;
    } else if (data.approved) {
      return <Tag color="blue">Approved</Tag>;
    } else if (data.authorised) {
      return <Tag color="orange">Authorized</Tag>;
    } else {
      return <Tag color="red">Pending</Tag>;
    }
  };

  const PrintableRequisition = () => {
    return (
      <div className="requisition-print-container">
        <div className="requisition-header">
          {company && company.logo && (
            <div className="company-logo">
              <img src={company.logo} alt="Company Logo" style={{ maxWidth: 150 }} />
            </div>
          )}
          <div className="requisition-title">
            <h1>REQUISITION</h1>
            <p>Requisition No: {data._id}</p>
            <p>Date: {moment(data.date).format("DD MMM YYYY")}</p>
          </div>
        </div>

        <Divider />

        <Descriptions title="Requisition Details" bordered column={2}>
          <Descriptions.Item label="Date">{moment(data.date).format("DD MMM YYYY")}</Descriptions.Item>
          <Descriptions.Item label="Status">{getStatusTag()}</Descriptions.Item>
          <Descriptions.Item label="Requested By">{data.receiver?.label || "N/A"}</Descriptions.Item>
          <Descriptions.Item label="Supplier">{supplier?.name || "N/A"}</Descriptions.Item>
          <Descriptions.Item label="Expense Category">{expenseCategory?.name || "N/A"}</Descriptions.Item>
          <Descriptions.Item label="Amount">{numberFormat(data.amount)}</Descriptions.Item>
          <Descriptions.Item label="Purpose" span={2}>{data.purpose}</Descriptions.Item>
        </Descriptions>

        {data.authorised && (
          <>
            <Divider />
            <Descriptions title="Authorization Details" bordered column={2}>
              <Descriptions.Item label="Authorized By">{data.authoriser?.label || "N/A"}</Descriptions.Item>
              <Descriptions.Item label="Authorized On">{data.authorisedAt ? moment(data.authorisedAt).format("DD MMM YYYY HH:mm") : "N/A"}</Descriptions.Item>
            </Descriptions>
          </>
        )}

        {data.approved && (
          <>
            <Divider />
            <Descriptions title="Approval Details" bordered column={2}>
              <Descriptions.Item label="Approved By">{data.approvedBy?.label || "N/A"}</Descriptions.Item>
              <Descriptions.Item label="Approved On">{data.approvedAt ? moment(data.approvedAt).format("DD MMM YYYY HH:mm") : "N/A"}</Descriptions.Item>
            </Descriptions>
          </>
        )}

        {data.released && (
          <>
            <Divider />
            <Descriptions title="Fund Release Details" bordered column={2}>
              <Descriptions.Item label="Released By">{data.releasedBy?.label || "N/A"}</Descriptions.Item>
              <Descriptions.Item label="Released On">{data.releasedAt ? moment(data.releasedAt).format("DD MMM YYYY HH:mm") : "N/A"}</Descriptions.Item>
              <Descriptions.Item label="Payment Method">{data.method_of_payment || "N/A"}</Descriptions.Item>
              <Descriptions.Item label="Account">{data.account?.label || "N/A"}</Descriptions.Item>
            </Descriptions>
          </>
        )}

        <div className="signature-section" style={{ marginTop: 50 }}>
          <Flex justify="space-between">
            <div className="signature-box">
              <p>Requested By:</p>
              <div className="signature-line">_____________________</div>
              <p>{data.receiver?.label}</p>
            </div>
            <div className="signature-box">
              <p>Authorized By:</p>
              <div className="signature-line">_____________________</div>
              <p>{data.authoriser?.label || ""}</p>
            </div>
            <div className="signature-box">
              <p>Approved By:</p>
              <div className="signature-line">_____________________</div>
              <p>{data.approvedBy?.label || ""}</p>
            </div>
          </Flex>
        </div>
      </div>
    );
  };

  return (
    <div className="requisition-view-container">
      <Card
        title={
          <Flex align="center" justify="space-between">
            <span>Requisition #{data._id}</span>
            <div>{getStatusTag()}</div>
          </Flex>
        }
        style={{ marginBottom: 20 }}
      >
        <Descriptions column={2}>
          <Descriptions.Item label="Date">{moment(data.date).format("DD MMM YYYY")}</Descriptions.Item>
          <Descriptions.Item label="Requested By">{data.receiver?.label || "N/A"}</Descriptions.Item>
          <Descriptions.Item label="Supplier">{supplier?.name || "N/A"}</Descriptions.Item>
          <Descriptions.Item label="Expense Category">{expenseCategory?.name || "N/A"}</Descriptions.Item>
          <Descriptions.Item label="Amount">{numberFormat(data.amount)}</Descriptions.Item>
          <Descriptions.Item label="Created On">{moment(data.createdAt).format("DD MMM YYYY HH:mm")}</Descriptions.Item>
          <Descriptions.Item label="Purpose" span={2}>{data.purpose}</Descriptions.Item>
        </Descriptions>
      </Card>

      <Tabs defaultActiveKey="1" style={{ marginTop: 20 }}>
        <TabPane tab="Approval Status" key="1">
          <Card>
            <Flex align="center" justify="space-around">
              <div className="status-item">
                <div className={`status-icon ${data.authorised ? 'completed' : 'pending'}`}>
                  {data.authorised ? <CheckCircleOutlined /> : <CloseCircleOutlined />}
                </div>
                <div className="status-label">Authorization</div>
                {data.authorised && (
                  <div className="status-details">
                    <p>By: {data.authoriser?.label}</p>
                    <p>On: {moment(data.authorisedAt).format("DD MMM YYYY")}</p>
                  </div>
                )}
              </div>

              <div className="status-item">
                <div className={`status-icon ${data.approved ? 'completed' : 'pending'}`}>
                  {data.approved ? <CheckCircleOutlined /> : <CloseCircleOutlined />}
                </div>
                <div className="status-label">Approval</div>
                {data.approved && (
                  <div className="status-details">
                    <p>By: {data.approvedBy?.label}</p>
                    <p>On: {moment(data.approvedAt).format("DD MMM YYYY")}</p>
                  </div>
                )}
              </div>

              <div className="status-item">
                <div className={`status-icon ${data.released ? 'completed' : 'pending'}`}>
                  {data.released ? <DollarOutlined /> : <CloseCircleOutlined />}
                </div>
                <div className="status-label">Funds Released</div>
                {data.released && (
                  <div className="status-details">
                    <p>By: {data.releasedBy?.label}</p>
                    <p>On: {moment(data.releasedAt).format("DD MMM YYYY")}</p>
                    <p>Method: {data.method_of_payment}</p>
                    <p>Account: {data.account?.label || "N/A"}</p>
                  </div>
                )}
              </div>
            </Flex>
          </Card>
        </TabPane>

        {/* Additional tabs can be added here if needed */}
      </Tabs>

      <PrintComponents
        trigger={
          <FloatButton
            icon={<PrinterOutlined />}
            tooltip={<div>Print Requisition</div>}
          />
        }
      >
        <PrintableRequisition />
      </PrintComponents>

      <style jsx>{`
        .status-item {
          text-align: center;
          padding: 20px;
        }
        .status-icon {
          font-size: 32px;
          margin-bottom: 10px;
        }
        .status-icon.completed {
          color: #52c41a;
        }
        .status-icon.pending {
          color: #ff4d4f;
        }
        .status-label {
          font-weight: bold;
          margin-bottom: 5px;
        }
        .status-details {
          font-size: 12px;
          color: #666;
        }
        .signature-section {
          margin-top: 30px;
        }
        .signature-line {
          margin: 10px 0;
        }
      `}</style>
    </div>
  );
};

export default RequisitionView;
