.requisition-view-container {
  padding: 20px;
}

.requisition-print-container {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.requisition-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.requisition-title {
  text-align: right;
}

.requisition-title h1 {
  font-size: 24px;
  margin-bottom: 5px;
}

.status-item {
  text-align: center;
  padding: 20px;
  border-radius: 8px;
  background-color: #f9f9f9;
  min-width: 150px;
}

.status-icon {
  font-size: 32px;
  margin-bottom: 10px;
}

.status-icon.completed {
  color: #52c41a;
}

.status-icon.pending {
  color: #ff4d4f;
}

.status-label {
  font-weight: bold;
  margin-bottom: 5px;
}

.status-details {
  font-size: 12px;
  color: #666;
}

.signature-section {
  margin-top: 50px;
}

.signature-box {
  text-align: center;
  min-width: 150px;
}

.signature-line {
  margin: 10px 0;
}

@media print {
  .ant-card-head {
    border-bottom: 1px solid #000;
  }
  
  .ant-descriptions-item-label {
    font-weight: bold;
  }
  
  .ant-tag {
    border: 1px solid #000;
    padding: 2px 6px;
  }
}
