import A4 from "./A4";
import AppDatabase from "../../../../Utils/AppDatabase";
import PrintComponents from "react-print-components";
import React, { useEffect, useState } from "react";
import Thermal from "./Thermal";
import { FloatButton, Radio } from "antd";
import { PageHeader } from "@ant-design/pro-components";
import { PrinterOutlined } from "@ant-design/icons";


const ReceiptTemplate = ({ data, documentTitle }) => {
  const [company, setCompany] = useState(null);
  const [value, setValue] = useState("Thermal Paper");

  useEffect(() => {
    const databasePrefix = localStorage.getItem("DB_PREFIX") || "";
    const CompanyDB = AppDatabase("organizations", databasePrefix);
    CompanyDB.getAllData().then((data) => {
      if (data.length > 0) {
        let comp = data[0];


        if (comp._attachments && comp._attachments.logo) {
          CompanyDB.getAttachment(comp._id, "logo").then((res) => {

            comp.logo = res;
            setCompany(comp);
          });
        } else {
          setCompany(comp);
        }
      }
    });
  }, []);

  const options = [
    { label: "A4", value: "A4" },
    { label: "Thermal Paper", value: "Thermal Paper" },
  ];

  const onChange = ({ target: { value } }) => {

    setValue(value);
  };

  return (
    <>
      {company && (
        <div>
          <PageHeader
            title={documentTitle}
            extra={
              <Radio.Group
                options={options}
                onChange={onChange}
                value={value}
                optionType="button"
                buttonStyle="solid"
              />
            }
            style={{ marginBottom: 24 }}
          ></PageHeader>

          <PrintComponents
            trigger={
              <FloatButton
                icon={<PrinterOutlined />}
                tooltip={<div>Print</div>}
              />
            }
          >
            {value === "Thermal Paper" ? (
              <Thermal
                company={company}
                data={data}
                documentTitle={documentTitle}
              />
            ) : (
              <A4 company={company} data={data} documentTitle={documentTitle} />
            )}
          </PrintComponents>

          {value === "Thermal Paper" && (
            <Thermal
              company={company}
              data={data}
              documentTitle={documentTitle}
            />
          )}
          {value === "A4" && (
            <A4 company={company} data={data} documentTitle={documentTitle} />
          )}
        </div>
      )}
    </>
  );
};

export default ReceiptTemplate;