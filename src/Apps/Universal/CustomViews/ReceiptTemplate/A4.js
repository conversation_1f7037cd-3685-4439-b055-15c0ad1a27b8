import "./a4.css";
import React from "react";
import ReferenceNumber from "../../../../Components/ReferenceNumber";
import RenderBlob from "../../../../Components/RenderBlob";
import moment from "moment";
import { numberFormat } from "../../../../Utils/functions";


const A4 = ({ company, data, documentTitle, taxable = false }) => {
  const { paid = 0 } = data;
  const Subtoal = data.invoice
    ? data.invoice.items.reduce((acc, item) => {
      return acc + item.price * item.quantity;
    }, 0)
    : 0;

  

  const Tax = taxable ? Subtoal * 0.18 : 0;

  const GrandTotal = Subtoal + Tax;

  return (
    <div>
      <div className="tm_invoice_wrap">
        <div className="tm_invoice tm_style1" id="tm_download_section">
          <div className="tm_invoice_in">
            <div className="tm_invoice_head tm_align_center tm_mb20">
              <div className="tm_invoice_left">
                <div className="tm_logo">
                  {company.logo && (
                    <RenderBlob blob={company.logo} size={150} />
                  )}
                  {/* <img src="{company.logo}" alt="Logo" /> */}
                </div>
              </div>
              <div className="tm_invoice_right tm_text_right">
                <div className="tm_primary_color tm_f50 tm_text_uppercase">
                  {documentTitle}
                </div>
              </div>
            </div>
            <div className="tm_invoice_info tm_mb20">
              <div className="tm_invoice_seperator tm_gray_bg"></div>
              <div className="tm_invoice_info_list">
                <p className="tm_invoice_number tm_m0">
                  Invoice#:{" "}
                  <b className="tm_primary_color">
                    {data.invoice.referenceNumber ? (
                      <ReferenceNumber value={data.invoice.referenceNumber} />
                    ) : (
                      '#' + data.invoice._id
                    )}
                  </b>
                </p>
                <p className="tm_invoice_number tm_m0">
                  Receipt#:
                  <b className="tm_primary_color">
                    {data.referenceNumber ? (
                      <ReferenceNumber value={data.referenceNumber} />
                    ) : (
                      '#' + data._id
                    )}
                  </b>
                </p>
                <p className="tm_invoice_date tm_m0">
                  Date:{" "}
                  <b className="tm_primary_color">
                    {moment(data.date).format("ddd MMM Do YYYY")}
                  </b>
                </p>
              </div>
            </div>
            <div className="tm_invoice_head tm_mb10">
              <div className="tm_invoice_left">
                <p className="tm_mb2">
                  <b className="tm_primary_color"> To:</b>
                </p>
                {data.client.name ? (
                  <p>
                    {data.client.name.sur_name} {data.client.name.first_name}
                    <br />
                    {data.client.name.email} <br />
                    {data.client.name.phone} <br />
                  </p>
                ) : (
                  <p>
                    Walkin Guest <br />
                  </p>
                )}
              </div>
              <div className="tm_invoice_right tm_text_right">
                <p className="tm_mb2">
                  <b className="tm_primary_color">Pay To:</b>
                </p>
                <p>
                  {company.name} <br />
                  {company.address}
                  <br />
                  {company.email} <br />
                  {company.phone}
                </p>
              </div>
            </div>
            <div className="tm_table tm_style1 tm_mb30">
              <div className="tm_round_border">
                <div className="tm_table_responsive">
                  <table>
                    <thead>
                      <tr>
                        <th className="tm_width_3 tm_semi_bold tm_primary_color tm_gray_bg">
                          Item
                        </th>
                        <th className="tm_width_2 tm_semi_bold tm_primary_color tm_gray_bg">
                          Price
                        </th>
                        <th className="tm_width_1 tm_semi_bold tm_primary_color tm_gray_bg">
                          Qty
                        </th>
                        <th className="tm_width_2 tm_semi_bold tm_primary_color tm_gray_bg tm_text_right">
                          Total
                        </th>
                      </tr>
                    </thead>
                    <tbody>
                      {data.invoice.items.map((item) => {
                        return (
                          <tr>
                            <td className="tm_width_3">{item.name}</td>
                            <td className="tm_width_2">{item.price}</td>
                            <td className="tm_width_1">{item.quantity}</td>
                            <td className="tm_width_2 tm_text_right">
                              {item.price * item.quantity}
                            </td>
                          </tr>
                        );
                      })}
                    </tbody>
                  </table>
                </div>
              </div>
              <div className="tm_invoice_footer">
                <div className="tm_left_footer">
                  <p className="tm_mb2">
                    <b className="tm_primary_color">{data.operator.title} :</b>
                  </p>
                  <p className="tm_m0">{data.operator.name}</p>
                </div>
                <div className="tm_right_footer">
                  <table>
                    <tbody>
                      <tr>
                        <td className="tm_width_3 tm_primary_color tm_border_none tm_bold">
                          Subtoal
                        </td>
                        <td className="tm_width_3 tm_primary_color tm_text_right tm_border_none tm_bold">
                          {numberFormat(Subtoal)}
                        </td>
                      </tr>
                      <tr>
                        <td className="tm_width_3 tm_primary_color tm_border_none tm_pt0">
                          Tax <span className="tm_ternary_color">(18%)</span>
                        </td>
                        <td className="tm_width_3 tm_primary_color tm_text_right tm_border_none tm_pt0">
                          {numberFormat(Tax)}
                        </td>
                      </tr>
                      <tr>
                        <td className="tm_width_3 tm_primary_color tm_border_none tm_pt0">
                          GrandTotal
                        </td>
                        <td className="tm_width_3 tm_primary_color tm_text_right tm_border_none tm_pt0">
                          {numberFormat(GrandTotal)}
                        </td>
                      </tr>
                      <tr className="tm_border_top tm_border_bottom">
                        <td className="tm_width_3 tm_border_top_0 tm_bold tm_f16 tm_primary_color">
                          Paid{" "}
                        </td>
                        <td className="tm_width_3 tm_border_top_0 tm_bold tm_f16 tm_primary_color tm_text_right">
                          {numberFormat(data.amount)}
                        </td>
                      </tr>
                      <tr>
                        <td className="tm_width_3 tm_primary_color tm_border_none tm_pt0">
                          Total Paid
                        </td>
                        <td className="tm_width_3 tm_primary_color tm_text_right tm_border_none tm_pt0">
                          {numberFormat(paid)}
                        </td>
                      </tr>
                      <tr>
                        <td className="tm_width_3 tm_primary_color tm_border_none tm_pt0">
                          Balance
                        </td>
                        <td className="tm_width_3 tm_primary_color tm_text_right tm_border_none tm_pt0">
                          {numberFormat(GrandTotal - paid)}
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
            {/* <div className="tm_padd_15_20 tm_round_border">
              <p className="tm_mb5">
                <b className="tm_primary_color">Terms &amp; Conditions:</b>
              </p>
              <ul className="tm_m0 tm_note_list">
                <li>
                  All claims relating to quantity or shipping errors shall be
                  waived by Buyer unless made in writing to Seller within thirty
                  (30) days after delivery of goods to the address stated.
                </li>
                <li>
                  Delivery dates are not guaranteed and Seller has no liability
                  for damages that may be incurred due to any delay in shipment
                  of goods hereunder. Taxes are excluded unless otherwise
                  stated.
                </li>
              </ul>
            </div> */}
            {/* .tm_note */}
          </div>
        </div>
      </div>
    </div>
  );
};

export default A4;