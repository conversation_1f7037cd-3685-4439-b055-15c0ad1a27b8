import "./thermal.css";
import React, { useMemo } from "react";
import ReferenceNumber from "../../../../Components/ReferenceNumber";
import RenderBlob from "../../../../Components/RenderBlob";
import moment from "moment";
import { numberFormat } from "../../../../Utils/functions";


const Thermal = ({ company, data, documentTitle, taxable = false }) => {
  const { paid = 0 } = data;

  // Memoize calculations to prevent recalculation on re-renders
  const { subtotal, tax, grandTotal, balance } = useMemo(() => {
    const subtotal = data.invoice
      ? data.invoice.items.reduce((acc, item) => {
        return acc + item.price * item.quantity;
      }, 0)
      : 0;

    const tax = taxable ? subtotal * 0.18 : 0;
    const grandTotal = subtotal + tax;
    const balance = grandTotal - paid;

    return { subtotal, tax, grandTotal, balance };
  }, [data.invoice, taxable, paid]);

  return (
    <>
      <div className="tm_pos_invoice_wrap">
        <div className="tm_pos_invoice_top">
          {company.logo && <RenderBlob blob={company.logo} size={100} />}
          <div className="tm_pos_company_name">{company.name}</div>
          <div className="tm_pos_company_address">{company.address}</div>
          <div className="tm_pos_company_mobile">{company.email}</div>
        </div>
        <div className="tm_pos_invoice_body">
          <div className="tm_pos_invoice_heading">
            <span>
              <strong>{documentTitle}</strong>
            </span>
          </div>
          <table>
            <tr>
              <td>
                {data.client && data.client.name ? (
                  <p style={{ fontSize: "10px" }}>
                    {data.client.name.sur_name} {data.client.name.first_name}
                  </p>
                ) : (
                  <p>
                    Walkin Guest <br />
                  </p>
                )}
              </td>
              <td style={{ textAlign: "right" }}>
                <p style={{ fontSize: "10px" }}>
                  {moment(data.date).format("Do MMM YY")} <br />#{" "}
                  <strong>
                    {data.referenceNumber ? (
                      <ReferenceNumber value={data.referenceNumber} />
                    ) : (
                      data.id || data._id
                    )}
                  </strong>
                </p>
              </td>
            </tr>
          </table>
          <table className="tm_pos_invoice_table" style={{ fontSize: "10px" }}>
            <thead>
              <tr>
                <th>Item</th>
                <th>Price</th>
                <th>Qty</th>
                <th>Total</th>
              </tr>
            </thead>
            <tbody>
              {data.invoice.items.map((item) => {
                return (
                  <tr>
                    <td>{item.name}</td>
                    <td>{item.price}</td>
                    <td>{item.quantity}</td>
                    <td>{item.price * item.quantity}</td>
                  </tr>
                );
              })}
            </tbody>
          </table>
          <div className="tm_bill_list" style={{ fontSize: "10px" }}>
            <div className="tm_bill_list_in">
              <div className="tm_bill_title">Sub-Total:</div>
              <div className="tm_bill_value">{numberFormat(subtotal)}</div>
            </div>
            {/* <div className="tm_bill_list_in">
              <div className="tm_bill_title">Discount: </div>
              <div className="tm_bill_value">-$150.00</div>
            </div>
            <div className="tm_invoice_seperator"></div>
            <div className="tm_bill_list_in">
              <div className="tm_bill_title">Service charge:</div>
              <div className="tm_bill_value">0.00tk</div>
            </div> */}
            {tax > 0 && (
              <div className="tm_bill_list_in">
                <div className="tm_bill_title">Tax(18%):</div>
                <div className="tm_bill_value">{numberFormat(tax)}</div>
              </div>
            )}
            <div className="tm_invoice_seperator"></div>
            <div className="tm_bill_list_in">
              <div className="tm_bill_title tm_bill_focus">Total payable:</div>
              <div className="tm_bill_value tm_bill_focus">
                {numberFormat(grandTotal)}
              </div>
            </div>
            <div className="tm_bill_list_in" style={{ fontSize: "18px" }}>
              <div className="tm_bill_title tm_bill_focus">Paid:</div>
              <div className="tm_bill_value tm_bill_focus">
                {numberFormat(data.amount)}
              </div>
            </div>
            {data.amount !== grandTotal && (
              <div className="tm_bill_list_in" style={{ fontSize: "15px" }}>
                <div className="tm_bill_title tm_bill_focus">Total Paid:</div>
                <div className="tm_bill_value tm_bill_focus">
                  {numberFormat(paid)}
                </div>
              </div>
            )}
            {data.amount !== grandTotal && (
              <div className="tm_bill_list_in" style={{ fontSize: "15px" }}>
                <div className="tm_bill_title tm_bill_focus">Balance:</div>
                <div className="tm_bill_value tm_bill_focus">
                  {numberFormat(balance)}
                </div>
              </div>
            )}
          </div>
          <div className="tm_pos_sample_text">
            {data.operator.title} :<strong> {data.operator.name}</strong>
          </div>
          <div className="tm_pos_invoice_footer">Thank you, Come again!</div>
        </div>
      </div>
    </>
  );
};

export default Thermal;