import Details from "./Details";
import React from "react";
import moment from "moment";
import { PageHeader } from "@ant-design/pro-components";
import { Tabs } from "antd";


const { TabPane } = Tabs;

const AccountTransfer = (props) => {
  const { data } = props;

  if (!data) return null;

  return (
    <PageHeader
      className="site-page-header-responsive"
      onBack={() => window.history.back()}
      title={`Transfer #${data._id}`}
      subTitle={`${data.status.charAt(0).toUpperCase() + data.status.slice(1)} - ${moment(data.date).format('DD MMM YYYY')}`}
      footer={
        <Tabs defaultActiveKey="1" type="card">
          <TabPane tab="Details" key="1">
            <Details {...data} />
          </TabPane>
        </Tabs>
      }
    />
  );
};

export default AccountTransfer;
