import React from "react";
import moment from "moment";
import { ArrowRightOutlined, BankOutlined, CalendarOutlined, DollarOutlined, FileTextOutlined } from "@ant-design/icons";
import { Card, Descriptions, Tag, Row, Col, Divider, Typography } from "antd";
import { formatNumber } from "../../../../Utils/functions";


const { Title, Text } = Typography;

const Details = (transfer) => {
  const getStatusColor = (status) => {
    const colors = {
      completed: 'success',
      pending: 'warning',
      failed: 'error'
    };
    return colors[status] || 'default';
  };

  return (
    <Card>
      <Row gutter={[24, 24]}>
        <Col span={24}>
          <div style={{ textAlign: 'center', marginBottom: 24 }}>
            <Title level={3}>Account Transfer</Title>
            <Text type="secondary">Reference: {transfer.reference || transfer._id}</Text>
          </div>
        </Col>

        <Col span={24}>
          <Card 
            bordered={false} 
            style={{ 
              background: '#f5f5f5', 
              borderRadius: '8px',
              boxShadow: '0 2px 8px rgba(0, 0, 0, 0.09)'
            }}
          >
            <Row align="middle" justify="space-around">
              <Col span={10} style={{ textAlign: 'center' }}>
                <BankOutlined style={{ fontSize: '24px', color: '#1890ff' }} />
                <div style={{ marginTop: 8 }}>
                  <Text strong style={{ fontSize: '16px', display: 'block' }}>
                    {transfer.from_account?.label || 'Unknown Account'}
                  </Text>
                  <Text type="secondary">Source Account</Text>
                </div>
              </Col>
              
              <Col span={4} style={{ textAlign: 'center' }}>
                <ArrowRightOutlined style={{ fontSize: '24px', color: '#52c41a' }} />
              </Col>
              
              <Col span={10} style={{ textAlign: 'center' }}>
                <BankOutlined style={{ fontSize: '24px', color: '#1890ff' }} />
                <div style={{ marginTop: 8 }}>
                  <Text strong style={{ fontSize: '16px', display: 'block' }}>
                    {transfer.to_account?.label || 'Unknown Account'}
                  </Text>
                  <Text type="secondary">Destination Account</Text>
                </div>
              </Col>
            </Row>
          </Card>
        </Col>

        <Col span={24}>
          <Card 
            bordered={false} 
            style={{ 
              background: '#f0f7ff', 
              borderRadius: '8px',
              boxShadow: '0 2px 8px rgba(0, 0, 0, 0.09)'
            }}
          >
            <Row gutter={[16, 16]}>
              <Col span={8}>
                <div style={{ textAlign: 'center' }}>
                  <DollarOutlined style={{ fontSize: '24px', color: '#1890ff' }} />
                  <div style={{ marginTop: 8 }}>
                    <Text strong style={{ fontSize: '18px', display: 'block' }}>
                      {formatNumber(transfer.amount)}
                    </Text>
                    <Text type="secondary">Transfer Amount</Text>
                  </div>
                </div>
              </Col>
              
              <Col span={8}>
                <div style={{ textAlign: 'center' }}>
                  <DollarOutlined style={{ fontSize: '24px', color: '#faad14' }} />
                  <div style={{ marginTop: 8 }}>
                    <Text strong style={{ fontSize: '18px', display: 'block' }}>
                      {formatNumber(transfer.charges || 0)}
                    </Text>
                    <Text type="secondary">Transfer Charges</Text>
                  </div>
                </div>
              </Col>
              
              <Col span={8}>
                <div style={{ textAlign: 'center' }}>
                  <DollarOutlined style={{ fontSize: '24px', color: '#52c41a' }} />
                  <div style={{ marginTop: 8 }}>
                    <Text strong style={{ fontSize: '18px', display: 'block' }}>
                      {formatNumber((transfer.amount || 0) + (transfer.charges || 0))}
                    </Text>
                    <Text type="secondary">Total Amount</Text>
                  </div>
                </div>
              </Col>
            </Row>
          </Card>
        </Col>

        <Col span={24}>
          <Divider orientation="left">Transfer Details</Divider>
          <Descriptions bordered column={2}>
            <Descriptions.Item label="Date">
              <CalendarOutlined style={{ marginRight: 8 }} />
              {moment(transfer.date).format('DD MMM YYYY')}
            </Descriptions.Item>
            <Descriptions.Item label="Status">
              <Tag color={getStatusColor(transfer.status)}>
                {transfer.status?.toUpperCase()}
              </Tag>
            </Descriptions.Item>
            <Descriptions.Item label="Reference" span={2}>
              <FileTextOutlined style={{ marginRight: 8 }} />
              {transfer.reference || 'N/A'}
            </Descriptions.Item>
            {transfer.notes && (
              <Descriptions.Item label="Notes" span={2}>
                {transfer.notes}
              </Descriptions.Item>
            )}
          </Descriptions>
        </Col>
      </Row>
    </Card>
  );
};

export default Details;