.permission-card {
    transition: all 0.3s;
    border-radius: 8px;
}

.permission-card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
}

.permission-card .ant-card-head {
    background-color: #f5f5f5;
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
    padding: 8px 16px;
}

.permission-card .ant-card-head-title {
    font-weight: 600;
    font-size: 14px;
    white-space: normal;
    text-overflow: ellipsis;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}

.permission-item {
    padding: 8px;
    border-radius: 4px;
    margin-bottom: 4px;
    transition: background-color 0.3s;
}

.permission-item:hover {
    background-color: #f0f0f0;
}

.permission-label {
    margin-left: 4px;
    display: flex;
    align-items: center;
    gap: 6px;
}

/* Custom colors for permission types */
.permission-item:nth-child(1):hover {
    background-color: rgba(24, 144, 255, 0.1);
}

.permission-item:nth-child(2):hover {
    background-color: rgba(82, 196, 26, 0.1);
}

.permission-item:nth-child(3):hover {
    background-color: rgba(114, 46, 209, 0.1);
}

.permission-item:nth-child(4):hover {
    background-color: rgba(245, 34, 45, 0.1);
}
