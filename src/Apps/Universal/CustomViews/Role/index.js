import './styles.css';
import React from "react";
import { Checkbox, Badge, Card, Descriptions, Typography, Space, Tag, Row, Col, Tooltip } from "antd";
import { KeyOutlined, UserOutlined, SafetyOutlined, DashboardOutlined, EyeOutlined, PlusOutlined, EditOutlined, DeleteOutlined } from "@ant-design/icons";


const { Title } = Typography;

const Role = (props) => {
    const { data } = props;

    // Count active permissions
    const getPermissionCounts = () => {
        if (!data.permissions) return { total: 0, view: 0, create: 0, update: 0, delete: 0 };

        return data.permissions.reduce((counts, perm) => {
            counts.total++;
            if (perm.view) counts.view++;
            if (perm.create) counts.create++;
            if (perm.update) counts.update++;
            if (perm.delete) counts.delete++;
            return counts;
        }, { total: 0, view: 0, create: 0, update: 0, delete: 0 });
    };

    const permCounts = getPermissionCounts();

    return (
        <Card
            title={
                <Badge.Ribbon
                    text={data.root ? "Super User" : (data.advanced_dashboard ? "Advanced Dashboard" : "Standard Dashboard")}
                    color={data.root ? "green" : (data.advanced_dashboard ? "blue" : "gray")}
                >
                    <Space>
                        <KeyOutlined />
                        <Title level={4} style={{ margin: 0 }}>{data.name}</Title>
                    </Space>
                </Badge.Ribbon>
            }
            style={{ marginTop: 24 }}
        >
            {/* Details Section */}
            <Card title={<span><UserOutlined /> Details</span>} style={{ marginBottom: 16 }}>
                <Descriptions bordered column={2}>
                    <Descriptions.Item label="Role Name">{data.name}</Descriptions.Item>
                    <Descriptions.Item label="Role Type">
                        {data.root ?
                            <Tag color="green">Super User</Tag> :
                            (data.advanced_dashboard ?
                                <Tag color="blue">Advanced Dashboard</Tag> :
                                <Tag color="default">Standard Dashboard</Tag>)
                        }
                    </Descriptions.Item>
                    <Descriptions.Item label="Access Level">
                        {data.root ? "Full System Access" : "Limited Access"}
                    </Descriptions.Item>
                    <Descriptions.Item label="Permission Summary">
                        <Space>
                            <Tag color="cyan">View: {permCounts.view}</Tag>
                            <Tag color="blue">Create: {permCounts.create}</Tag>
                            <Tag color="purple">Update: {permCounts.update}</Tag>
                            <Tag color="red">Delete: {permCounts.delete}</Tag>
                        </Space>
                    </Descriptions.Item>
                </Descriptions>
            </Card>

            {/* Dashboard Access Section (conditional) */}
            {data.advanced_dashboard && (
                <Card title={<span><DashboardOutlined /> Dashboard Access</span>} style={{ marginBottom: 16 }}>
                    <Descriptions bordered column={1}>
                        <Descriptions.Item label="Dashboard Type">
                            <Tag color="blue">Advanced Dashboard</Tag>
                        </Descriptions.Item>
                        <Descriptions.Item label="Description">
                            This role has access to advanced dashboard features including detailed analytics,
                            business intelligence reports, and enhanced data visualization tools.
                        </Descriptions.Item>
                    </Descriptions>
                </Card>
            )}

            {/* Permissions Section */}
            <Card title={<span><SafetyOutlined /> Permissions</span>}>
                {data.permissions && (
                    <>
                        {data.permissions.filter(permission =>
                            permission.view || permission.create || permission.update || permission.delete
                        ).length === 0 ? (
                            <div style={{ textAlign: 'center', padding: '20px' }}>
                                <Title level={5} style={{ color: '#999' }}>No permissions assigned to this role</Title>
                            </div>
                        ) : (
                            <Row gutter={[16, 16]}>
                                {data.permissions
                                    .filter(permission => permission.view || permission.create || permission.update || permission.delete)
                                    .map((permission) => (
                                        <Col xs={24} sm={12} md={8} lg={6} key={permission.module}>
                                            <Card
                                                size="small"
                                                title={permission.module}
                                                style={{ height: '100%' }}
                                                className="permission-card"
                                            >
                                                <Space direction="vertical" style={{ width: '100%' }}>
                                                    <Tooltip title="View permission allows users to see and access this module">
                                                        <div className="permission-item">
                                                            <Space>
                                                                <Checkbox checked={permission.view} disabled />
                                                                <span className="permission-label">
                                                                    <EyeOutlined style={{ color: permission.view ? '#1890ff' : '#d9d9d9' }} /> View
                                                                </span>
                                                            </Space>
                                                        </div>
                                                    </Tooltip>

                                                    <Tooltip title="Create permission allows users to add new items in this module">
                                                        <div className="permission-item">
                                                            <Space>
                                                                <Checkbox checked={permission.create} disabled />
                                                                <span className="permission-label">
                                                                    <PlusOutlined style={{ color: permission.create ? '#52c41a' : '#d9d9d9' }} /> Create
                                                                </span>
                                                            </Space>
                                                        </div>
                                                    </Tooltip>

                                                    <Tooltip title="Update permission allows users to modify existing items in this module">
                                                        <div className="permission-item">
                                                            <Space>
                                                                <Checkbox checked={permission.update} disabled />
                                                                <span className="permission-label">
                                                                    <EditOutlined style={{ color: permission.update ? '#722ed1' : '#d9d9d9' }} /> Update
                                                                </span>
                                                            </Space>
                                                        </div>
                                                    </Tooltip>

                                                    <Tooltip title="Delete permission allows users to remove items from this module">
                                                        <div className="permission-item">
                                                            <Space>
                                                                <Checkbox checked={permission.delete} disabled />
                                                                <span className="permission-label">
                                                                    <DeleteOutlined style={{ color: permission.delete ? '#f5222d' : '#d9d9d9' }} /> Delete
                                                                </span>
                                                            </Space>
                                                        </div>
                                                    </Tooltip>
                                                </Space>
                                            </Card>
                                        </Col>
                                    ))}
                            </Row>
                        )}
                    </>
                )}
            </Card>
        </Card>
    );
};

export default Role;