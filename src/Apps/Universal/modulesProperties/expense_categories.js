import ExpenseCategory from "../CustomViews/ExpenseCategory";
import React from "react";
import { PlusOutlined, PrinterOutlined  } from "@ant-design/icons";
import { TableDropdown  } from "@ant-design/pro-components";
import { message  } from "antd";


const expense_categories = {
  CustomView: (data) => <ExpenseCategory {...data} />,

  // Add more actions menu for expense categories
  MoreActions: (props) => {
    const {
      pouchDatabase,
      collection,
      databasePrefix,
      record,
      actionRef,
    } = props;

    return (
      <TableDropdown
        key="actionGroup"
        onSelect={async (key) => {
          try {
            if (key === "add_subcategory") {
              // This would typically open a form to add a subcategory
              // For now, we'll just show a message
              message.info("Add subcategory functionality would open here");
            }
          } catch (error) {
            console.error("Error performing expense category action:", error);
            message.error("Failed to perform action");
          }
        }}
        menus={[
          {
            key: "add_subcategory",
            name: (
              <span>
                <PlusOutlined /> Add Subcategory
              </span>
            ),
          },
          {
            key: "print_details",
            name: (
              <span>
                <PrinterOutlined /> Print Category Details
              </span>
            ),
          }
        ]}
      />
    );
  }
};

export default expense_categories;
