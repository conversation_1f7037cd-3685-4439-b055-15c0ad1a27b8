import PouchDb from "pouchdb-browser";
import React, { useRef } from "react";
import { BetaSchemaForm, TableDropdown  } from "@ant-design/pro-components";
import { ReconciliationOutlined, BankOutlined  } from "@ant-design/icons";
import { formatNumber  } from "../../../Utils/functions";
import { message  } from "antd";


const enhanced_accounts = {
    CustomView: (data) => {
        // We'll implement a custom view for enhanced accounts later
        return (
            <div>
                <h2>{data.data.name} ({data.data.code})</h2>
                <p><strong>Type:</strong> {data.data.category} - {data.data.type}</p>
                <p><strong>Current Balance:</strong> {formatNumber(data.data.current_balance || data.data.opening_balance || 0)}</p>
                <p><strong>Description:</strong> {data.data.description}</p>
            </div>
        );
    },
    MoreActions: (props) => {
        const action = useRef();
        const {
            pouchDatabase,
            databasePrefix,
            record,
            CRUD_USER
        } = props;

        return (
            <TableDropdown
                key="actionGroup"
                menus={[
                    {
                        key: "reconcile_account",
                        name: (
                            <BetaSchemaForm
                                formRef={action}
                                submitter={{
                                    searchConfig: {
                                        resetText: "Cancel",
                                        submitText: "Save",
                                    },
                                }}
                                modalProps={{ centered: true }}
                                grid={true}
                                trigger={
                                    <a key="button" type="primary">
                                        <ReconciliationOutlined /> Reconcile Account
                                    </a>
                                }
                                title={"Reconcile Account"}
                                onFinish={async (values) => {
                                    // Create a reconciliation record
                                    await pouchDatabase("account_reconciliations", databasePrefix)
                                        .saveDocument({
                                            ...values,
                                            account: {
                                                label: record.name,
                                                value: record._id
                                            },
                                            createdAt: new Date().toISOString()
                                        }, CRUD_USER);

                                    // Update the account balance
                                    const accountDoc = await pouchDatabase("enhanced_accounts", databasePrefix).get(record._id);
                                    const currentBalance = accountDoc.current_balance || accountDoc.opening_balance || 0;
                                    const newBalance = currentBalance + (values.amount || 0);

                                    await pouchDatabase("enhanced_accounts", databasePrefix).put({
                                        ...accountDoc,
                                        current_balance: newBalance,
                                        updatedAt: new Date().toISOString()
                                    });

                                    message.success("Account reconciled successfully");
                                    return true;
                                }}
                                columns={[
                                    {
                                        title: "Date",
                                        dataIndex: "date",
                                        valueType: "date",
                                        isRequired: true,
                                        initialValue: new Date(),
                                    },
                                    {
                                        title: "Amount",
                                        dataIndex: "amount",
                                        valueType: "money",
                                        isRequired: true,
                                        tooltip: "Can be positive (credit) or negative (debit)"
                                    },
                                    {
                                        title: "Reason",
                                        dataIndex: "reason",
                                        valueType: "textarea",
                                        isRequired: true,
                                        colProps: {
                                            span: 24,
                                        },
                                    },
                                    {
                                        title: "Status",
                                        dataIndex: "status",
                                        valueType: "select",
                                        valueEnum: {
                                            completed: { text: "Completed", status: "Success" },
                                            pending: { text: "Pending", status: "Warning" },
                                            cancelled: { text: "Cancelled", status: "Error" }
                                        },
                                        initialValue: "completed",
                                    }
                                ]}
                            />
                        ),
                    },
                ]}
            />
        );
    },
    buffData: async (results, { pouchDatabase, databasePrefix }) => {
        // Get all transactions that affect account balances
        const receipts = await pouchDatabase("receipts", databasePrefix).getAllData();
        const expenses = await pouchDatabase("expenses", databasePrefix).getAllData();
        const transfers = await pouchDatabase("account_transfers", databasePrefix).getAllData();
        const reconciliations = await pouchDatabase("account_reconciliations", databasePrefix).getAllData();

        return results.map(account => {
            // Initialize balance with opening balance if it exists
            let balance = Number(account.opening_balance) || 0;

            // Add receipts
            receipts
                .filter(receipt => receipt.account?.value === account._id)
                .forEach(receipt => {
                    balance += Number(receipt.amount) || 0;
                });

            // Subtract expenses
            expenses
                .filter(expense => expense.account?.value === account._id)
                .forEach(expense => {
                    balance -= Number(expense.amount) || 0;
                });

            // Process transfers
            transfers.forEach(transfer => {
                if (transfer.from_account?.value === account._id) {
                    balance -= Number(transfer.amount) || 0;
                    // Also subtract transfer charges if applicable
                    if (transfer.charges) {
                        balance -= Number(transfer.charges) || 0;
                    }
                }
                if (transfer.to_account?.value === account._id) {
                    balance += Number(transfer.amount) || 0;
                }
            });

            // Process reconciliations
            reconciliations
                .filter(r => r.status === 'completed' && 
                    ((r.account && r.account.value === account._id) || 
                     (r.account && r.account._id === account._id)))
                .forEach(reconciliation => {
                    balance += Number(reconciliation.amount) || 0;
                });

            // For contra accounts, we might want to negate the balance
            if (account.is_contra) {
                balance = -balance;
            }

            return {
                ...account,
                current_balance: balance,
                transaction_count:
                    receipts.filter(r => r.account?.value === account._id).length +
                    expenses.filter(e => e.account?.value === account._id).length +
                    transfers.filter(t => t.from_account?.value === account._id || t.to_account?.value === account._id).length +
                    reconciliations.filter(r => r.status === 'completed' &&
                        ((r.account && r.account.value === account._id) ||
                            (r.account && r.account._id === account._id))).length
            };
        });
    }
};

export default enhanced_accounts;
