import React from "react";
import dayjs from "dayjs";
import { formatNumber  } from "../../../Utils/functions";


const inventory_valuations = {
    CustomView: (data) => {
        const valuation = data.data;
        
        return (
            <div>
                <h2>Inventory Valuation</h2>
                <p><strong>Product:</strong> {valuation.product?.label || 'Unknown Product'}</p>
                <p><strong>Date:</strong> {dayjs(valuation.date).format('DD/MM/YYYY')}</p>
                <p><strong>Valuation Method:</strong> {valuation.valuation_method}</p>
                <p><strong>Quantity:</strong> {valuation.quantity}</p>
                <p><strong>Unit Cost:</strong> {formatNumber(valuation.unit_cost)}</p>
                <p><strong>Total Value:</strong> {formatNumber(valuation.total_value)}</p>
                
                {valuation.previous_valuation !== undefined && (
                    <p><strong>Previous Valuation:</strong> {formatNumber(valuation.previous_valuation)}</p>
                )}
                
                {valuation.valuation_change !== undefined && (
                    <p>
                        <strong>Valuation Change:</strong> 
                        <span style={{ 
                            color: valuation.valuation_change >= 0 ? '#52c41a' : '#f5222d',
                            marginLeft: '8px'
                        }}>
                            {valuation.valuation_change >= 0 ? '+' : ''}
                            {formatNumber(valuation.valuation_change)}
                        </span>
                    </p>
                )}
                
                <p><strong>Status:</strong> {valuation.status}</p>
                
                {valuation.notes && (
                    <div style={{ marginTop: 20 }}>
                        <h3>Notes</h3>
                        <p>{valuation.notes}</p>
                    </div>
                )}
            </div>
        );
    },
    buffData: async (results, { pouchDatabase, databasePrefix }) => {
        // Get all products
        const products = await pouchDatabase("products", databasePrefix).getAllData();
        
        return results.map(valuation => {
            // Find the associated product
            const productId = valuation.product?.value || valuation.product?._id;
            const product = products.find(p => p._id === productId);
            
            return {
                ...valuation,
                product_name: product?.name || 'Unknown Product',
                product_code: product?.code || '',
            };
        });
    }
};

export default inventory_valuations;
