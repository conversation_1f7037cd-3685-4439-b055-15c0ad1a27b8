import Expense from "../CustomViews/Expense";
import React from "react";
import { PrinterOutlined, CheckCircleOutlined  } from "@ant-design/icons";
import { TableDropdown  } from "@ant-design/pro-components";
import { message  } from "antd";


const expenses = {
    CustomView: (data) => <Expense {...data} />,

    // Add more actions menu for expenses
    MoreActions: (props) => {
        const {
            pouchDatabase,
            collection,
            databasePrefix,
            record,
            CRUD_USER,
            actionRef,
        } = props;

        return (
            <TableDropdown
                key="actionGroup"
                onSelect={async (key) => {
                    try {
                        if (key === "mark_approved") {
                            // Get the expense document
                            const expenseDoc = await pouchDatabase(
                                collection,
                                databasePrefix
                            ).get(record._id);

                            // Update the expense document
                            await pouchDatabase(collection, databasePrefix).put({
                                ...expenseDoc,
                                status: "approved",
                                approved_at: new Date().toISOString(),
                                approved_by: CRUD_USER._id
                            });

                            message.success("Expense marked as approved");

                            // Refresh the table
                            if (actionRef.current) {
                                actionRef.current.reload();
                            }
                        }
                    } catch (error) {
                        console.error("Error approving expense:", error);
                        message.error("Failed to approve expense");
                    }
                }}
                menus={[
                    {
                        key: "mark_approved",
                        name: (
                            <span>
                                <CheckCircleOutlined /> Mark as Approved
                            </span>
                        ),
                    },
                    {
                        key: "print_voucher",
                        name: (
                            <span>
                                <PrinterOutlined /> Print Voucher
                            </span>
                        ),
                    }
                ]}
            />
        );
    },

    // Add any additional properties or functions needed for expenses
    buffResults: (results) => {
        // Process expense results if needed
        return results.map(expense => {
            // Add any computed properties here
            return {
                ...expense,
                // Add status if not present
                status: expense.status || "pending"
            };
        });
    },

    // Add statistics for expenses
    statistics: async function expensesStatistics(pouchDatabase, databasePrefix = '') {
        try {
            if (!pouchDatabase) {
                console.error("pouchDatabase is required for expensesStatistics");
                return [{ title: "Total Expenses", value: 0 }];
            }

            const expensesDb = pouchDatabase("expenses", databasePrefix);

            // Get all expenses
            const allExpenses = await expensesDb.getAllData();

            // Calculate date ranges
            const now = new Date();
            const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
            const yesterday = new Date(today);
            yesterday.setDate(yesterday.getDate() - 1);

            const thisMonth = new Date(now.getFullYear(), now.getMonth(), 1);
            const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);
            const lastMonthEnd = new Date(now.getFullYear(), now.getMonth(), 0);

            // Filter expenses by date ranges
            const todayExpenses = allExpenses.filter(expense => {
                const expenseDate = new Date(expense.date);
                return expenseDate >= today;
            });

            const yesterdayExpenses = allExpenses.filter(expense => {
                const expenseDate = new Date(expense.date);
                return expenseDate >= yesterday && expenseDate < today;
            });

            const thisMonthExpenses = allExpenses.filter(expense => {
                const expenseDate = new Date(expense.date);
                return expenseDate >= thisMonth;
            });

            const lastMonthExpenses = allExpenses.filter(expense => {
                const expenseDate = new Date(expense.date);
                return expenseDate >= lastMonth && expenseDate <= lastMonthEnd;
            });

            // Calculate expense statistics by category
            const expensesByCategory = allExpenses.reduce((acc, expense) => {
                const category = expense['expense-category']?.label || 'Uncategorized';
                acc[category] = (acc[category] || 0) + (parseFloat(expense.amount) || 0);
                return acc;
            }, {});

            // Separate job-related vs general expenses
            const jobRelatedExpenses = allExpenses.filter(expense => expense.job);
            const generalExpenses = allExpenses.filter(expense => !expense.job);

            // Calculate totals
            const totalAmount = allExpenses.reduce((sum, expense) => sum + (parseFloat(expense.amount) || 0), 0);
            const thisMonthTotal = thisMonthExpenses.reduce((sum, expense) => sum + (parseFloat(expense.amount) || 0), 0);
            const lastMonthTotal = lastMonthExpenses.reduce((sum, expense) => sum + (parseFloat(expense.amount) || 0), 0);
            const todayTotal = todayExpenses.reduce((sum, expense) => sum + (parseFloat(expense.amount) || 0), 0);
            const yesterdayTotal = yesterdayExpenses.reduce((sum, expense) => sum + (parseFloat(expense.amount) || 0), 0);

            const jobRelatedTotal = jobRelatedExpenses.reduce((sum, expense) => sum + (parseFloat(expense.amount) || 0), 0);
            const generalTotal = generalExpenses.reduce((sum, expense) => sum + (parseFloat(expense.amount) || 0), 0);

            const averageExpenseAmount = allExpenses.length > 0 ? totalAmount / allExpenses.length : 0;

            // Calculate percentage changes
            const monthlyChange = lastMonthTotal > 0
                ? ((thisMonthTotal - lastMonthTotal) / lastMonthTotal) * 100
                : thisMonthTotal > 0 ? 100 : 0;

            const dailyChange = yesterdayTotal > 0
                ? ((todayTotal - yesterdayTotal) / yesterdayTotal) * 100
                : todayTotal > 0 ? 100 : 0;

            return [
                {
                    title: "Total Expenses",
                    value: allExpenses.length,
                    precision: 0
                },
                {
                    title: "Expenses This Month",
                    value: thisMonthTotal,
                    prefix: "UGX ",
                    suffix: `${monthlyChange >= 0 ? '+' : ''}${monthlyChange.toFixed(1)}%`,
                    precision: 0
                },
                {
                    title: "Expenses Today",
                    value: todayTotal,
                    prefix: "UGX ",
                    suffix: `${dailyChange >= 0 ? '+' : ''}${dailyChange.toFixed(1)}%`,
                    precision: 0
                },
                {
                    title: "Average Expense Amount",
                    value: averageExpenseAmount,
                    prefix: "UGX ",
                    precision: 0
                },
                {
                    title: "Job-Related Expenses",
                    value: jobRelatedTotal,
                    prefix: "UGX ",
                    precision: 0
                },
                {
                    title: "General Expenses",
                    value: generalTotal,
                    prefix: "UGX ",
                    precision: 0
                }
            ];
        } catch (error) {
            console.error("Error calculating expenses statistics:", error);
            return [
                { title: "Total Expenses", value: 0 },
                { title: "Expenses This Month", value: 0, prefix: "UGX " },
                { title: "Expenses Today", value: 0, prefix: "UGX " },
                { title: "Average Expense Amount", value: 0, prefix: "UGX " },
                { title: "Job-Related Expenses", value: 0, prefix: "UGX " },
                { title: "General Expenses", value: 0, prefix: "UGX " }
            ];
        }
    }
};

export default expenses;
