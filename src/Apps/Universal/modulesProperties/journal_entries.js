import React, { useRef } from "react";
import dayjs from "dayjs";
import { BetaSchemaForm, TableDropdown  } from "@ant-design/pro-components";
import { RollbackOutlined  } from "@ant-design/icons";
import { formatNumber  } from "../../../Utils/functions";
import { message, Table, Typography  } from "antd";


const { Text } = Typography;

const journal_entries = {
    CustomView: (data) => {
        const entry = data.data;
        
        // Calculate totals
        let totalDebits = 0;
        let totalCredits = 0;
        
        if (entry.entries && Array.isArray(entry.entries)) {
            entry.entries.forEach(line => {
                totalDebits += Number(line.debit || 0);
                totalCredits += Number(line.credit || 0);
            });
        }
        
        // Define columns for the entries table
        const columns = [
            {
                title: 'Account',
                dataIndex: 'account',
                key: 'account',
                render: (account) => account?.label || 'Unknown Account'
            },
            {
                title: 'Description',
                dataIndex: 'line_description',
                key: 'line_description',
            },
            {
                title: 'Debit',
                dataIndex: 'debit',
                key: 'debit',
                align: 'right',
                render: (value) => formatNumber(value || 0)
            },
            {
                title: 'Credit',
                dataIndex: 'credit',
                key: 'credit',
                align: 'right',
                render: (value) => formatNumber(value || 0)
            }
        ];
        
        return (
            <div>
                <h2>Journal Entry</h2>
                <p><strong>Reference:</strong> {entry.reference}</p>
                <p><strong>Date:</strong> {dayjs(entry.date).format('DD/MM/YYYY')}</p>
                <p><strong>Description:</strong> {entry.description}</p>
                <p><strong>Status:</strong> {entry.status}</p>
                
                <div style={{ marginTop: 20, marginBottom: 20 }}>
                    <Table 
                        dataSource={entry.entries || []} 
                        columns={columns} 
                        pagination={false}
                        rowKey={(record, index) => index}
                        summary={() => (
                            <Table.Summary fixed>
                                <Table.Summary.Row>
                                    <Table.Summary.Cell index={0} colSpan={2}>
                                        <Text strong>Total</Text>
                                    </Table.Summary.Cell>
                                    <Table.Summary.Cell index={2} align="right">
                                        <Text strong>{formatNumber(totalDebits)}</Text>
                                    </Table.Summary.Cell>
                                    <Table.Summary.Cell index={3} align="right">
                                        <Text strong>{formatNumber(totalCredits)}</Text>
                                    </Table.Summary.Cell>
                                </Table.Summary.Row>
                                <Table.Summary.Row>
                                    <Table.Summary.Cell index={0} colSpan={3}>
                                        <Text strong>Difference</Text>
                                    </Table.Summary.Cell>
                                    <Table.Summary.Cell index={3} align="right">
                                        <Text 
                                            strong 
                                            type={Math.abs(totalDebits - totalCredits) < 0.01 ? 'success' : 'danger'}
                                        >
                                            {formatNumber(Math.abs(totalDebits - totalCredits))}
                                        </Text>
                                    </Table.Summary.Cell>
                                </Table.Summary.Row>
                            </Table.Summary>
                        )}
                    />
                </div>
            </div>
        );
    },
    MoreActions: (props) => {
        const action = useRef();
        const {
            pouchDatabase,
            databasePrefix,
            record,
            CRUD_USER
        } = props;

        // Only show reverse option for posted entries
        if (record.status !== 'posted') {
            return null;
        }

        return (
            <TableDropdown
                key="actionGroup"
                menus={[
                    {
                        key: "reverse_entry",
                        name: (
                            <BetaSchemaForm
                                formRef={action}
                                submitter={{
                                    searchConfig: {
                                        resetText: "Cancel",
                                        submitText: "Reverse Entry",
                                    },
                                }}
                                modalProps={{ centered: true }}
                                grid={true}
                                trigger={
                                    <a key="button" type="primary">
                                        <RollbackOutlined /> Reverse Journal Entry
                                    </a>
                                }
                                title={"Reverse Journal Entry"}
                                onFinish={async (values) => {
                                    try {
                                        // Mark the original entry as reversed
                                        await pouchDatabase("journal_entries", databasePrefix).put({
                                            ...record,
                                            status: 'reversed',
                                            updatedAt: new Date().toISOString()
                                        });
                                        
                                        // Create a new reversing entry
                                        const reversedEntries = record.entries.map(entry => ({
                                            account: entry.account,
                                            debit: entry.credit || 0,
                                            credit: entry.debit || 0,
                                            line_description: entry.line_description
                                                ? `Reversal: ${entry.line_description}`
                                                : 'Reversal entry'
                                        }));
                                        
                                        await pouchDatabase("journal_entries", databasePrefix).saveDocument({
                                            date: values.date,
                                            reference: `Reversal of ${record.reference}`,
                                            description: values.description || `Reversal of journal entry ${record.reference}`,
                                            entries: reversedEntries,
                                            status: 'posted',
                                            original_entry: record._id,
                                            createdAt: new Date().toISOString(),
                                            createdBy: CRUD_USER
                                        }, CRUD_USER);
                                        
                                        message.success("Journal entry reversed successfully");
                                        return true;
                                    } catch (error) {
                                        console.error("Error reversing journal entry:", error);
                                        message.error("Failed to reverse journal entry");
                                        return false;
                                    }
                                }}
                                columns={[
                                    {
                                        title: "Date",
                                        dataIndex: "date",
                                        valueType: "date",
                                        isRequired: true,
                                        initialValue: dayjs().format('YYYY-MM-DD'),
                                    },
                                    {
                                        title: "Description",
                                        dataIndex: "description",
                                        valueType: "textarea",
                                        initialValue: `Reversal of journal entry ${record.reference}`,
                                        colProps: {
                                            span: 24,
                                        },
                                    }
                                ]}
                            />
                        ),
                    }
                ]}
            />
        );
    },
    buffData: async (results, { pouchDatabase, databasePrefix }) => {
        // Get all accounts
        const accounts = await pouchDatabase("enhanced_accounts", databasePrefix).getAllData();
        
        return results.map(entry => {
            // Calculate totals
            let totalDebits = 0;
            let totalCredits = 0;
            
            if (entry.entries && Array.isArray(entry.entries)) {
                // Enhance each entry line with account details
                entry.entries = entry.entries.map(line => {
                    const accountId = line.account?.value || line.account?._id;
                    const account = accounts.find(a => a._id === accountId);
                    
                    totalDebits += Number(line.debit || 0);
                    totalCredits += Number(line.credit || 0);
                    
                    return {
                        ...line,
                        account_name: account?.name || 'Unknown Account',
                        account_code: account?.code || '',
                    };
                });
            }
            
            return {
                ...entry,
                total_debits: totalDebits,
                total_credits: totalCredits,
                is_balanced: Math.abs(totalDebits - totalCredits) < 0.01
            };
        });
    }
};

export default journal_entries;
