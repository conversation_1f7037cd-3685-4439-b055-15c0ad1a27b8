import React, { useRef } from "react";
import RequisitionView from "../CustomViews/Requisition";
import { BetaSchemaForm, TableDropdown  } from "@ant-design/pro-components";
import { DollarOutlined, ExceptionOutlined, CheckCircleOutlined,   } from "@ant-design/icons";
import { Popconfirm, message  } from "antd";


const requisitions = {
  CustomView: (data) => <RequisitionView {...data} />,
  print: true,
  MoreActions: (props) => {

    const currentUser = JSON.parse(localStorage.getItem("LOCAL_STORAGE_USER"));

    const APP_USER_PERMISSIONS = JSON.parse(
      localStorage.getItem("APP_USER_PERMISSIONS")
    );

    const CanUpdateRequisition =
      APP_USER_PERMISSIONS.root ||
      APP_USER_PERMISSIONS.permissions.find((p) => p.module === "requisitions")
        .update;

    const action = useRef();

    const {
      pouchDatabase,
      collection,
      databasePrefix,
      record,
      CRUD_USER,
      singular,
      modules,
    } = props;

    const cancel = () => {
      //    setOpen(false);
      message.error("Click on cancel.");
    };

    const actionMenu = []

    !record.authorised && actionMenu.push({

      key: "Authorize Requisition",
      name: (
        <Popconfirm
          title="Authorize Requisition"
          description={`Authorize ${record.amount.toLocaleString()} for ${record.purpose
            } by ${record.receiver.label}?`}
          // open={open}
          // onOpenChange={handleOpenChange}
          onConfirm={async () => {
            const updatedRecord = {
              ...record,
              authorisedAt: Date(),
              authoriser: {
                ...currentUser,
                value: currentUser._id,
                label: `${currentUser.first_name} ${currentUser.last_name}`,
              },
              authorised: true,
            };

            // Apply optimistic update for immediate UI feedback
            if (props.updateOptimisticRecord) {
              props.updateOptimisticRecord(updatedRecord);
            }

            try {
              await pouchDatabase(
                modules.requisitions.collection,
                databasePrefix
              ).saveDocument(updatedRecord, CRUD_USER);
              message.success(`Authorization Successful.`);
              return true;
            } catch (error) {
              // If save fails, revert the optimistic update
              if (props.updateOptimisticRecord) {
                props.updateOptimisticRecord(record);
              }
              message.error(`Authorization failed: ${error.message}`);
              throw error;
            }
          }}
          // onCancel={cancel}
          okText="Yes"
          cancelText="No"
        >
          <a key="button" type="primary">
            <ExceptionOutlined /> Authorize Requisition
          </a>
        </Popconfirm>
      ),
    },
    )


    !record.approved && record.authorised && actionMenu.push({

      key: "Approve Requisition",
      name: (
        <Popconfirm
          title="Approve Requisition"
          description={`Authorize ${record.amount.toLocaleString()} for ${record.purpose
            } by ${record.receiver.label}?`}
          // open={open}
          // onOpenChange={handleOpenChange}
          onConfirm={async () => {
            const updatedRecord = {
              ...record,
              approvedAt: Date(),
              approvedBy: {
                ...currentUser,
                value: currentUser._id,
                label: `${currentUser.first_name} ${currentUser.last_name}`,
              },
              approved: true,
            };

            // Apply optimistic update for immediate UI feedback
            if (props.updateOptimisticRecord) {
              props.updateOptimisticRecord(updatedRecord);
            }

            try {
              await pouchDatabase(
                modules.requisitions.collection,
                databasePrefix
              ).saveDocument(updatedRecord, CRUD_USER);
              message.success(`Approval Successful.`);
              return true;
            } catch (error) {
              // If save fails, revert the optimistic update
              if (props.updateOptimisticRecord) {
                props.updateOptimisticRecord(record);
              }
              message.error(`Approval failed: ${error.message}`);
              throw error;
            }
          }}
          // onCancel={cancel}
          okText="Yes"
          cancelText="No"
        >
          <a key="button" type="primary">
            <CheckCircleOutlined /> Approve Requisition
          </a>
        </Popconfirm>
      ),
    },
    )

    record.authorised && record.approved && !record.released &&
      actionMenu.push({
        key: "Release Funds",
        name: (
          <BetaSchemaForm
            formRef={action}
            submitter={{
              searchConfig: {
                resetText: "Close",
                submitText: "Save",
              },
            }}
            modalProps={{ centered: true, destroyOnHidden: true }}
            grid={true}
            trigger={
              <a key="button" type="primary">
                <CheckCircleOutlined /> Release Funds
              </a>
            }
            title={"Release Funds"}
            layoutType="ModalForm"
            onFinish={async (values) => {
              // ,
              //   amount: record.amount,
              //   description: record.purpose,
              //   "expense-category": record["expense-category"],
              //   expense_by: record.receiver.value,
              //   ...values,
              // });

              if (record) {
                // If account is not provided, try to use default expense account from settings
                if (!values.account || !values.account.value) {
                  values.account = localStorage.getItem("APP_SETTINGS") ? JSON.parse(localStorage.getItem("APP_SETTINGS")).default_expense_account : null;
                }

                const updatedRecord = {
                  ...record,
                  releasedAt: Date(),
                  releasedBy: {
                    ...currentUser,
                    value: currentUser._id,
                    label: `${currentUser.first_name} ${currentUser.last_name}`,
                  },
                  released: true,
                  ...values,
                };

                // Apply optimistic update for immediate UI feedback
                if (props.updateOptimisticRecord) {
                  props.updateOptimisticRecord(updatedRecord);
                }

                try {
                  await pouchDatabase(
                    modules.expenses.collection,
                    databasePrefix
                  ).saveDocument(
                    {
                      supplier: record.supplier,
                      date: Date(),
                      amount: record.amount,
                      description: `${record.purpose} - ${record.receiver.label}`,
                      "expense-category": record["expense-category"],
                      expense_by: record.receiver.value,
                      ...values,
                    },
                    CRUD_USER
                  );
                  await pouchDatabase(
                    modules.requisitions.collection,
                    databasePrefix
                  ).saveDocument(updatedRecord, CRUD_USER);
                  message.success(`Funds Released, Expense Added.`);
                  return true;
                } catch (error) {
                  // If save fails, revert the optimistic update
                  if (props.updateOptimisticRecord) {
                    props.updateOptimisticRecord(record);
                  }
                  message.error(`Release failed: ${error.message}`);
                  throw error;
                }
              }
            }}
            columns={[
              {
                title: "Method Of Payment",
                dataIndex: "method_of_payment",
                sorter: true,
                hideInTable: true,
                valueType: "select",
                isPrintable: true,
                formItemProps: {
                  rules: [{ required: true, message: "Required" }],
                },
                width: "lg",
                colProps: {
                  md: 12,
                },
                valueEnum: {
                  Cash: {
                    text: "Cash",
                  },
                  "Credit Card": {
                    text: "Credit Card",
                  },
                  Cheque: {
                    text: "Cheque",
                  },
                  "MTN Mobile Money": {
                    text: "MTN Mobile Money",
                  },
                  "Airtel Mobile Money": {
                    text: "Airtel Mobile Money",
                  },
                  "Bank Deposit": {
                    text: "Bank Deposit",
                  },
                  "Flexipay": {
                    text: "Flexipay",
                  },
                },
              },
              {
                title: "Account",
                dataIndex: "account",
                valueType: "select",
                isPrintable: true,
                initialValue: localStorage.getItem("APP_SETTINGS") ? JSON.parse(localStorage.getItem("APP_SETTINGS")).default_expense_account : null,
                formItemProps: {
                  disabled: true,
                },
                width: "lg",
                colProps: {
                  md: 12,
                },
              },
            ]}
            initialValues={{
              account: localStorage.getItem("APP_SETTINGS") ? JSON.parse(localStorage.getItem("APP_SETTINGS")).default_expense_account : null,
            }}
          />
        ),
      });



    return <TableDropdown
      key="actionGroup"
      menus={actionMenu}
    />;
  },
};

export default requisitions;
