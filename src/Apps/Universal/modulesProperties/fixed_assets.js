import React, { useRef } from "react";
import dayjs from "dayjs";
import { BetaSchemaForm, TableDropdown  } from "@ant-design/pro-components";
import { CalculatorOutlined, ToolOutlined  } from "@ant-design/icons";
import { formatNumber  } from "../../../Utils/functions";
import { message, Progress, Tooltip  } from "antd";


const fixed_assets = {
    CustomView: (data) => {
        const asset = data.data;
        const purchaseCost = asset.purchase_cost || 0;
        const salvageValue = asset.salvage_value || 0;
        const accumulatedDepreciation = asset.accumulated_depreciation || 0;
        const currentBookValue = asset.current_book_value || purchaseCost;
        
        // Calculate depreciation percentage
        const totalDepreciable = purchaseCost - salvageValue;
        const depreciationPercentage = totalDepreciable > 0 
            ? ((purchaseCost - currentBookValue) / totalDepreciable) * 100 
            : 0;
        
        // Calculate remaining useful life
        const purchaseDate = dayjs(asset.purchase_date);
        const usefulLifeYears = asset.useful_life_years || 5;
        const endDate = purchaseDate.add(usefulLifeYears, 'year');
        const today = dayjs();
        const remainingMonths = endDate.diff(today, 'month');
        const remainingYears = (remainingMonths / 12).toFixed(1);
        
        return (
            <div>
                <h2>{asset.name} ({asset.code})</h2>
                <p><strong>Type:</strong> {asset.asset_type}</p>
                <p><strong>Purchase Date:</strong> {dayjs(asset.purchase_date).format('DD/MM/YYYY')}</p>
                <p><strong>Purchase Cost:</strong> {formatNumber(purchaseCost)}</p>
                <p><strong>Current Book Value:</strong> {formatNumber(currentBookValue)}</p>
                <p><strong>Accumulated Depreciation:</strong> {formatNumber(accumulatedDepreciation)}</p>
                
                <div style={{ marginTop: 20, marginBottom: 20 }}>
                    <Tooltip title={`${depreciationPercentage.toFixed(1)}% depreciated`}>
                        <Progress 
                            percent={depreciationPercentage} 
                            status={depreciationPercentage >= 100 ? "success" : "active"}
                            format={percent => `${percent.toFixed(1)}%`}
                        />
                    </Tooltip>
                </div>
                
                <p><strong>Depreciation Method:</strong> {asset.depreciation_method}</p>
                <p><strong>Useful Life:</strong> {asset.useful_life_years} years</p>
                {remainingMonths > 0 && (
                    <p><strong>Remaining Life:</strong> {remainingYears} years ({remainingMonths} months)</p>
                )}
                <p><strong>Salvage Value:</strong> {formatNumber(salvageValue)}</p>
                
                {asset.location && <p><strong>Location:</strong> {asset.location}</p>}
                {asset.serial_number && <p><strong>Serial Number:</strong> {asset.serial_number}</p>}
                {asset.warranty_expiry && (
                    <p>
                        <strong>Warranty Expiry:</strong> {dayjs(asset.warranty_expiry).format('DD/MM/YYYY')}
                        {dayjs(asset.warranty_expiry).isAfter(today) 
                            ? ` (${dayjs(asset.warranty_expiry).diff(today, 'day')} days remaining)` 
                            : ' (Expired)'}
                    </p>
                )}
                
                {asset.notes && (
                    <div style={{ marginTop: 20 }}>
                        <h3>Notes</h3>
                        <p>{asset.notes}</p>
                    </div>
                )}
            </div>
        );
    },
    MoreActions: (props) => {
        const action = useRef();
        const {
            pouchDatabase,
            databasePrefix,
            record,
            CRUD_USER
        } = props;

        // Calculate monthly depreciation amount based on straight-line method
        const calculateMonthlyDepreciation = () => {
            const purchaseCost = record.purchase_cost || 0;
            const salvageValue = record.salvage_value || 0;
            const usefulLifeYears = record.useful_life_years || 5;
            
            const depreciableAmount = purchaseCost - salvageValue;
            const monthlyDepreciation = depreciableAmount / (usefulLifeYears * 12);
            
            return monthlyDepreciation;
        };

        return (
            <TableDropdown
                key="actionGroup"
                menus={[
                    {
                        key: "record_depreciation",
                        name: (
                            <BetaSchemaForm
                                formRef={action}
                                submitter={{
                                    searchConfig: {
                                        resetText: "Cancel",
                                        submitText: "Record Depreciation",
                                    },
                                }}
                                modalProps={{ centered: true }}
                                grid={true}
                                trigger={
                                    <a key="button" type="primary">
                                        <CalculatorOutlined /> Record Depreciation
                                    </a>
                                }
                                title={"Record Depreciation"}
                                onFinish={async (values) => {
                                    // Create a depreciation entry
                                    await pouchDatabase("depreciation_entries", databasePrefix)
                                        .saveDocument({
                                            ...values,
                                            asset: {
                                                label: record.name,
                                                value: record._id
                                            },
                                            status: "posted",
                                            createdAt: new Date().toISOString(),
                                            createdBy: CRUD_USER
                                        }, CRUD_USER);

                                    message.success("Depreciation recorded successfully");
                                    return true;
                                }}
                                columns={[
                                    {
                                        title: "Date",
                                        dataIndex: "date",
                                        valueType: "date",
                                        isRequired: true,
                                        initialValue: dayjs().format('YYYY-MM-DD'),
                                    },
                                    {
                                        title: "Period Start",
                                        dataIndex: "period_start",
                                        valueType: "date",
                                        isRequired: true,
                                        initialValue: dayjs().subtract(1, 'month').startOf('month').format('YYYY-MM-DD'),
                                    },
                                    {
                                        title: "Period End",
                                        dataIndex: "period_end",
                                        valueType: "date",
                                        isRequired: true,
                                        initialValue: dayjs().subtract(1, 'month').endOf('month').format('YYYY-MM-DD'),
                                    },
                                    {
                                        title: "Amount",
                                        dataIndex: "amount",
                                        valueType: "money",
                                        isRequired: true,
                                        initialValue: calculateMonthlyDepreciation(),
                                    },
                                    {
                                        title: "Notes",
                                        dataIndex: "notes",
                                        valueType: "textarea",
                                        colProps: {
                                            span: 24,
                                        },
                                    }
                                ]}
                            />
                        ),
                    },
                    {
                        key: "record_maintenance",
                        name: (
                            <BetaSchemaForm
                                formRef={action}
                                submitter={{
                                    searchConfig: {
                                        resetText: "Cancel",
                                        submitText: "Record Maintenance",
                                    },
                                }}
                                modalProps={{ centered: true }}
                                grid={true}
                                trigger={
                                    <a key="button" type="primary">
                                        <ToolOutlined /> Record Maintenance
                                    </a>
                                }
                                title={"Record Asset Maintenance"}
                                onFinish={async (values) => {
                                    // Create a maintenance record
                                    await pouchDatabase("asset_maintenance", databasePrefix)
                                        .saveDocument({
                                            ...values,
                                            asset: {
                                                label: record.name,
                                                value: record._id
                                            },
                                            createdAt: new Date().toISOString(),
                                            createdBy: CRUD_USER
                                        }, CRUD_USER);

                                    message.success("Maintenance recorded successfully");
                                    return true;
                                }}
                                columns={[
                                    {
                                        title: "Date",
                                        dataIndex: "date",
                                        valueType: "date",
                                        isRequired: true,
                                        initialValue: dayjs().format('YYYY-MM-DD'),
                                    },
                                    {
                                        title: "Maintenance Type",
                                        dataIndex: "maintenance_type",
                                        valueType: "select",
                                        valueEnum: {
                                            routine: "Routine Maintenance",
                                            repair: "Repair",
                                            upgrade: "Upgrade",
                                            inspection: "Inspection"
                                        },
                                        isRequired: true,
                                    },
                                    {
                                        title: "Cost",
                                        dataIndex: "cost",
                                        valueType: "money",
                                        isRequired: true,
                                    },
                                    {
                                        title: "Performed By",
                                        dataIndex: "performed_by",
                                        valueType: "text",
                                    },
                                    {
                                        title: "Description",
                                        dataIndex: "description",
                                        valueType: "textarea",
                                        isRequired: true,
                                        colProps: {
                                            span: 24,
                                        },
                                    },
                                    {
                                        title: "Next Maintenance Date",
                                        dataIndex: "next_maintenance_date",
                                        valueType: "date",
                                    }
                                ]}
                            />
                        ),
                    },
                ]}
            />
        );
    },
    buffData: async (results, { pouchDatabase, databasePrefix }) => {
        // Get all depreciation entries
        const depreciationEntries = await pouchDatabase("depreciation_entries", databasePrefix).getAllData();
        
        return results.map(asset => {
            // Filter depreciation entries for this asset
            const assetDepreciationEntries = depreciationEntries.filter(entry => 
                entry.status === 'posted' && 
                ((entry.asset && entry.asset.value === asset._id) || 
                 (entry.asset && entry.asset._id === asset._id))
            );
            
            // Calculate accumulated depreciation and current book value if not already set
            let accumulatedDepreciation = asset.accumulated_depreciation || 0;
            let currentBookValue = asset.current_book_value || asset.purchase_cost || 0;
            
            // If these values aren't set, calculate them from the entries
            if (!asset.accumulated_depreciation || !asset.current_book_value) {
                accumulatedDepreciation = assetDepreciationEntries.reduce((sum, entry) => sum + (entry.amount || 0), 0);
                currentBookValue = (asset.purchase_cost || 0) - accumulatedDepreciation;
            }
            
            // Find the latest depreciation date
            let lastDepreciationDate = asset.last_depreciation_date;
            if (assetDepreciationEntries.length > 0) {
                const latestEntry = assetDepreciationEntries.sort((a, b) => 
                    new Date(b.period_end) - new Date(a.period_end)
                )[0];
                lastDepreciationDate = latestEntry.period_end;
            }
            
            return {
                ...asset,
                accumulated_depreciation: accumulatedDepreciation,
                current_book_value: currentBookValue,
                last_depreciation_date: lastDepreciationDate,
                depreciation_entries_count: assetDepreciationEntries.length
            };
        });
    }
};

export default fixed_assets;
