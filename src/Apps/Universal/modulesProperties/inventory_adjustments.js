import React, { useRef } from "react";
import dayjs from "dayjs";
import { Table, Typography  } from "antd";
import { formatNumber  } from "../../../Utils/functions";


const { Text } = Typography;

const inventory_adjustments = {
    CustomView: (data) => {
        const adjustment = data.data;
        
        // Calculate totals
        let totalIncrease = 0;
        let totalDecrease = 0;
        let totalValue = 0;
        
        if (adjustment.items && Array.isArray(adjustment.items)) {
            adjustment.items.forEach(item => {
                if (item.adjustment > 0) {
                    totalIncrease += item.adjustment;
                } else if (item.adjustment < 0) {
                    totalDecrease += Math.abs(item.adjustment);
                }
                totalValue += item.total_value || 0;
            });
        }
        
        // Define columns for the items table
        const columns = [
            {
                title: 'Product',
                dataIndex: 'product',
                key: 'product',
                render: (product) => product?.label || 'Unknown Product'
            },
            {
                title: 'Current Qty',
                dataIndex: 'current_quantity',
                key: 'current_quantity',
                align: 'right',
            },
            {
                title: 'New Qty',
                dataIndex: 'new_quantity',
                key: 'new_quantity',
                align: 'right',
            },
            {
                title: 'Adjustment',
                dataIndex: 'adjustment',
                key: 'adjustment',
                align: 'right',
                render: (value) => {
                    const color = value > 0 ? '#52c41a' : value < 0 ? '#f5222d' : 'inherit';
                    return <Text style={{ color }}>{value > 0 ? `+${value}` : value}</Text>;
                }
            },
            {
                title: 'Unit Cost',
                dataIndex: 'unit_cost',
                key: 'unit_cost',
                align: 'right',
                render: (value) => formatNumber(value || 0)
            },
            {
                title: 'Total Value',
                dataIndex: 'total_value',
                key: 'total_value',
                align: 'right',
                render: (value) => formatNumber(value || 0)
            },
            {
                title: 'Reason',
                dataIndex: 'reason',
                key: 'reason',
            }
        ];
        
        return (
            <div>
                <h2>Inventory Adjustment</h2>
                <p><strong>Reference:</strong> {adjustment.reference}</p>
                <p><strong>Date:</strong> {dayjs(adjustment.date).format('DD/MM/YYYY')}</p>
                <p><strong>Adjustment Type:</strong> {adjustment.adjustment_type}</p>
                <p><strong>Status:</strong> {adjustment.status}</p>
                
                <div style={{ marginTop: 20, marginBottom: 20 }}>
                    <Table 
                        dataSource={adjustment.items || []} 
                        columns={columns} 
                        pagination={false}
                        rowKey={(record, index) => index}
                        summary={() => (
                            <Table.Summary fixed>
                                <Table.Summary.Row>
                                    <Table.Summary.Cell index={0} colSpan={3}>
                                        <Text strong>Total</Text>
                                    </Table.Summary.Cell>
                                    <Table.Summary.Cell index={3} align="right">
                                        <Text strong>
                                            +{totalIncrease} / -{totalDecrease}
                                        </Text>
                                    </Table.Summary.Cell>
                                    <Table.Summary.Cell index={4}></Table.Summary.Cell>
                                    <Table.Summary.Cell index={5} align="right">
                                        <Text strong>{formatNumber(totalValue)}</Text>
                                    </Table.Summary.Cell>
                                    <Table.Summary.Cell index={6}></Table.Summary.Cell>
                                </Table.Summary.Row>
                            </Table.Summary>
                        )}
                    />
                </div>
                
                {adjustment.notes && (
                    <div style={{ marginTop: 20 }}>
                        <h3>Notes</h3>
                        <p>{adjustment.notes}</p>
                    </div>
                )}
            </div>
        );
    },
    buffData: async (results, { pouchDatabase, databasePrefix }) => {
        // Get all products
        const products = await pouchDatabase("products", databasePrefix).getAllData();
        
        return results.map(adjustment => {
            // Process items if they exist
            if (adjustment.items && Array.isArray(adjustment.items)) {
                let totalItems = 0;
                let totalValue = 0;
                
                adjustment.items = adjustment.items.map(item => {
                    // Find the associated product
                    const productId = item.product?.value || item.product?._id;
                    const product = products.find(p => p._id === productId);
                    
                    totalItems++;
                    totalValue += item.total_value || 0;
                    
                    return {
                        ...item,
                        product_name: product?.name || 'Unknown Product',
                    };
                });
                
                return {
                    ...adjustment,
                    item_count: totalItems,
                    total_value: totalValue
                };
            }
            
            return adjustment;
        });
    }
};

export default inventory_adjustments;
