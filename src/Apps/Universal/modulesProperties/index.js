import account_reconciliations from "./account_reconciliations";
import account_transfers from "./account_transfers";
import accounts from "./accounts";
import attendance from "./hr/attendance";
import departments from "./hr/departments";
import depreciation_entries from "./depreciation_entries";
import employees from "./hr/employees";
import enhanced_accounts from "./enhanced_accounts";
import expense_categories from "./expense_categories";
import expenses from "./expenses";
import fixed_assets from "./fixed_assets";
import hrModule from "./hr/index";
import inventory_adjustments from "./inventory_adjustments";
import inventory_valuations from "./inventory_valuations";
import journal_entries from "./journal_entries";
import leaves from "./hr/leaves";
import logs from "./logs";
import payroll from "./hr/payroll";
import performance_reviews from "./hr/performance_reviews";
import requisitions from "./requisitions";
import roles from "./roles";
import sms_messages from "./sms_messages";
import users from "./users";


export default {
  logs: logs,
  roles: roles,
  users: users,
  accounts: accounts,
  enhanced_accounts: enhanced_accounts,
  account_transfers: account_transfers,
  account_reconciliations: account_reconciliations,
  requisitions: requisitions,
  expenses: expenses,
  expense_categories: expense_categories,
  fixed_assets: fixed_assets,
  depreciation_entries: depreciation_entries,
  journal_entries: journal_entries,
  inventory_valuations: inventory_valuations,
  inventory_adjustments: inventory_adjustments,
  sms_messages: sms_messages,
  // HR Module Properties
  hr: hrModule,
  employees: employees,
  departments: departments,
  attendance: attendance,
  leaves: leaves,
  payroll: payroll,
  performance_reviews: performance_reviews,
};
