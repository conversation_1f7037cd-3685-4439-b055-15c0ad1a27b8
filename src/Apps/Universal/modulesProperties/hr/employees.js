import React, { useState, useEffect } from "react";
import moment from "moment";
import syncUtils from "../../modules/hr/utils/userEmployeeSync";
import { Card, Avatar, Typography, Divider, Tag, Space, Row, Col, Button, Modal, Form, Select, message, Alert  } from "antd";
import { TableDropdown  } from "@ant-design/pro-components";
import { UserOutlined, MailOutlined, PhoneOutlined, MobileOutlined, CalendarOutlined, BankOutlined, TeamOutlined, EnvironmentOutlined, IdcardOutlined, ContactsOutlined, LinkOutlined, SyncOutlined, DollarOutlined, FileTextOutlined, PlusOutlined, DisconnectOutlined,   } from "@ant-design/icons";


let syncEmployeeWithUser, createEmployeeFromUser;

try {
  syncEmployeeWithUser = syncUtils.syncEmployeeWithUser;
  createEmployeeFromUser = syncUtils.createEmployeeFromUser;
} catch (error) {
  console.warn('Sync utilities not available:', error.message);
  // Provide fallback functions
  syncEmployeeWithUser = async () => { throw new Error('Sync utilities not available'); };
  createEmployeeFromUser = async () => { throw new Error('Sync utilities not available'); };
}

const { Text, Title } = Typography;



// Component for creating employee from existing user
const CreateEmployeeFromUser = ({ pouchDatabase, databasePrefix, currentUser, onSuccess }) => {
  const [modalVisible, setModalVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const [users, setUsers] = useState([]);
  const [form] = Form.useForm();

  const loadAvailableUsers = async () => {
    try {
      const usersDb = pouchDatabase('users', databasePrefix);
      const employeesDb = pouchDatabase('employees', databasePrefix);

      // Get all users
      const allUsers = await usersDb.getDocuments({
        limit: 1000,
        include_docs: true,
      });

      // Get all employees to filter out users who already have employee records
      const allEmployees = await employeesDb.getDocuments({
        limit: 1000,
        include_docs: true,
      });

      const linkedUserIds = allEmployees.docs?.map(emp => emp.user_id).filter(Boolean) || [];

      // Filter users who don't have employee records
      const availableUsers = allUsers.docs?.filter(user => !linkedUserIds.includes(user._id)) || [];

      setUsers(availableUsers);
    } catch (error) {
      console.error('Error loading users:', error);
      message.error('Failed to load users');
    }
  };

  useEffect(() => {
    if (modalVisible && pouchDatabase && databasePrefix) {
      loadAvailableUsers();
    }
  }, [modalVisible, pouchDatabase, databasePrefix]);

  // Early return if required props are missing (after hooks)
  if (!pouchDatabase || !databasePrefix || !currentUser) {
    return (
      <Button disabled title="Required props missing">
        Create Employee from User
      </Button>
    );
  }

  const handleCreateEmployee = async (values) => {
    try {
      setLoading(true);

      // Check if sync utilities are available
      if (!createEmployeeFromUser || typeof createEmployeeFromUser !== 'function') {
        message.error('Employee sync functionality is not available');
        return;
      }

      const hrData = {
        department: values.department,
        position: values.position,
        employment_date: values.employment_date,
        employment_type: values.employment_type,
        basic_salary: values.basic_salary,
        housing_allowance: values.housing_allowance,
        transport_allowance: values.transport_allowance,
        medical_allowance: values.medical_allowance,
      };

      await createEmployeeFromUser(
        values.user_id,
        hrData,
        pouchDatabase,
        databasePrefix,
        currentUser
      );

      message.success('Employee created successfully from user account!');
      setModalVisible(false);
      form.resetFields();
      if (onSuccess) onSuccess();
    } catch (error) {
      console.error('Error creating employee:', error);
      message.error(error.message || 'Failed to create employee');
    } finally {
      setLoading(false);
    }
  };

  return (
    <>
      <Button
        type="primary"
        icon={<LinkOutlined />}
        onClick={() => setModalVisible(true)}
      >
        Create Employee from User
      </Button>

      <Modal
        title="Create Employee from Existing User"
        visible={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={null}
        width={600}
      >
        <Alert
          message="Link to Existing User"
          description="This will create an employee record linked to an existing user account, avoiding data duplication."
          type="info"
          showIcon
          style={{ marginBottom: 16 }}
        />

        <Form form={form} onFinish={handleCreateEmployee} layout="vertical">
          <Form.Item
            name="user_id"
            label="Select User Account"
            rules={[{ required: true, message: 'Please select a user account' }]}
          >
            <Select
              placeholder="Search and select user"
              showSearch
              filterOption={(input, option) =>
                option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
              }
            >
              {users.map(user => (
                <Select.Option key={user._id} value={user._id}>
                  {user.first_name} {user.last_name} - {user.email}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="department"
                label="Department"
                rules={[{ required: true, message: 'Please select department' }]}
              >
                <Select placeholder="Select department">
                  {/* This would be populated from departments collection */}
                  <Select.Option value="it">IT Department</Select.Option>
                  <Select.Option value="hr">HR Department</Select.Option>
                  <Select.Option value="finance">Finance Department</Select.Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="position"
                label="Position"
                rules={[{ required: true, message: 'Please enter position' }]}
              >
                <Select placeholder="Enter position">
                  <Select.Option value="Software Developer">Software Developer</Select.Option>
                  <Select.Option value="HR Manager">HR Manager</Select.Option>
                  <Select.Option value="Accountant">Accountant</Select.Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="employment_date"
                label="Employment Date"
                rules={[{ required: true, message: 'Please select employment date' }]}
              >
                <input type="date" style={{ width: '100%', padding: '8px' }} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="employment_type"
                label="Employment Type"
                rules={[{ required: true, message: 'Please select employment type' }]}
              >
                <Select placeholder="Select employment type">
                  <Select.Option value="full_time">Full Time</Select.Option>
                  <Select.Option value="part_time">Part Time</Select.Option>
                  <Select.Option value="contract">Contract</Select.Option>
                  <Select.Option value="intern">Intern</Select.Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item name="basic_salary" label="Basic Salary">
                <input type="number" placeholder="Enter basic salary" style={{ width: '100%', padding: '8px' }} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="housing_allowance" label="Housing Allowance">
                <input type="number" placeholder="Enter housing allowance" style={{ width: '100%', padding: '8px' }} />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit" loading={loading}>
                Create Employee
              </Button>
              <Button onClick={() => setModalVisible(false)}>
                Cancel
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
};

// Component for syncing employee data with user
const SyncEmployeeWithUser = ({ data = {}, pouchDatabase, databasePrefix, currentUser, onSuccess }) => {
  const [loading, setLoading] = useState(false);

  // Early return if required props are missing
  if (!data || !pouchDatabase || !databasePrefix || !currentUser) {
    return null;
  }

  const handleSync = async () => {
    try {
      setLoading(true);

      if (!data.user_id) {
        message.warning('This employee is not linked to a user account');
        return;
      }

      // Check if sync utilities are available
      if (!syncEmployeeWithUser || typeof syncEmployeeWithUser !== 'function') {
        message.error('Employee sync functionality is not available');
        return;
      }

      const syncedData = await syncEmployeeWithUser(data, pouchDatabase, databasePrefix);

      // Save the synced data
      const employeesDb = pouchDatabase('employees', databasePrefix);
      await employeesDb.saveDocument(syncedData, currentUser);

      message.success('Employee data synchronized with user account!');
      if (onSuccess) onSuccess();
    } catch (error) {
      console.error('Error syncing employee:', error);
      message.error(error.message || 'Failed to sync employee data');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Button
      type="default"
      icon={<SyncOutlined />}
      onClick={handleSync}
      loading={loading}
      disabled={!data.user_id}
      title={data.user_id ? 'Sync with linked user account' : 'No user account linked'}
    >
      Sync with User Account
    </Button>
  );
};

const InfoItem = ({ icon, label, value, span = 8 }) => {
  if (!value) return null;

  return (
    <Col span={span}>
      <div style={{ marginBottom: '16px' }}>
        <Text type="secondary" style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          {icon} {label}
        </Text>
        <Text strong style={{ display: 'block', marginTop: '4px' }}>
          {value}
        </Text>
      </div>
    </Col>
  );
};

const getStatusColor = (status) => {
  const statusColors = {
    active: '#52c41a',
    inactive: '#faad14',
    terminated: '#f5222d',
    on_leave: '#1890ff',
  };
  return statusColors[status] || '#d9d9d9';
};

const getEmploymentTypeColor = (type) => {
  const typeColors = {
    full_time: '#52c41a',
    part_time: '#faad14',
    contract: '#1890ff',
    intern: '#722ed1',
    consultant: '#eb2f96',
  };
  return typeColors[type] || '#d9d9d9';
};

const EmployeeModuleProperties = {
  CustomView: ({ data = {} }) => {
    // Handle missing data gracefully
    if (!data || Object.keys(data).length === 0) {
      return (
        <div style={{ padding: '24px', textAlign: 'center' }}>
          <Card>
            <div style={{ padding: '40px' }}>
              <UserOutlined style={{ fontSize: '48px', color: '#d9d9d9', marginBottom: '16px' }} />
              <Title level={3} type="secondary">No Employee Data</Title>
              <Text type="secondary">Employee information is not available.</Text>
            </div>
          </Card>
        </div>
      );
    }
    const formatCurrency = (amount) => {
      if (!amount) return 'Not set';
      return new Intl.NumberFormat('en-UG', {
        style: 'currency',
        currency: 'UGX',
        minimumFractionDigits: 0,
      }).format(amount);
    };

    const calculateTenure = (employmentDate) => {
      if (!employmentDate) return 'N/A';
      const start = moment(employmentDate);
      const now = moment();
      const years = now.diff(start, 'years');
      const months = now.diff(start, 'months') % 12;
      
      if (years > 0) {
        return `${years} year${years > 1 ? 's' : ''} ${months > 0 ? `${months} month${months > 1 ? 's' : ''}` : ''}`;
      }
      return `${months} month${months > 1 ? 's' : ''}`;
    };

    return (
      <div style={{ padding: '24px' }}>
        <Card>
          {/* Header Section */}
          <div style={{ textAlign: 'center', marginBottom: '32px' }}>
            <Avatar
              size={120}
              src={data.avatar}
              icon={<UserOutlined />}
              style={{ marginBottom: '16px' }}
            />
            <Title level={2} style={{ margin: '8px 0' }}>
              {data.first_name} {data.last_name}
            </Title>
            <Space size="middle">
              <Tag color={getStatusColor(data.status)} style={{ fontSize: '14px', padding: '4px 12px' }}>
                {data.status?.replace('_', ' ').toUpperCase()}
              </Tag>
              <Tag color={getEmploymentTypeColor(data.employment_type)} style={{ fontSize: '14px', padding: '4px 12px' }}>
                {data.employment_type?.replace('_', ' ').toUpperCase()}
              </Tag>
            </Space>
            <div style={{ marginTop: '8px' }}>
              <Text type="secondary" style={{ fontSize: '16px' }}>
                {data.position} • {data.department?.label || 'No Department'}
              </Text>
            </div>
            {data.employee_id && (
              <div style={{ marginTop: '4px' }}>
                <Text type="secondary">Employee ID: {data.employee_id}</Text>
              </div>
            )}
          </div>

          <Divider />

          {/* Contact Information */}
          <Title level={4} style={{ marginBottom: '16px' }}>Contact Information</Title>
          <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
            <InfoItem
              icon={<MailOutlined />}
              label="Email"
              value={data.email}
              span={12}
            />
            <InfoItem
              icon={<PhoneOutlined />}
              label="Phone"
              value={data.phone}
              span={12}
            />
            <InfoItem
              icon={<MobileOutlined />}
              label="Mobile"
              value={data.mobile}
              span={12}
            />
            <InfoItem
              icon={<EnvironmentOutlined />}
              label="Address"
              value={data.address}
              span={12}
            />
          </Row>

          <Divider />

          {/* Employment Information */}
          <Title level={4} style={{ marginBottom: '16px' }}>Employment Information</Title>
          <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
            <InfoItem
              icon={<CalendarOutlined />}
              label="Employment Date"
              value={data.employment_date ? moment(data.employment_date).format('MMMM DD, YYYY') : 'Not set'}
              span={12}
            />
            <InfoItem
              icon={<CalendarOutlined />}
              label="Tenure"
              value={calculateTenure(data.employment_date)}
              span={12}
            />
            <InfoItem
              icon={<TeamOutlined />}
              label="Manager"
              value={data.manager?.label || 'Not assigned'}
              span={12}
            />
            <InfoItem
              icon={<EnvironmentOutlined />}
              label="Branch"
              value={data.branch?.label || 'Not assigned'}
              span={12}
            />
          </Row>

          <Divider />

          {/* Salary Information */}
          <Title level={4} style={{ marginBottom: '16px' }}>Compensation</Title>
          <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
            <InfoItem
              icon={<BankOutlined />}
              label="Basic Salary"
              value={formatCurrency(data.basic_salary)}
              span={12}
            />
            <InfoItem
              icon={<BankOutlined />}
              label="Housing Allowance"
              value={formatCurrency(data.housing_allowance)}
              span={12}
            />
            <InfoItem
              icon={<BankOutlined />}
              label="Transport Allowance"
              value={formatCurrency(data.transport_allowance)}
              span={12}
            />
            <InfoItem
              icon={<BankOutlined />}
              label="Medical Allowance"
              value={formatCurrency(data.medical_allowance)}
              span={12}
            />
          </Row>

          <Divider />

          {/* Personal Information */}
          <Title level={4} style={{ marginBottom: '16px' }}>Personal Information</Title>
          <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
            <InfoItem
              icon={<CalendarOutlined />}
              label="Date of Birth"
              value={data.date_of_birth ? moment(data.date_of_birth).format('MMMM DD, YYYY') : 'Not provided'}
              span={12}
            />
            <InfoItem
              icon={<UserOutlined />}
              label="Gender"
              value={data.gender ? data.gender.charAt(0).toUpperCase() + data.gender.slice(1) : 'Not specified'}
              span={12}
            />
            <InfoItem
              icon={<IdcardOutlined />}
              label={data.id_type || 'ID Type'}
              value={data.id_number || 'Not provided'}
              span={12}
            />
          </Row>

          {/* Emergency Contact */}
          {(data.emergency_contact_name || data.emergency_contact_phone) && (
            <>
              <Divider />
              <Title level={4} style={{ marginBottom: '16px' }}>Emergency Contact</Title>
              <Row gutter={[16, 16]}>
                <InfoItem
                  icon={<ContactsOutlined />}
                  label="Contact Name"
                  value={data.emergency_contact_name}
                  span={12}
                />
                <InfoItem
                  icon={<PhoneOutlined />}
                  label="Contact Phone"
                  value={data.emergency_contact_phone}
                  span={12}
                />
              </Row>
            </>
          )}
        </Card>
      </div>
    );
  },

  // Custom actions for employee management
  MoreActions: (props = {}) => {
    const {
      record,
      pouchDatabase,
      databasePrefix,
      currentUser,
      actionRef,
    } = props;

    // Only show if we have the required props and data
    if (!record || !pouchDatabase || !databasePrefix || !currentUser) {
      return null;
    }

    const generatePayroll = async () => {
      try {
        const currentMonth = new Date().toISOString().slice(0, 7);
        const payrollDb = pouchDatabase('payroll', databasePrefix);

        // Check if payroll already exists for this period
        const allPayrollDocs = await payrollDb.getAll();
        const existingPayroll = allPayrollDocs.find(doc =>
          doc.employee === record._id &&
          doc.pay_period === 'monthly' &&
          doc.month === currentMonth
        );

        if (existingPayroll) {
          message.warning('Payroll record already exists for this employee and current month');
          return;
        }

        // Calculate payroll values
        const basicSalary = parseFloat(record.basic_salary) || 0;
        const housingAllowance = parseFloat(record.housing_allowance) || 0;
        const transportAllowance = parseFloat(record.transport_allowance) || 0;
        const medicalAllowance = parseFloat(record.medical_allowance) || 0;

        const grossSalary = basicSalary + housingAllowance + transportAllowance + medicalAllowance;

        // Calculate PAYE tax
        let taxDeduction = 0;
        if (grossSalary > 235000) {
          const taxableAmount = grossSalary - 235000;
          if (taxableAmount <= 135000) {
            taxDeduction = taxableAmount * 0.10;
          } else if (taxableAmount <= 545000) {
            taxDeduction = (135000 * 0.10) + ((taxableAmount - 135000) * 0.20);
          } else {
            taxDeduction = (135000 * 0.10) + (410000 * 0.20) + ((taxableAmount - 545000) * 0.30);
          }
        }

        // Calculate NSSF (5% of gross, max 200,000)
        const nssfDeduction = Math.min(grossSalary * 0.05, 200000);
        const totalDeductions = Math.round(taxDeduction) + Math.round(nssfDeduction);
        const netSalary = Math.round(grossSalary) - totalDeductions;

        // Create payroll record
        const payrollData = {
          employee: record._id,
          employee_name: `${record.first_name} ${record.last_name}`,
          employee_id: record.employee_id,
          department: record.department,
          position: record.position,
          pay_period: 'monthly',
          month: currentMonth,
          basic_salary: basicSalary,
          housing_allowance: housingAllowance,
          transport_allowance: transportAllowance,
          medical_allowance: medicalAllowance,
          other_allowances: 0,
          overtime_amount: 0,
          gross_salary: Math.round(grossSalary),
          tax_deduction: Math.round(taxDeduction),
          nssf_deduction: Math.round(nssfDeduction),
          insurance_deduction: 0,
          loan_deduction: 0,
          advance_deduction: 0,
          other_deductions: 0,
          total_deductions: totalDeductions,
          net_salary: netSalary,
          payment_status: 'pending',
          status: 'draft',
          branch: record.branch,
          created_at: new Date().toISOString(),
          created_by: currentUser._id,
        };

        // Save payroll record
        await payrollDb.save(payrollData, currentUser);

        message.success(`Payroll record generated for ${record.first_name} ${record.last_name}`);
        if (actionRef?.current) {
          actionRef.current.reload();
        }
      } catch (error) {
        console.error('Error generating payroll:', error);
        message.error('Failed to generate payroll record');
      }
    };

    return (
      <TableDropdown
        key="actionGroup"
        onSelect={async (key) => {
          try {
            if (key === "generate_payroll") {
              await generatePayroll();
            } else if (key === "view_payroll_history") {
              message.info("Payroll history view would open here");
            } else if (key === "sync_user") {
              // Handle user sync
              if (record.user_id) {
                console.log('Attempting to sync employee with user:', record.user_id);

                // First check if the user exists
                const usersDb = pouchDatabase('users', databasePrefix);
                const userData = await usersDb.get(record.user_id);

                if (!userData) {
                  message.error(`User account not found (ID: ${record.user_id}). The linked user may have been deleted. You can unlink this employee from the user account by editing the employee record.`);
                  return;
                }

                console.log('Found user data:', userData);

                // Sync employee data with user data
                const syncedEmployeeData = await syncEmployeeWithUser(record, pouchDatabase, databasePrefix);

                // Save the synced data back to the employee record
                const employeesDb = pouchDatabase('employees', databasePrefix);
                await employeesDb.save(syncedEmployeeData, currentUser);

                message.success('Employee synced with user successfully');
                if (actionRef?.current) {
                  actionRef.current.reload();
                }
              } else {
                message.warning('No linked user found for this employee');
              }
            } else if (key === "unlink_user") {
              // Handle user unlinking
              if (record.user_id) {
                // Remove the user link
                const employeesDb = pouchDatabase('employees', databasePrefix);
                const updatedRecord = {
                  ...record,
                  user_id: null,
                  user_reference: null,
                  last_user_sync: null,
                };

                await employeesDb.save(updatedRecord, currentUser);

                message.success('Employee unlinked from user account successfully');
                if (actionRef?.current) {
                  actionRef.current.reload();
                }
              } else {
                message.warning('Employee is not linked to any user account');
              }
            }
          } catch (error) {
            console.error("Error performing employee action:", error);
            message.error("Failed to perform action");
          }
        }}
        menus={[
          {
            key: "generate_payroll",
            name: (
              <span>
                <DollarOutlined /> Generate Payroll
              </span>
            ),
          },
          {
            key: "view_payroll_history",
            name: (
              <span>
                <FileTextOutlined /> View Payroll History
              </span>
            ),
          },
          {
            key: "sync_user",
            name: (
              <span>
                <SyncOutlined /> Sync with User
              </span>
            ),
          },
          {
            key: "unlink_user",
            name: (
              <span>
                <DisconnectOutlined /> Unlink User
              </span>
            ),
          },
        ]}
      />
    );
  },

  // Extra components for employee management
  ExtraComponents: {
    CreateEmployeeFromUser: CreateEmployeeFromUser,
    SyncEmployeeWithUser: SyncEmployeeWithUser,
  },

  // Custom toolbar buttons
  toolbarButtons: [
    {
      type: "primary",
      icon: "LinkOutlined",
      text: "Create from User",
      component: "CreateEmployeeFromUser"
    },
    {
      type: "default",
      icon: "DollarOutlined",
      text: "Bulk Payroll",
      onClick: async (props) => {
        const { pouchDatabase, databasePrefix, currentUser, selectedRows } = props;

        if (!selectedRows || selectedRows.length === 0) {
          message.warning('Please select employees to generate payroll for');
          return;
        }

        Modal.confirm({
          title: 'Generate Bulk Payroll',
          content: `Generate payroll records for ${selectedRows.length} selected employees?`,
          onOk: async () => {
            try {
              const payrollDb = pouchDatabase('payroll', databasePrefix);
              const currentMonth = new Date().toISOString().slice(0, 7);
              let successCount = 0;
              let errorCount = 0;

              for (const employee of selectedRows) {
                try {
                  // Check if payroll already exists
                  const allPayrollDocs = await payrollDb.getAll();
                  const existingPayroll = allPayrollDocs.find(doc =>
                    doc.employee === employee._id &&
                    doc.pay_period === 'monthly' &&
                    doc.month === currentMonth
                  );

                  if (existingPayroll) {
                    console.log(`Payroll already exists for ${employee.first_name} ${employee.last_name}`);
                    continue;
                  }

                  // Create payroll record
                  const payrollData = {
                    employee: employee._id,
                    employee_name: `${employee.first_name} ${employee.last_name}`,
                    employee_id: employee.employee_id,
                    department: employee.department,
                    position: employee.position,
                    pay_period: 'monthly',
                    month: currentMonth,

                    // Salary components
                    basic_salary: employee.basic_salary || 0,
                    housing_allowance: employee.housing_allowance || 0,
                    transport_allowance: employee.transport_allowance || 0,
                    medical_allowance: employee.medical_allowance || 0,
                    other_allowances: 0,
                    overtime_amount: 0,

                    // Deductions
                    insurance_deduction: 0,
                    loan_deduction: 0,
                    advance_deduction: 0,
                    other_deductions: 0,

                    // Status
                    payment_status: 'pending',
                    status: 'draft',
                    branch: employee.branch,
                    created_at: new Date().toISOString(),
                    created_by: currentUser._id,
                  };

                  // Calculate values
                  const grossSalary = payrollData.basic_salary + payrollData.housing_allowance +
                                     payrollData.transport_allowance + payrollData.medical_allowance;

                  let taxDeduction = 0;
                  if (grossSalary > 235000) {
                    const taxableAmount = grossSalary - 235000;
                    if (taxableAmount <= 135000) {
                      taxDeduction = taxableAmount * 0.10;
                    } else if (taxableAmount <= 545000) {
                      taxDeduction = (135000 * 0.10) + ((taxableAmount - 135000) * 0.20);
                    } else {
                      taxDeduction = (135000 * 0.10) + (410000 * 0.20) + ((taxableAmount - 545000) * 0.30);
                    }
                  }

                  const nssfDeduction = Math.min(grossSalary * 0.05, 200000);

                  payrollData.gross_salary = Math.round(grossSalary);
                  payrollData.tax_deduction = Math.round(taxDeduction);
                  payrollData.nssf_deduction = Math.round(nssfDeduction);
                  payrollData.total_deductions = Math.round(taxDeduction) + Math.round(nssfDeduction);
                  payrollData.net_salary = payrollData.gross_salary - payrollData.total_deductions;

                  await payrollDb.save(payrollData, currentUser);
                  successCount++;
                } catch (error) {
                  console.error(`Error generating payroll for ${employee.first_name} ${employee.last_name}:`, error);
                  errorCount++;
                }
              }

              if (successCount > 0) {
                message.success(`Successfully generated payroll for ${successCount} employees`);
              }
              if (errorCount > 0) {
                message.warning(`Failed to generate payroll for ${errorCount} employees`);
              }
            } catch (error) {
              console.error('Bulk payroll generation error:', error);
              message.error('Failed to generate bulk payroll');
            }
          }
        });
      }
    }
  ],
};

export default EmployeeModuleProperties;
