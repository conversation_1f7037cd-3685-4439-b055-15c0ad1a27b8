import HRDashboard from "./dashboard";
import ModuleTable from "../../../../Components/ModuleTable";
import React, { useState } from 'react';
import moduleProperties from "../index";
import { Tabs, Card  } from "antd";
import { UserOutlined, ApartmentOutlined, ClockCircleOutlined, CalendarOutlined, DollarOutlined, TrophyOutlined, DashboardOutlined,   } from "@ant-design/icons";
import { modules  } from "../../modules";


const { TabPane } = Tabs;

const HRModule = (props) => {
  const [activeTab, setActiveTab] = useState('dashboard');

  const hrModules = {
    employees: modules.employees,
    departments: modules.departments,
    attendance: modules.attendance,
    leaves: modules.leaves,
    payroll: modules.payroll,
    performance_reviews: modules.performance_reviews,
  };

  const hrModuleProperties = {
    employees: moduleProperties.employees,
    departments: require('./departments').default,
    attendance: require('./attendance').default,
    leaves: require('./leaves').default,
    payroll: require('./payroll').default,
    performance_reviews: require('./performance_reviews').default,
  };

  const renderModuleTab = (moduleKey, module) => {
    const moduleProps = hrModuleProperties[moduleKey] || {};
    
    return (
      <ModuleTable
        {...module}
        {...props}
        {...moduleProps}
      />
    );
  };

  const tabItems = [
    {
      key: 'dashboard',
      label: (
        <span>
          <DashboardOutlined />
          Dashboard
        </span>
      ),
      children: <HRDashboard {...props} />,
    },
    {
      key: 'employees',
      label: (
        <span>
          <UserOutlined />
          Employees
        </span>
      ),
      children: renderModuleTab('employees', hrModules.employees),
    },
    {
      key: 'departments',
      label: (
        <span>
          <ApartmentOutlined />
          Departments
        </span>
      ),
      children: renderModuleTab('departments', hrModules.departments),
    },
    {
      key: 'attendance',
      label: (
        <span>
          <ClockCircleOutlined />
          Attendance
        </span>
      ),
      children: renderModuleTab('attendance', hrModules.attendance),
    },
    {
      key: 'leaves',
      label: (
        <span>
          <CalendarOutlined />
          Leave Management
        </span>
      ),
      children: renderModuleTab('leaves', hrModules.leaves),
    },
    {
      key: 'payroll',
      label: (
        <span>
          <DollarOutlined />
          Payroll
        </span>
      ),
      children: renderModuleTab('payroll', hrModules.payroll),
    },
    {
      key: 'performance_reviews',
      label: (
        <span>
          <TrophyOutlined />
          Performance Reviews
        </span>
      ),
      children: renderModuleTab('performance_reviews', hrModules.performance_reviews),
    },
  ];

  return (
    <div style={{ padding: '24px' }}>
      <Card>
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          items={tabItems}
          type="card"
          size="large"
        />
      </Card>
    </div>
  );
};

const index = {
  CustomView: HRModule,
};

export default index;
