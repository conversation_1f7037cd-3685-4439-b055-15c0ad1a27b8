import React, { useState, useEffect } from "react";
import moment from "moment";
import { Card, Typography, Divider, Tag, Space, Row, Col, Button, Modal, Form, DatePicker, Select, Input, message, Progress, Rate, Statistic  } from "antd";
import { TrophyOutlined, UserOutlined, CalendarOutlined, StarOutlined, FileTextOutlined, PlusOutlined, EditOutlined, CheckCircleOutlined,   } from "@ant-design/icons";


const { Text, Title } = Typography;
const { TextArea } = Input;
const { RangePicker } = DatePicker;

const InfoItem = ({ icon, label, value, span = 8 }) => {
  if (!value) return null;

  return (
    <Col span={span}>
      <div style={{ marginBottom: '16px' }}>
        <Text type="secondary" style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          {icon} {label}
        </Text>
        <Text strong style={{ display: 'block', marginTop: '4px' }}>
          {value}
        </Text>
      </div>
    </Col>
  );
};

const getStatusColor = (status) => {
  const statusColors = {
    draft: '#d9d9d9',
    in_progress: '#faad14',
    completed: '#52c41a',
    approved: '#1890ff',
  };
  return statusColors[status] || '#d9d9d9';
};

const getReviewTypeColor = (type) => {
  const typeColors = {
    annual: '#1890ff',
    quarterly: '#52c41a',
    probation: '#faad14',
    promotion: '#722ed1',
    disciplinary: '#f5222d',
  };
  return typeColors[type] || '#d9d9d9';
};

const getRatingColor = (rating) => {
  const ratingColors = {
    5: '#52c41a',
    4: '#73d13d',
    3: '#faad14',
    2: '#ff7a45',
    1: '#f5222d',
  };
  return ratingColors[rating] || '#d9d9d9';
};

const PerformanceReviewForm = ({ pouchDatabase, databasePrefix, currentUser, onSuccess, editData = null }) => {
  const [reviewModal, setReviewModal] = useState(false);
  const [loading, setLoading] = useState(false);
  const [employees, setEmployees] = useState([]);
  const [form] = Form.useForm();

  useEffect(() => {
    if (reviewModal) {
      loadEmployees();
      if (editData) {
        form.setFieldsValue({
          ...editData,
          dateRange: editData.start_date && editData.end_date ? 
            [moment(editData.start_date), moment(editData.end_date)] : null,
          review_date: editData.review_date ? moment(editData.review_date) : null,
          next_review_date: editData.next_review_date ? moment(editData.next_review_date) : null,
        });
      }
    }
  }, [reviewModal, editData]);

  const loadEmployees = async () => {
    try {
      const employeesDb = pouchDatabase('employees', databasePrefix);
      const result = await employeesDb.getDocuments({
        selector: { status: 'active' },
        limit: 1000,
        include_docs: true,
      });
      setEmployees(result.docs || []);
    } catch (error) {
      console.error('Error loading employees:', error);
    }
  };

  const handleSubmit = async (values) => {
    try {
      setLoading(true);
      const reviewsDb = pouchDatabase('performance_reviews', databasePrefix);
      
      const reviewData = {
        ...values,
        start_date: values.dateRange[0].format('YYYY-MM-DD'),
        end_date: values.dateRange[1].format('YYYY-MM-DD'),
        review_date: values.review_date ? values.review_date.format('YYYY-MM-DD') : null,
        next_review_date: values.next_review_date ? values.next_review_date.format('YYYY-MM-DD') : null,
        branch: currentUser.branch,
        created_at: editData ? editData.created_at : new Date().toISOString(),
        created_by: editData ? editData.created_by : currentUser._id,
        updated_at: new Date().toISOString(),
        updated_by: currentUser._id,
      };

      if (editData) {
        reviewData._id = editData._id;
        reviewData._rev = editData._rev;
      }

      await reviewsDb.saveDocument(reviewData, currentUser);
      message.success(editData ? 'Performance review updated successfully!' : 'Performance review created successfully!');
      setReviewModal(false);
      form.resetFields();
      if (onSuccess) onSuccess();
    } catch (error) {
      console.error('Error saving performance review:', error);
      message.error('Failed to save performance review');
    } finally {
      setLoading(false);
    }
  };

  return (
    <>
      <Button
        type="primary"
        icon={editData ? <EditOutlined /> : <PlusOutlined />}
        onClick={() => setReviewModal(true)}
      >
        {editData ? 'Edit Review' : 'Create Review'}
      </Button>

      <Modal
        title={editData ? 'Edit Performance Review' : 'Create Performance Review'}
        visible={reviewModal}
        onCancel={() => setReviewModal(false)}
        footer={null}
        width={800}
      >
        <Form form={form} onFinish={handleSubmit} layout="vertical">
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="employee"
                label="Employee"
                rules={[{ required: true, message: 'Please select employee' }]}
              >
                <Select placeholder="Select employee" disabled={editData}>
                  {employees.map(emp => (
                    <Select.Option key={emp._id} value={emp._id}>
                      {emp.first_name} {emp.last_name} - {emp.position}
                    </Select.Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="review_type"
                label="Review Type"
                rules={[{ required: true, message: 'Please select review type' }]}
              >
                <Select placeholder="Select review type">
                  <Select.Option value="annual">Annual Review</Select.Option>
                  <Select.Option value="quarterly">Quarterly Review</Select.Option>
                  <Select.Option value="probation">Probation Review</Select.Option>
                  <Select.Option value="promotion">Promotion Review</Select.Option>
                  <Select.Option value="disciplinary">Disciplinary Review</Select.Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="review_period"
                label="Review Period"
                rules={[{ required: true, message: 'Please enter review period' }]}
              >
                <Input placeholder="e.g., Q1 2024, Annual 2024" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="dateRange"
                label="Review Period Dates"
                rules={[{ required: true, message: 'Please select review period dates' }]}
              >
                <RangePicker style={{ width: '100%' }} />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="reviewer"
                label="Reviewer"
                rules={[{ required: true, message: 'Please select reviewer' }]}
              >
                <Select placeholder="Select reviewer">
                  {employees.map(emp => (
                    <Select.Option key={emp._id} value={emp._id}>
                      {emp.first_name} {emp.last_name} - {emp.position}
                    </Select.Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="overall_rating"
                label="Overall Rating"
              >
                <Select placeholder="Select overall rating">
                  <Select.Option value="5">Excellent (5)</Select.Option>
                  <Select.Option value="4">Good (4)</Select.Option>
                  <Select.Option value="3">Satisfactory (3)</Select.Option>
                  <Select.Option value="2">Needs Improvement (2)</Select.Option>
                  <Select.Option value="1">Unsatisfactory (1)</Select.Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="goals_achievement"
            label="Goals Achievement (%)"
          >
            <Input type="number" min={0} max={100} placeholder="Enter percentage" />
          </Form.Item>

          <Form.Item
            name="strengths"
            label="Strengths"
          >
            <TextArea rows={3} placeholder="List employee's key strengths and achievements" />
          </Form.Item>

          <Form.Item
            name="areas_for_improvement"
            label="Areas for Improvement"
          >
            <TextArea rows={3} placeholder="Areas where employee can improve" />
          </Form.Item>

          <Form.Item
            name="goals_next_period"
            label="Goals for Next Period"
          >
            <TextArea rows={3} placeholder="Set goals and objectives for the next review period" />
          </Form.Item>

          <Form.Item
            name="training_needs"
            label="Training Needs"
          >
            <TextArea rows={2} placeholder="Identify training and development needs" />
          </Form.Item>

          <Form.Item
            name="employee_comments"
            label="Employee Comments"
          >
            <TextArea rows={2} placeholder="Employee's self-assessment and comments" />
          </Form.Item>

          <Form.Item
            name="manager_comments"
            label="Manager Comments"
          >
            <TextArea rows={2} placeholder="Additional manager comments and recommendations" />
          </Form.Item>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name="status"
                label="Status"
                initialValue="draft"
              >
                <Select>
                  <Select.Option value="draft">Draft</Select.Option>
                  <Select.Option value="in_progress">In Progress</Select.Option>
                  <Select.Option value="completed">Completed</Select.Option>
                  <Select.Option value="approved">Approved</Select.Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="review_date"
                label="Review Date"
              >
                <DatePicker style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="next_review_date"
                label="Next Review Date"
              >
                <DatePicker style={{ width: '100%' }} />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit" loading={loading}>
                {editData ? 'Update Review' : 'Create Review'}
              </Button>
              <Button onClick={() => setReviewModal(false)}>
                Cancel
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
};

const performance_reviews = {
  CustomView: ({ data, pouchDatabase, databasePrefix, currentUser }) => {
    const formatDate = (date) => {
      if (!date) return 'Not set';
      return moment(date).format('MMMM DD, YYYY');
    };

    const getRatingText = (rating) => {
      const ratings = {
        5: 'Excellent',
        4: 'Good',
        3: 'Satisfactory',
        2: 'Needs Improvement',
        1: 'Unsatisfactory',
      };
      return ratings[rating] || 'Not rated';
    };

    const calculateReviewProgress = () => {
      let progress = 0;
      if (data.status === 'draft') progress = 25;
      else if (data.status === 'in_progress') progress = 50;
      else if (data.status === 'completed') progress = 75;
      else if (data.status === 'approved') progress = 100;
      return progress;
    };

    return (
      <div style={{ padding: '24px' }}>
        <Card>
          {/* Header Section */}
          <div style={{ textAlign: 'center', marginBottom: '32px' }}>
            <TrophyOutlined style={{ fontSize: '64px', color: '#faad14', marginBottom: '16px' }} />
            <Title level={2} style={{ margin: '8px 0' }}>
              Performance Review
            </Title>
            <Space size="middle">
              <Tag color={getStatusColor(data.status)} style={{ fontSize: '14px', padding: '4px 12px' }}>
                {data.status?.replace('_', ' ').toUpperCase()}
              </Tag>
              <Tag color={getReviewTypeColor(data.review_type)} style={{ fontSize: '14px', padding: '4px 12px' }}>
                {data.review_type?.replace('_', ' ').toUpperCase()}
              </Tag>
            </Space>
            <div style={{ marginTop: '16px' }}>
              <Text type="secondary" style={{ fontSize: '16px' }}>
                {data.review_period} • {formatDate(data.start_date)} - {formatDate(data.end_date)}
              </Text>
            </div>
            {data.overall_rating && (
              <div style={{ marginTop: '16px' }}>
                <Rate disabled value={parseInt(data.overall_rating)} />
                <div style={{ marginTop: '8px' }}>
                  <Text strong style={{ color: getRatingColor(data.overall_rating) }}>
                    {getRatingText(data.overall_rating)} ({data.overall_rating}/5)
                  </Text>
                </div>
              </div>
            )}
          </div>

          <Divider />

          {/* Review Progress */}
          <Title level={4} style={{ marginBottom: '16px' }}>Review Progress</Title>
          <Progress
            percent={calculateReviewProgress()}
            strokeColor={getStatusColor(data.status)}
            style={{ marginBottom: '24px' }}
          />

          {/* Review Information */}
          <Title level={4} style={{ marginBottom: '16px' }}>Review Details</Title>
          <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
            <InfoItem
              icon={<UserOutlined />}
              label="Employee"
              value={data.employee?.label || 'Unknown Employee'}
              span={12}
            />
            <InfoItem
              icon={<UserOutlined />}
              label="Reviewer"
              value={data.reviewer?.label || 'Not assigned'}
              span={12}
            />
            <InfoItem
              icon={<CalendarOutlined />}
              label="Review Date"
              value={formatDate(data.review_date)}
              span={12}
            />
            <InfoItem
              icon={<CalendarOutlined />}
              label="Next Review Date"
              value={formatDate(data.next_review_date)}
              span={12}
            />
          </Row>

          {/* Performance Metrics */}
          {(data.overall_rating || data.goals_achievement) && (
            <>
              <Divider />
              <Title level={4} style={{ marginBottom: '16px' }}>Performance Metrics</Title>
              <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
                {data.overall_rating && (
                  <Col xs={24} sm={12}>
                    <Card size="small">
                      <Statistic
                        title="Overall Rating"
                        value={`${data.overall_rating}/5`}
                        prefix={<StarOutlined />}
                        valueStyle={{ color: getRatingColor(data.overall_rating) }}
                      />
                      <div style={{ marginTop: '8px' }}>
                        <Text type="secondary">{getRatingText(data.overall_rating)}</Text>
                      </div>
                    </Card>
                  </Col>
                )}
                {data.goals_achievement && (
                  <Col xs={24} sm={12}>
                    <Card size="small">
                      <Statistic
                        title="Goals Achievement"
                        value={data.goals_achievement}
                        suffix="%"
                        prefix={<CheckCircleOutlined />}
                        valueStyle={{ color: data.goals_achievement >= 80 ? '#52c41a' : data.goals_achievement >= 60 ? '#faad14' : '#f5222d' }}
                      />
                      <Progress
                        percent={data.goals_achievement}
                        showInfo={false}
                        strokeColor={data.goals_achievement >= 80 ? '#52c41a' : data.goals_achievement >= 60 ? '#faad14' : '#f5222d'}
                        style={{ marginTop: '8px' }}
                      />
                    </Card>
                  </Col>
                )}
              </Row>
            </>
          )}

          {/* Strengths */}
          {data.strengths && (
            <>
              <Divider />
              <Title level={4} style={{ marginBottom: '16px' }}>Strengths</Title>
              <Card size="small" style={{ backgroundColor: '#f6ffed', marginBottom: '24px' }}>
                <Text>{data.strengths}</Text>
              </Card>
            </>
          )}

          {/* Areas for Improvement */}
          {data.areas_for_improvement && (
            <>
              <Title level={4} style={{ marginBottom: '16px' }}>Areas for Improvement</Title>
              <Card size="small" style={{ backgroundColor: '#fff7e6', marginBottom: '24px' }}>
                <Text>{data.areas_for_improvement}</Text>
              </Card>
            </>
          )}

          {/* Goals for Next Period */}
          {data.goals_next_period && (
            <>
              <Title level={4} style={{ marginBottom: '16px' }}>Goals for Next Period</Title>
              <Card size="small" style={{ backgroundColor: '#e6f7ff', marginBottom: '24px' }}>
                <Text>{data.goals_next_period}</Text>
              </Card>
            </>
          )}

          {/* Training Needs */}
          {data.training_needs && (
            <>
              <Title level={4} style={{ marginBottom: '16px' }}>Training Needs</Title>
              <Card size="small" style={{ backgroundColor: '#f9f0ff', marginBottom: '24px' }}>
                <Text>{data.training_needs}</Text>
              </Card>
            </>
          )}

          {/* Comments */}
          {(data.employee_comments || data.manager_comments) && (
            <>
              <Divider />
              <Title level={4} style={{ marginBottom: '16px' }}>Comments</Title>
              <Row gutter={[16, 16]}>
                {data.employee_comments && (
                  <Col xs={24} md={12}>
                    <Card size="small" title="Employee Comments">
                      <Text>{data.employee_comments}</Text>
                    </Card>
                  </Col>
                )}
                {data.manager_comments && (
                  <Col xs={24} md={12}>
                    <Card size="small" title="Manager Comments">
                      <Text>{data.manager_comments}</Text>
                    </Card>
                  </Col>
                )}
              </Row>
            </>
          )}

          <Divider />

          {/* Actions */}
          <div style={{ textAlign: 'center' }}>
            <Space>
              <PerformanceReviewForm
                pouchDatabase={pouchDatabase}
                databasePrefix={databasePrefix}
                currentUser={currentUser}
                editData={data}
              />
            </Space>
          </div>
        </Card>
      </div>
    );
  },

  // Add performance review functionality
  ExtraComponents: {
    PerformanceReviewForm: PerformanceReviewForm,
  },

  // Custom toolbar buttons
  toolbarButtons: [
    {
      type: "primary",
      icon: "PlusOutlined",
      text: "Create Review",
      component: "PerformanceReviewForm"
    }
  ],
};

export default performance_reviews;
