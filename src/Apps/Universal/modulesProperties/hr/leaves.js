import React, { useState, useEffect } from "react";
import moment from "moment";
import { CalendarOutlined, UserOutlined, CheckCircleOutlined, CloseCircleOutlined, ExclamationCircleOutlined, PlusOutlined, FileTextOutlined,   } from "@ant-design/icons";
import { Card, Typography, Divider, Tag, Space, Row, Col, Button, Modal, Form, DatePicker, Select, Input, message, Progress, Statistic  } from "antd";


const { Text, Title } = Typography;
const { TextArea } = Input;
const { RangePicker } = DatePicker;

const InfoItem = ({ icon, label, value, span = 8 }) => {
  if (!value) return null;

  return (
    <Col span={span}>
      <div style={{ marginBottom: '16px' }}>
        <Text type="secondary" style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          {icon} {label}
        </Text>
        <Text strong style={{ display: 'block', marginTop: '4px' }}>
          {value}
        </Text>
      </div>
    </Col>
  );
};

const getStatusColor = (status) => {
  const statusColors = {
    pending: '#faad14',
    approved: '#52c41a',
    rejected: '#f5222d',
    cancelled: '#d9d9d9',
  };
  return statusColors[status] || '#d9d9d9';
};

const getLeaveTypeColor = (type) => {
  const typeColors = {
    annual: '#1890ff',
    sick: '#f5222d',
    maternity: '#722ed1',
    paternity: '#13c2c2',
    emergency: '#fa8c16',
    unpaid: '#8c8c8c',
    study: '#52c41a',
    compassionate: '#eb2f96',
  };
  return typeColors[type] || '#d9d9d9';
};

const LeaveRequestForm = ({ pouchDatabase, databasePrefix, currentUser, onSuccess }) => {
  const [requestModal, setRequestModal] = useState(false);
  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm();

  const handleSubmit = async (values) => {
    try {
      setLoading(true);
      const leavesDb = pouchDatabase('leaves', databasePrefix);
      
      const startDate = values.dateRange[0];
      const endDate = values.dateRange[1];
      const daysRequested = endDate.diff(startDate, 'days') + 1;

      const leaveRequest = {
        employee: currentUser._id,
        leave_type: values.leave_type,
        start_date: startDate.format('YYYY-MM-DD'),
        end_date: endDate.format('YYYY-MM-DD'),
        days_requested: daysRequested,
        reason: values.reason,
        status: 'pending',
        applied_date: moment().format('YYYY-MM-DD'),
        handover_notes: values.handover_notes,
        contact_during_leave: values.contact_during_leave,
        branch: currentUser.branch,
        created_at: new Date().toISOString(),
        created_by: currentUser._id,
      };

      await leavesDb.saveDocument(leaveRequest, currentUser);
      message.success('Leave request submitted successfully!');
      setRequestModal(false);
      form.resetFields();
      if (onSuccess) onSuccess();
    } catch (error) {
      console.error('Error submitting leave request:', error);
      message.error('Failed to submit leave request');
    } finally {
      setLoading(false);
    }
  };

  return (
    <>
      <Button
        type="primary"
        icon={<PlusOutlined />}
        onClick={() => setRequestModal(true)}
      >
        Request Leave
      </Button>

      <Modal
        title="Submit Leave Request"
        visible={requestModal}
        onCancel={() => setRequestModal(false)}
        footer={null}
        width={600}
      >
        <Form form={form} onFinish={handleSubmit} layout="vertical">
          <Form.Item
            name="leave_type"
            label="Leave Type"
            rules={[{ required: true, message: 'Please select leave type' }]}
          >
            <Select placeholder="Select leave type">
              <Select.Option value="annual">Annual Leave</Select.Option>
              <Select.Option value="sick">Sick Leave</Select.Option>
              <Select.Option value="maternity">Maternity Leave</Select.Option>
              <Select.Option value="paternity">Paternity Leave</Select.Option>
              <Select.Option value="emergency">Emergency Leave</Select.Option>
              <Select.Option value="unpaid">Unpaid Leave</Select.Option>
              <Select.Option value="study">Study Leave</Select.Option>
              <Select.Option value="compassionate">Compassionate Leave</Select.Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="dateRange"
            label="Leave Period"
            rules={[{ required: true, message: 'Please select leave dates' }]}
          >
            <RangePicker style={{ width: '100%' }} />
          </Form.Item>

          <Form.Item
            name="reason"
            label="Reason for Leave"
            rules={[{ required: true, message: 'Please provide reason for leave' }]}
          >
            <TextArea rows={3} placeholder="Please explain the reason for your leave request" />
          </Form.Item>

          <Form.Item
            name="handover_notes"
            label="Handover Notes"
          >
            <TextArea rows={2} placeholder="Any work handover instructions or notes" />
          </Form.Item>

          <Form.Item
            name="contact_during_leave"
            label="Contact Information During Leave"
          >
            <Input placeholder="Phone number or email for emergency contact" />
          </Form.Item>

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit" loading={loading}>
                Submit Request
              </Button>
              <Button onClick={() => setRequestModal(false)}>
                Cancel
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
};

const LeaveBalance = ({ pouchDatabase, databasePrefix, currentUser }) => {
  const [balances, setBalances] = useState({});
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadLeaveBalances();
  }, []);

  const loadLeaveBalances = async () => {
    try {
      const leavesDb = pouchDatabase('leaves', databasePrefix);
      const currentYear = moment().year();
      
      const result = await leavesDb.getDocuments({
        selector: {
          employee: currentUser._id,
          start_date: { $gte: `${currentYear}-01-01` },
          status: 'approved',
        },
        limit: 1000,
        include_docs: true,
      });

      // Calculate used days by leave type
      const usedDays = {};
      result.docs?.forEach(leave => {
        const type = leave.leave_type;
        usedDays[type] = (usedDays[type] || 0) + leave.days_requested;
      });

      // Standard leave allocations (could be made configurable)
      const allocations = {
        annual: 21,
        sick: 10,
        maternity: 84,
        paternity: 7,
        study: 5,
      };

      const calculatedBalances = {};
      Object.keys(allocations).forEach(type => {
        const allocated = allocations[type];
        const used = usedDays[type] || 0;
        const remaining = allocated - used;
        calculatedBalances[type] = {
          allocated,
          used,
          remaining: Math.max(0, remaining),
          percentage: Math.min(100, (used / allocated) * 100),
        };
      });

      setBalances(calculatedBalances);
    } catch (error) {
      console.error('Error loading leave balances:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card title="Leave Balance" size="small" loading={loading}>
      <Space direction="vertical" style={{ width: '100%' }}>
        {Object.entries(balances).map(([type, balance]) => (
          <div key={type}>
            <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '4px' }}>
              <Text strong>{type.charAt(0).toUpperCase() + type.slice(1)} Leave</Text>
              <Text>{balance.remaining}/{balance.allocated} days</Text>
            </div>
            <Progress
              percent={balance.percentage}
              strokeColor={balance.percentage > 80 ? '#f5222d' : balance.percentage > 60 ? '#faad14' : '#52c41a'}
              showInfo={false}
              size="small"
            />
          </div>
        ))}
      </Space>
    </Card>
  );
};

const leaves = {
  CustomView: ({ data }) => {
    const formatDate = (date) => {
      if (!date) return 'Not set';
      return moment(date).format('MMMM DD, YYYY');
    };

    const calculateDuration = () => {
      if (!data.start_date || !data.end_date) return 'Not calculated';
      const start = moment(data.start_date);
      const end = moment(data.end_date);
      const days = end.diff(start, 'days') + 1;
      return `${days} day${days > 1 ? 's' : ''}`;
    };

    return (
      <div style={{ padding: '24px' }}>
        <Card>
          {/* Header Section */}
          <div style={{ textAlign: 'center', marginBottom: '32px' }}>
            <CalendarOutlined style={{ fontSize: '64px', color: '#1890ff', marginBottom: '16px' }} />
            <Title level={2} style={{ margin: '8px 0' }}>
              Leave Request
            </Title>
            <Space size="middle">
              <Tag color={getStatusColor(data.status)} style={{ fontSize: '14px', padding: '4px 12px' }}>
                {data.status?.toUpperCase()}
              </Tag>
              <Tag color={getLeaveTypeColor(data.leave_type)} style={{ fontSize: '14px', padding: '4px 12px' }}>
                {data.leave_type?.replace('_', ' ').toUpperCase()}
              </Tag>
            </Space>
            <div style={{ marginTop: '16px' }}>
              <Text type="secondary" style={{ fontSize: '16px' }}>
                {formatDate(data.start_date)} - {formatDate(data.end_date)}
              </Text>
            </div>
          </div>

          <Divider />

          {/* Leave Information */}
          <Title level={4} style={{ marginBottom: '16px' }}>Leave Details</Title>
          <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
            <InfoItem
              icon={<UserOutlined />}
              label="Employee"
              value={data.employee?.label || 'Unknown Employee'}
              span={12}
            />
            <InfoItem
              icon={<CalendarOutlined />}
              label="Duration"
              value={calculateDuration()}
              span={12}
            />
            <InfoItem
              icon={<CalendarOutlined />}
              label="Applied Date"
              value={formatDate(data.applied_date)}
              span={12}
            />
            <InfoItem
              icon={<UserOutlined />}
              label="Approved By"
              value={data.approved_by?.label || 'Pending approval'}
              span={12}
            />
            <InfoItem
              icon={<CalendarOutlined />}
              label="Approved Date"
              value={formatDate(data.approved_date)}
              span={12}
            />
            <InfoItem
              icon={<CalendarOutlined />}
              label="Return Date"
              value={formatDate(data.return_date)}
              span={12}
            />
          </Row>

          <Divider />

          {/* Reason and Notes */}
          <Title level={4} style={{ marginBottom: '16px' }}>Reason for Leave</Title>
          <Card size="small" style={{ backgroundColor: '#fafafa', marginBottom: '24px' }}>
            <Text>{data.reason || 'No reason provided'}</Text>
          </Card>

          {data.handover_notes && (
            <>
              <Title level={4} style={{ marginBottom: '16px' }}>Handover Notes</Title>
              <Card size="small" style={{ backgroundColor: '#fafafa', marginBottom: '24px' }}>
                <Text>{data.handover_notes}</Text>
              </Card>
            </>
          )}

          {data.manager_comments && (
            <>
              <Title level={4} style={{ marginBottom: '16px' }}>Manager Comments</Title>
              <Card size="small" style={{ backgroundColor: '#fafafa', marginBottom: '24px' }}>
                <Text>{data.manager_comments}</Text>
              </Card>
            </>
          )}

          {/* Contact Information */}
          {data.contact_during_leave && (
            <>
              <Divider />
              <Title level={4} style={{ marginBottom: '16px' }}>Emergency Contact</Title>
              <Row gutter={[16, 16]}>
                <InfoItem
                  icon={<UserOutlined />}
                  label="Contact During Leave"
                  value={data.contact_during_leave}
                  span={24}
                />
              </Row>
            </>
          )}
        </Card>
      </div>
    );
  },

  // Add leave request functionality
  ExtraComponents: {
    LeaveRequest: LeaveRequestForm,
    LeaveBalance: LeaveBalance,
  },

  // Custom toolbar buttons
  toolbarButtons: [
    {
      type: "primary",
      icon: "PlusOutlined",
      text: "Request Leave",
      component: "LeaveRequest"
    },
    {
      type: "default",
      icon: "CalendarOutlined",
      text: "Leave Balance",
      component: "LeaveBalance"
    }
  ],
};

export default leaves;
