import React, { useState, useEffect } from "react";
import moment from "moment";
import { Card, Typography, Divider, Tag, Space, Row, Col, Button, Modal, Form, DatePicker, message, Table, Statistic, Select, InputNumber, Alert  } from "antd";
import { DollarOutlined, UserOutlined, CalendarOutlined, CalculatorOutlined, FileTextOutlined, PrinterOutlined, BankOutlined, InfoCircleOutlined,   } from "@ant-design/icons";
import { calculatePayroll, validatePayrollData  } from "../../modules/hr/utils/payrollCalculations";


const { Text, Title } = Typography;
const { MonthPicker } = DatePicker;

const InfoItem = ({ icon, label, value, span = 8 }) => {
  if (!value) return null;

  return (
    <Col span={span}>
      <div style={{ marginBottom: '16px' }}>
        <Text type="secondary" style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          {icon} {label}
        </Text>
        <Text strong style={{ display: 'block', marginTop: '4px' }}>
          {value}
        </Text>
      </div>
    </Col>
  );
};

const getStatusColor = (status) => {
  const statusColors = {
    pending: '#faad14',
    paid: '#52c41a',
    cancelled: '#f5222d',
    on_hold: '#d9d9d9',
  };
  return statusColors[status] || '#d9d9d9';
};

const formatCurrency = (amount) => {
  if (!amount) return 'UGX 0';
  return new Intl.NumberFormat('en-UG', {
    style: 'currency',
    currency: 'UGX',
    minimumFractionDigits: 0,
  }).format(amount);
};

const PayrollProcessor = ({ pouchDatabase, databasePrefix, currentUser, onSuccess }) => {
  const [processModal, setProcessModal] = useState(false);
  const [loading, setLoading] = useState(false);
  const [selectedMonth, setSelectedMonth] = useState(moment());
  const [employees, setEmployees] = useState([]);
  const [payrollData, setPayrollData] = useState([]);

  useEffect(() => {
    if (processModal) {
      loadEmployees();
    }
  }, [processModal]);

  const loadEmployees = async () => {
    try {
      const employeesDb = pouchDatabase('employees', databasePrefix);
      const result = await employeesDb.getDocuments({
        selector: { status: 'active' },
        limit: 1000,
        include_docs: true,
      });
      setEmployees(result.docs || []);
    } catch (error) {
      console.error('Error loading employees:', error);
    }
  };

  const calculateSalary = async (employee) => {
    try {
      // Get attendance data for the month
      const attendanceDb = pouchDatabase('attendance', databasePrefix);
      const monthStart = selectedMonth.clone().startOf('month').format('YYYY-MM-DD');
      const monthEnd = selectedMonth.clone().endOf('month').format('YYYY-MM-DD');

      const attendanceResult = await attendanceDb.getDocuments({
        selector: {
          employee: employee._id,
          date: { $gte: monthStart, $lte: monthEnd },
        },
        limit: 1000,
        include_docs: true,
      });

      // Get leave data for the month
      const leavesDb = pouchDatabase('leaves', databasePrefix);
      const leavesResult = await leavesDb.getDocuments({
        selector: {
          employee: employee._id,
          start_date: { $lte: monthEnd },
          end_date: { $gte: monthStart },
          status: 'approved',
        },
        limit: 1000,
        include_docs: true,
      });

      const basicSalary = employee.basic_salary || 0;
      const workingDays = selectedMonth.daysInMonth();
      
      // Calculate attendance-based deductions
      const attendanceRecords = attendanceResult.docs || [];
      const presentDays = attendanceRecords.filter(a => a.status === 'present').length;
      const absentDays = attendanceRecords.filter(a => a.status === 'absent').length;
      const lateDays = attendanceRecords.filter(a => a.status === 'late').length;
      
      // Calculate leave days
      const leaveRecords = leavesResult.docs || [];
      const leaveDays = leaveRecords.reduce((total, leave) => {
        const start = moment.max(moment(leave.start_date), moment(monthStart));
        const end = moment.min(moment(leave.end_date), moment(monthEnd));
        return total + end.diff(start, 'days') + 1;
      }, 0);

      // Calculate overtime
      const overtimeHours = attendanceRecords.reduce((total, record) => {
        return total + (record.overtime_hours || 0);
      }, 0);
      const overtimeRate = basicSalary / (workingDays * 8); // Assuming 8-hour workday
      const overtimeAmount = overtimeHours * overtimeRate * 1.5; // 1.5x overtime rate

      // Calculate allowances
      const allowances = {
        housing: employee.housing_allowance || 0,
        transport: employee.transport_allowance || 0,
        medical: employee.medical_allowance || 0,
        other: 0, // Can be added manually
      };
      const totalAllowances = Object.values(allowances).reduce((a, b) => a + b, 0);

      // Calculate deductions using the new calculation utilities
      const payrollData = {
        basic_salary: basicSalary,
        housing_allowance: allowances.housing,
        transport_allowance: allowances.transport,
        medical_allowance: allowances.medical,
        other_allowances: allowances.other,
        overtime_amount: overtimeAmount
      };

      const calculated = calculatePayroll(payrollData);
      const grossSalary = calculated.gross_salary;
      const taxDeduction = calculated.tax_deduction;
      const nssfDeduction = calculated.nssf_deduction;

      const deductions = {
        tax: taxDeduction,
        nssf: nssfDeduction,
        insurance: grossSalary * 0.01, // 1% insurance
        loan: 0, // Can be added manually
        advance: 0, // Can be added manually
        other: 0, // Can be added manually
      };

      // Attendance-based deductions
      const attendanceDeduction = (basicSalary / workingDays) * absentDays;
      deductions.attendance = attendanceDeduction;

      const totalDeductions = Object.values(deductions).reduce((a, b) => a + b, 0);
      const netSalary = grossSalary - totalDeductions;

      return {
        employee: employee._id,
        employee_name: `${employee.first_name} ${employee.last_name}`,
        month: selectedMonth.format('YYYY-MM'),
        pay_period: selectedMonth.format('MMMM YYYY'),
        basic_salary: basicSalary,
        housing_allowance: allowances.housing,
        transport_allowance: allowances.transport,
        medical_allowance: allowances.medical,
        other_allowances: allowances.other,
        overtime_amount: overtimeAmount,
        gross_salary: grossSalary,
        tax_deduction: deductions.tax,
        nssf_deduction: deductions.nssf,
        insurance_deduction: deductions.insurance,
        loan_deduction: deductions.loan,
        advance_deduction: deductions.advance,
        other_deductions: deductions.other,
        attendance_deduction: deductions.attendance,
        total_deductions: totalDeductions,
        net_salary: netSalary,
        payment_status: 'pending',
        working_days: workingDays,
        present_days: presentDays,
        absent_days: absentDays,
        late_days: lateDays,
        leave_days: leaveDays,
        overtime_hours: overtimeHours,
        branch: employee.branch,
        created_at: new Date().toISOString(),
        created_by: currentUser._id,
      };
    } catch (error) {
      console.error('Error calculating salary for employee:', employee._id, error);
      return null;
    }
  };

  const processPayroll = async () => {
    try {
      setLoading(true);
      
      // Check if payroll already exists for this month
      const payrollDb = pouchDatabase('payroll', databasePrefix);
      const existingPayroll = await payrollDb.getDocuments({
        selector: { month: selectedMonth.format('YYYY-MM') },
        limit: 1,
        include_docs: true,
      });

      if (existingPayroll.docs && existingPayroll.docs.length > 0) {
        message.warning('Payroll for this month already exists!');
        setLoading(false);
        return;
      }

      // Calculate payroll for all active employees
      const payrollPromises = employees.map(employee => calculateSalary(employee));
      const payrollResults = await Promise.all(payrollPromises);
      const validPayrollRecords = payrollResults.filter(record => record !== null);

      // Save payroll records
      const savePromises = validPayrollRecords.map(record =>
        payrollDb.saveDocument(record, currentUser)
      );
      await Promise.all(savePromises);

      setPayrollData(validPayrollRecords);
      message.success(`Payroll processed successfully for ${validPayrollRecords.length} employees!`);
      if (onSuccess) onSuccess();
    } catch (error) {
      console.error('Error processing payroll:', error);
      message.error('Failed to process payroll');
    } finally {
      setLoading(false);
      setProcessModal(false);
    }
  };

  const payrollColumns = [
    {
      title: 'Employee',
      dataIndex: 'employee_name',
      key: 'employee_name',
    },
    {
      title: 'Basic Salary',
      dataIndex: 'basic_salary',
      key: 'basic_salary',
      render: (value) => formatCurrency(value),
    },
    {
      title: 'Gross Salary',
      dataIndex: 'gross_salary',
      key: 'gross_salary',
      render: (value) => formatCurrency(value),
    },
    {
      title: 'Total Deductions',
      dataIndex: 'total_deductions',
      key: 'total_deductions',
      render: (value) => formatCurrency(value),
    },
    {
      title: 'Net Salary',
      dataIndex: 'net_salary',
      key: 'net_salary',
      render: (value) => formatCurrency(value),
    },
  ];

  return (
    <>
      <Button
        type="primary"
        icon={<CalculatorOutlined />}
        onClick={() => setProcessModal(true)}
      >
        Process Payroll
      </Button>

      <Modal
        title="Process Monthly Payroll"
        visible={processModal}
        onCancel={() => setProcessModal(false)}
        footer={null}
        width={800}
      >
        <Space direction="vertical" style={{ width: '100%' }}>
          <div>
            <Text strong>Select Month:</Text>
            <MonthPicker
              value={selectedMonth}
              onChange={setSelectedMonth}
              style={{ marginLeft: '16px' }}
            />
          </div>

          <div>
            <Text>This will process payroll for {employees.length} active employees for {selectedMonth.format('MMMM YYYY')}.</Text>
          </div>

          {payrollData.length > 0 && (
            <div>
              <Title level={4}>Payroll Preview</Title>
              <Table
                columns={payrollColumns}
                dataSource={payrollData}
                rowKey="employee"
                size="small"
                pagination={false}
                scroll={{ y: 300 }}
              />
            </div>
          )}

          <Space>
            <Button
              type="primary"
              icon={<CalculatorOutlined />}
              onClick={processPayroll}
              loading={loading}
            >
              Process Payroll
            </Button>
            <Button onClick={() => setProcessModal(false)}>
              Cancel
            </Button>
          </Space>
        </Space>
      </Modal>
    </>
  );
};

const PayslipGenerator = ({ data, pouchDatabase, databasePrefix }) => {
  const generatePayslip = () => {
    const payslipContent = `
      <div style="font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px;">
        <div style="text-align: center; margin-bottom: 30px;">
          <h1>PAYSLIP</h1>
          <h3>${data.pay_period}</h3>
        </div>
        
        <div style="margin-bottom: 20px;">
          <h3>Employee Information</h3>
          <p><strong>Name:</strong> ${data.employee_name}</p>
          <p><strong>Employee ID:</strong> ${data.employee}</p>
          <p><strong>Pay Period:</strong> ${data.pay_period}</p>
        </div>

        <table style="width: 100%; border-collapse: collapse; margin-bottom: 20px;">
          <tr style="background-color: #f5f5f5;">
            <th style="border: 1px solid #ddd; padding: 8px; text-align: left;">EARNINGS</th>
            <th style="border: 1px solid #ddd; padding: 8px; text-align: right;">AMOUNT (UGX)</th>
          </tr>
          <tr>
            <td style="border: 1px solid #ddd; padding: 8px;">Basic Salary</td>
            <td style="border: 1px solid #ddd; padding: 8px; text-align: right;">${data.basic_salary?.toLocaleString()}</td>
          </tr>
          <tr>
            <td style="border: 1px solid #ddd; padding: 8px;">Housing Allowance</td>
            <td style="border: 1px solid #ddd; padding: 8px; text-align: right;">${data.housing_allowance?.toLocaleString()}</td>
          </tr>
          <tr>
            <td style="border: 1px solid #ddd; padding: 8px;">Transport Allowance</td>
            <td style="border: 1px solid #ddd; padding: 8px; text-align: right;">${data.transport_allowance?.toLocaleString()}</td>
          </tr>
          <tr>
            <td style="border: 1px solid #ddd; padding: 8px;">Medical Allowance</td>
            <td style="border: 1px solid #ddd; padding: 8px; text-align: right;">${data.medical_allowance?.toLocaleString()}</td>
          </tr>
          <tr>
            <td style="border: 1px solid #ddd; padding: 8px;">Overtime</td>
            <td style="border: 1px solid #ddd; padding: 8px; text-align: right;">${data.overtime_amount?.toLocaleString()}</td>
          </tr>
          <tr style="background-color: #f5f5f5; font-weight: bold;">
            <td style="border: 1px solid #ddd; padding: 8px;">GROSS SALARY</td>
            <td style="border: 1px solid #ddd; padding: 8px; text-align: right;">${data.gross_salary?.toLocaleString()}</td>
          </tr>
        </table>

        <table style="width: 100%; border-collapse: collapse; margin-bottom: 20px;">
          <tr style="background-color: #f5f5f5;">
            <th style="border: 1px solid #ddd; padding: 8px; text-align: left;">DEDUCTIONS</th>
            <th style="border: 1px solid #ddd; padding: 8px; text-align: right;">AMOUNT (UGX)</th>
          </tr>
          <tr>
            <td style="border: 1px solid #ddd; padding: 8px;">PAYE Tax</td>
            <td style="border: 1px solid #ddd; padding: 8px; text-align: right;">${data.tax_deduction?.toLocaleString()}</td>
          </tr>
          <tr>
            <td style="border: 1px solid #ddd; padding: 8px;">NSSF</td>
            <td style="border: 1px solid #ddd; padding: 8px; text-align: right;">${data.nssf_deduction?.toLocaleString()}</td>
          </tr>
          <tr>
            <td style="border: 1px solid #ddd; padding: 8px;">Insurance</td>
            <td style="border: 1px solid #ddd; padding: 8px; text-align: right;">${data.insurance_deduction?.toLocaleString()}</td>
          </tr>
          <tr>
            <td style="border: 1px solid #ddd; padding: 8px;">Attendance Deduction</td>
            <td style="border: 1px solid #ddd; padding: 8px; text-align: right;">${data.attendance_deduction?.toLocaleString()}</td>
          </tr>
          <tr style="background-color: #f5f5f5; font-weight: bold;">
            <td style="border: 1px solid #ddd; padding: 8px;">TOTAL DEDUCTIONS</td>
            <td style="border: 1px solid #ddd; padding: 8px; text-align: right;">${data.total_deductions?.toLocaleString()}</td>
          </tr>
        </table>

        <div style="text-align: center; font-size: 18px; font-weight: bold; margin: 20px 0; padding: 10px; background-color: #e6f7ff; border: 2px solid #1890ff;">
          NET SALARY: UGX ${data.net_salary?.toLocaleString()}
        </div>

        <div style="margin-top: 30px;">
          <h4>Attendance Summary</h4>
          <p>Working Days: ${data.working_days}</p>
          <p>Present Days: ${data.present_days}</p>
          <p>Absent Days: ${data.absent_days}</p>
          <p>Late Days: ${data.late_days}</p>
          <p>Leave Days: ${data.leave_days}</p>
          <p>Overtime Hours: ${data.overtime_hours}</p>
        </div>
      </div>
    `;

    const printWindow = window.open('', '_blank');
    printWindow.document.write(`
      <html>
        <head>
          <title>Payslip - ${data.employee_name} - ${data.pay_period}</title>
        </head>
        <body>
          ${payslipContent}
        </body>
      </html>
    `);
    printWindow.document.close();
    printWindow.print();
  };

  return (
    <Button
      type="default"
      icon={<PrinterOutlined />}
      onClick={generatePayslip}
    >
      Generate Payslip
    </Button>
  );
};

// Component for automatic payroll calculations
const PayrollCalculator = ({ formRef, initialValues = {} }) => {
  const [calculatedData, setCalculatedData] = useState({});
  const [isCalculating, setIsCalculating] = useState(false);

  // Watch for form changes and recalculate
  useEffect(() => {
    if (formRef?.current) {
      const formValues = formRef.current.getFieldsValue();
      calculatePayrollData(formValues);
    }
  }, [formRef, initialValues]);

  const calculatePayrollData = (formValues) => {
    try {
      setIsCalculating(true);

      // Merge form values with initial values
      const payrollData = { ...initialValues, ...formValues };

      // Only calculate if we have basic salary
      if (payrollData.basic_salary && payrollData.basic_salary > 0) {
        const calculated = calculatePayroll(payrollData);
        setCalculatedData(calculated);

        // Update form with calculated values
        if (formRef?.current) {
          formRef.current.setFieldsValue({
            gross_salary: calculated.gross_salary,
            tax_deduction: calculated.tax_deduction,
            nssf_deduction: calculated.nssf_deduction,
            total_deductions: calculated.total_deductions,
            net_salary: calculated.net_salary
          });
        }
      }
    } catch (error) {
      console.error('Calculation error:', error);
    } finally {
      setIsCalculating(false);
    }
  };

  // Manual recalculation trigger
  const handleRecalculate = () => {
    if (formRef?.current) {
      const formValues = formRef.current.getFieldsValue();
      calculatePayrollData(formValues);
    }
  };

  return (
    <Card
      title={
        <Space>
          <CalculatorOutlined />
          <span>Payroll Calculator</span>
          <Button
            size="small"
            type="link"
            icon={<CalculatorOutlined />}
            onClick={handleRecalculate}
            loading={isCalculating}
          >
            Recalculate
          </Button>
        </Space>
      }
      size="small"
      style={{ marginBottom: 16 }}
    >
      <Alert
        message="Automatic Calculations"
        description="PAYE tax and NSSF contributions are calculated automatically based on Uganda tax laws. Gross salary, total deductions, and net salary are computed in real-time."
        type="info"
        showIcon
        style={{ marginBottom: 16 }}
      />

      {calculatedData.gross_salary > 0 && (
        <Row gutter={16}>
          <Col span={6}>
            <Statistic
              title="Gross Salary"
              value={calculatedData.gross_salary}
              formatter={(value) => formatCurrency(value)}
              valueStyle={{ color: '#52c41a' }}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="PAYE Tax"
              value={calculatedData.tax_deduction}
              formatter={(value) => formatCurrency(value)}
              valueStyle={{ color: '#f5222d' }}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="NSSF (5%)"
              value={calculatedData.nssf_deduction}
              formatter={(value) => formatCurrency(value)}
              valueStyle={{ color: '#fa8c16' }}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="Net Salary"
              value={calculatedData.net_salary}
              formatter={(value) => formatCurrency(value)}
              valueStyle={{ color: '#1890ff', fontWeight: 'bold' }}
            />
          </Col>
        </Row>
      )}

      {calculatedData.taxable_amount > 0 && (
        <div style={{ marginTop: 16, padding: 12, backgroundColor: '#f9f9f9', borderRadius: 4 }}>
          <Text type="secondary" style={{ fontSize: '12px' }}>
            <InfoCircleOutlined /> Tax-free threshold: {formatCurrency(235000)} |
            Taxable amount: {formatCurrency(calculatedData.taxable_amount)} |
            NSSF base: {formatCurrency(calculatedData.nssf_contribution_base)}
          </Text>
        </div>
      )}
    </Card>
  );
};

const payroll = {
  CustomView: ({ data }) => {
    return (
      <div style={{ padding: '24px' }}>
        <Card>
          {/* Header Section */}
          <div style={{ textAlign: 'center', marginBottom: '32px' }}>
            <DollarOutlined style={{ fontSize: '64px', color: '#52c41a', marginBottom: '16px' }} />
            <Title level={2} style={{ margin: '8px 0' }}>
              Payroll Record
            </Title>
            <Space size="middle">
              <Tag color={getStatusColor(data.payment_status)} style={{ fontSize: '14px', padding: '4px 12px' }}>
                {data.payment_status?.toUpperCase()}
              </Tag>
              <Tag style={{ fontSize: '14px', padding: '4px 12px' }}>
                {data.pay_period}
              </Tag>
            </Space>
            <div style={{ marginTop: '16px' }}>
              <Text type="secondary" style={{ fontSize: '16px' }}>
                {data.employee_name}
              </Text>
            </div>
          </div>

          <Divider />

          {/* Salary Breakdown */}
          <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
            <Col xs={24} md={8}>
              <Card size="small" title="Earnings">
                <Space direction="vertical" style={{ width: '100%' }}>
                  <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                    <Text>Basic Salary:</Text>
                    <Text strong>{formatCurrency(data.basic_salary)}</Text>
                  </div>
                  <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                    <Text>Allowances:</Text>
                    <Text strong>{formatCurrency((data.housing_allowance || 0) + (data.transport_allowance || 0) + (data.medical_allowance || 0))}</Text>
                  </div>
                  <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                    <Text>Overtime:</Text>
                    <Text strong>{formatCurrency(data.overtime_amount)}</Text>
                  </div>
                  <Divider style={{ margin: '8px 0' }} />
                  <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                    <Text strong>Gross Salary:</Text>
                    <Text strong style={{ color: '#52c41a' }}>{formatCurrency(data.gross_salary)}</Text>
                  </div>
                </Space>
              </Card>
            </Col>
            <Col xs={24} md={8}>
              <Card size="small" title="Deductions">
                <Space direction="vertical" style={{ width: '100%' }}>
                  <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                    <Text>Tax (PAYE):</Text>
                    <Text>{formatCurrency(data.tax_deduction)}</Text>
                  </div>
                  <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                    <Text>NSSF:</Text>
                    <Text>{formatCurrency(data.nssf_deduction)}</Text>
                  </div>
                  <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                    <Text>Insurance:</Text>
                    <Text>{formatCurrency(data.insurance_deduction)}</Text>
                  </div>
                  <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                    <Text>Other:</Text>
                    <Text>{formatCurrency((data.loan_deduction || 0) + (data.advance_deduction || 0) + (data.attendance_deduction || 0))}</Text>
                  </div>
                  <Divider style={{ margin: '8px 0' }} />
                  <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                    <Text strong>Total Deductions:</Text>
                    <Text strong style={{ color: '#f5222d' }}>{formatCurrency(data.total_deductions)}</Text>
                  </div>
                </Space>
              </Card>
            </Col>
            <Col xs={24} md={8}>
              <Card size="small" title="Net Salary">
                <div style={{ textAlign: 'center', padding: '20px 0' }}>
                  <Statistic
                    value={data.net_salary}
                    formatter={(value) => formatCurrency(value)}
                    valueStyle={{ color: '#1890ff', fontSize: '24px' }}
                  />
                </div>
              </Card>
            </Col>
          </Row>

          <Divider />

          {/* Attendance Summary */}
          <Title level={4} style={{ marginBottom: '16px' }}>Attendance Summary</Title>
          <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
            <InfoItem
              icon={<CalendarOutlined />}
              label="Working Days"
              value={data.working_days}
              span={8}
            />
            <InfoItem
              icon={<CalendarOutlined />}
              label="Present Days"
              value={data.present_days}
              span={8}
            />
            <InfoItem
              icon={<CalendarOutlined />}
              label="Absent Days"
              value={data.absent_days}
              span={8}
            />
            <InfoItem
              icon={<CalendarOutlined />}
              label="Late Days"
              value={data.late_days}
              span={8}
            />
            <InfoItem
              icon={<CalendarOutlined />}
              label="Leave Days"
              value={data.leave_days}
              span={8}
            />
            <InfoItem
              icon={<CalendarOutlined />}
              label="Overtime Hours"
              value={data.overtime_hours}
              span={8}
            />
          </Row>

          <Divider />

          {/* Payment Information */}
          <Title level={4} style={{ marginBottom: '16px' }}>Payment Information</Title>
          <Row gutter={[16, 16]}>
            <InfoItem
              icon={<BankOutlined />}
              label="Payment Method"
              value={data.payment_method || 'Not specified'}
              span={12}
            />
            <InfoItem
              icon={<CalendarOutlined />}
              label="Payment Date"
              value={data.payment_date ? moment(data.payment_date).format('MMMM DD, YYYY') : 'Not paid'}
              span={12}
            />
            <InfoItem
              icon={<BankOutlined />}
              label="Bank Account"
              value={data.bank_account || 'Not specified'}
              span={12}
            />
            <InfoItem
              icon={<UserOutlined />}
              label="Branch"
              value={data.branch?.label || 'Not specified'}
              span={12}
            />
          </Row>

          {data.notes && (
            <>
              <Divider />
              <Title level={4} style={{ marginBottom: '16px' }}>Notes</Title>
              <Card size="small" style={{ backgroundColor: '#fafafa' }}>
                <Text>{data.notes}</Text>
              </Card>
            </>
          )}

          <Divider />

          {/* Actions */}
          <div style={{ textAlign: 'center' }}>
            <Space>
              <PayslipGenerator data={data} />
            </Space>
          </div>
        </Card>
      </div>
    );
  },

  // Form enhancement with automatic calculations
  FormEnhancement: ({ formRef, initialValues }) => {
    return <PayrollCalculator formRef={formRef} initialValues={initialValues} />;
  },

  // Custom form hooks for automatic calculations
  onValuesChange: (changedValues, allValues, formRef) => {
    // Trigger recalculation when salary components change
    const salaryFields = [
      'basic_salary', 'housing_allowance', 'transport_allowance',
      'medical_allowance', 'other_allowances', 'overtime_amount'
    ];

    const hasChangedSalaryField = Object.keys(changedValues).some(field =>
      salaryFields.includes(field)
    );

    if (hasChangedSalaryField && formRef?.current) {
      try {
        const calculated = calculatePayroll(allValues);
        formRef.current.setFieldsValue({
          gross_salary: calculated.gross_salary,
          tax_deduction: calculated.tax_deduction,
          nssf_deduction: calculated.nssf_deduction,
          total_deductions: calculated.total_deductions,
          net_salary: calculated.net_salary
        });
      } catch (error) {
        console.error('Auto-calculation error:', error);
      }
    }
  },

  // Validation before save
  beforeSave: (values) => {
    const validation = validatePayrollData(values);

    if (!validation.isValid) {
      throw new Error(`Validation failed: ${validation.errors.join(', ')}`);
    }

    // Add calculated values
    const calculated = calculatePayroll(values);
    return { ...values, ...calculated };
  },

  // Add payroll processing functionality
  ExtraComponents: {
    PayrollProcessor: PayrollProcessor,
    PayrollCalculator: PayrollCalculator,
  },

  // Custom toolbar buttons
  toolbarButtons: [
    {
      type: "primary",
      icon: "CalculatorOutlined",
      text: "Process Payroll",
      component: "PayrollProcessor"
    }
  ],
};

export default payroll;
