import React, { useState, useEffect } from 'react';
import moment from "moment";
import { Card, Row, Col, Statistic, Typography, Space, Tag, Progress, Table, Avatar, Divider, Button, DatePicker, Select  } from "antd";
import { UserOutlined, TeamOutlined, CalendarOutlined, DollarOutlined, ClockCircleOutlined, TrophyOutlined, CheckCircleOutlined, ExclamationCircleOutlined, DownloadOutlined, BarChartOutlined,   } from "@ant-design/icons";


const { Title, Text } = Typography;
const { MonthPicker } = DatePicker;

const HRDashboard = ({ pouchDatabase, databasePrefix, currentUser }) => {
  const [dashboardData, setDashboardData] = useState({
    employees: { total: 0, active: 0, onLeave: 0, newHires: 0 },
    departments: { total: 0 },
    attendance: { present: 0, absent: 0, late: 0 },
    leaves: { pending: 0, approved: 0 },
    payroll: { processed: 0, pending: 0 },
    performance: { reviews: 0, overdue: 0 },
  });
  const [loading, setLoading] = useState(true);
  const [recentEmployees, setRecentEmployees] = useState([]);
  const [upcomingReviews, setUpcomingReviews] = useState([]);
  const [selectedMonth, setSelectedMonth] = useState(moment());
  const [departmentStats, setDepartmentStats] = useState([]);
  const [monthlyTrends, setMonthlyTrends] = useState([]);

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      setLoading(true);

      // Load employees data
      const employeesDb = pouchDatabase('employees', databasePrefix);
      const employees = await employeesDb.getDocuments({
        limit: 1000,
        include_docs: true,
      });

      // Load departments data
      const departmentsDb = pouchDatabase('departments', databasePrefix);
      const departments = await departmentsDb.getDocuments({
        limit: 1000,
        include_docs: true,
      });

      // Load attendance data for today
      const attendanceDb = pouchDatabase('attendance', databasePrefix);
      const today = moment().format('YYYY-MM-DD');
      const attendance = await attendanceDb.getDocuments({
        selector: { date: today },
        limit: 1000,
        include_docs: true,
      });

      // Load leaves data
      const leavesDb = pouchDatabase('leaves', databasePrefix);
      const leaves = await leavesDb.getDocuments({
        limit: 1000,
        include_docs: true,
      });

      // Load payroll data for current month
      const payrollDb = pouchDatabase('payroll', databasePrefix);
      const currentMonth = moment().format('YYYY-MM');
      const payroll = await payrollDb.getDocuments({
        selector: { month: currentMonth },
        limit: 1000,
        include_docs: true,
      });

      // Load performance reviews
      const reviewsDb = pouchDatabase('performance_reviews', databasePrefix);
      const reviews = await reviewsDb.getDocuments({
        limit: 1000,
        include_docs: true,
      });

      // Process data
      const employeeStats = {
        total: employees.docs?.length || 0,
        active: employees.docs?.filter(emp => emp.status === 'active').length || 0,
        onLeave: employees.docs?.filter(emp => emp.status === 'on_leave').length || 0,
        newHires: employees.docs?.filter(emp => 
          moment(emp.employment_date).isAfter(moment().subtract(30, 'days'))
        ).length || 0,
      };

      const attendanceStats = {
        present: attendance.docs?.filter(att => att.status === 'present').length || 0,
        absent: attendance.docs?.filter(att => att.status === 'absent').length || 0,
        late: attendance.docs?.filter(att => att.status === 'late').length || 0,
      };

      const leaveStats = {
        pending: leaves.docs?.filter(leave => leave.status === 'pending').length || 0,
        approved: leaves.docs?.filter(leave => leave.status === 'approved').length || 0,
      };

      const payrollStats = {
        processed: payroll.docs?.filter(pay => pay.payment_status === 'paid').length || 0,
        pending: payroll.docs?.filter(pay => pay.payment_status === 'pending').length || 0,
      };

      const performanceStats = {
        reviews: reviews.docs?.length || 0,
        overdue: reviews.docs?.filter(review => 
          moment(review.end_date).isBefore(moment()) && review.status !== 'completed'
        ).length || 0,
      };

      setDashboardData({
        employees: employeeStats,
        departments: { total: departments.docs?.length || 0 },
        attendance: attendanceStats,
        leaves: leaveStats,
        payroll: payrollStats,
        performance: performanceStats,
      });

      // Set recent employees (last 5 hired)
      const recent = employees.docs
        ?.sort((a, b) => moment(b.employment_date).diff(moment(a.employment_date)))
        .slice(0, 5) || [];
      setRecentEmployees(recent);

      // Set upcoming reviews (next 5)
      const upcoming = reviews.docs
        ?.filter(review => moment(review.end_date).isAfter(moment()) && review.status !== 'completed')
        .sort((a, b) => moment(a.end_date).diff(moment(b.end_date)))
        .slice(0, 5) || [];
      setUpcomingReviews(upcoming);

      // Load department statistics
      const deptStats = await loadDepartmentStatistics(departments.docs || [], employees.docs || []);
      setDepartmentStats(deptStats);

      // Load monthly trends
      const trends = await loadMonthlyTrends();
      setMonthlyTrends(trends);

    } catch (error) {
      console.error('Error loading dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadDepartmentStatistics = async (departments, employees) => {
    return departments.map(dept => {
      const deptEmployees = employees.filter(emp => emp.department === dept._id);
      return {
        name: dept.name,
        totalEmployees: deptEmployees.length,
        activeEmployees: deptEmployees.filter(emp => emp.status === 'active').length,
        budget: dept.budget || 0,
        head: dept.department_head?.label || 'Not assigned',
      };
    });
  };

  const loadMonthlyTrends = async () => {
    try {
      const trends = [];
      for (let i = 5; i >= 0; i--) {
        const month = moment().subtract(i, 'months');
        const monthStr = month.format('YYYY-MM');

        // Get attendance for the month
        const attendanceDb = pouchDatabase('attendance', databasePrefix);
        const monthStart = month.clone().startOf('month').format('YYYY-MM-DD');
        const monthEnd = month.clone().endOf('month').format('YYYY-MM-DD');

        const attendanceResult = await attendanceDb.getDocuments({
          selector: {
            date: { $gte: monthStart, $lte: monthEnd }
          },
          limit: 10000,
          include_docs: true,
        });

        // Get payroll for the month
        const payrollDb = pouchDatabase('payroll', databasePrefix);
        const payrollResult = await payrollDb.getDocuments({
          selector: { month: monthStr },
          limit: 1000,
          include_docs: true,
        });

        const attendanceRecords = attendanceResult.docs || [];
        const payrollRecords = payrollResult.docs || [];

        trends.push({
          month: month.format('MMM YYYY'),
          attendance: attendanceRecords.filter(a => a.status === 'present').length,
          totalPayroll: payrollRecords.reduce((sum, p) => sum + (p.net_salary || 0), 0),
          employeeCount: payrollRecords.length,
        });
      }
      return trends;
    } catch (error) {
      console.error('Error loading monthly trends:', error);
      return [];
    }
  };

  const getStatusColor = (status) => {
    const colors = {
      active: '#52c41a',
      inactive: '#faad14',
      on_leave: '#1890ff',
      terminated: '#f5222d',
      present: '#52c41a',
      absent: '#f5222d',
      late: '#faad14',
      pending: '#faad14',
      approved: '#52c41a',
      paid: '#52c41a',
    };
    return colors[status] || '#d9d9d9';
  };

  const attendanceRate = dashboardData.employees.active > 0
    ? Math.round((dashboardData.attendance.present / dashboardData.employees.active) * 100)
    : 0;

  const generateHRReport = () => {
    const reportData = {
      generatedDate: moment().format('MMMM DD, YYYY'),
      period: selectedMonth.format('MMMM YYYY'),
      summary: dashboardData,
      departmentStats,
      monthlyTrends,
      recentEmployees,
      upcomingReviews,
    };

    const reportContent = `
      <div style="font-family: Arial, sans-serif; max-width: 1000px; margin: 0 auto; padding: 20px;">
        <div style="text-align: center; margin-bottom: 30px;">
          <h1>HR DASHBOARD REPORT</h1>
          <h3>${reportData.period}</h3>
          <p>Generated on ${reportData.generatedDate}</p>
        </div>

        <div style="margin-bottom: 30px;">
          <h2>Executive Summary</h2>
          <table style="width: 100%; border-collapse: collapse;">
            <tr>
              <td style="border: 1px solid #ddd; padding: 8px;"><strong>Total Employees:</strong></td>
              <td style="border: 1px solid #ddd; padding: 8px;">${reportData.summary.employees.total}</td>
              <td style="border: 1px solid #ddd; padding: 8px;"><strong>Active Employees:</strong></td>
              <td style="border: 1px solid #ddd; padding: 8px;">${reportData.summary.employees.active}</td>
            </tr>
            <tr>
              <td style="border: 1px solid #ddd; padding: 8px;"><strong>Attendance Rate:</strong></td>
              <td style="border: 1px solid #ddd; padding: 8px;">${attendanceRate}%</td>
              <td style="border: 1px solid #ddd; padding: 8px;"><strong>Pending Leaves:</strong></td>
              <td style="border: 1px solid #ddd; padding: 8px;">${reportData.summary.leaves.pending}</td>
            </tr>
            <tr>
              <td style="border: 1px solid #ddd; padding: 8px;"><strong>Departments:</strong></td>
              <td style="border: 1px solid #ddd; padding: 8px;">${reportData.summary.departments.total}</td>
              <td style="border: 1px solid #ddd; padding: 8px;"><strong>Overdue Reviews:</strong></td>
              <td style="border: 1px solid #ddd; padding: 8px;">${reportData.summary.performance.overdue}</td>
            </tr>
          </table>
        </div>

        <div style="margin-bottom: 30px;">
          <h2>Department Statistics</h2>
          <table style="width: 100%; border-collapse: collapse;">
            <tr style="background-color: #f5f5f5;">
              <th style="border: 1px solid #ddd; padding: 8px;">Department</th>
              <th style="border: 1px solid #ddd; padding: 8px;">Total Employees</th>
              <th style="border: 1px solid #ddd; padding: 8px;">Active Employees</th>
              <th style="border: 1px solid #ddd; padding: 8px;">Department Head</th>
            </tr>
            ${reportData.departmentStats.map(dept => `
              <tr>
                <td style="border: 1px solid #ddd; padding: 8px;">${dept.name}</td>
                <td style="border: 1px solid #ddd; padding: 8px;">${dept.totalEmployees}</td>
                <td style="border: 1px solid #ddd; padding: 8px;">${dept.activeEmployees}</td>
                <td style="border: 1px solid #ddd; padding: 8px;">${dept.head}</td>
              </tr>
            `).join('')}
          </table>
        </div>

        <div style="margin-bottom: 30px;">
          <h2>Recent Hires</h2>
          <table style="width: 100%; border-collapse: collapse;">
            <tr style="background-color: #f5f5f5;">
              <th style="border: 1px solid #ddd; padding: 8px;">Name</th>
              <th style="border: 1px solid #ddd; padding: 8px;">Position</th>
              <th style="border: 1px solid #ddd; padding: 8px;">Department</th>
              <th style="border: 1px solid #ddd; padding: 8px;">Hire Date</th>
            </tr>
            ${reportData.recentEmployees.map(emp => `
              <tr>
                <td style="border: 1px solid #ddd; padding: 8px;">${emp.first_name} ${emp.last_name}</td>
                <td style="border: 1px solid #ddd; padding: 8px;">${emp.position || 'N/A'}</td>
                <td style="border: 1px solid #ddd; padding: 8px;">${emp.department?.label || 'N/A'}</td>
                <td style="border: 1px solid #ddd; padding: 8px;">${moment(emp.employment_date).format('MMM DD, YYYY')}</td>
              </tr>
            `).join('')}
          </table>
        </div>
      </div>
    `;

    const printWindow = window.open('', '_blank');
    printWindow.document.write(`
      <html>
        <head>
          <title>HR Dashboard Report - ${reportData.period}</title>
        </head>
        <body>
          ${reportContent}
        </body>
      </html>
    `);
    printWindow.document.close();
    printWindow.print();
  };

  const exportToCSV = () => {
    const csvData = [
      ['HR Dashboard Report', selectedMonth.format('MMMM YYYY')],
      ['Generated Date', moment().format('MMMM DD, YYYY')],
      [],
      ['Metric', 'Value'],
      ['Total Employees', dashboardData.employees.total],
      ['Active Employees', dashboardData.employees.active],
      ['On Leave', dashboardData.employees.onLeave],
      ['New Hires (Last 30 days)', dashboardData.employees.newHires],
      ['Attendance Rate', `${attendanceRate}%`],
      ['Present Today', dashboardData.attendance.present],
      ['Absent Today', dashboardData.attendance.absent],
      ['Late Today', dashboardData.attendance.late],
      ['Pending Leave Requests', dashboardData.leaves.pending],
      ['Approved Leaves', dashboardData.leaves.approved],
      ['Pending Payroll', dashboardData.payroll.pending],
      ['Processed Payroll', dashboardData.payroll.processed],
      [],
      ['Department Statistics'],
      ['Department', 'Total Employees', 'Active Employees', 'Department Head'],
      ...departmentStats.map(dept => [dept.name, dept.totalEmployees, dept.activeEmployees, dept.head])
    ];

    const csvContent = csvData.map(row => row.join(',')).join('\n');
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `hr-dashboard-report-${selectedMonth.format('YYYY-MM')}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
  };

  const recentEmployeesColumns = [
    {
      title: 'Employee',
      dataIndex: 'name',
      key: 'name',
      render: (_, record) => (
        <Space>
          <Avatar src={record.avatar} icon={<UserOutlined />} />
          <div>
            <div>{record.first_name} {record.last_name}</div>
            <Text type="secondary" style={{ fontSize: '12px' }}>
              {record.position}
            </Text>
          </div>
        </Space>
      ),
    },
    {
      title: 'Department',
      dataIndex: 'department',
      key: 'department',
      render: (dept) => dept?.label || 'N/A',
    },
    {
      title: 'Hire Date',
      dataIndex: 'employment_date',
      key: 'employment_date',
      render: (date) => moment(date).format('MMM DD, YYYY'),
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status) => (
        <Tag color={getStatusColor(status)}>
          {status?.replace('_', ' ').toUpperCase()}
        </Tag>
      ),
    },
  ];

  return (
    <div style={{ padding: '24px' }}>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '24px' }}>
        <Title level={2} style={{ margin: 0 }}>
          HR Dashboard
        </Title>
        <Space>
          <MonthPicker
            value={selectedMonth}
            onChange={setSelectedMonth}
            placeholder="Select Month"
          />
          <Button
            type="primary"
            icon={<DownloadOutlined />}
            onClick={generateHRReport}
          >
            Generate Report
          </Button>
          <Button
            icon={<BarChartOutlined />}
            onClick={exportToCSV}
          >
            Export CSV
          </Button>
        </Space>
      </div>

      {/* Key Metrics */}
      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="Total Employees"
              value={dashboardData.employees.total}
              prefix={<TeamOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
            <div style={{ marginTop: '8px' }}>
              <Text type="secondary">
                {dashboardData.employees.active} active • {dashboardData.employees.newHires} new hires
              </Text>
            </div>
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="Departments"
              value={dashboardData.departments.total}
              prefix={<UserOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="Today's Attendance"
              value={`${attendanceRate}%`}
              prefix={<ClockCircleOutlined />}
              valueStyle={{ color: attendanceRate >= 90 ? '#52c41a' : attendanceRate >= 75 ? '#faad14' : '#f5222d' }}
            />
            <Progress 
              percent={attendanceRate} 
              showInfo={false} 
              strokeColor={attendanceRate >= 90 ? '#52c41a' : attendanceRate >= 75 ? '#faad14' : '#f5222d'}
              style={{ marginTop: '8px' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="Pending Leaves"
              value={dashboardData.leaves.pending}
              prefix={<CalendarOutlined />}
              valueStyle={{ color: dashboardData.leaves.pending > 0 ? '#faad14' : '#52c41a' }}
            />
          </Card>
        </Col>
      </Row>

      {/* Detailed Stats */}
      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
        <Col xs={24} md={12}>
          <Card title="Attendance Overview" size="small">
            <Row gutter={16}>
              <Col span={8}>
                <Statistic
                  title="Present"
                  value={dashboardData.attendance.present}
                  valueStyle={{ color: '#52c41a', fontSize: '18px' }}
                />
              </Col>
              <Col span={8}>
                <Statistic
                  title="Absent"
                  value={dashboardData.attendance.absent}
                  valueStyle={{ color: '#f5222d', fontSize: '18px' }}
                />
              </Col>
              <Col span={8}>
                <Statistic
                  title="Late"
                  value={dashboardData.attendance.late}
                  valueStyle={{ color: '#faad14', fontSize: '18px' }}
                />
              </Col>
            </Row>
          </Card>
        </Col>
        <Col xs={24} md={12}>
          <Card title="Payroll Status" size="small">
            <Row gutter={16}>
              <Col span={12}>
                <Statistic
                  title="Processed"
                  value={dashboardData.payroll.processed}
                  prefix={<CheckCircleOutlined />}
                  valueStyle={{ color: '#52c41a', fontSize: '18px' }}
                />
              </Col>
              <Col span={12}>
                <Statistic
                  title="Pending"
                  value={dashboardData.payroll.pending}
                  prefix={<ExclamationCircleOutlined />}
                  valueStyle={{ color: '#faad14', fontSize: '18px' }}
                />
              </Col>
            </Row>
          </Card>
        </Col>
      </Row>

      {/* Department Statistics */}
      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
        <Col span={24}>
          <Card title="Department Statistics" size="small">
            <Table
              columns={[
                {
                  title: 'Department',
                  dataIndex: 'name',
                  key: 'name',
                },
                {
                  title: 'Total Employees',
                  dataIndex: 'totalEmployees',
                  key: 'totalEmployees',
                },
                {
                  title: 'Active Employees',
                  dataIndex: 'activeEmployees',
                  key: 'activeEmployees',
                },
                {
                  title: 'Department Head',
                  dataIndex: 'head',
                  key: 'head',
                },
                {
                  title: 'Budget',
                  dataIndex: 'budget',
                  key: 'budget',
                  render: (value) => value ? `UGX ${value.toLocaleString()}` : 'Not set',
                },
              ]}
              dataSource={departmentStats}
              pagination={false}
              size="small"
              loading={loading}
              rowKey="name"
            />
          </Card>
        </Col>
      </Row>

      {/* Monthly Trends */}
      {monthlyTrends.length > 0 && (
        <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
          <Col span={24}>
            <Card title="6-Month Trends" size="small">
              <Row gutter={16}>
                {monthlyTrends.map((trend, index) => (
                  <Col xs={24} sm={12} md={4} key={index}>
                    <Card size="small" style={{ textAlign: 'center' }}>
                      <div style={{ marginBottom: '8px' }}>
                        <Text strong>{trend.month}</Text>
                      </div>
                      <div style={{ marginBottom: '4px' }}>
                        <Text type="secondary">Employees: </Text>
                        <Text>{trend.employeeCount}</Text>
                      </div>
                      <div style={{ marginBottom: '4px' }}>
                        <Text type="secondary">Attendance: </Text>
                        <Text>{trend.attendance}</Text>
                      </div>
                      <div>
                        <Text type="secondary">Payroll: </Text>
                        <Text>UGX {(trend.totalPayroll / 1000000).toFixed(1)}M</Text>
                      </div>
                    </Card>
                  </Col>
                ))}
              </Row>
            </Card>
          </Col>
        </Row>
      )}

      {/* Recent Activity */}
      <Row gutter={[16, 16]}>
        <Col xs={24} lg={16}>
          <Card title="Recent Hires" size="small">
            <Table
              columns={recentEmployeesColumns}
              dataSource={recentEmployees}
              pagination={false}
              size="small"
              loading={loading}
              rowKey="_id"
            />
          </Card>
        </Col>
        <Col xs={24} lg={8}>
          <Card title="Quick Actions" size="small">
            <Space direction="vertical" style={{ width: '100%' }}>
              <Text strong>Performance Reviews</Text>
              <div>
                <Text type="secondary">
                  {dashboardData.performance.overdue} overdue reviews
                </Text>
              </div>
              <Divider />
              <Text strong>Leave Requests</Text>
              <div>
                <Text type="secondary">
                  {dashboardData.leaves.pending} pending approvals
                </Text>
              </div>
              <Divider />
              <Text strong>Payroll Status</Text>
              <div>
                <Text type="secondary">
                  {dashboardData.payroll.pending} pending payments
                </Text>
              </div>
            </Space>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default HRDashboard;
