import React from "react";
import moment from "moment";
import { ApartmentOutlined, UserOutlined, TeamOutlined, BankOutlined, EnvironmentOutlined, InfoCircleOutlined,   } from "@ant-design/icons";
import { Card, Typography, Divider, Tag, Space, Row, Col, Tooltip, Tree, Statistic  } from "antd";


const { Text, Title } = Typography;

const InfoItem = ({ icon, label, value, span = 8 }) => {
  if (!value) return null;

  return (
    <Col span={span}>
      <div style={{ marginBottom: '16px' }}>
        <Text type="secondary" style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          {icon} {label}
        </Text>
        <Text strong style={{ display: 'block', marginTop: '4px' }}>
          {value}
        </Text>
      </div>
    </Col>
  );
};

const getStatusColor = (status) => {
  const statusColors = {
    active: '#52c41a',
    inactive: '#f5222d',
  };
  return statusColors[status] || '#d9d9d9';
};

const departments = {
  CustomView: ({ data, pouchDatabase, databasePrefix }) => {
    const [employees, setEmployees] = React.useState([]);
    const [subDepartments, setSubDepartments] = React.useState([]);
    const [loading, setLoading] = React.useState(true);

    React.useEffect(() => {
      loadDepartmentData();
    }, [data._id]);

    const loadDepartmentData = async () => {
      try {
        setLoading(true);

        // Load employees in this department
        const employeesDb = pouchDatabase('employees', databasePrefix);
        const employeesResult = await employeesDb.getDocuments({
          selector: { department: data._id },
          limit: 1000,
          include_docs: true,
        });

        // Load sub-departments
        const departmentsDb = pouchDatabase('departments', databasePrefix);
        const subDepartmentsResult = await departmentsDb.getDocuments({
          selector: { parent_department: data._id },
          limit: 1000,
          include_docs: true,
        });

        setEmployees(employeesResult.docs || []);
        setSubDepartments(subDepartmentsResult.docs || []);
      } catch (error) {
        console.error('Error loading department data:', error);
      } finally {
        setLoading(false);
      }
    };

    const formatCurrency = (amount) => {
      if (!amount) return 'Not set';
      return new Intl.NumberFormat('en-UG', {
        style: 'currency',
        currency: 'UGX',
        minimumFractionDigits: 0,
      }).format(amount);
    };

    const getEmployeeStats = () => {
      const total = employees.length;
      const active = employees.filter(emp => emp.status === 'active').length;
      const onLeave = employees.filter(emp => emp.status === 'on_leave').length;
      const inactive = employees.filter(emp => emp.status === 'inactive').length;

      return { total, active, onLeave, inactive };
    };

    const stats = getEmployeeStats();

    return (
      <div style={{ padding: '24px' }}>
        <Card>
          {/* Header Section */}
          <div style={{ textAlign: 'center', marginBottom: '32px' }}>
            <ApartmentOutlined style={{ fontSize: '64px', color: '#1890ff', marginBottom: '16px' }} />
            <Title level={2} style={{ margin: '8px 0' }}>
              {data.name}
            </Title>
            <Space size="middle">
              <Tag color={getStatusColor(data.status)} style={{ fontSize: '14px', padding: '4px 12px' }}>
                {data.status?.toUpperCase()}
              </Tag>
              {data.code && (
                <Tag style={{ fontSize: '14px', padding: '4px 12px' }}>
                  Code: {data.code}
                </Tag>
              )}
            </Space>
            {data.description && (
              <div style={{ marginTop: '16px', maxWidth: '600px', margin: '16px auto 0' }}>
                <Text type="secondary" style={{ fontSize: '16px' }}>
                  {data.description}
                </Text>
              </div>
            )}
          </div>

          <Divider />

          {/* Department Information */}
          <Title level={4} style={{ marginBottom: '16px' }}>Department Information</Title>
          <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
            <InfoItem
              icon={<UserOutlined />}
              label="Department Head"
              value={data.department_head?.label || 'Not assigned'}
              span={12}
            />
            <InfoItem
              icon={<ApartmentOutlined />}
              label="Parent Department"
              value={data.parent_department?.label || 'None (Top Level)'}
              span={12}
            />
            <InfoItem
              icon={<BankOutlined />}
              label="Budget"
              value={formatCurrency(data.budget)}
              span={12}
            />
            <InfoItem
              icon={<EnvironmentOutlined />}
              label="Location"
              value={data.location || 'Not specified'}
              span={12}
            />
            <InfoItem
              icon={<EnvironmentOutlined />}
              label="Branch"
              value={data.branch?.label || 'Not assigned'}
              span={12}
            />
            <InfoItem
              icon={<InfoCircleOutlined />}
              label="Created Date"
              value={data.createdAt ? moment(data.createdAt).format('MMMM DD, YYYY') : 'Not available'}
              span={12}
            />
          </Row>

          <Divider />

          {/* Employee Statistics */}
          <Title level={4} style={{ marginBottom: '16px' }}>Employee Statistics</Title>
          <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
            <Col xs={24} sm={6}>
              <Card size="small">
                <Statistic
                  title="Total Employees"
                  value={stats.total}
                  prefix={<TeamOutlined />}
                  valueStyle={{ color: '#1890ff' }}
                />
              </Card>
            </Col>
            <Col xs={24} sm={6}>
              <Card size="small">
                <Statistic
                  title="Active"
                  value={stats.active}
                  prefix={<UserOutlined />}
                  valueStyle={{ color: '#52c41a' }}
                />
              </Card>
            </Col>
            <Col xs={24} sm={6}>
              <Card size="small">
                <Statistic
                  title="On Leave"
                  value={stats.onLeave}
                  prefix={<UserOutlined />}
                  valueStyle={{ color: '#faad14' }}
                />
              </Card>
            </Col>
            <Col xs={24} sm={6}>
              <Card size="small">
                <Statistic
                  title="Inactive"
                  value={stats.inactive}
                  prefix={<UserOutlined />}
                  valueStyle={{ color: '#f5222d' }}
                />
              </Card>
            </Col>
          </Row>

          {/* Sub-departments */}
          {subDepartments.length > 0 && (
            <>
              <Divider />
              <Title level={4} style={{ marginBottom: '16px' }}>Sub-Departments</Title>
              <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
                {subDepartments.map(subDept => (
                  <Col xs={24} sm={12} md={8} key={subDept._id}>
                    <Card size="small" hoverable>
                      <div style={{ textAlign: 'center' }}>
                        <ApartmentOutlined style={{ fontSize: '24px', color: '#1890ff', marginBottom: '8px' }} />
                        <div>
                          <Text strong>{subDept.name}</Text>
                        </div>
                        {subDept.code && (
                          <div>
                            <Text type="secondary" style={{ fontSize: '12px' }}>
                              {subDept.code}
                            </Text>
                          </div>
                        )}
                        <div style={{ marginTop: '8px' }}>
                          <Tag color={getStatusColor(subDept.status)} size="small">
                            {subDept.status?.toUpperCase()}
                          </Tag>
                        </div>
                      </div>
                    </Card>
                  </Col>
                ))}
              </Row>
            </>
          )}

          {/* Recent Employees */}
          {employees.length > 0 && (
            <>
              <Divider />
              <Title level={4} style={{ marginBottom: '16px' }}>Department Employees</Title>
              <Row gutter={[16, 16]}>
                {employees.slice(0, 6).map(employee => (
                  <Col xs={24} sm={12} md={8} key={employee._id}>
                    <Card size="small" hoverable>
                      <div style={{ textAlign: 'center' }}>
                        <UserOutlined style={{ fontSize: '24px', color: '#52c41a', marginBottom: '8px' }} />
                        <div>
                          <Text strong>{employee.first_name} {employee.last_name}</Text>
                        </div>
                        <div>
                          <Text type="secondary" style={{ fontSize: '12px' }}>
                            {employee.position || 'No position'}
                          </Text>
                        </div>
                        <div style={{ marginTop: '8px' }}>
                          <Tag color={getStatusColor(employee.status)} size="small">
                            {employee.status?.replace('_', ' ').toUpperCase()}
                          </Tag>
                        </div>
                      </div>
                    </Card>
                  </Col>
                ))}
                {employees.length > 6 && (
                  <Col xs={24}>
                    <Text type="secondary">
                      ... and {employees.length - 6} more employees
                    </Text>
                  </Col>
                )}
              </Row>
            </>
          )}
        </Card>
      </div>
    );
  },

  // Custom actions for department management
  MoreActions: (props) => {
    // Add custom actions like generating department reports, etc.
    return null;
  },
};

export default departments;
