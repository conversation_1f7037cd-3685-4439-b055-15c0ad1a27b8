import React, { useState, useEffect } from "react";
import moment from "moment";
import { Card, Typography, Divider, Tag, Space, Row, Col, Button, Modal, Form, TimePicker, message, Table, Statistic  } from "antd";
import { ClockCircleOutlined, UserOutlined, CalendarOutlined, CheckCircleOutlined, CloseCircleOutlined, ExclamationCircleOutlined, PlayCircleOutlined, PauseCircleOutlined,   } from "@ant-design/icons";


const { Text, Title } = Typography;

const InfoItem = ({ icon, label, value, span = 8 }) => {
  if (!value) return null;

  return (
    <Col span={span}>
      <div style={{ marginBottom: '16px' }}>
        <Text type="secondary" style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          {icon} {label}
        </Text>
        <Text strong style={{ display: 'block', marginTop: '4px' }}>
          {value}
        </Text>
      </div>
    </Col>
  );
};

const getStatusColor = (status) => {
  const statusColors = {
    present: '#52c41a',
    absent: '#f5222d',
    late: '#faad14',
    half_day: '#1890ff',
    on_leave: '#722ed1',
  };
  return statusColors[status] || '#d9d9d9';
};

const AttendanceClockInOut = ({ pouchDatabase, databasePrefix, currentUser, onUpdate }) => {
  const [clockInModal, setClockInModal] = useState(false);
  const [clockOutModal, setClockOutModal] = useState(false);
  const [todayAttendance, setTodayAttendance] = useState(null);
  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm();

  useEffect(() => {
    loadTodayAttendance();
  }, []);

  const loadTodayAttendance = async () => {
    try {
      const attendanceDb = pouchDatabase('attendance', databasePrefix);
      const today = moment().format('YYYY-MM-DD');
      
      const result = await attendanceDb.getDocuments({
        selector: {
          employee: currentUser._id,
          date: today,
        },
        limit: 1,
        include_docs: true,
      });

      if (result.docs && result.docs.length > 0) {
        setTodayAttendance(result.docs[0]);
      }
    } catch (error) {
      console.error('Error loading today attendance:', error);
    }
  };

  const handleClockIn = async (values) => {
    try {
      setLoading(true);
      const attendanceDb = pouchDatabase('attendance', databasePrefix);
      
      const attendanceRecord = {
        employee: currentUser._id,
        date: moment().format('YYYY-MM-DD'),
        clock_in: values.clock_in.format('HH:mm'),
        status: 'present',
        location: 'Office', // Could be enhanced with geolocation
        ip_address: await getClientIP(),
        device: navigator.userAgent,
        branch: currentUser.branch,
        created_at: new Date().toISOString(),
        created_by: currentUser._id,
      };

      await attendanceDb.saveDocument(attendanceRecord, currentUser);
      message.success('Clocked in successfully!');
      setClockInModal(false);
      loadTodayAttendance();
      if (onUpdate) onUpdate();
    } catch (error) {
      console.error('Error clocking in:', error);
      message.error('Failed to clock in');
    } finally {
      setLoading(false);
    }
  };

  const handleClockOut = async (values) => {
    try {
      setLoading(true);
      const attendanceDb = pouchDatabase('attendance', databasePrefix);
      
      const clockInTime = moment(todayAttendance.clock_in, 'HH:mm');
      const clockOutTime = values.clock_out;
      const totalHours = clockOutTime.diff(clockInTime, 'hours', true);
      const overtimeHours = Math.max(0, totalHours - 8); // Assuming 8-hour workday

      const updatedRecord = {
        ...todayAttendance,
        clock_out: values.clock_out.format('HH:mm'),
        total_hours: totalHours,
        overtime_hours: overtimeHours,
        updated_at: new Date().toISOString(),
        updated_by: currentUser._id,
      };

      await attendanceDb.saveDocument(updatedRecord, currentUser);
      message.success('Clocked out successfully!');
      setClockOutModal(false);
      loadTodayAttendance();
      if (onUpdate) onUpdate();
    } catch (error) {
      console.error('Error clocking out:', error);
      message.error('Failed to clock out');
    } finally {
      setLoading(false);
    }
  };

  const getClientIP = async () => {
    try {
      const response = await fetch('https://api.ipify.org?format=json');
      const data = await response.json();
      return data.ip;
    } catch (error) {
      return 'Unknown';
    }
  };

  const canClockIn = !todayAttendance || !todayAttendance.clock_in;
  const canClockOut = todayAttendance && todayAttendance.clock_in && !todayAttendance.clock_out;

  return (
    <Card title="Quick Clock In/Out" size="small">
      <Space direction="vertical" style={{ width: '100%' }}>
        {todayAttendance && (
          <div>
            <Text type="secondary">Today's Status: </Text>
            <Tag color={getStatusColor(todayAttendance.status)}>
              {todayAttendance.status?.replace('_', ' ').toUpperCase()}
            </Tag>
          </div>
        )}
        
        <Space>
          <Button
            type="primary"
            icon={<PlayCircleOutlined />}
            onClick={() => setClockInModal(true)}
            disabled={!canClockIn}
            loading={loading}
          >
            Clock In
          </Button>
          <Button
            type="default"
            icon={<PauseCircleOutlined />}
            onClick={() => setClockOutModal(true)}
            disabled={!canClockOut}
            loading={loading}
          >
            Clock Out
          </Button>
        </Space>

        {todayAttendance && (
          <div>
            {todayAttendance.clock_in && (
              <div>
                <Text type="secondary">Clock In: </Text>
                <Text strong>{todayAttendance.clock_in}</Text>
              </div>
            )}
            {todayAttendance.clock_out && (
              <div>
                <Text type="secondary">Clock Out: </Text>
                <Text strong>{todayAttendance.clock_out}</Text>
              </div>
            )}
            {todayAttendance.total_hours && (
              <div>
                <Text type="secondary">Total Hours: </Text>
                <Text strong>{todayAttendance.total_hours.toFixed(2)}</Text>
              </div>
            )}
          </div>
        )}
      </Space>

      <Modal
        title="Clock In"
        visible={clockInModal}
        onCancel={() => setClockInModal(false)}
        footer={null}
      >
        <Form form={form} onFinish={handleClockIn} layout="vertical">
          <Form.Item
            name="clock_in"
            label="Clock In Time"
            rules={[{ required: true, message: 'Please select clock in time' }]}
            initialValue={moment()}
          >
            <TimePicker format="HH:mm" style={{ width: '100%' }} />
          </Form.Item>
          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit" loading={loading}>
                Clock In
              </Button>
              <Button onClick={() => setClockInModal(false)}>
                Cancel
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      <Modal
        title="Clock Out"
        visible={clockOutModal}
        onCancel={() => setClockOutModal(false)}
        footer={null}
      >
        <Form form={form} onFinish={handleClockOut} layout="vertical">
          <Form.Item
            name="clock_out"
            label="Clock Out Time"
            rules={[{ required: true, message: 'Please select clock out time' }]}
            initialValue={moment()}
          >
            <TimePicker format="HH:mm" style={{ width: '100%' }} />
          </Form.Item>
          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit" loading={loading}>
                Clock Out
              </Button>
              <Button onClick={() => setClockOutModal(false)}>
                Cancel
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </Card>
  );
};

const attendance = {
  CustomView: ({ data }) => {
    const formatTime = (time) => {
      if (!time) return 'Not recorded';
      return moment(time, 'HH:mm').format('h:mm A');
    };

    const calculateWorkingHours = () => {
      if (!data.clock_in || !data.clock_out) return 'Incomplete';
      
      const clockIn = moment(data.clock_in, 'HH:mm');
      const clockOut = moment(data.clock_out, 'HH:mm');
      const duration = moment.duration(clockOut.diff(clockIn));
      
      return `${Math.floor(duration.asHours())}h ${duration.minutes()}m`;
    };

    return (
      <div style={{ padding: '24px' }}>
        <Card>
          {/* Header Section */}
          <div style={{ textAlign: 'center', marginBottom: '32px' }}>
            <ClockCircleOutlined style={{ fontSize: '64px', color: '#1890ff', marginBottom: '16px' }} />
            <Title level={2} style={{ margin: '8px 0' }}>
              Attendance Record
            </Title>
            <Space size="middle">
              <Tag color={getStatusColor(data.status)} style={{ fontSize: '14px', padding: '4px 12px' }}>
                {data.status?.replace('_', ' ').toUpperCase()}
              </Tag>
              <Tag style={{ fontSize: '14px', padding: '4px 12px' }}>
                {moment(data.date).format('MMMM DD, YYYY')}
              </Tag>
            </Space>
          </div>

          <Divider />

          {/* Attendance Information */}
          <Title level={4} style={{ marginBottom: '16px' }}>Attendance Details</Title>
          <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
            <InfoItem
              icon={<UserOutlined />}
              label="Employee"
              value={data.employee?.label || 'Unknown Employee'}
              span={12}
            />
            <InfoItem
              icon={<CalendarOutlined />}
              label="Date"
              value={moment(data.date).format('dddd, MMMM DD, YYYY')}
              span={12}
            />
            <InfoItem
              icon={<PlayCircleOutlined />}
              label="Clock In"
              value={formatTime(data.clock_in)}
              span={12}
            />
            <InfoItem
              icon={<PauseCircleOutlined />}
              label="Clock Out"
              value={formatTime(data.clock_out)}
              span={12}
            />
            <InfoItem
              icon={<ClockCircleOutlined />}
              label="Total Hours"
              value={data.total_hours ? `${data.total_hours.toFixed(2)} hours` : calculateWorkingHours()}
              span={12}
            />
            <InfoItem
              icon={<ExclamationCircleOutlined />}
              label="Overtime Hours"
              value={data.overtime_hours ? `${data.overtime_hours.toFixed(2)} hours` : '0 hours'}
              span={12}
            />
          </Row>

          <Divider />

          {/* Additional Information */}
          <Title level={4} style={{ marginBottom: '16px' }}>Additional Information</Title>
          <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
            <InfoItem
              icon={<ClockCircleOutlined />}
              label="Break Start"
              value={formatTime(data.break_start)}
              span={12}
            />
            <InfoItem
              icon={<ClockCircleOutlined />}
              label="Break End"
              value={formatTime(data.break_end)}
              span={12}
            />
            <InfoItem
              icon={<UserOutlined />}
              label="Location"
              value={data.location || 'Not specified'}
              span={12}
            />
            <InfoItem
              icon={<UserOutlined />}
              label="IP Address"
              value={data.ip_address || 'Not recorded'}
              span={12}
            />
            <InfoItem
              icon={<UserOutlined />}
              label="Approved By"
              value={data.approved_by?.label || 'Not approved'}
              span={12}
            />
            <InfoItem
              icon={<UserOutlined />}
              label="Branch"
              value={data.branch?.label || 'Not specified'}
              span={12}
            />
          </Row>

          {data.notes && (
            <>
              <Divider />
              <Title level={4} style={{ marginBottom: '16px' }}>Notes</Title>
              <Card size="small" style={{ backgroundColor: '#fafafa' }}>
                <Text>{data.notes}</Text>
              </Card>
            </>
          )}
        </Card>
      </div>
    );
  },

  // Add clock in/out functionality to the main attendance module
  ExtraComponents: {
    ClockInOut: AttendanceClockInOut,
  },

  // Custom toolbar buttons
  toolbarButtons: [
    {
      type: "primary",
      icon: "ClockCircleOutlined",
      text: "Quick Clock In/Out",
      component: "ClockInOut"
    }
  ],
};

export default attendance;
