import React from "react";
import dayjs from "dayjs";
import { formatNumber  } from "../../../Utils/functions";


const depreciation_entries = {
    CustomView: (data) => {
        const entry = data.data;
        
        return (
            <div>
                <h2>Depreciation Entry</h2>
                <p><strong>Asset:</strong> {entry.asset?.label || 'Unknown Asset'}</p>
                <p><strong>Date:</strong> {dayjs(entry.date).format('DD/MM/YYYY')}</p>
                <p><strong>Period:</strong> {dayjs(entry.period_start).format('DD/MM/YYYY')} to {dayjs(entry.period_end).format('DD/MM/YYYY')}</p>
                <p><strong>Amount:</strong> {formatNumber(entry.amount)}</p>
                
                {entry.book_value_before && (
                    <p><strong>Book Value Before:</strong> {formatNumber(entry.book_value_before)}</p>
                )}
                
                {entry.book_value_after && (
                    <p><strong>Book Value After:</strong> {formatNumber(entry.book_value_after)}</p>
                )}
                
                <p><strong>Status:</strong> {entry.status}</p>
                
                {entry.notes && (
                    <div style={{ marginTop: 20 }}>
                        <h3>Notes</h3>
                        <p>{entry.notes}</p>
                    </div>
                )}
            </div>
        );
    },
    buffData: async (results, { pouchDatabase, databasePrefix }) => {
        // Get all fixed assets
        const fixedAssets = await pouchDatabase("fixed_assets", databasePrefix).getAllData();
        
        return results.map(entry => {
            // Find the associated asset
            const assetId = entry.asset?.value || entry.asset?._id;
            const asset = fixedAssets.find(a => a._id === assetId);
            
            return {
                ...entry,
                asset_name: asset?.name || 'Unknown Asset',
                asset_code: asset?.code || '',
            };
        });
    }
};

export default depreciation_entries;
