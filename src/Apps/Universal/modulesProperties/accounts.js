import Account from "../CustomViews/Account";
import AppDatabase from "../../../Utils/AppDatabase";
import React, { useRef } from "react";
import { BetaSchemaForm, TableDropdown  } from "@ant-design/pro-components";
import { ReconciliationOutlined  } from "@ant-design/icons";
import { message  } from "antd";


const accounts = {
    CustomView: (data) => <Account {...data} />,
    MoreActions: (props) => {
        const action = useRef();
        const {
            pouchDatabase,
            databasePrefix,
            record,
            CRUD_USER
        } = props;

        return (
            <TableDropdown
                key="actionGroup"
                menus={[
                    {
                        key: "reconcile_account",
                        name: (
                            <BetaSchemaForm
                                formRef={action}
                                submitter={{
                                    searchConfig: {
                                        resetText: "Cancel",
                                        submitText: "Save",
                                    },
                                }}
                                modalProps={{ centered: true }}
                                grid={true}
                                trigger={
                                    <a key="button" type="primary">
                                        <ReconciliationOutlined /> Reconcile Account
                                    </a>
                                }
                                title="Reconcile Account"
                                destroyOnClose={true}
                                layoutType="ModalForm"
                                onFinish={async (values) => {
                                    try {
                                        await pouchDatabase('account_reconciliations', databasePrefix).saveDocument({
                                            ...values,
                                            account: {
                                                value: record._id,
                                                label: record.name,
                                            },
                                            status: values.status || 'completed',
                                        }, CRUD_USER);
                                        message.success(`Account reconciliation created successfully`);
                                        return true;
                                    } catch (error) {
                                        message.error(`Failed to create reconciliation: ${error.message}`);
                                        return false;
                                    }
                                }}
                                columns={[
                                    {
                                        title: "Date",
                                        dataIndex: "date",
                                        valueType: "date",
                                        isRequired: true,
                                        initialValue: new Date(),
                                    },
                                    {
                                        title: "Amount",
                                        dataIndex: "amount",
                                        valueType: "money",
                                        isRequired: true,
                                        tooltip: "Can be positive (credit) or negative (debit)"
                                    },
                                    {
                                        title: "Reason",
                                        dataIndex: "reason",
                                        valueType: "textarea",
                                        isRequired: true,
                                        colProps: {
                                            span: 24,
                                        },
                                    },
                                    {
                                        title: "Status",
                                        dataIndex: "status",
                                        valueType: "select",
                                        valueEnum: {
                                            completed: { text: "Completed", status: "Success" },
                                            pending: { text: "Pending", status: "Warning" },
                                            cancelled: { text: "Cancelled", status: "Error" }
                                        },
                                        initialValue: "completed",
                                    }
                                ]}
                            />
                        ),
                    },
                ]}
            />
        );
    },
    buffResults: async (results, pouchDatabase, databasePrefix) => {
        const receiptsDB = await AppDatabase("receipts", databasePrefix);
        const expensesDB = await AppDatabase("expenses", databasePrefix);
        const transfersDB = await AppDatabase("account_transfers", databasePrefix);
        const reconciliationsDB = await AppDatabase("account_reconciliations", databasePrefix);

        const [receipts, expenses, transfers, reconciliations] = await Promise.all([
            await receiptsDB.getAllData(),
            await expensesDB.getAllData(),
            await transfersDB.getAllData(),
            await reconciliationsDB.getAllData()
        ]);

        return results.map(account => {
            // Initialize balance with opening balance if it exists
            let balance = Number(account.opening_balance) || 0; // <--- Change is here

            // Add receipts
            receipts
                .filter(receipt => receipt.account?.value === account._id)
                .forEach(receipt => {
                    balance += Number(receipt.amount) || 0;
                });

            // Subtract expenses
            expenses
                .filter(expense => expense.account?.value === account._id)
                .forEach(expense => {
                    balance -= Number(expense.amount) || 0;
                });

            // Handle transfers
            transfers
                .filter(transfer => transfer.status === 'completed')
                .forEach(transfer => {
                    // If this account is the source, subtract amount + charges
                    if (transfer.from_account.value === account._id) {
                        balance -= Number(transfer.amount) || 0;
                        balance -= Number(transfer.charges) || 0;
                    }
                    // If this account is the destination, add amount
                    if (transfer.to_account.value === account._id) {
                        balance += Number(transfer.amount) || 0;
                    }
                });

            // Handle reconciliations
            reconciliations
                .filter(recon => {
                    return recon.status === 'completed' &&
                        ((recon.account && recon.account.value === account._id) ||
                            (recon.account && recon.account._id === account._id));
                })
                .forEach(recon => {
                    balance += Number(recon.amount) || 0;
                });

            return {
                ...account,
                balance: balance,
                transaction_count:
                    receipts.filter(r => r.account?.value === account._id).length +
                    expenses.filter(e => e.account?.value === account._id).length +
                    transfers.filter(t => t.from_account?.value === account._id || t.to_account?.value === account._id).length +
                    reconciliations.filter(r => r.status === 'completed' &&
                        ((r.account && r.account.value === account._id) ||
                            (r.account && r.account._id === account._id))).length
            };
        });
    }
};

export default accounts;
