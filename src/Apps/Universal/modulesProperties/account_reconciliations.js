import AccountReconciliation from "../CustomViews/AccountReconciliation";
import React from "react";


const account_reconciliations = {
    CustomView: (data) => {
        // Ensure account is properly formatted for display
        if (data && data.data && data.data.account) {
            // Handle case where account might be an object with key, label, value properties
            if (typeof data.data.account === 'object' && data.data.account.value) {
                // Make sure we're not passing an object where a string is expected
                
            }
        }
        return <AccountReconciliation {...data} />;
    },
    buffResults: async (results) => {
        // No additional processing needed for reconciliations in the list view
        return results;
    }
};

export default account_reconciliations;
