import React, { useRef } from "react";
import moment from "moment";
import { Button, message, Card, Avatar, Typography, Divider, Tag, Space, Row, Col, Tooltip  } from "antd";
import { TableDropdown  } from "@ant-design/pro-table";
import { UserOutlined, ClockCircleOutlined, TeamOutlined, BranchesOutlined, KeyOutlined, PhoneOutlined, MobileOutlined, MailOutlined, IdcardOutlined, CalendarOutlined, GlobalOutlined, HomeOutlined, InfoCircleOutlined  } from "@ant-design/icons";
import { encryptPassword  } from "../../../Utils/functions";


const { Text, Title } = Typography;

const userPermissions = JSON.parse(
  localStorage.getItem("APP_USER_PERMISSIONS")
);

const InfoItem = ({ icon, label, value, span = 8 }) => {
  if (!value) return null;

  return (
    <Col span={span}>
      <div style={{ marginBottom: '16px' }}>
        <Text type="secondary" style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          {icon} {label}
        </Text>
        <Text strong style={{ display: 'block', marginTop: '4px' }}>
          {value}
        </Text>
      </div>
    </Col>
  );
};

const users = {
  CustomView: ({ data }) => {
    const getRoleColor = (role) => {
      const roleColors = {
        admin: '#f50',
        superuser: '#108ee9',
        manager: '#87d068',
        supervisor: '#722ed1',
        user: '#2db7f5',
        default: '#888888'
      };
      return roleColors[role?.toLowerCase()] || roleColors.default;
    };

    const getInitials = (firstName, lastName) => {
      const first = firstName?.[0] || '';
      const last = lastName?.[0] || '';
      return (first + last).toUpperCase() || '?';
    };

    const getFullName = () => {
      const { first_name, last_name, title } = data;
      const parts = [];
      if (title) parts.push(title);
      if (first_name) parts.push(first_name);
      if (last_name) parts.push(last_name);
      return parts.join(' ') || data.name || data.username || 'Unknown User';
    };

    return (
      <Card
        style={{
          background: 'linear-gradient(145deg, #ffffff 0%, #f6f6f6 100%)',
          borderRadius: '12px',
          boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
        }}
      >
        <Row gutter={[24, 0]}>
          <Col xs={24} sm={8} md={6}>
            <div style={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              gap: '16px'
            }}>
              <Avatar
                size={96}
                style={{
                  backgroundColor: '#1890ff',
                  fontSize: '36px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
                }}
              >
                {data.avatar ? (
                  <img src={data.avatar} alt="user" />
                ) : (
                  getInitials(data.first_name, data.last_name)
                )}
              </Avatar>

              <div style={{ textAlign: 'center' }}>
                <Title level={4} style={{ margin: 0, marginBottom: '8px' }}>
                  {getFullName()}
                </Title>
                <Space size={4} wrap style={{ justifyContent: 'center' }}>
                  <Tooltip title="Role">
                    <Tag color={getRoleColor(data.role?.label)} icon={<KeyOutlined />}>
                      {data.role?.label || 'User'}
                    </Tag>
                  </Tooltip>
                  {data.branch?.label && (
                    <Tooltip title="Branch">
                      <Tag color="blue" icon={<BranchesOutlined />}>
                        {data.branch.label}
                      </Tag>
                    </Tooltip>
                  )}
                  {data.status && (
                    <Tooltip title="Status">
                      <Tag color={data.status === 'active' ? 'green' : 'red'}>
                        {data.status}
                      </Tag>
                    </Tooltip>
                  )}
                </Space>
              </div>
            </div>
          </Col>

          <Col xs={24} sm={16} md={18}>
            <Divider orientation="left">Contact Information</Divider>
            <Row gutter={[16, 0]}>
              <InfoItem
                icon={<MailOutlined />}
                label="Email"
                value={data.email}
              />
              <InfoItem
                icon={<PhoneOutlined />}
                label="Phone"
                value={data.phone}
              />
              <InfoItem
                icon={<MobileOutlined />}
                label="Mobile"
                value={data.mobile}
              />
            </Row>

            <Divider orientation="left">Identity Details</Divider>
            <Row gutter={[16, 0]}>
              <InfoItem
                icon={<IdcardOutlined />}
                label="Username"
                value={data.username}
              />
              <InfoItem
                icon={<InfoCircleOutlined />}
                label="User ID"
                value={data._id}
              />
              <InfoItem
                icon={<TeamOutlined />}
                label="Department"
                value={data.department?.label}
              />
            </Row>

            <Divider orientation="left">System Information</Divider>
            <Row gutter={[16, 0]}>
              <InfoItem
                icon={<CalendarOutlined />}
                label="Created At"
                value={data.createdAt && moment(data.createdAt).format('DD MMM YYYY, HH:mm')}
              />
              <InfoItem
                icon={<ClockCircleOutlined />}
                label="Last Updated"
                value={data.updatedAt && moment(data.updatedAt).format('DD MMM YYYY, HH:mm')}
              />
              {data.entrant && (
                <InfoItem
                  icon={<UserOutlined />}
                  label="Created By"
                  value={data.entrant.label}
                />
              )}
            </Row>

            {(data.address || data.location) && (
              <>
                <Divider orientation="left">Location Details</Divider>
                <Row gutter={[16, 0]}>
                  {data.address && (
                    <InfoItem
                      icon={<HomeOutlined />}
                      label="Address"
                      value={data.address}
                      span={12}
                    />
                  )}
                  {data.location && (
                    <InfoItem
                      icon={<GlobalOutlined />}
                      label="Location"
                      value={data.location}
                      span={12}
                    />
                  )}
                </Row>
              </>
            )}
          </Col>
        </Row>
      </Card>
    );
  },
  MoreActions:
    userPermissions && userPermissions.root
      ? (props) => {
        const action = useRef();
        const {
          pouchDatabase,
          collection,
          databasePrefix,
          record,
          CRUD_USER,
          singular,
          modules,
        } = props;

        return (
          <TableDropdown
            key="actionGroup"
            //   onSelect={(key) => }
            menus={[
              {
                key: "resetPassword",
                name: "Reset Password",
                onClick: () => {
                  alert("Reset Password");
                  pouchDatabase(modules.users.collection, databasePrefix)
                    .saveDocument(
                      {
                        ...record,
                        password: encryptPassword("123456789"),
                      },
                      CRUD_USER
                    )
                    .then(() => {
                      message.success("Password Reset Successful");
                    });
                  return true;
                },
              },
            ]}
          />
        );
      }
      : null,
};

export default users;
