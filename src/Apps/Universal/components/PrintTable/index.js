import DocumentHead from "../../customViews/Components/DocumentHead";
import PrintComponents from "react-print-components";
import { Button } from "antd";
import { PrinterOutlined } from "@ant-design/icons";
import { ProTable } from "@ant-design/pro-components";


const PrintTable = ({ columns, data, Trigger }) => {


  return (
    <PrintComponents
      trigger={
        Trigger ? (
          <Trigger />
        ) : (
          <Button
            icon={<PrinterOutlined />}
            type="primary"
            style={{ marginBottom: 16 }}
          >
            Print
          </Button>
        )
      }
    >
      <div style={{ margin: 20 }}>
        <DocumentHead />
        <ProTable
          columns={columns}
          dataSource={data}
          size="small"
          pagination={false}
          className="custom-table"
          searchFormRender={() => null}
          toolBarRender={false}
          tableAlertRender={false}
        />
        {/* <Typography.Title level={3}>Daily Collection Report</Typography.Title>
        <Table
          size="small"
          columns={columns}
          dataSource={data}
          pagination={false}
          className="custom-table"
        /> */}
      </div>
    </PrintComponents>
  );
};

export default PrintTable;