import "./ThermalPaper.css";
import React from "react";
import converter from "number-to-words";
import { Image } from "antd";
import { LOCAL_STORAGE_ORGANIZATION } from "../../../../../Utils/constants";


const ThermalPaper = (props) => {
  const company = JSON.parse(localStorage.getItem(LOCAL_STORAGE_ORGANIZATION));

  return (
    <div id="invoice" class="row">
      <div class="col-xs-12 col-md-12">
        <section class="receipt-template">
          <header class="receipt-header">
            <Image width={200} src={props.companyLogo} />
            <h2 class="store-name">{company.name}</h2>
            <div class="address-area">
              <span class="info address">{company.address}</span>
              <div class="block">
                <span class="info phone">Mobile: {company.phone}</span>,{" "}
                <span class="info email">Email: {company.email}</span>
                <br />
                <br />
              </div>
            </div>
          </header>

          <section class="info-area">
            <table>
              <tbody>
                <tr>
                  <td class="w-30">
                    <span>
                      <strong>Invoice ID : </strong>
                      {props.data._id}
                    </span>
                  </td>
                </tr>
                <tr>
                  <td class="w-30">
                    <span>
                      <strong>Issue Date : </strong>
                      {props.data.date}
                    </span>
                  </td>
                </tr>
                <tr>
                  <td class="w-30">
                    <strong>Client : </strong>
                    {props.data.project.label.split("_")[0]}
                  </td>
                </tr>
              </tbody>
            </table>
          </section>

          <h4 class="main-title">
            {props.data.tTax && props.data.tTax > 0 ? "TAX INVOICE" : "INVOICE"}
          </h4>

          <section class="listing-area item-list">
            <table>
              <thead>
                <tr>
                  <td class="w-50 text-left">Item Description</td>
                  <td class="w-25 text-right">Qty</td>
                  <td class="w-25 text-center">Unit</td>
                  <td class="w-30 text-right">Amount</td>
                </tr>
              </thead>
              <tbody>
                {props.data.items &&
                  props.data.items.map((n) => (
                    <tr>
                      <td>{n.item}</td>
                      <td class="text-center">{Number(n.quantity)}</td>
                      <td class="text-center">
                        {Number(n.cost).toLocaleString()}
                      </td>
                      <td class="text-right">
                        {(n.quantity * n.cost).toLocaleString()}
                      </td>
                    </tr>
                  ))}
              </tbody>
            </table>
          </section>

          <section class="info-area calculation-area">
            <table>
              <tbody>
                <tr>
                  <td class="w-70">Total Amt:</td>
                  <td>
                    {props.data.items
                      .reduce((sum, item) => sum + item.quantity * item.cost, 0)
                      .toLocaleString()}
                  </td>
                </tr>
                <tr>
                  <td class="w-70">Tax:</td>
                  <td>
                    {props.data.items
                      .reduce(
                        (sum, item) =>
                          sum +
                          (item.tax
                            ? (item.tax / 100) * (item.quantity * item.cost)
                            : 0),
                        0
                      )
                      .toLocaleString()}
                  </td>
                </tr>
                <tr>
                  <td style={{ fontSize: "20px" }} class="w-50">
                    Total:
                  </td>
                  <td style={{ fontSize: "20px" }} class="w-50">
                    UGX{" "}
                    {props.data.items
                      .reduce(
                        (sum, item) =>
                          sum +
                          ((item.tax
                            ? (item.tax / 100) * (item.quantity * item.cost)
                            : 0) +
                            item.quantity * item.cost),
                        0
                      )
                      .toLocaleString()}
                  </td>
                </tr>
              </tbody>
            </table>
          </section>

          <section class="info-area italic">
            <span class="">
              <br />
              <b>In Text:</b>{" "}
              {converter.toWords(
                props.data.items.reduce(
                  (sum, item) =>
                    sum +
                    ((item.tax
                      ? (item.tax / 100) * (item.quantity * item.cost)
                      : 0) +
                      item.quantity * item.cost),
                  0
                )
              )}{" "}
              shillings
              <br />
            </span>
            <br />
          </section>
          <section class="info-area align-center footer-area">
            <span class="block bold">Thank you for choosing us!</span>
            <p class="powered-by">
              <small>© Hytech Uganda Limited</small>
            </p>
          </section>
        </section>
      </div>
    </div>
  );
};

export default ThermalPaper;