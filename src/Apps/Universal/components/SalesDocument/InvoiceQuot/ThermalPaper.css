.receipt-template {
	width: 302px;
	margin: 0 auto;
}
.receipt-template .text-small {
	font-size: 10px;
}
.receipt-template .block {
	display: block;
}
.receipt-template .inline-block {
	display: inline-block;
}
.receipt-template .bold {
	font-weight: 700;
}
.receipt-template .italic {
	font-style: italic;
}
.receipt-template .align-right {
	text-align: right;
}
.receipt-template .align-center {
	text-align: center;
}
.receipt-template .main-title {
	font-size: 14px;
	font-weight: 700;
	text-align: center;
	margin: 10px 0 5px 0;
	padding: 0;
}
.receipt-template .heading {
	position: relation;
}
.receipt-template .title {
	font-size: 16px;
	font-weight: 700;
	margin: 10px 0 5px 0;
}
.receipt-template .sub-title {
	font-size: 12px;
	font-weight: 700;
	margin: 10px 0 5px 0;
}
.receipt-template table {
	width: 100%;
}
.receipt-template td,
.receipt-template th {
	font-size: 12px;
}
.receipt-template .info-area {
	font-size: 12px;
	line-height: 1.222;
}
.receipt-template .listing-area {
	line-height: 1.222;
}
.receipt-template .listing-area table {
}
.receipt-template .listing-area table thead tr {
	border-top: 1px solid rgba(0, 0, 0, 0.06);
	border-bottom: 1px solid rgba(0, 0, 0, 0.06);
	font-weight: 700;
}
.receipt-template .listing-area table tbody tr {
	border-top: 1px dashed rgba(0, 0, 0, 0.06);
	border-bottom: 1px dashed rgba(0, 0, 0, 0.06);
}
.receipt-template .listing-area table tbody tr:last-child {
	border-bottom: none;
}
.receipt-template .listing-area table td {
	vertical-align: top;
}
.receipt-template .info-area table {
}
.receipt-template .info-area table thead tr {
	border-top: 1px solid rgba(0, 0, 0, 0.06);
	border-bottom: 1px solid rgba(0, 0, 0, 0.06);
}

/*Receipt Heading*/
.receipt-template .receipt-header {
	text-align: center;
}
.receipt-template .receipt-header .logo-area {
	width: 80px;
	height: 80px;
	margin: 0 auto;
}
.receipt-template .receipt-header .logo-area img.logo {
	display: inline-block;
	max-width: 100%;
	max-height: 100%;
}
.receipt-template .receipt-header .address-area {
	margin-bottom: 5px;
	line-height: 1;
}
.receipt-template .receipt-header .info {
	font-size: 12px;
}
.receipt-template .receipt-header .store-name {
	font-size: 24px;
	font-weight: 700;
	margin: 0;
	padding: 0;
}

/*Invoice Info Area*/
.receipt-template .invoice-info-area {
}

/*Customer Customer Area*/
.receipt-template .customer-area {
	margin-top: 10px;
}

/*Calculation Area*/
.receipt-template .calculation-area {
	border-top: 2px solid rgba(0, 0, 0, 0.06);
	font-weight: bold;
}
.receipt-template .calculation-area table td {
	text-align: right;
}
.receipt-template .calculation-area table td:nth-child(2) {
	border-bottom: 1px dashed rgba(0, 0, 0, 0.1);
}

/*Item Listing*/
.receipt-template .item-list table tr {
}

/*Barcode Area*/
.receipt-template .barcode-area {
	margin-top: 10px;
	text-align: center;
}
.receipt-template .barcode-area img {
	max-width: 100%;
	display: inline-block;
}

/*Footer Area*/
.receipt-template .footer-area {
	line-height: 1.222;
	font-size: 10px;
}
.text-left {
	text-align: left;
}
.text-center {
	text-align: center;
}
.text-right {
	text-align: right;
}

/*Media Query*/
@media print {
	.receipt-template {
		width: 100%;
	}
}
@media all and (max-width: 215px) {
}
