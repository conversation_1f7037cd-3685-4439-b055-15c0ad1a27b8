import React, { useState, useEffect } from "react";
import ThermalPaper from "./ThermalPaper";
import converter from "number-to-words";
import { Col, Divider, Image, Row, Table } from "antd";
import { LOCAL_STORAGE_ORGANIZATION } from "../../../../../Utils/constants";


const InvoiceQuot = (props) => {
  const {
    data,
    columns,
    singular,
    documentSize,
    pouchDatabase,
    databasePrefix,
  } = props;

  const organization = JSON.parse(
    localStorage.getItem(LOCAL_STORAGE_ORGANIZATION)
  );
  const [receiptTotal, setReceiptTotal] = useState(0);
  const [taxTotal, setTaxTotal] = useState(0);
  const [companyLogo, setCompanyLogo] = useState("");

  // organization.logo && blobToBase64(organization.logo.file).then(l => setCompanyLogo(l))

  useEffect(() => {
    pouchDatabase("organizations", databasePrefix)
      .getAttachment(organization._id, "logo")
      .then((res) => setCompanyLogo(res));
  }, [databasePrefix, organization._id, pouchDatabase]);

  // URL.createObjectURL(response)

  let padding = 20;
  let dataSource = data.items;

  let tableColumns = [];

  columns.map((column) => {
    if (column.dataIndex === "items") {
      tableColumns = [
        {
          title: "S/N",
          dataIndex: "id",
          render: (text, row, index) => dataSource.indexOf(row) + 1,
          width: "10px",
        },
        ...column.columns[0].columns.map((c) => c.valueType !== "group" && c),
      ];
    }
    return false;
  });

  tableColumns.pop();
  tableColumns.push({
    title: "Total",
    dataIndex: "amount",
    render: (x, row) =>
      "UGX " +
      (row.cost * row.quantity +
        (row.tax ? (row.tax / 100) * (row.cost * row.quantity) : 0)),
  });

  const project = data.job.label.split(" - ");

  return documentSize === "a4" ? (
    <div style={{ padding: padding }}>
      {/* <Row>
        <Col>
          <Divider>sss</Divider>
        </Col>
      </Row> */}

      <Row gutter={24} style={{ marginTop: 32 }}>
        <Col span={8} style={{ textAlign: "left", fontSize: "15px" }}>
          <h3>{organization.name}</h3>

          <div>{organization.address}</div>
          <div>
            <strong>Phone no: </strong>
            {organization.phone} {documentSize}
          </div>
          <div>
            <strong>email: </strong>
            {organization.email}
          </div>
          <div>
            <strong>website: </strong>
            {organization.site}
          </div>
        </Col>
        <Col span={16} style={{ textAlign: "right" }}>
          <Image width={300} src={companyLogo} />
        </Col>
      </Row>

      <Divider align="center" style={{ fontSize: "30px" }}>
        {`${
          singular === "Invoice" && taxTotal > 0 ? "TAX" : ""
        } ${singular.toUpperCase()}`}
      </Divider>

      <Row justify="space-between" style={{ marginTop: 32 }}>
        <Col>
          <div>Bill To:</div>
          <strong>{project[0]}</strong>
          <div>{project[1]}</div>
          <div>{project[2]}</div>
        </Col>
        <Col>
          <div>
            <strong>Invoice # :</strong> {data._id}
          </div>
          <div>
            <strong>Invoice Date :</strong> 22-22-2222
          </div>
        </Col>
      </Row>

      <Row gutter={24} style={{ marginTop: 10 }}>
        <Table
          bordered
          size="small"
          className="invoiceTable"
          style={{ width: "100%" }}
          dataSource={dataSource}
          columns={tableColumns}
          pagination={false}
          summary={(pageData) => {
            let totalQuantity = 0;
            let totalAmount = 0;

            let itemCost = 0,
              itemTax = 0;

            pageData.forEach(({ quantity, cost, tax = 0 }) => {
              totalQuantity += quantity;
              totalAmount += cost * quantity + (tax / 100) * (cost * quantity);

              itemCost += cost * quantity;
              itemTax += (tax / 100) * (cost * quantity);
            });

            setReceiptTotal(itemCost);
            setTaxTotal(itemTax);
            return (
              <Table.Summary.Row>
                <Table.Summary.Cell></Table.Summary.Cell>
                <Table.Summary.Cell>
                  <strong>Total</strong>
                </Table.Summary.Cell>
                <Table.Summary.Cell>
                  <strong>{totalQuantity}</strong>
                </Table.Summary.Cell>
                <Table.Summary.Cell></Table.Summary.Cell>
                <Table.Summary.Cell></Table.Summary.Cell>
                <Table.Summary.Cell>
                  <strong>UGX {totalAmount}</strong>
                </Table.Summary.Cell>
              </Table.Summary.Row>
            );
          }}
        ></Table>
      </Row>
      <Row justify="space-between" style={{ marginTop: 10 }}>
        <Col>
          <strong>{singular.toUpperCase()} AMOUNT IN WORDS</strong>
          <div>{converter.toWords(receiptTotal + taxTotal)} shillings only</div>
          <br />
        </Col>
        <Col>
          <table>
            <tr>
              <th>Sub Total :</th>
              <td>UGX {receiptTotal}</td>
            </tr>
            <tr>
              <th>Taxes :</th>
              <td>UGX {taxTotal}</td>
            </tr>
            <tr>
              <th>Discount :</th>
              <td>UGX 0</td>
            </tr>
            <tr style={{ fontSize: "25px" }}>
              <th>Total :</th>
              <td>UGX {receiptTotal + taxTotal}</td>
            </tr>
          </table>
        </Col>
      </Row>
      <Row
        justify="space-between"
        style={{
          marginTop: 15,
          paddingTop: 15,
          borderTop: "1px solid rgba(0, 0, 0, 0.06)",
        }}
      >
        <Col>
          <strong>TERMS AND CONDITIONS</strong>
          <div>non returnable</div>
        </Col>
        <Col>
          <strong>PAYMENT OPTIONS</strong>
          <div>non returnable</div>
        </Col>
      </Row>
      <Divider align="center" style={{ color: "#1890ff" }}></Divider>
      <Row justify="space-between">
        <Col style={{ textAlign: "center" }}>
          <div>for, Client. </div>
          <div>
            <br />
            ................................................
            <br />
          </div>
          <strong>Client's Signature</strong>
        </Col>
        <Col style={{ textAlign: "center" }}>
          <div>for, {organization.name}. </div>
          <div>
            <br />
            ................................................
            <br />
          </div>
          <strong>Authorized Signatory</strong>
        </Col>
      </Row>
    </div>
  ) : documentSize === "thermal" ? (
    <ThermalPaper {...props} companyLogo={companyLogo} />
  ) : (
    <></>
  );
};

export default InvoiceQuot;