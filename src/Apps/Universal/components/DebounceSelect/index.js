import ProForm, { BetaSchemaForm, ProFormSelect } from '@ant-design/pro-form';
import React, { useEffect } from 'react';
import debounce from "lodash.debounce";
import { FileAddOutlined } from "@ant-design/icons";
import { Select, Spin, Drawer, message } from "antd";
import { encryptPassword } from "../../utils";


const DebounceSelect = (props) => {
    const { debounceTimeout = 800, pouchDatabase, databasePrefix, collection, label, value, onChange, singular, columns, CRUD_USER, dataIndex } = props
    const [fetching, setFetching] = React.useState(false);
    const [options, setOptions] = React.useState([]);
    const [visible, setVisible] = React.useState(false);
    const [selectValue, setselectValue] = React.useState(value)
    const fetchRef = React.useRef(0);
    const formRef = React.useRef(1);

    async function fetchUserList(query) {
        return pouchDatabase(collection, databasePrefix).getAllData()
            .then((records) => {
                return records.filter(record => label.some(filterEl => record[filterEl].toLowerCase().includes(query.toLowerCase()))).map((record) => ({
                    label: label.map(item => `${record[item]} `).toString(),
                    value: record._id,
                }))
            }
            )
    }

    useEffect(() => {

    }, [selectValue])

    const itemSelect = (sProps) => {
        const { value } = sProps


        if (value === 'new') {

            showDrawer()
        }
        return null
    }

    const showDrawer = () => {
        setVisible(true);
    };
    const onClose = () => {
        setVisible(false);
    };

    const debounceFetcher = React.useMemo(() => {
        const loadOptions = (value) => {
            fetchRef.current += 1;
            const fetchId = fetchRef.current;
            setOptions([]);
            setFetching(true);
            fetchUserList(value).then((newOptions) => {
                if (fetchId !== fetchRef.current) {
                    // for fetch callback order
                    return;
                }
                let ops = {}
                newOptions.map(item => { ops = { ...ops, [item['value']]: item['label'] } })
                setOptions({ ...ops, new: ` Create new ${singular}` });
                setFetching(false);
            });
        };

        return debounce(loadOptions, debounceTimeout);
    }, [fetchUserList, debounceTimeout]);
    return (
        <ProForm.Item noStyle shouldUpdate>
            {(form) => {
                return (
                    <>
                        <ProFormSelect
                            {...props}
                            labelInValue
                            mode="multiple"
                            filterOption={false}
                            onSearch={debounceFetcher}
                            notFoundContent={fetching ? <Spin size="small" /> : null}
                            // options={options}
                            showSearch
                            // onChange={onChange}
                            onSelect={itemSelect}
                            placeholder={`Select from ${collection}`}
                            style={{
                                width: '100%',
                            }}
                            valueEnum={options}
                        >
                        </ProFormSelect>
                        <BetaSchemaForm
                            submitter={{ searchConfig: { resetText: 'Clear', submitText: 'Save', } }}
                            onVisibleChange={setVisible}
                            open={visible}
                            formRef={formRef}
                            layoutType="DrawerForm"
                            onFinish={async (values) => {
                                if (values.password) { values.password = encryptPassword(values.password) }
                                await pouchDatabase(collection, databasePrefix).saveDocument(values, { key: CRUD_USER._id, label: `${CRUD_USER.first_name} ${CRUD_USER.last_name}`, value: CRUD_USER._id, })
                                    .then(
                                        v => {
                                            setselectValue({ [dataIndex]: { key: v.id, label: label.map(item => values[item] && values[item]), value: v.id } })
                                            // Safely set field value only if form is available
                                            if (form && form.setFieldsValue) {
                                                try {
                                                    form.setFieldsValue({ [dataIndex]: { key: v.id, label: label.map(item => values[item] && values[item]), value: v.id } })
                                                } catch (error) {
                                                    console.warn('Could not set field value in DebounceSelect:', error);
                                                }
                                            }
                                            setVisible(v.id)
                                            message.success(`${singular} added`)
                                        }
                                    )
                                return true;
                            }}
                            columns={columns}
                        />
                    </>
                );
            }}
        </ProForm.Item>
    );
}
export default DebounceSelect;