import { <PERSON><PERSON>, Mo<PERSON> } from "antd";
import { Document, PDFViewer, Page, StyleSheet, Text, View, } from "@react-pdf/renderer";
import { DownloadOutlined } from "@ant-design/icons";
import { Image } from "@react-pdf/renderer";
import { LOCAL_STORAGE_ORGANIZATION } from "../../../../Utils/constants";
import { useEffect, useState } from "react";


const styles = StyleSheet.create({
  page: {
    fontFamily: "Helvetica",
    padding: "30px",
  },
  header: {
    marginBottom: "10px",
    flexDirection: "row",
    alignItems: "center",
  },
  logo: {
    width: "80px",
    marginRight: "20px",
  },
  companyInfo: {
    flexGrow: 1,
  },
  companyName: {
    fontSize: 20,
    fontWeight: "bold",
    marginBottom: "5px",
  },
  companyAddress: {
    fontSize: 12,
    marginBottom: "5px",
  },
  contactInfo: {
    fontSize: 12,
    marginBottom: "5px",
  },
  separator: {
    borderBottom: "3px solid black",
    marginBottom: "10px",
  },
  section: {
    marginBottom: "10px",
  },
  heading: {
    fontSize: 16,
    fontWeight: "bold",
    marginBottom: "10px",
  },
  content: {
    fontSize: 12,
    lineHeight: 1.5,
  },
  table: {
    marginBottom: 20,
    display: "table",
    width: "auto",
    borderStyle: "solid",
    borderWidth: 1,
    borderRightWidth: 0,
    borderBottomWidth: 0,
  },
  tableRow: {
    margin: "auto",
    flexDirection: "row",
  },
  tableCol: {
    width: "17%",
    borderStyle: "solid",
    borderWidth: 1,
    borderLeftWidth: 0,
    borderTopWidth: 0,
  },
  tableCell: {
    margin: "auto",
    marginTop: 5,
    fontSize: 10,
  },
});

const PrintReport = (props) => {
  const { PrintComponent, data } = props;
  const organization = localStorage.getItem(LOCAL_STORAGE_ORGANIZATION);
  const { pouchDatabase, databasePrefix } = data.otherProps;
  const [logo, setLogo] = useState(null);

  

  useEffect(() => {
    pouchDatabase("organizations", databasePrefix)
      .getAttachment(organization._id, "logo")
      .then((img) => setLogo(img));
  }, []);

  const [isModalOpen, setIsModalOpen] = useState(false);

  // const onDocumentLoadSuccess = ({ numPages }) => {
  //   setNumPages(numPages);
  // };
  // const goToPrevPage = () =>
  //   setPageNumber(pageNumber - 1 <= 1 ? 1 : pageNumber - 1);
  // const goToNextPage = () =>
  //   setPageNumber(pageNumber + 1 >= numPages ? numPages : pageNumber + 1);
  // const showModal = () => {
  //   setIsModalOpen(true);
  // };
  // const handleOk = () => {
  //   setIsModalOpen(false);
  // };
  // const handleCancel = () => {
  //   setIsModalOpen(false);
  // };
  return (
    <span>
      <Button
        // onClick={showModal}
        key="1"
        type="primary"
        icon={<DownloadOutlined />}
      >
        Download Report
      </Button>
      <Modal
        title={`Report`}
        width={800}
        centered
        open={isModalOpen}
        // onOk={handleOk}
        // onCancel={handleCancel}
      >
        <div
          style={{
            margin: 0,
            padding: 0,
            width: "100%",
            height: 800,
          }}
        >
          <PDFViewer width={"100%"} height={800}>
            <Document>
              <Page
                style={{
                  fontSize: 10,
                  fontFamily: "Helvetica",
                  padding: "30",
                }}
              >
                <View style={styles.header}>
                  {logo && <Image src={logo} style={styles.logo} />}
                  <View style={styles.companyInfo}>
                    <Text style={styles.companyName}>{organization.name}</Text>
                    <Text style={styles.companyAddress}>
                      {organization.address}
                    </Text>
                    <Text style={styles.contactInfo}>
                      {organization.phone} | {organization.email} |{" "}
                      {organization.website}
                    </Text>
                  </View>
                </View>
                <View style={styles.separator} />
                <PrintComponent data={data} />
              </Page>
            </Document>
          </PDFViewer>
        </div>
      </Modal>
    </span>
  );
};
export default PrintReport;