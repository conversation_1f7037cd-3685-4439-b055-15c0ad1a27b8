import React, { Component } from "react";


module.exports = class PSPDFKit extends Component {
  containerRef = React.createRef();

  // componentDidMount() {
  //   const url = URL.createObjectURL(this.props.blob);
  //   window.PSPDFKit.load({
  //     document: url,
  //     container: this.containerRef.current
  //   });
  // }

  componentWillUnmount() {
    window.PSPDFKit.unload(this.containerRef.current);
  }

  render() {
    return (
      <div
        ref={this.containerRef}
        style={{ width: "95%", height: "90%", position: "absolute" }}
      />
    );
  }
}
