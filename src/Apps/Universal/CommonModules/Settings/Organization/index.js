import React, { useEffect, useState } from "react";
import { BankOutlined, InfoCircleOutlined, CheckCircleOutlined  } from "@ant-design/icons";
import { BetaSchemaForm  } from "@ant-design/pro-form";
import { Divider, message, Card, Typography, Space  } from "antd";
import { LOCAL_STORAGE_USER  } from "../../../../../Utils/constants";
import { buffModules  } from "../../../../../Utils/functions";
import { fetchCurrentOrganization  } from "../../../../../Utils/organizationUtils";
import { modules  } from "../../../../mission-control/modules";


const Organization = (props) => {
  const currentUser = JSON.parse(localStorage.getItem(LOCAL_STORAGE_USER));
  const { pouchDatabase, databasePrefix } = props;
  const organizations = buffModules(
    modules,
    pouchDatabase,
    props.currentUser,
    databasePrefix
  ).organizations;
  const { columns, collection } = organizations;
  const [currentOrganisation, setCurrentOrganisation] = useState(null);
  const [update, setUpdate] = useState(0);
  useEffect(() => {
    // Use utility function for efficient organization fetching
    fetchCurrentOrganization(pouchDatabase, collection, databasePrefix)
      .then((organization) => {
        setCurrentOrganisation(organization);
      })
      .catch((error) => {
        console.error("Error fetching organization:", error);
        setCurrentOrganisation(null);
      });
  }, [update, collection, databasePrefix, pouchDatabase]);

  const layout = {
    labelCol: { span: 4 },
    wrapperCol: { span: 16 },
  };
  const { Title, Text } = Typography;

  return (
    <div className="enhanced-settings-form">
      <div className="settings-section-header">
        <Divider orientation="left">
          <Space>
            <BankOutlined />
            Organization Information
          </Space>
        </Divider>
      </div>

      {currentUser.role.label === "Superuser" && currentOrganisation ? (
        <div className="enhanced-form-container">
          <div className="form-field-group">
            <div className="form-field-group-title">
              <InfoCircleOutlined />
              Organization Details
            </div>
            <Text type="secondary" className="form-help-text">
              <InfoCircleOutlined />
              Update your organization's basic information and contact details.
            </Text>

            <BetaSchemaForm
              {...layout}
              layoutType="ProForm"
              submitter={{
                searchConfig: {
                  resetText: "Reset",
                  submitText: (
                    <Space>
                      <CheckCircleOutlined />
                      Update Organization
                    </Space>
                  )
                },
                render: (props, doms) => {
                  return (
                    <div className="form-actions-container">
                      {doms}
                    </div>
                  );
                },
              }}
              onFinish={async (values) => {
                pouchDatabase(collection, databasePrefix)
                  .saveDocument(
                    { ...currentOrganisation, ...values },
                    {
                      key: currentUser._id,
                      label: `${currentUser.first_name} ${currentUser.last_name}`,
                      value: currentUser._id,
                    }
                  )
                  .then((res) => {
                    message.success({
                      content: `${currentOrganisation.name}'s information has been updated successfully!`,
                      icon: <CheckCircleOutlined style={{ color: '#52c41a' }} />,
                      duration: 4,
                    });
                    setUpdate(update + 1);
                  });
              }}
              initialValues={currentOrganisation}
              columns={columns.filter(
                (r) =>
                  r.dataIndex !== "password" &&
                  r.dataIndex !== "software" &&
                  r.dataIndex !== "userType"
              )}
            />
          </div>
        </div>
      ) : (
        <Card>
          <div style={{ textAlign: 'center', padding: '40px 20px' }}>
            <InfoCircleOutlined style={{ fontSize: '48px', color: '#d9d9d9', marginBottom: '16px' }} />
            <Title level={4} type="secondary">Access Restricted</Title>
            <Text type="secondary">
              Only superusers can modify organization information.
            </Text>
          </div>
        </Card>
      )}
    </div>
  );
};

export default Organization;
