import React, { useState } from "react";
import { Form, Input, Button, Divider, message, Alert, Progress, Space, Typography  } from "antd";
import { checkPasswordComplexity, getSecuritySettings  } from "../../../../../Utils/SecurityUtils";
import { encryptPassword, userLogout  } from "../../../../../Utils/functions";


const layout = {
  labelCol: { span: 4 },
  wrapperCol: { span: 16 },
};
const tailLayout = {
  wrapperCol: { offset: 4, span: 16 },
};

const ChangePassword = ({ pouchDatabase, databasePrefix, currentUser }) => {
  const [form] = Form.useForm();
  const [passwordStrength, setPasswordStrength] = useState(0);
  const [complexityErrors, setComplexityErrors] = useState([]);
  const [passwordValid, setPasswordValid] = useState(true);

  // Get security settings
  const securitySettings = getSecuritySettings();

  // Check password complexity
  const validatePasswordComplexity = (password) => {
    if (!password) {
      setPasswordStrength(0);
      setComplexityErrors([]);
      setPasswordValid(false);
      return false;
    }

    const result = checkPasswordComplexity(password);
    setPasswordStrength(result.strength);
    setComplexityErrors(result.reasons);
    setPasswordValid(result.isValid);
    return result.isValid;
  };

  // Get color based on password strength
  const getStrengthColor = () => {
    if (passwordStrength < 30) return '#ff4d4f';
    if (passwordStrength < 60) return '#faad14';
    return '#52c41a';
  };

  // Get text description of password strength
  const getStrengthText = () => {
    if (passwordStrength < 30) return 'Weak';
    if (passwordStrength < 60) return 'Moderate';
    if (passwordStrength < 80) return 'Good';
    return 'Strong';
  };

  const onFinish = (values) => {
    // Validate password complexity
    if (!validatePasswordComplexity(values.newPassword)) {
      message.error('New password does not meet complexity requirements');
      return;
    }

    pouchDatabase("users", databasePrefix)
      .getDocument(currentUser._id)
      .then((user) => {
        if (user.password === encryptPassword(values.password)) {
          if (values.newPassword === values.confirmNewPassword) {
            if (user.password === encryptPassword(values.confirmNewPassword)) {
              message.error(
                "You are trying to change to a password you are already using"
              );
            } else {
              // Check if we need to store password history
              let passwordHistory = user.passwordHistory || [];

              if (securitySettings.preventPasswordReuse) {
                // Add current password to history
                passwordHistory.unshift(user.password);

                // Trim history to configured size
                if (passwordHistory.length > securitySettings.passwordHistoryCount) {
                  passwordHistory = passwordHistory.slice(0, securitySettings.passwordHistoryCount);
                }

                // Check if new password exists in history
                const newPasswordEncrypted = encryptPassword(values.newPassword);
                if (passwordHistory.includes(newPasswordEncrypted)) {
                  message.error(
                    `You cannot reuse one of your last ${securitySettings.passwordHistoryCount} passwords`
                  );
                  return;
                }
              }

              // Save new password
              pouchDatabase("users", databasePrefix)
                .saveDocument(
                  {
                    ...user,
                    password: encryptPassword(values.newPassword),
                    passwordHistory,
                    passwordLastChanged: new Date().toISOString()
                  },
                  {
                    key: currentUser._id,
                    label: `${currentUser.first_name} ${currentUser.last_name}`,
                    value: currentUser._id,
                  }
                )
                .then((res) => {
                  if (res.ok) {
                    message.success(
                      "Congrats!, You have successfully changed your password"
                    );
                    userLogout(
                      "logs",
                      {
                        key: currentUser._id,
                        label: `${currentUser.first_name} ${currentUser.last_name}`,
                        value: currentUser._id,
                      },
                      databasePrefix,
                      pouchDatabase,
                    );
                  } else {
                    message.error("Failed to update password");
                  }
                });
            }
          } else {
            message.error("New passwords do not match");
          }
        } else {
          message.error("Wrong password");
        }
      });
  };

  const onFinishFailed = (errorInfo) => {
    console.error('Failed:', errorInfo);
  };
  return (
    <div className="enhanced-settings-form">
      <div className="settings-section-header">
        <Divider orientation="left">Change Password</Divider>
      </div>
      <Form
        {...layout}
        form={form}
        name="basic"
        onFinish={onFinish}
        onFinishFailed={onFinishFailed}
      >

      {/* Display password complexity requirements */}
      {securitySettings.requireUppercase ||
        securitySettings.requireLowercase ||
        securitySettings.requireNumbers ||
        securitySettings.requireSpecialChars ||
        securitySettings.minPasswordLength > 0 ? (
        <div className="settings-card-section" style={{ marginBottom: 24 }}>
          <Alert
            type="info"
            message="Password Requirements"
            description={
              <ul style={{ paddingLeft: 20, marginBottom: 0 }}>
                {securitySettings.minPasswordLength > 0 && (
                  <li>At least {securitySettings.minPasswordLength} characters long</li>
                )}
                {securitySettings.requireUppercase && (
                  <li>At least one uppercase letter (A-Z)</li>
                )}
                {securitySettings.requireLowercase && (
                  <li>At least one lowercase letter (a-z)</li>
                )}
                {securitySettings.requireNumbers && (
                  <li>At least one number (0-9)</li>
                )}
                {securitySettings.requireSpecialChars && (
                  <li>At least one special character (!@#$%^&*)</li>
                )}
                {securitySettings.preventPasswordReuse && (
                  <li>Cannot reuse your last {securitySettings.passwordHistoryCount} passwords</li>
                )}
              </ul>
            }
          />
        </div>
      ) : null}

      <Form.Item
        label="Current Password"
        name="password"
        rules={[{ required: true, message: "Please input your current password!" }]}
      >
        <Input.Password />
      </Form.Item>

      <Form.Item
        label="New Password"
        name="newPassword"
        rules={[
          { required: true, message: "Please input your new password!" },
          {
            validator: (_, value) => {
              if (!value || validatePasswordComplexity(value)) {
                return Promise.resolve();
              }
              return Promise.reject(new Error('Password does not meet complexity requirements'));
            }
          }
        ]}
      >
        <Input.Password onChange={(e) => validatePasswordComplexity(e.target.value)} />
      </Form.Item>

      {/* Password strength indicator */}
      {form.getFieldValue('newPassword') && (
        <Form.Item wrapperCol={{ offset: 4, span: 16 }}>
          <div style={{ marginBottom: 16 }}>
            <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 4 }}>
              <Typography.Text type="secondary" style={{ fontSize: 12 }}>
                Password Strength
              </Typography.Text>
              <Typography.Text type="secondary" style={{ fontSize: 12 }}>
                {getStrengthText()}
              </Typography.Text>
            </div>
            <Progress
              percent={passwordStrength}
              showInfo={false}
              strokeColor={getStrengthColor()}
              size="small"
            />
          </div>

          {/* Show complexity errors if any */}
          {complexityErrors.length > 0 && (
            <Alert
              type="warning"
              message="Password does not meet the following requirements:"
              description={
                <ul style={{ paddingLeft: 20, marginBottom: 0 }}>
                  {complexityErrors.map((error, index) => (
                    <li key={index}>{error}</li>
                  ))}
                </ul>
              }
              style={{ marginBottom: 16 }}
            />
          )}
        </Form.Item>
      )}

      <Form.Item
        label="Confirm New Password"
        name="confirmNewPassword"
        dependencies={['newPassword']}
        rules={[
          { required: true, message: "Please confirm your new password!" },
          ({ getFieldValue }) => ({
            validator(_, value) {
              if (!value || getFieldValue('newPassword') === value) {
                return Promise.resolve();
              }
              return Promise.reject(new Error('The two passwords do not match!'));
            },
          }),
        ]}
      >
        <Input.Password />
      </Form.Item>

      <Form.Item {...tailLayout}>
        <Button type="primary" htmlType="submit" disabled={!passwordValid}>
          Change Password
        </Button>
      </Form.Item>
    </Form>
    </div>
  );
};

export default ChangePassword;
