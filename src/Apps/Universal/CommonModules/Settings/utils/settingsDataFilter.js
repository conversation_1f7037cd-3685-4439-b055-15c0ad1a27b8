
/**
 * Utility functions for filtering contaminated data from settings collections
 /**
  * This addresses the issue where data from people, occupations, and other modules
 * incorrectly appears in the settings collection.
 */

/**
 * Filters out contaminated data from settings collection
 * @param {Array} data - Array of documents from settings collection
 * @returns {Array} - Filtered array containing only actual settings documents
 */
export const filterSettingsData = (data) => {
  if (!Array.isArray(data)) {
    console.warn('[settingsDataFilter] Expected array, got:', typeof data);
    return [];
  }

  const filteredData = data.filter(doc => {
    if (!doc || typeof doc !== 'object') {
      return false;
    }

    // Exclude documents that have fields typical of other collections
    const hasPersonFields = doc.name || doc.email || doc.phone || doc.category;
    const hasOccupationFields = doc.tenant || doc.unit || doc.property || doc.dateIn;
    const hasPropertyFields = doc.landlord || doc.manager || doc.address;
    const hasInvoiceFields = doc.items || doc.total || doc.subtotal;
    const hasReceiptFields = doc.amount || doc.paymentMethod;
    const hasExpenseFields = doc.supplier || doc.description;
    const hasUnitFields = doc.code || doc.rent || doc.deposit;
    const hasComplaintFields = doc.complaint || doc.status || doc.priority;
    const hasFollowUpFields = doc.followUp || doc.dueDate;
    
    // Keep only documents that don't have these collection-specific fields
    return !(hasPersonFields || hasOccupationFields || hasPropertyFields || 
            hasInvoiceFields || hasReceiptFields || hasExpenseFields ||
            hasUnitFields || hasComplaintFields || hasFollowUpFields);
  });

  if (filteredData.length !== data.length) {
    console.log(`[settingsDataFilter] Filtered ${data.length} documents to ${filteredData.length} actual settings`);
  }

  return filteredData;
};

/**
 * Identifies contaminated documents in settings collection
 * @param {Array} data - Array of documents from settings collection
 * @returns {Array} - Array of contaminated documents that should be removed
 */
export const identifyContaminatedData = (data) => {
  if (!Array.isArray(data)) {
    return [];
  }

  return data.filter(doc => {
    if (!doc || typeof doc !== 'object') {
      return false;
    }

    // Identify documents that have fields typical of other collections
    const hasPersonFields = doc.name || doc.email || doc.phone || doc.category;
    const hasOccupationFields = doc.tenant || doc.unit || doc.property || doc.dateIn;
    const hasPropertyFields = doc.landlord || doc.manager || doc.address;
    const hasInvoiceFields = doc.items || doc.total || doc.subtotal;
    const hasReceiptFields = doc.amount || doc.paymentMethod;
    const hasExpenseFields = doc.supplier || doc.description;
    const hasUnitFields = doc.code || doc.rent || doc.deposit;
    const hasComplaintFields = doc.complaint || doc.status || doc.priority;
    const hasFollowUpFields = doc.followUp || doc.dueDate;
    
    // Return true for contaminated documents
    return hasPersonFields || hasOccupationFields || hasPropertyFields || 
           hasInvoiceFields || hasReceiptFields || hasExpenseFields ||
           hasUnitFields || hasComplaintFields || hasFollowUpFields;
  });
};

/**
 * Cleans up contaminated data from settings database
 * @param {Function} pouchDatabase - Database function
 * @param {string} databasePrefix - Database prefix
 * @param {Object} currentUser - Current user object
 * @returns {Promise<number>} - Number of documents cleaned up
 */
export const cleanupContaminatedSettings = async (pouchDatabase, databasePrefix, currentUser) => {
  try {
    const settingsDb = pouchDatabase('settings', databasePrefix);
    const allData = await settingsDb.getAllData();
    
    const contaminatedDocs = identifyContaminatedData(allData);
    
    if (contaminatedDocs.length === 0) {
      console.log('[settingsDataFilter] No contaminated data found in settings database');
      return 0;
    }
    
    console.log(`[settingsDataFilter] Found ${contaminatedDocs.length} contaminated documents in settings database`);
    
    let cleanedCount = 0;
    for (const doc of contaminatedDocs) {
      try {
        await settingsDb.deleteDocument(doc, currentUser);
        cleanedCount++;
        console.log(`[settingsDataFilter] Removed contaminated document: ${doc._id} (type: ${getDocumentType(doc)})`);
      } catch (error) {
        console.warn(`[settingsDataFilter] Failed to remove contaminated document ${doc._id}:`, error);
      }
    }
    
    console.log(`[settingsDataFilter] Cleanup completed. Removed ${cleanedCount}/${contaminatedDocs.length} contaminated documents.`);
    return cleanedCount;
  } catch (error) {
    console.error('[settingsDataFilter] Error during cleanup:', error);
    return 0;
  }
};

/**
 * Determines the likely collection type of a contaminated document
 * @param {Object} doc - Document to analyze
 * @returns {string} - Likely collection name
 */
const getDocumentType = (doc) => {
  if (doc.name || doc.email || doc.phone || doc.category) return 'people';
  if (doc.tenant || doc.unit || doc.property || doc.dateIn) return 'occupations';
  if (doc.landlord || doc.manager || doc.address) return 'properties';
  if (doc.items || doc.total || doc.subtotal) return 'invoices';
  if (doc.amount || doc.paymentMethod) return 'receipts';
  if (doc.supplier || doc.description) return 'expenses';
  if (doc.code || doc.rent || doc.deposit) return 'units';
  if (doc.complaint || doc.status || doc.priority) return 'complaints';
  if (doc.followUp || doc.dueDate) return 'follow_ups';
  return 'unknown';
};

export default {
  filterSettingsData,
  identifyContaminatedData,
  cleanupContaminatedSettings
};
