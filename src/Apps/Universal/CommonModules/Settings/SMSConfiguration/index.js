import React, { useState, useEffect } from 'react';
import { APP_SETTINGS  } from "../../../../../Utils/constants";
import { Form, Button, Card, Table, Modal, Select, Switch, Space, message, Popconfirm, Alert, Typography, Tag, Divider  } from "antd";
import { PlusOutlined, EditOutlined, DeleteOutlined, SettingOutlined, MessageOutlined  } from "@ant-design/icons";
import { filterSettingsData  } from "../utils/settingsDataFilter";


const { Option } = Select;
const { Text } = Typography;

const SMSConfiguration = ({ pouchDatabase, databasePrefix, currentUser, modules }) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingConfig, setEditingConfig] = useState(null);
  const [configurations, setConfigurations] = useState([]);
  const [templates, setTemplates] = useState([]);
  const [availableModules, setAvailableModules] = useState([]);

  useEffect(() => {
    loadConfigurations();
    loadTemplates();
    loadModules();
  }, []);

  const loadConfigurations = async () => {
    try {
      setLoading(true);
      const result = await pouchDatabase('settings', databasePrefix).getAllData();

      // CRITICAL FIX: Filter out contaminated data from other collections
      const settingsData = filterSettingsData(result);

      const settings = settingsData[0] || {};
      const smsConfigs = settings.sms_configurations || [];
      setConfigurations(smsConfigs);
    } catch (error) {
      console.error('Error loading SMS configurations:', error);
      message.error('Failed to load SMS configurations');
    } finally {
      setLoading(false);
    }
  };

  const loadTemplates = async () => {
    try {
      const result = await pouchDatabase('sms_templates', databasePrefix).getAllData();
      setTemplates(result);
    } catch (error) {
      console.error('Error loading SMS templates:', error);
    }
  };

  const loadModules = () => {
    if (modules) {
      const moduleList = Object.keys(modules).map(key => ({
        key,
        name: modules[key].name,
        collection: modules[key].collection
      })).filter(module => 
        // Filter out system modules that shouldn't have SMS
        !['settings', 'roles', 'users', 'organizations'].includes(module.collection)
      );
      setAvailableModules(moduleList);
    }
  };

  const handleSave = async (values) => {
    try {
      setLoading(true);
      
      // Get current settings
      const result = await pouchDatabase('settings', databasePrefix).getAllData();
      const currentSettings = result[0] || {};
      
      let updatedConfigurations = [...configurations];
      
      if (editingConfig) {
        // Update existing configuration
        const index = updatedConfigurations.findIndex(config => config.id === editingConfig.id);
        if (index !== -1) {
          updatedConfigurations[index] = {
            ...editingConfig,
            ...values,
            updatedAt: new Date().toISOString(),
            updatedBy: currentUser._id
          };
        }
      } else {
        // Add new configuration
        const newConfig = {
          id: Date.now().toString(),
          ...values,
          createdAt: new Date().toISOString(),
          createdBy: currentUser._id,
          updatedAt: new Date().toISOString(),
          updatedBy: currentUser._id
        };
        updatedConfigurations.push(newConfig);
      }

      // Update settings with new configurations
      const updatedSettings = {
        ...currentSettings,
        sms_configurations: updatedConfigurations
      };

      await pouchDatabase('settings', databasePrefix).saveDocument(
        updatedSettings,
        {
          key: currentUser._id,
          label: `${currentUser.first_name} ${currentUser.last_name}`,
          value: currentUser._id,
        }
      );

      // Update localStorage for immediate access
      localStorage.setItem(APP_SETTINGS, JSON.stringify(updatedSettings));
      
      // Trigger storage event for other components
      window.dispatchEvent(new StorageEvent('storage', {
        key: APP_SETTINGS,
        newValue: JSON.stringify(updatedSettings),
        storageArea: localStorage
      }));

      setConfigurations(updatedConfigurations);
      setModalVisible(false);
      setEditingConfig(null);
      form.resetFields();
      message.success(editingConfig ? 'Configuration updated successfully!' : 'Configuration added successfully!');
    } catch (error) {
      console.error('Error saving SMS configuration:', error);
      message.error('Failed to save SMS configuration');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (configId) => {
    try {
      setLoading(true);
      
      const result = await pouchDatabase('settings', databasePrefix).getAllData();
      const currentSettings = result[0] || {};
      
      const updatedConfigurations = configurations.filter(config => config.id !== configId);
      
      const updatedSettings = {
        ...currentSettings,
        sms_configurations: updatedConfigurations
      };

      await pouchDatabase('settings', databasePrefix).saveDocument(
        updatedSettings,
        {
          key: currentUser._id,
          label: `${currentUser.first_name} ${currentUser.last_name}`,
          value: currentUser._id,
        }
      );

      // Update localStorage
      localStorage.setItem(APP_SETTINGS, JSON.stringify(updatedSettings));
      
      // Trigger storage event
      window.dispatchEvent(new StorageEvent('storage', {
        key: APP_SETTINGS,
        newValue: JSON.stringify(updatedSettings),
        storageArea: localStorage
      }));

      setConfigurations(updatedConfigurations);
      message.success('Configuration deleted successfully!');
    } catch (error) {
      console.error('Error deleting SMS configuration:', error);
      message.error('Failed to delete SMS configuration');
    } finally {
      setLoading(false);
    }
  };

  const columns = [
    {
      title: 'Module',
      dataIndex: 'module',
      key: 'module',
      render: (moduleKey) => {
        const module = availableModules.find(m => m.key === moduleKey);
        return module ? module.name : moduleKey;
      },
    },
    {
      title: 'Template',
      dataIndex: 'template',
      key: 'template',
      render: (templateId) => {
        const template = templates.find(t => t._id === templateId);
        return template ? (
          <div>
            <Text strong>{template.name}</Text>
            <br />
            <Tag color="blue">{template.category}</Tag>
          </div>
        ) : 'Template not found';
      },
    },
    {
      title: 'Send Timing',
      dataIndex: 'timing',
      key: 'timing',
      render: (timing) => (
        <Tag color={timing === 'before' ? 'orange' : 'green'}>
          {timing === 'before' ? 'Before Save' : 'After Save'}
        </Tag>
      ),
    },
    {
      title: 'Status',
      dataIndex: 'enabled',
      key: 'enabled',
      render: (enabled) => (
        <Tag color={enabled ? 'green' : 'red'}>
          {enabled ? 'Enabled' : 'Disabled'}
        </Tag>
      ),
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_, record) => (
        <Space>
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => {
              setEditingConfig(record);
              form.setFieldsValue(record);
              setModalVisible(true);
            }}
          >
            Edit
          </Button>
          <Popconfirm
            title="Are you sure you want to delete this configuration?"
            onConfirm={() => handleDelete(record.id)}
            okText="Yes"
            cancelText="No"
          >
            <Button type="link" danger icon={<DeleteOutlined />}>
              Delete
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div>
      <Alert
        message="SMS Configuration"
        description="Configure when and how SMS notifications are sent for different modules. These settings apply to all users in your organization."
        type="info"
        showIcon
        style={{ marginBottom: 16 }}
      />

      <Card
        title={
          <Space>
            <SettingOutlined />
            SMS Module Configurations
          </Space>
        }
        extra={
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => {
              setEditingConfig(null);
              form.resetFields();
              setModalVisible(true);
            }}
          >
            Add Configuration
          </Button>
        }
      >
        <Table
          columns={columns}
          dataSource={configurations}
          rowKey="id"
          loading={loading}
          pagination={{ pageSize: 10 }}
          locale={{
            emptyText: 'No SMS configurations found. Click "Add Configuration" to create one.'
          }}
        />
      </Card>

      <Modal
        title={editingConfig ? 'Edit SMS Configuration' : 'Add SMS Configuration'}
        open={modalVisible}
        onCancel={() => {
          setModalVisible(false);
          setEditingConfig(null);
          form.resetFields();
        }}
        footer={null}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSave}
        >
          <Form.Item
            name="module"
            label="Module"
            rules={[{ required: true, message: 'Please select a module' }]}
            tooltip="Select the module for which SMS notifications should be sent"
          >
            <Select
              placeholder="Select module"
              showSearch
              filterOption={(input, option) =>
                option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
              }
            >
              {availableModules.map(module => (
                <Option key={module.key} value={module.key}>
                  {module.name}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="template"
            label="SMS Template"
            rules={[{ required: true, message: 'Please select a template' }]}
            tooltip="Select the SMS template to use for notifications"
          >
            <Select
              placeholder="Select template"
              showSearch
              filterOption={(input, option) =>
                option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
              }
            >
              {templates.map(template => (
                <Option key={template._id} value={template._id}>
                  {template.name} ({template.category})
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="timing"
            label="Send Timing"
            rules={[{ required: true, message: 'Please select when to send SMS' }]}
            tooltip="Choose when the SMS should be sent relative to the save operation"
          >
            <Select placeholder="Select timing">
              <Option value="before">Before Save</Option>
              <Option value="after">After Save</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="enabled"
            label="Enable Configuration"
            valuePropName="checked"
            tooltip="Enable or disable this SMS configuration"
          >
            <Switch />
          </Form.Item>

          <Divider />

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => {
                setModalVisible(false);
                setEditingConfig(null);
                form.resetFields();
              }}>
                Cancel
              </Button>
              <Button type="primary" htmlType="submit" loading={loading}>
                {editingConfig ? 'Update' : 'Add'} Configuration
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default SMSConfiguration;
