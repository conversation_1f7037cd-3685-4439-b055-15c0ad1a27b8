# SMS Configuration

This module allows users to configure SMS notifications for different modules in the system. Users can specify which template to use, when to send the SMS (before or after save), and enable/disable the configuration.

## Features

- **Module Selection**: Choose from available system modules
- **Template Selection**: Select from existing SMS templates
- **Timing Control**: Send SMS before or after save operations
- **Enable/Disable**: Toggle configurations on/off with inline switches or in the edit form
- **Organization-wide**: Settings apply to all users in the organization

## Usage

### Accessing SMS Configuration

1. Navigate to **Settings** → **SMS Settings**
2. Click on the **Configuration** tab
3. Click **Add Configuration** to create a new SMS configuration

### Configuration Options

- **Module**: Select the module for which SMS should be sent (e.g., Invoices, Receipts, etc.)
- **Template**: Choose an SMS template from the Templates tab
- **Send Timing**:
  - **Before Save**: SMS is sent before the document is saved
  - **After Save**: SMS is sent after the document is successfully saved
- **Enable Configuration**: Toggle to enable/disable this configuration
  - You can also toggle configurations directly from the table using the inline switch

### Using SMS Configurations in Code

The system provides utility functions to check and use SMS configurations:

```javascript
import {
  getSMSConfigurationsForModule,
  isSMSConfiguredForModule,
  getSMSTemplateForModule
} from '../../../../../Utils/functions';

// Check if SMS is configured for a module
const isConfigured = isSMSConfiguredForModule('invoices', 'after');

// Get all configurations for a module
const configurations = getSMSConfigurationsForModule('invoices');

// Get specific template for a module and timing
const templateId = getSMSTemplateForModule('invoices', 'after');
```

### Integration with Module Save Operations

To integrate SMS sending with module save operations, use the `beforeSave` or `afterSave` hooks in your module properties:

```javascript
// In modulesProperties/invoices.js
export default {
  afterSave: async (data, pouchDatabase, databasePrefix, currentUser) => {
    const configurations = getSMSConfigurationsForModule('invoices');
    const afterSaveConfigs = configurations.filter(config => config.timing === 'after');

    for (const config of afterSaveConfigs) {
      // Send SMS using the configured template
      await sendSMSWithTemplate(config.template, data, pouchDatabase, databasePrefix);
    }
  }
};
```

## Data Structure

SMS configurations are stored in the app settings with the following structure:

```json
{
  "sms_configurations": [
    {
      "id": "1640995200000",
      "module": "invoices",
      "template": "template_id_here",
      "timing": "after",
      "enabled": true,
      "createdAt": "2023-12-01T10:00:00.000Z",
      "createdBy": "user_id",
      "updatedAt": "2023-12-01T10:00:00.000Z",
      "updatedBy": "user_id"
    }
  ]
}
```

## Benefits

1. **Centralized Configuration**: All SMS settings in one place
2. **Flexible Timing**: Choose when to send SMS notifications
3. **Template Reuse**: Use existing templates across different modules
4. **Organization-wide**: Settings apply to all users in the organization
5. **Easy Management**: Simple interface to add, edit, and delete configurations

## Notes

- SMS configurations are saved in app settings and accessible organization-wide
- Changes to configurations take effect immediately
- Disabled configurations are ignored during SMS sending
- Templates must be created in the Templates tab before they can be used in configurations
