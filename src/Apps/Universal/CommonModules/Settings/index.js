import './settings.css';
import AISettings from "./AISettings";
import Account from "./Account";
import AppSettings from "./AppSettings";
import ChangePassword from "./ChangePassword";
import DatabaseSync from "./DatabaseSync";
import Organization from "./Organization";
import React from "react";
import SMSSettings from "./SMSSettings";
import SecuritySettings from "./SecuritySettings";
import SubscriptionSettings from "./SubscriptionSettings";
import { BankOutlined, CreditCardOutlined, UserOutlined, LockOutlined, SettingOutlined, SyncOutlined, RobotOutlined, SecurityScanOutlined, MessageOutlined } from "@ant-design/icons";
import { Layout, Tabs } from "antd";


const { TabPane } = Tabs;

const Settings = (props) => {

    return (
        <div className="enhanced-settings-container">
            <Layout style={{ background: 'transparent' }}>
                <Tabs
                    tabPosition="left"
                    className="enhanced-settings-tabs"
                    size="large"
                >
                    <TabPane
                        tab={
                            <span>
                                <BankOutlined />
                                Organization
                            </span>
                        }
                        key={1}
                    >
                        <div className="settings-content-wrapper">
                            <Organization {...props} />
                        </div>
                    </TabPane>
                    <TabPane
                        tab={
                            <span>
                                <CreditCardOutlined />
                                Subscription
                            </span>
                        }
                        key={2}
                    >
                        <div className="settings-content-wrapper">
                            <SubscriptionSettings {...props} />
                        </div>
                    </TabPane>
                    <TabPane
                        tab={
                            <span>
                                <UserOutlined />
                                Account
                            </span>
                        }
                        key={3}
                    >
                        <div className="settings-content-wrapper">
                            <Account {...props} />
                        </div>
                    </TabPane>
                    <TabPane
                        tab={
                            <span>
                                <LockOutlined />
                                Change Password
                            </span>
                        }
                        key={4}
                    >
                        <div className="settings-content-wrapper">
                            <ChangePassword {...props} />
                        </div>
                    </TabPane>
                    {props.settings &&
                        <TabPane
                            tab={
                                <span>
                                    <SettingOutlined />
                                    App Settings
                                </span>
                            }
                            key={5}
                        >
                            <div className="settings-content-wrapper">
                                <AppSettings {...props} />
                            </div>
                        </TabPane>
                    }
                    <TabPane
                        tab={
                            <span>
                                <SyncOutlined />
                                Database Sync
                            </span>
                        }
                        key={6}
                    >
                        <div className="settings-content-wrapper">
                            <DatabaseSync {...props} />
                        </div>
                    </TabPane>
                    <TabPane
                        tab={
                            <span>
                                <RobotOutlined />
                                AI Settings
                            </span>
                        }
                        key={7}
                    >
                        <div className="settings-content-wrapper">
                            <AISettings {...props} />
                        </div>
                    </TabPane>
                    <TabPane
                        tab={
                            <span>
                                <SecurityScanOutlined />
                                Security
                            </span>
                        }
                        key={8}
                    >
                        <div className="settings-content-wrapper">
                            <SecuritySettings {...props} />
                        </div>
                    </TabPane>
                    <TabPane
                        tab={
                            <span>
                                <MessageOutlined />
                                SMS Settings
                            </span>
                        }
                        key={9}
                    >
                        <div className="settings-content-wrapper">
                            <SMSSettings {...props} />
                        </div>
                    </TabPane>
                    {/* <TabPane tab={"Local Database"} key={8}>
                        <LocalDatabaseSettings {...props} />
                    </TabPane> */}
                </Tabs>
            </Layout>
        </div>
    )
}
export default Settings;