import React, { useEffect, useState } from "react";
import { APP_SETTINGS  } from "../../../../../Utils/constants";
import { BetaSchemaForm  } from "@ant-design/pro-form";
import { Divider, message, Card, Typography, Space, Alert  } from "antd";
import { SettingOutlined, InfoCircleOutlined, CheckCircleOutlined, AppstoreOutlined, MessageOutlined  } from "@ant-design/icons";
import { buffModules  } from "../../../../../Utils/functions";
import { filterSettingsData, cleanupContaminatedSettings  } from "../utils/settingsDataFilter";


const AppSettings = (props) => {
  const [initialValues, setInitialValues] = useState(null);
  const [update, setUpdate] = useState(0);
  const [cleanupPerformed, setCleanupPerformed] = useState(false);

  const { databasePrefix, pouchDatabase, currentUser, settings, modules } = props;
  const { collection = "settings", columns = [] } = settings;

  // Function to clean up contaminated data from settings database
  const cleanupContaminatedData = async () => {
    if (cleanupPerformed) return;

    try {
      const cleanedCount = await cleanupContaminatedSettings(pouchDatabase, databasePrefix, currentUser);
      console.log(`[AppSettings] Cleanup completed. Removed ${cleanedCount} contaminated documents.`);
      setCleanupPerformed(true);
    } catch (error) {
      console.error('[AppSettings] Error during cleanup:', error);
      setCleanupPerformed(true); // Mark as performed to avoid infinite retries
    }
  };



  const layout = {
    labelCol: { span: 4 },
    wrapperCol: { span: 16 },
  };

  useEffect(() => {
    const loadSettings = async () => {
      // First, perform cleanup if needed
      await cleanupContaminatedData();

      // Then load the clean settings data
      try {
        const data = await pouchDatabase(collection, databasePrefix).getAllData();

        // CRITICAL FIX: Filter out any remaining contaminated data from other collections
        const settingsData = filterSettingsData(data);

        setInitialValues(settingsData[0] ? settingsData[0] : {});
      } catch (error) {
        console.error('[AppSettings] Error loading settings:', error);
        setInitialValues({});
      }
    };

    loadSettings();
  }, [update, cleanupPerformed]);

  // Universal default columns available to all apps
  const defaultColumns = [
    {
      dataIndex: "default_receipt_account",
      title: "Default Receipt Account",
      type: "dbSelect",
      collection: "accounts",
      label: ["name"],
      colProps: {
        sm: 6,
      },
    },
    {
      dataIndex: "default_expense_account",
      title: "Default Expense Account",
      type: "dbSelect",
      collection: "accounts",
      label: ["name"],
      colProps: {
        sm: 6,
      },
    },
    // Document Settings Group
    {
      valueType: "group",
      title: "Document Settings",
      colProps: {
        md: 24
      },
      columns: [
        {
          dataIndex: "currency",
          title: "Currency",
          valueType: "select",
          valueEnum: JSON.parse(localStorage.getItem("GEO") || "{}").currencies || {},
          tooltip: "Default currency for the application",
          fieldProps: {
            showSearch: true,
          },
          colProps: {
            md: 8
          }
        },
        {
          dataIndex: "documentHeader",
          title: "Document Header Style",
          valueType: "select",
          initialValue: "V1",
          valueEnum: {
            V1: { text: "V1 - Classic Header" },
            V2: { text: "V2 - Modern Header" },
          },
          tooltip: "Choose the header style for invoices, receipts, and other documents",
          colProps: {
            md: 8
          }
        },
        {
          dataIndex: "showOrganizationName",
          title: "Show Organization Name on Documents",
          valueType: "switch",
          initialValue: true,
          tooltip: "Toggle to show or hide organization name on invoices, receipts, and other documents",
          colProps: {
            md: 8
          }
        },
        {
          dataIndex: "defaultPageSize",
          title: "Default Page Size",
          valueType: "select",
          initialValue: "A4",
          valueEnum: {
            A4: { text: "A4 Paper" },
            Thermal: { text: "Thermal Paper (80mm)" },
          },
          tooltip: "Default page size for printing receipts and documents",
          colProps: {
            md: 8
          }
        }
      ]
    },
    // SMS Settings Group - Available to all apps by default
    {
      valueType: "group",
      title: "SMS Notifications",
      colProps: {
        md: 24
      },
      columns: [
        {
          dataIndex: "enable_sms",
          title: "Enable SMS Notifications",
          valueType: "switch",
          initialValue: false,
          tooltip: "Enable SMS notifications for invoices, receipts, and other documents",
          colProps: {
            md: 12
          }
        },
        {
          dataIndex: "sms_provider",
          title: "SMS Provider",
          valueType: "select",
          initialValue: "egosms",
          valueEnum: {
            egosms: { text: "EgoSMS" },
            twilio: { text: "Twilio (Coming Soon)", disabled: true },
            nexmo: { text: "Nexmo (Coming Soon)", disabled: true }
          },
          colProps: {
            md: 12
          }
        },
        {
          dataIndex: "sms_username",
          title: "SMS Username",
          valueType: "text",
          tooltip: "Your SMS provider username",
          colProps: {
            md: 8
          }
        },
        {
          dataIndex: "sms_password",
          title: "SMS Password",
          valueType: "password",
          tooltip: "Your SMS provider password",
          colProps: {
            md: 8
          }
        },
        {
          dataIndex: "sms_sender",
          title: "Sender ID",
          valueType: "text",
          tooltip: "The name that appears as the sender (max 11 characters)",
          fieldProps: {
            maxLength: 11
          },
          colProps: {
            md: 8
          }
        }
      ]
    },
    // SMS Configuration Group - Available to all apps by default
    {
      valueType: "group",
      title: "SMS Configuration",
      colProps: {
        md: 24
      },
      columns: [
        {
          dataIndex: "sms_configurations",
          title: "SMS Module Configurations",
          valueType: "jsonCode",
          tooltip: "SMS configurations for different modules (managed via SMS Settings)",
          hideInForm: true,
          hideInTable: true,
          colProps: {
            md: 24
          }
        }
      ]
    }
  ];

  // Merge universal default columns with app-specific columns BEFORE buffing
  const mergedColumns = [...defaultColumns, ...columns];

  // Debug logging to verify column merging
  console.log('[AppSettings] Universal default columns:', defaultColumns.length);
  console.log('[AppSettings] App-specific columns:', columns.length);
  console.log('[AppSettings] Merged columns total:', mergedColumns.length);
  console.log('[AppSettings] ', columns, mergedColumns);

  // Create the merged settings module
  const mergedSettingsModule = {
    ...settings,
    collection: "settings",
    columns: mergedColumns
  };

  console.log('[AppSettings] Merged settings module:', mergedSettingsModule);

  // Now buff the modules with the properly merged settings
  const newModules = buffModules(
    { ...modules, settings: mergedSettingsModule },
    pouchDatabase,
    currentUser,
    databasePrefix
  );
  
  console.log('[AppSettings] Final buffed modules:', newModules);
  console.log('[AppSettings] Final buffed settings:', newModules.settings);

  // Get the buffed settings columns for the form
  const buffedSettingsColumns = newModules.settings?.columns || mergedColumns;

  const { Title, Text } = Typography;

  // Debug logging to verify final columns
  console.log('[AppSettings] Final buffed columns:', buffedSettingsColumns);
  console.log('[AppSettings] Column titles:', buffedSettingsColumns.map(col => col.title || col.dataIndex));

  // Group columns by their type for better organization
  const groupedColumns = buffedSettingsColumns.reduce((groups, column) => {
    if (column.valueType === 'group') {
      groups.groups.push(column);
    } else if (column.dataIndex?.includes('sms')) {
      groups.sms.push(column);
    } else {
      groups.general.push(column);
    }
    return groups;
  }, { general: [], sms: [], groups: [] });

  return (
    <div className="enhanced-settings-form">
      <div className="settings-section-header">
        <Divider orientation="left">
          <Space>
            <SettingOutlined />
            Application Settings
          </Space>
        </Divider>
      </div>

      {currentUser.role.label === "Superuser" && initialValues ? (
        <div className="enhanced-form-container">
          <Alert
            message="Application Configuration"
            description="Configure application-specific settings, default accounts, and notification preferences."
            type="info"
            icon={<InfoCircleOutlined />}
            showIcon
            style={{ marginBottom: 24 }}
          />

          <BetaSchemaForm
            layoutType="ProForm"
            submitter={{
              searchConfig: {
                resetText: "Reset",
                submitText: (
                  <Space>
                    <CheckCircleOutlined />
                    Update Settings
                  </Space>
                )
              },
              render: (props, doms) => {
                return (
                  <div className="form-actions-container">
                    {doms}
                  </div>
                );
              },
            }}
            onFinish={async (values) => {
              const updatedSettings = { ...initialValues, ...values };

              pouchDatabase(collection, databasePrefix)
                .saveDocument(
                  updatedSettings,
                  {
                    key: currentUser._id,
                    label: `${currentUser.first_name} ${currentUser.last_name}`,
                    value: currentUser._id,
                  }
                )
                .then((res) => {
                  // Update localStorage immediately for SMS and other utilities
                  localStorage.setItem(APP_SETTINGS, JSON.stringify(updatedSettings));

                  // Trigger storage event for other components
                  window.dispatchEvent(new StorageEvent('storage', {
                    key: APP_SETTINGS,
                    newValue: JSON.stringify(updatedSettings),
                    storageArea: localStorage
                  }));

                  message.success({
                    content: "Application settings have been updated successfully!",
                    icon: <CheckCircleOutlined style={{ color: '#52c41a' }} />,
                    duration: 4,
                  });
                  setUpdate(update + 1);
                });
            }}
            initialValues={initialValues}
            columns={buffedSettingsColumns}
          />
        </div>
      ) : (
        <Card>
          <div style={{ textAlign: 'center', padding: '40px 20px' }}>
            <SettingOutlined style={{ fontSize: '48px', color: '#d9d9d9', marginBottom: '16px' }} />
            <Title level={4} type="secondary">Access Restricted</Title>
            <Text type="secondary">
              Only superusers can modify application settings.
            </Text>
          </div>
        </Card>
      )}
    </div>
  );
};

export default AppSettings;
