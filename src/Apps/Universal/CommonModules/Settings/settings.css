/* Enhanced Settings Component Styles */

.enhanced-settings-container {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  overflow: hidden;
  margin: 0;
  padding: 0;
}

.enhanced-settings-container .ant-layout {
  background: transparent;
}

/* Override Ant Design default tab styles */
/* .enhanced-settings-tabs.ant-tabs.ant-tabs-left > .ant-tabs-nav .ant-tabs-tab {
  margin: 4px 8px !important;
  padding: 12px 16px !important;
  border-radius: 8px !important;
  border: none !important;
  background: transparent !important;
  transition: all 0.3s ease !important;
  width: calc(100% - 16px) !important;
  justify-content: flex-start !important;
}

.enhanced-settings-tabs.ant-tabs.ant-tabs-left > .ant-tabs-nav .ant-tabs-tab-active {
  background: var(--primary-color, #722ed1) !important;
  color: #fff !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15) !important;
}

/* Enhanced Tabs Styling */
/* .enhanced-settings-tabs {
  min-height: 600px;
}

.enhanced-settings-tabs .ant-tabs-nav {
  background: #fafafa;
  border-right: 1px solid #f0f0f0;
  margin: 0;
  padding: 16px 0;
  width: 240px;
}

.enhanced-settings-tabs .ant-tabs-nav-wrap {
  padding: 0 8px;
}

.enhanced-settings-tabs .ant-tabs-nav-list {
  width: 100%;
} */

/* .enhanced-settings-tabs .ant-tabs-tab {
  margin: 4px 8px !important;
  padding: 12px 16px !important;
  border-radius: 8px !important;
  border: none !important;
  background: transparent !important;
  transition: all 0.3s ease !important;
  position: relative;
  overflow: hidden;
  width: calc(100% - 16px) !important;
  justify-content: flex-start !important;
}

.enhanced-settings-tabs .ant-tabs-tab:hover {
  background: #f0f0f0 !important;
  color: var(--primary-color, #722ed1) !important;
}

.enhanced-settings-tabs .ant-tabs-tab-active {
  background: var(--primary-color, #722ed1) !important;
  color: #fff !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15) !important;
}

.enhanced-settings-tabs .ant-tabs-tab-active:hover {
  background: var(--primary-color, #722ed1) !important;
  color: #fff !important;
}

.enhanced-settings-tabs .ant-tabs-tab-active .ant-tabs-tab-btn {
  color: #fff !important;
}

.enhanced-settings-tabs .ant-tabs-tab-active .ant-tabs-tab-btn .anticon {
  color: #fff !important;
}

.enhanced-settings-tabs .ant-tabs-tab-btn {
  display: flex;
  align-items: center;
  font-weight: 500;
  font-size: 14px;
  width: 100%;
  text-align: left;
  justify-content: flex-start;
}

.enhanced-settings-tabs .ant-tabs-tab-btn .anticon {
  margin-right: 12px;
  font-size: 16px;
  min-width: 16px;
} */

/* Remove default tab borders and backgrounds */
/* .enhanced-settings-tabs .ant-tabs-tab::before {
  display: none !important;
}

.enhanced-settings-tabs .ant-tabs-tab::after {
  display: none !important;
} */

/* Content Area Styling */
.enhanced-settings-tabs .ant-tabs-content-holder {
  background: #fff;
  padding: 0;
}

.enhanced-settings-tabs .ant-tabs-tabpane {
  padding: 32px;
  min-height: 600px;
}

/* Settings Content Styling */
.settings-content-wrapper {
  max-width: 800px;
}

.settings-section-header {
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 2px solid #f0f0f0;
}

.settings-section-header .ant-divider {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #262626;
}

.settings-section-header .ant-divider::before,
.settings-section-header .ant-divider::after {
  border-top: 2px solid #f0f0f0;
}

/* Form Enhancements */
.enhanced-settings-form .ant-form-item-label > label {
  font-weight: 500;
  color: #262626;
}

.enhanced-settings-form .ant-input,
.enhanced-settings-form .ant-select-selector,
.enhanced-settings-form .ant-picker {
  border-radius: 6px;
  border: 1px solid #d9d9d9;
  transition: all 0.3s ease;
}

.enhanced-settings-form .ant-input:hover,
.enhanced-settings-form .ant-select-selector:hover,
.enhanced-settings-form .ant-picker:hover {
  border-color: var(--primary-color, #722ed1);
  box-shadow: 0 0 0 2px rgba(0, 0, 0, 0.05);
}

.enhanced-settings-form .ant-input:focus,
.enhanced-settings-form .ant-select-focused .ant-select-selector,
.enhanced-settings-form .ant-picker-focused {
  border-color: var(--primary-color, #722ed1);
  box-shadow: 0 0 0 2px rgba(0, 0, 0, 0.1);
}

/* Button Enhancements */
/* .enhanced-settings-form .ant-btn-primary {
  border-radius: 6px;
  font-weight: 500;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
}

.enhanced-settings-form .ant-btn-primary:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
} */

/* Card-like sections for better organization */
.settings-card-section {
  background: #fafafa;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
}

.settings-card-section-title {
  font-size: 16px;
  font-weight: 600;
  color: #262626;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
}

.settings-card-section-title .anticon {
  margin-right: 8px;
  color: var(--primary-color, #722ed1);
}

/* Enhanced Form Container */
.enhanced-form-container {
  background: #fff;
  border: 1px solid #f0f0f0;
  border-radius: 12px;
  padding: 24px;
  margin-top: 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

/* Form Field Groups */
.form-field-group {
  background: #fafafa;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 24px;
}

.form-field-group-title {
  font-size: 16px;
  font-weight: 600;
  color: #262626;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 2px solid #e8e8e8;
  display: flex;
  align-items: center;
}

.form-field-group-title .anticon {
  margin-right: 8px;
  color: var(--primary-color, #722ed1);
  font-size: 18px;
}

/* Enhanced ProForm Styling */
.enhanced-settings-form .ant-pro-form {
  background: transparent;
}

.enhanced-settings-form .ant-pro-form-group-title {
  font-size: 16px;
  font-weight: 600;
  color: #262626;
  margin-bottom: 16px;
  padding: 12px 16px;
  background: #f8f9fa;
  border-left: 4px solid var(--primary-color, #722ed1);
  border-radius: 4px;
}

.enhanced-settings-form .ant-form-item {
  margin-bottom: 20px;
}

.enhanced-settings-form .ant-form-item-label {
  padding-bottom: 8px;
}

.enhanced-settings-form .ant-form-item-label > label {
  font-weight: 500;
  color: #262626;
  font-size: 14px;
}

.enhanced-settings-form .ant-form-item-label > label.ant-form-item-required::before {
  color: var(--primary-color, #722ed1);
}

/* Form Actions */
.form-actions-container {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 16px 20px;
  margin-top: 24px;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.form-actions-container .ant-btn {
  margin-left: 8px;
}

/* Success/Error States */
.form-success-message {
  background: rgba(82, 196, 26, 0.1);
  border: 1px solid rgba(82, 196, 26, 0.3);
  border-radius: 6px;
  padding: 12px 16px;
  margin-bottom: 16px;
  color: #52c41a;
  display: flex;
  align-items: center;
}

.form-success-message .anticon {
  margin-right: 8px;
  font-size: 16px;
}

/* Loading States */
.form-loading-overlay {
  position: relative;
}

.form-loading-overlay::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 8px;
  z-index: 10;
}

/* Form Help Text */
.form-help-text {
  color: #8c8c8c;
  font-size: 13px;
  margin-top: 4px;
  line-height: 1.4;
}

.form-help-text .anticon {
  margin-right: 4px;
  color: var(--primary-color, #722ed1);
}

/* Progress and Status Indicators */
.settings-progress-indicator {
  margin-bottom: 16px;
}

.settings-status-indicator {
  display: inline-flex;
  align-items: center;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.settings-status-success {
  background: rgba(82, 196, 26, 0.1);
  color: #52c41a;
}

.settings-status-warning {
  background: rgba(250, 173, 20, 0.1);
  color: #faad14;
}

.settings-status-error {
  background: rgba(245, 34, 45, 0.1);
  color: #f5222d;
}

/* Additional Tab Fixes */
.enhanced-settings-tabs .ant-tabs-tab-remove {
  display: none !important;
}

.enhanced-settings-tabs .ant-tabs-nav-more {
  display: none !important;
}

/* Force proper tab styling */
.enhanced-settings-tabs .ant-tabs-tab[aria-selected="true"] {
  background: var(--primary-color, #722ed1) !important;
  color: #fff !important;
}

.enhanced-settings-tabs .ant-tabs-tab[aria-selected="true"] .ant-tabs-tab-btn {
  color: #fff !important;
}

.enhanced-settings-tabs .ant-tabs-tab[aria-selected="true"] .anticon {
  color: #fff !important;
}

/* Ensure consistent tab height */
/* .enhanced-settings-tabs .ant-tabs-tab {
  min-height: 48px !important;
  display: flex !important;
  align-items: center !important;
} */

/* Enhanced Form Responsive Design */
@media (max-width: 768px) {
  .enhanced-settings-tabs .ant-tabs-nav {
    width: 200px;
  }

  .enhanced-settings-tabs .ant-tabs-tabpane {
    padding: 20px;
  }

  .settings-content-wrapper {
    max-width: 100%;
  }

  .enhanced-form-container {
    padding: 16px;
    margin-top: 12px;
  }

  .form-field-group {
    padding: 16px;
    margin-bottom: 16px;
  }

  .form-field-group-title {
    font-size: 14px;
    margin-bottom: 12px;
  }

  .form-actions-container {
    padding: 12px 16px;
    flex-direction: column;
    align-items: stretch;
  }

  .form-actions-container .ant-btn {
    margin: 4px 0;
    width: 100%;
  }

  /* Mobile tab adjustments */
  .enhanced-settings-tabs .ant-tabs-tab {
    min-height: 44px !important;
    padding: 10px 12px !important;
  }

  /* Mobile sub-tab adjustments */
  .enhanced-settings-form .ant-tabs.ant-tabs-top .ant-tabs-tab {
    padding: 6px 12px !important;
    font-size: 13px;
  }

  .enhanced-settings-form .ant-tabs.ant-tabs-top .ant-tabs-content-holder {
    padding: 16px;
  }
}

@media (max-width: 576px) {
  .enhanced-settings-container {
    border-radius: 0;
    box-shadow: none;
  }

  .enhanced-settings-tabs {
    min-height: auto;
  }

  .enhanced-settings-tabs .ant-tabs-nav {
    width: 160px;
  }

  .enhanced-settings-tabs .ant-tabs-tab {
    padding: 10px 12px;
  }

  .enhanced-settings-tabs .ant-tabs-tab-btn {
    font-size: 13px;
  }

  .enhanced-settings-tabs .ant-tabs-tab-btn .anticon {
    margin-right: 8px;
    font-size: 14px;
  }

  .enhanced-settings-tabs .ant-tabs-tabpane {
    padding: 16px;
  }
}

/* Sub-tabs styling (horizontal tabs within content) */
.enhanced-settings-form .ant-tabs {
  background: transparent;
}

.enhanced-settings-form .ant-tabs.ant-tabs-top > .ant-tabs-nav {
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
  border-radius: 8px 8px 0 0;
  margin-bottom: 0;
  padding: 8px 16px 0;
}

.enhanced-settings-form .ant-tabs.ant-tabs-top .ant-tabs-tab {
  margin: 0 4px !important;
  padding: 8px 16px !important;
  border-radius: 6px 6px 0 0 !important;
  border: none !important;
  background: transparent !important;
  transition: all 0.3s ease !important;
  position: relative;
}

.enhanced-settings-form .ant-tabs.ant-tabs-top .ant-tabs-tab:hover {
  background: #fff !important;
  color: var(--primary-color, #722ed1) !important;
}

.enhanced-settings-form .ant-tabs.ant-tabs-top .ant-tabs-tab-active {
  background: #fff !important;
  color: var(--primary-color, #722ed1) !important;
  border-bottom: 2px solid var(--primary-color, #722ed1) !important;
  font-weight: 500 !important;
}

.enhanced-settings-form .ant-tabs.ant-tabs-top .ant-tabs-tab-active:hover {
  background: #fff !important;
  color: var(--primary-color, #722ed1) !important;
}

.enhanced-settings-form .ant-tabs.ant-tabs-top .ant-tabs-tab-btn {
  color: #666 !important;
  font-weight: 400;
}

.enhanced-settings-form .ant-tabs.ant-tabs-top .ant-tabs-tab-active .ant-tabs-tab-btn {
  color: var(--primary-color, #722ed1) !important;
  font-weight: 500 !important;
}

.enhanced-settings-form .ant-tabs.ant-tabs-top .ant-tabs-tab:hover .ant-tabs-tab-btn {
  color: var(--primary-color, #722ed1) !important;
}

.enhanced-settings-form .ant-tabs.ant-tabs-top .ant-tabs-ink-bar {
  background: var(--primary-color, #722ed1) !important;
  height: 2px !important;
}

.enhanced-settings-form .ant-tabs.ant-tabs-top .ant-tabs-content-holder {
  background: #fff;
  border: 1px solid #e9ecef;
  border-top: none;
  border-radius: 0 0 8px 8px;
  padding: 20px;
}

.enhanced-settings-form .ant-tabs.ant-tabs-top .ant-tabs-tabpane {
  padding: 0;
}

/* Sub-tab icons styling */
.enhanced-settings-form .ant-tabs.ant-tabs-top .ant-tabs-tab .anticon {
  margin-right: 6px;
  font-size: 14px;
}

/* Dark mode support */
.enhanced-settings-container.dark-mode {
  background: #1f1f1f;
  color: #fff;
}

.enhanced-settings-container.dark-mode .enhanced-settings-tabs .ant-tabs-nav {
  background: #262626;
  border-right-color: #434343;
}

.enhanced-settings-container.dark-mode .enhanced-settings-tabs .ant-tabs-tab:hover {
  background: rgba(24, 144, 255, 0.15);
}

.enhanced-settings-container.dark-mode .enhanced-settings-tabs .ant-tabs-content-holder {
  background: #1f1f1f;
}

.enhanced-settings-container.dark-mode .settings-card-section {
  background: #262626;
  border-color: #434343;
}

.enhanced-settings-container.dark-mode .settings-section-header .ant-divider {
  color: #fff;
}

.enhanced-settings-container.dark-mode .settings-section-header .ant-divider::before,
.enhanced-settings-container.dark-mode .settings-section-header .ant-divider::after {
  border-top-color: #434343;
}

/* Dark mode for sub-tabs */
.enhanced-settings-container.dark-mode .enhanced-settings-form .ant-tabs.ant-tabs-top > .ant-tabs-nav {
  background: #262626;
  border-bottom-color: #434343;
}

.enhanced-settings-container.dark-mode .enhanced-settings-form .ant-tabs.ant-tabs-top .ant-tabs-tab:hover {
  background: #1f1f1f !important;
}

.enhanced-settings-container.dark-mode .enhanced-settings-form .ant-tabs.ant-tabs-top .ant-tabs-tab-active {
  background: #1f1f1f !important;
}

.enhanced-settings-container.dark-mode .enhanced-settings-form .ant-tabs.ant-tabs-top .ant-tabs-content-holder {
  background: #1f1f1f;
  border-color: #434343;
}
