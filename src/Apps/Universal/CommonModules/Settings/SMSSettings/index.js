import React, { useState, useEffect } from 'react';
import SMSConfiguration from "../SMSConfiguration";
import SMSMessages from "../SMSMessages";
import SMSTemplates from "../SMSTemplates";
import { APP_SETTINGS  } from "../../../../../Utils/constants";
import { Card, Form, Input, Switch, Button, Select, Divider, message, Space, Alert, Typography, Row, Col, Modal, Tabs  } from "antd";
import { SaveOutlined, SendOutlined, InfoCircleOutlined, MessageOutlined  } from "@ant-design/icons";
import { filterSettingsData  } from "../utils/settingsDataFilter";
import { sendSMSWithDB  } from "../../../../../Utils/functions";


const { Text, Paragraph } = Typography;
const { Option } = Select;
const { TabPane } = Tabs;

const SMSSettings = ({ pouchDatabase, databasePrefix, currentUser, modules }) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [testLoading, setTestLoading] = useState(false);
  const [initialValues, setInitialValues] = useState({});
  const [templates, setTemplates] = useState([]);
  const [selectedTemplate, setSelectedTemplate] = useState(null);
  const [messagesRefreshKey, setMessagesRefreshKey] = useState(0);

  // Watch form values for conditional rendering
  const enableSms = Form.useWatch('enable_sms', form);

  // Load current settings and templates
  useEffect(() => {
    loadSettings();
    loadTemplates();
  }, []);

  const loadTemplates = async () => {
    try {
      const result = await pouchDatabase('sms_templates', databasePrefix).getAllData();
      setTemplates(result);
    } catch (error) {
      console.error('Error loading SMS templates:', error);
    }
  };

  const loadSettings = async () => {
    try {
      // Load from database
      const result = await pouchDatabase('settings', databasePrefix).getAllData();

      // CRITICAL FIX: Filter out contaminated data from other collections
      const settingsData = filterSettingsData(result);

      const dbSettings = settingsData[0] || {};

      // Also check localStorage for current values
      const appSettings = localStorage.getItem(APP_SETTINGS);
      const localSettings = appSettings ? JSON.parse(appSettings) : {};

      // Merge settings with priority to database values
      const mergedSettings = {
        enable_sms: false,
        sms_provider: 'egosms',
        sms_username: '',
        sms_password: '',
        sms_sender: '',
        ...localSettings,
        ...dbSettings
      };

      setInitialValues(mergedSettings);
      form.setFieldsValue(mergedSettings);
    } catch (error) {
      console.error('Error loading SMS settings:', error);
      message.error('Failed to load SMS settings');
    }
  };

  const handleSave = async (values) => {
    try {
      setLoading(true);

      // Get current settings document
      const result = await pouchDatabase('settings', databasePrefix).getAllData();
      const currentSettings = result[0] || {};

      // Merge with new SMS settings
      const updatedSettings = {
        ...currentSettings,
        ...values,
        updatedAt: new Date().toISOString(),
        updatedBy: currentUser._id
      };

      // Save to database
      await pouchDatabase('settings', databasePrefix).saveDocument(
        updatedSettings,
        {
          key: currentUser._id,
          label: `${currentUser.first_name} ${currentUser.last_name}`,
          value: currentUser._id,
        }
      );

      // Update localStorage immediately
      localStorage.setItem(APP_SETTINGS, JSON.stringify(updatedSettings));

      // Trigger storage event for other components
      window.dispatchEvent(new StorageEvent('storage', {
        key: APP_SETTINGS,
        newValue: JSON.stringify(updatedSettings),
        storageArea: localStorage
      }));

      message.success('SMS settings saved successfully!');
      setInitialValues(updatedSettings);
    } catch (error) {
      console.error('Error saving SMS settings:', error);
      message.error('Failed to save SMS settings');
    } finally {
      setLoading(false);
    }
  };

  const handleTestSMS = async () => {
    try {
      setTestLoading(true);

      // Get current form values
      const values = form.getFieldsValue();

      if (!values.enable_sms) {
        message.warning('Please enable SMS first');
        return;
      }

      if (!values.sms_username || !values.sms_password) {
        message.warning('Please configure SMS credentials first');
        return;
      }

      if (!values.test_phone_number) {
        message.warning('Please enter a test phone number');
        return;
      }

      // Temporarily update localStorage with current form values for testing
      const currentAppSettings = JSON.parse(localStorage.getItem(APP_SETTINGS) || '{}');
      const testSettings = { ...currentAppSettings, ...values };
      localStorage.setItem(APP_SETTINGS, JSON.stringify(testSettings));

      console.log('Testing SMS with settings:', {
        username: values.sms_username,
        sender: values.sms_sender,
        phone: values.test_phone_number,
        provider: values.sms_provider
      });

      // Prepare test message
      let testMessage;
      if (selectedTemplate) {
        const template = templates.find(t => t._id === selectedTemplate);
        if (template) {
          // Replace template variables with sample data
          testMessage = template.message
            .replace(/\{\{customer_name\}\}/g, currentUser.first_name + ' ' + currentUser.last_name)
            .replace(/\{\{amount\}\}/g, '100,000 UGX')
            .replace(/\{\{document_id\}\}/g, 'TEST-001')
            .replace(/\{\{company_name\}\}/g, values.sms_sender || 'Your Company')
            .replace(/\{\{date\}\}/g, new Date().toLocaleDateString())
            .replace(/\{\{due_date\}\}/g, new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toLocaleDateString())
            .replace(/\{\{balance\}\}/g, '50,000 UGX');
        } else {
          testMessage = `Test SMS from ${currentUser.first_name} ${currentUser.last_name} at ${new Date().toLocaleString()}`;
        }
      } else {
        testMessage = `Test SMS from ${currentUser.first_name} ${currentUser.last_name} at ${new Date().toLocaleString()}`;
      }

      const result = await sendSMSWithDB(
        values.test_phone_number,
        testMessage,
        {},
        pouchDatabase,
        databasePrefix,
        currentUser
      );

      console.log('SMS Test Result:', result);

      // Check for CORS error (which means SMS was likely sent successfully)
      if (result.corsError) {
        Modal.success({
          title: 'Test SMS Sent Successfully!',
          content: (
            <div>
              <p>✅ <strong>SMS has been sent successfully!</strong></p>
              <p><strong>Phone:</strong> {values.test_phone_number}</p>
              <p><strong>Note:</strong> CORS error is expected when testing from browser - this doesn't affect SMS delivery.</p>
              <p>Check your phone for the message. You can view this message in the Messages tab.</p>
            </div>
          ),
          width: 500
        });
      } else if (result.error) {
        Modal.error({
          title: 'Test SMS Failed',
          content: (
            <div>
              <p><strong>Error:</strong> {result.error}</p>
              {result.smsDocument && (
                <div>
                  <p><strong>Status:</strong> {result.smsDocument.status}</p>
                  {result.smsDocument.api_response && (
                    <p><strong>API Response:</strong> {result.smsDocument.api_response}</p>
                  )}
                </div>
              )}
              <p>Please check your SMS credentials and try again.</p>
            </div>
          ),
          width: 500
        });
      } else if (result.response && result.response.data) {
        // Check the actual API response
        const apiResponse = result.response.data;
        console.log('EgoSMS API Response:', apiResponse);

        if (typeof apiResponse === 'string' && apiResponse.toLowerCase().includes('error')) {
          Modal.error({
            title: 'SMS API Error',
            content: (
              <div>
                <p><strong>API Response:</strong> {apiResponse}</p>
                <p>Please check your EgoSMS credentials and account balance.</p>
              </div>
            ),
            width: 500
          });
        } else {
          Modal.success({
            title: 'Test SMS Sent',
            content: (
              <div>
                <p>✅ SMS has been sent successfully!</p>
                <p><strong>Phone:</strong> {values.test_phone_number}</p>
                <p><strong>API Response:</strong> {apiResponse}</p>
                <p>Check your phone for the message. If you don't receive it, please verify your phone number and account balance.</p>
                <p>You can view this message in the Messages tab.</p>
              </div>
            ),
            width: 500
          });
        }
      } else {
        message.success('Test SMS sent successfully! Check your phone.');
      }

      // Refresh the messages list
      setMessagesRefreshKey(prev => prev + 1);

      // Restore original settings
      localStorage.setItem(APP_SETTINGS, JSON.stringify(currentAppSettings));
    } catch (error) {
      console.error('Error sending test SMS:', error);
      Modal.error({
        title: 'Test SMS Error',
        content: (
          <div>
            <p><strong>Error:</strong> {error.message}</p>
            <p>Please check the browser console for more details.</p>
          </div>
        )
      });
    } finally {
      setTestLoading(false);
    }
  };

  const showProviderInfo = () => {
    Modal.info({
      title: 'SMS Provider Information & Troubleshooting',
      width: 700,
      content: (
        <div>
          <Paragraph>
            <strong>EgoSMS (Default Provider)</strong>
          </Paragraph>
          <Paragraph>
            EgoSMS is a reliable SMS gateway service. To get your credentials:
          </Paragraph>
          <ol>
            <li>Visit <a href="https://www.egosms.co" target="_blank" rel="noopener noreferrer">www.egosms.co</a></li>
            <li>Create an account or log in</li>
            <li>Navigate to API settings</li>
            <li>Copy your username and password</li>
            <li>Set your sender ID (usually your company name, max 11 characters)</li>
          </ol>

          <Divider />

          <Paragraph>
            <strong>Troubleshooting SMS Issues</strong>
          </Paragraph>

          <Alert
            message="Common Issues"
            description={
              <ul style={{ marginBottom: 0, paddingLeft: 20 }}>
                <li><strong>CORS/Network Error:</strong> This is normal when testing from browser - SMS still gets sent</li>
                <li><strong>No SMS received:</strong> Check account balance, verify phone number format</li>
                <li><strong>Invalid credentials:</strong> Verify username and password are correct</li>
                <li><strong>Sender ID issues:</strong> Use alphanumeric sender ID (max 11 chars)</li>
                <li><strong>Phone number format:</strong> Use international format (+************)</li>
              </ul>
            }
            type="warning"
            showIcon
            style={{ marginBottom: 16 }}
          />

          <Alert
            message="Account Requirements"
            description="Make sure you have sufficient SMS credits in your EgoSMS account and your account is active."
            type="info"
            showIcon
          />

          <Paragraph style={{ marginTop: 16 }}>
            <strong>Need Help?</strong> Use the "Debug Settings" button to check your configuration,
            and check the browser console (F12) for detailed error messages.
          </Paragraph>
        </div>
      ),
    });
  };

  const renderSettingsTab = () => (
    <Form
      form={form}
      layout="vertical"
      onFinish={handleSave}
      initialValues={initialValues}
    >
      <Alert
        message="SMS Configuration"
        description="Configure SMS settings to enable text message notifications for invoices, receipts, and other documents."
        type="info"
        showIcon
        style={{ marginBottom: 24 }}
      />

      <Divider orientation="left">General Settings</Divider>

      <Row gutter={16}>
        <Col span={12}>
          <Form.Item
            name="enable_sms"
            label="Enable SMS Notifications"
            valuePropName="checked"
            tooltip="Turn on/off SMS notifications globally"
          >
            <Switch />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item
            name="sms_provider"
            label="SMS Provider"
            tooltip="Select your SMS service provider"
          >
            <Select>
              <Option value="egosms">EgoSMS</Option>
              <Option value="twilio" disabled>Twilio (Coming Soon)</Option>
              <Option value="nexmo" disabled>Nexmo (Coming Soon)</Option>
            </Select>
          </Form.Item>
        </Col>
      </Row>

      <Divider orientation="left">Provider Credentials</Divider>

      <Row gutter={16}>
        <Col span={12}>
          <Form.Item
            name="sms_username"
            label="Username"
            rules={[
              {
                required: enableSms,
                message: 'Username is required when SMS is enabled'
              }
            ]}
            tooltip="Your SMS provider username"
          >
            <Input placeholder="Enter your SMS username" />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item
            name="sms_password"
            label="Password"
            rules={[
              {
                required: enableSms,
                message: 'Password is required when SMS is enabled'
              }
            ]}
            tooltip="Your SMS provider password"
          >
            <Input.Password placeholder="Enter your SMS password" />
          </Form.Item>
        </Col>
      </Row>

      <Row gutter={16}>
        <Col span={12}>
          <Form.Item
            name="sms_sender"
            label="Sender ID"
            tooltip="The name that appears as the sender (usually your company name)"
          >
            <Input placeholder="e.g., YourCompany" maxLength={11} />
          </Form.Item>
        </Col>
      </Row>

      <Divider orientation="left">Test SMS</Divider>

      <Alert
        message="SMS Testing"
        description="Use this section to test your SMS configuration. Make sure to save your settings first, then enter a valid phone number to receive a test message."
        type="info"
        showIcon
        style={{ marginBottom: 16 }}
      />

      <Row gutter={16}>
        <Col span={8}>
          <Form.Item
            name="test_phone_number"
            label="Test Phone Number"
            tooltip="Enter a phone number to test SMS functionality"
            rules={[
              {
                pattern: /^(\+?[1-9]\d{1,14})$/,
                message: 'Please enter a valid phone number (e.g., +************)'
              }
            ]}
          >
            <Input
              placeholder="+************"
              addonBefore="📱"
            />
          </Form.Item>
        </Col>
        <Col span={8}>
          <Form.Item
            label="Test Template (Optional)"
            tooltip="Select a template to test with"
          >
            <Select
              placeholder="Select template or use default"
              allowClear
              value={selectedTemplate}
              onChange={setSelectedTemplate}
            >
              {templates.map(template => (
                <Option key={template._id} value={template._id}>
                  {template.name} ({template.category})
                </Option>
              ))}
            </Select>
          </Form.Item>
        </Col>
        <Col span={8}>
          <Form.Item label=" ">
            <Space>
              <Button
                icon={<SendOutlined />}
                onClick={handleTestSMS}
                loading={testLoading}
                disabled={!enableSms}
              >
                Send Test SMS
              </Button>
              <Button
                onClick={() => {
                  console.log('Current SMS Settings:', window.checkSMSSettings());
                  message.info('SMS settings logged to console. Press F12 to view.');
                }}
                type="link"
              >
                Debug Settings
              </Button>
            </Space>
          </Form.Item>
        </Col>
      </Row>

      <Divider />

      <Form.Item>
        <Space>
          <Button
            type="primary"
            htmlType="submit"
            icon={<SaveOutlined />}
            loading={loading}
          >
            Save SMS Settings
          </Button>
          <Button onClick={loadSettings}>
            Reset
          </Button>
        </Space>
      </Form.Item>
    </Form>
  );

  return (
    <Card
      title={
        <Space>
          <MessageOutlined />
          SMS Settings
        </Space>
      }
      extra={
        <Button
          icon={<InfoCircleOutlined />}
          onClick={showProviderInfo}
          type="link"
        >
          Provider Info
        </Button>
      }
    >
      <Tabs defaultActiveKey="settings">
        <TabPane tab="Settings" key="settings">
          {renderSettingsTab()}
        </TabPane>
        <TabPane tab="Configuration" key="configuration">
          <SMSConfiguration
            pouchDatabase={pouchDatabase}
            databasePrefix={databasePrefix}
            currentUser={currentUser}
            modules={modules}
          />
        </TabPane>
        <TabPane tab="Templates" key="templates">
          <SMSTemplates
            pouchDatabase={pouchDatabase}
            databasePrefix={databasePrefix}
            currentUser={currentUser}
          />
        </TabPane>
        <TabPane tab="Messages" key="messages">
          <SMSMessages
            pouchDatabase={pouchDatabase}
            databasePrefix={databasePrefix}
            currentUser={currentUser}
            refreshKey={messagesRefreshKey}
          />
        </TabPane>
      </Tabs>
    </Card>
  );
};

export default SMSSettings;
