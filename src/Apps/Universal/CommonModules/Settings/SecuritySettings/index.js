import "./security-settings.css";
import ActiveSessions from "./ActiveSessions";
import React, { useEffect, useState } from "react";
import { APP_SETTINGS  } from "../../../../../Utils/constants";
import { Form, Button, Divider, message, InputNumber, Select, Card, Switch, Tabs, Slider, Input, Row, Col, Space, Typography, notification  } from "antd";
import { LockOutlined, UserOutlined, ClockCircleOutlined, KeyOutlined, EyeOutlined, SafetyOutlined, TeamOutlined, CheckCircleOutlined  } from "@ant-design/icons";
import { filterSettingsData  } from "../utils/settingsDataFilter";
import { setupBrowserCloseHandler  } from "../../../../../Utils/SecurityUtils";


const { Option } = Select;
const { TabPane } = Tabs;
const { Text } = Typography;

const SecuritySettings = (props) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  const { databasePrefix, pouchDatabase, currentUser } = props;
  const collection = "settings";

  useEffect(() => {
    const loadSettings = async () => {
      try {
        setLoading(true);

        // Get all settings data
        const allSettings = await pouchDatabase(collection, databasePrefix).getAllData();

        // CRITICAL FIX: Filter out contaminated data from other collections
        const settingsData = filterSettingsData(allSettings);

        // Find security settings document
        const securitySettings = settingsData.find(doc => doc.type === 'security_settings');

        if (securitySettings) {
          form.setFieldsValue(securitySettings.settings || {});
        }
      } catch (error) {
        console.error('Error loading security settings:', error);
      } finally {
        setLoading(false);
      }
    };

    loadSettings();
  }, [databasePrefix, pouchDatabase, form, collection]);

  const handleSaveSettings = async (values) => {
    try {
      setLoading(true);

      // Get all settings data
      const allSettings = await pouchDatabase(collection, databasePrefix).getAllData();

      // Find security settings document
      const existingSettings = allSettings.find(doc => doc.type === 'security_settings');

      // Save to database
      if (existingSettings) {
        // Update existing document
        await pouchDatabase(collection, databasePrefix).saveDocument({
          ...existingSettings,
          settings: values,
          updatedAt: new Date().toISOString(),
        }, {
          key: currentUser._id,
          label: `${currentUser.first_name} ${currentUser.last_name}`,
          value: currentUser._id,
        });
      } else {
        // Create new settings document
        await pouchDatabase(collection, databasePrefix).saveDocument({
          type: 'security_settings',
          settings: values,
          createdAt: new Date().toISOString(),
        }, {
          key: currentUser._id,
          label: `${currentUser.first_name} ${currentUser.last_name}`,
          value: currentUser._id,
        });
      }

      // Update localStorage for immediate use
      const appSettings = JSON.parse(localStorage.getItem(APP_SETTINGS) || "{}");

      // Ensure values have the correct types
      const processedValues = {
        ...values,
        enableAutoLock: Boolean(values.enableAutoLock),
        lockscreenTimeout: Number(values.lockscreenTimeout || 5),
        sessionTimeout: Number(values.sessionTimeout || 30),
        forceLogoutOnBrowserClose: Boolean(values.forceLogoutOnBrowserClose),
        allowConcurrentSessions: Boolean(values.allowConcurrentSessions),
        maxLoginAttempts: Number(values.maxLoginAttempts || 5),
        accountLockoutDuration: Number(values.accountLockoutDuration || 15),
        enableAccountLockout: Boolean(values.enableAccountLockout),
      };

      const updatedAppSettings = {
        ...appSettings,
        security: processedValues
      };

      // Save to localStorage
      localStorage.setItem(APP_SETTINGS, JSON.stringify(updatedAppSettings));

      console.log('Security settings saved to localStorage:', {
        processedValues,
        updatedAppSettings
      });

      // Trigger a storage event so other components can react to the change
      window.dispatchEvent(new StorageEvent('storage', {
        key: APP_SETTINGS,
        newValue: JSON.stringify(updatedAppSettings),
        storageArea: localStorage
      }));

      // Also dispatch a custom event for components that might not catch the storage event
      window.dispatchEvent(new CustomEvent('storage-updated'));

      console.log('Security settings saved:', values);

      // Update browser close handler
      setupBrowserCloseHandler();

      // Show success notification
      notification.success({
        message: 'Security Settings Saved',
        description: 'Your security settings have been updated successfully.',
        icon: <CheckCircleOutlined style={{ color: '#52c41a' }} />,
        duration: 4,
      });
    } catch (error) {
      console.error('Error saving security settings:', error);
      notification.error({
        message: 'Failed to Save Settings',
        description: 'There was an error saving your security settings. Please try again.',
        duration: 4,
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div>
      <Divider orientation="left">Security Settings</Divider>
      <Form
        form={form}
        layout="vertical"
        onFinish={handleSaveSettings}
        initialValues={{
          // Session settings
          lockscreenTimeout: 5,
          enableAutoLock: true,
          sessionTimeout: 30,
          forceLogoutOnBrowserClose: false,
          allowConcurrentSessions: true,

          // Password policy
          minPasswordLength: 8,
          requireUppercase: true,
          requireLowercase: true,
          requireNumbers: true,
          requireSpecialChars: false,
          passwordExpiryDays: 90,
          preventPasswordReuse: true,
          passwordHistoryCount: 5,

          // Login security
          maxLoginAttempts: 5,
          accountLockoutDuration: 15,
          rememberDeviceDays: 30,
          requirePasswordForSensitiveOperations: true
        }}
      >
        <Tabs defaultActiveKey="1" className="security-tabs">
          <TabPane
            tab={<span><ClockCircleOutlined /> Session Management</span>}
            key="1"
          >
            <Card title="Auto Lock Settings" className="security-card">
              <Form.Item
                name="enableAutoLock"
                label="Enable Auto Lock"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>

              <Form.Item
                name="lockscreenTimeout"
                label="Lockscreen Timeout (minutes)"
                rules={[
                  { required: true, message: 'Please enter lockscreen timeout' },
                  { type: 'number', min: 1, max: 60, message: 'Timeout must be between 1 and 60 minutes' }
                ]}
                extra={
                  <Button
                    type="link"
                    onClick={() => {
                      localStorage.setItem("LOCKED", "true");
                      message.success("Screen locked for testing");
                      // Dispatch a custom event to notify the LockScreen component
                      window.dispatchEvent(new CustomEvent('app-lock-screen'));
                    }}
                    style={{ padding: 0 }}
                  >
                    Test lock screen now
                  </Button>
                }
              >
                <InputNumber min={1} max={60} style={{ width: '100%' }} />
              </Form.Item>

              <Form.Item
                name="sessionTimeout"
                label="Session Timeout (minutes)"
                tooltip="How long until an inactive session expires and requires re-login"
                rules={[
                  { required: true, message: 'Please enter session timeout' },
                  { type: 'number', min: 5, max: 1440, message: 'Timeout must be between 5 minutes and 24 hours' }
                ]}
              >
                <InputNumber min={5} max={1440} style={{ width: '100%' }} />
              </Form.Item>

              <Form.Item
                name="forceLogoutOnBrowserClose"
                label="Force Logout on Browser Close"
                tooltip="When enabled, users will be logged out when they close their browser"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>

              <Form.Item
                name="allowConcurrentSessions"
                label="Allow Multiple Active Sessions"
                tooltip="When enabled, users can be logged in from multiple devices simultaneously"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>
            </Card>

            <ActiveSessions pouchDatabase={pouchDatabase} databasePrefix={databasePrefix} />
          </TabPane>

          <TabPane
            tab={<span><KeyOutlined /> Password Policy</span>}
            key="2"
          >
            <Card title="Password Requirements" className="security-card">
              <Form.Item
                name="minPasswordLength"
                label="Minimum Password Length"
                rules={[
                  { required: true, message: 'Please specify minimum password length' },
                  { type: 'number', min: 6, max: 32, message: 'Length must be between 6 and 32 characters' }
                ]}
              >
                <Slider min={6} max={32} marks={{ 6: '6', 12: '12', 18: '18', 24: '24', 32: '32' }} />
              </Form.Item>

              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    name="requireUppercase"
                    label="Require Uppercase Letters"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name="requireLowercase"
                    label="Require Lowercase Letters"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                </Col>
              </Row>

              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    name="requireNumbers"
                    label="Require Numbers"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name="requireSpecialChars"
                    label="Require Special Characters"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                </Col>
              </Row>
            </Card>

            <Card title="Password Expiration" className="security-card" style={{ marginTop: 16 }}>
              <Form.Item
                name="passwordExpiryDays"
                label="Password Expiration (days)"
                tooltip="Number of days before users are required to change their password (0 = never expire)"
                rules={[
                  { required: true, message: 'Please specify password expiration period' }
                ]}
              >
                <InputNumber min={0} max={365} style={{ width: '100%' }} />
              </Form.Item>

              <Form.Item
                name="preventPasswordReuse"
                label="Prevent Password Reuse"
                tooltip="When enabled, users cannot reuse their recent passwords"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>

              <Form.Item
                name="passwordHistoryCount"
                label="Password History Count"
                tooltip="Number of previous passwords that cannot be reused"
                dependencies={['preventPasswordReuse']}
                rules={[
                  { required: true, message: 'Please specify password history count' }
                ]}
              >
                <InputNumber
                  min={1}
                  max={20}
                  style={{ width: '100%' }}
                  disabled={form.getFieldValue('preventPasswordReuse') === false}
                />
              </Form.Item>
            </Card>
          </TabPane>

          <TabPane
            tab={<span><UserOutlined /> Login Security</span>}
            key="3"
          >
            <Card title="Login Attempt Limits" className="security-card">
              <Form.Item
                name="maxLoginAttempts"
                label="Maximum Failed Login Attempts"
                tooltip="Number of failed login attempts before account is temporarily locked"
                rules={[
                  { required: true, message: 'Please specify maximum login attempts' },
                  { type: 'number', min: 3, max: 10, message: 'Value must be between 3 and 10' }
                ]}
              >
                <InputNumber min={3} max={10} style={{ width: '100%' }} />
              </Form.Item>

              <Form.Item
                name="accountLockoutDuration"
                label="Account Lockout Duration (minutes)"
                tooltip="How long an account remains locked after too many failed login attempts"
                rules={[
                  { required: true, message: 'Please specify lockout duration' },
                  { type: 'number', min: 5, max: 1440, message: 'Duration must be between 5 minutes and 24 hours' }
                ]}
              >
                <InputNumber min={5} max={1440} style={{ width: '100%' }} />
              </Form.Item>
            </Card>

            <Card title="Authentication Options" className="security-card" style={{ marginTop: 16 }}>
              <Form.Item
                name="rememberDeviceDays"
                label="Remember Device Duration (days)"
                tooltip="How long to remember a device for login (0 = don't remember)"
                rules={[
                  { required: true, message: 'Please specify remember device duration' }
                ]}
              >
                <InputNumber min={0} max={365} style={{ width: '100%' }} />
              </Form.Item>

              <Form.Item
                name="requirePasswordForSensitiveOperations"
                label="Require Password for Sensitive Operations"
                tooltip="When enabled, users must re-enter their password for sensitive operations like changing settings"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>
            </Card>
          </TabPane>
        </Tabs>

        <div style={{ marginTop: 16, textAlign: 'right' }}>
          <Space>
            <Button onClick={() => form.resetFields()}>
              Reset to Defaults
            </Button>
            <Button type="primary" htmlType="submit" loading={loading}>
              Save All Settings
            </Button>
          </Space>
        </div>
      </Form>
    </div>
  );
};

export default SecuritySettings;
