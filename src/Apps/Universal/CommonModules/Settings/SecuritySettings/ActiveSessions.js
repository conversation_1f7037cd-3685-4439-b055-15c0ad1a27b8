import React, { useState, useEffect } from 'react';
import { DesktopOutlined, MobileOutlined, TabletOutlined, GlobalOutlined, ClockCircleOutlined, UserOutlined, LogoutOutlined  } from "@ant-design/icons";
import { LOCAL_STORAGE_USER  } from "../../../../../Utils/constants";
import { Table, Button, Card, Typography, Space, Popconfirm, Tag, Tooltip, Empty  } from "antd";
import { getActiveSessions, endSession  } from "../../../../../Utils/SecurityUtils";
import { userLogout  } from "../../../../../Utils/functions";


const { Text, Title } = Typography;

const ActiveSessions = ({ pouchDatabase, databasePrefix }) => {
  const [sessions, setSessions] = useState([]);
  const [loading, setLoading] = useState(false);
  const [currentSessionId, setCurrentSessionId] = useState(null);
  
  // Get current user
  const currentUser = JSON.parse(localStorage.getItem(LOCAL_STORAGE_USER));
  
  // Get current session ID
  useEffect(() => {
    try {
      const sessionData = JSON.parse(localStorage.getItem('SESSION_DATA') || '{}');
      setCurrentSessionId(sessionData.id);
    } catch (error) {
      console.error('Error getting current session ID:', error);
    }
  }, []);
  
  // Load active sessions
  const loadSessions = () => {
    if (!currentUser || !currentUser._id) return;
    
    setLoading(true);
    try {
      const activeSessions = getActiveSessions(currentUser._id);
      setSessions(activeSessions);
    } catch (error) {
      console.error('Error loading active sessions:', error);
    } finally {
      setLoading(false);
    }
  };
  
  // Load sessions on mount and when current user changes
  useEffect(() => {
    loadSessions();
    
    // Refresh sessions every 30 seconds
    const intervalId = setInterval(loadSessions, 30000);
    
    return () => clearInterval(intervalId);
  }, [currentUser]);
  
  // Format date for display
  const formatDate = (dateString) => {
    if (!dateString) return '';
    
    const date = new Date(dateString);
    return date.toLocaleString(undefined, {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };
  
  // Format time ago
  const formatTimeAgo = (dateString) => {
    if (!dateString) return '';
    
    const date = new Date(dateString);
    const now = new Date();
    const diffMs = now - date;
    
    // Convert to seconds, minutes, hours, days
    const diffSec = Math.floor(diffMs / 1000);
    const diffMin = Math.floor(diffSec / 60);
    const diffHrs = Math.floor(diffMin / 60);
    const diffDays = Math.floor(diffHrs / 24);
    
    if (diffDays > 0) {
      return `${diffDays} day${diffDays !== 1 ? 's' : ''} ago`;
    } else if (diffHrs > 0) {
      return `${diffHrs} hour${diffHrs !== 1 ? 's' : ''} ago`;
    } else if (diffMin > 0) {
      return `${diffMin} minute${diffMin !== 1 ? 's' : ''} ago`;
    } else {
      return 'Just now';
    }
  };
  
  // Get device icon based on user agent
  const getDeviceIcon = (deviceInfo) => {
    if (!deviceInfo || !deviceInfo.userAgent) {
      return <GlobalOutlined />;
    }
    
    const userAgent = deviceInfo.userAgent.toLowerCase();
    
    if (/mobile|android|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(userAgent)) {
      if (/ipad|tablet/i.test(userAgent)) {
        return <TabletOutlined />;
      }
      return <MobileOutlined />;
    }
    
    return <DesktopOutlined />;
  };
  
  // Get device name based on user agent
  const getDeviceName = (deviceInfo) => {
    if (!deviceInfo || !deviceInfo.userAgent) {
      return 'Unknown Device';
    }
    
    const userAgent = deviceInfo.userAgent;
    let deviceType = 'Desktop';
    let os = 'Unknown OS';
    let browser = 'Unknown Browser';
    
    // Detect device type
    if (/mobile|android|iphone|ipod|blackberry|iemobile|opera mini/i.test(userAgent.toLowerCase())) {
      deviceType = 'Mobile';
    } else if (/ipad|tablet/i.test(userAgent.toLowerCase())) {
      deviceType = 'Tablet';
    }
    
    // Detect OS
    if (/windows/i.test(userAgent)) {
      os = 'Windows';
    } else if (/macintosh|mac os x/i.test(userAgent)) {
      os = 'macOS';
    } else if (/linux/i.test(userAgent)) {
      os = 'Linux';
    } else if (/android/i.test(userAgent)) {
      os = 'Android';
    } else if (/iphone|ipad|ipod/i.test(userAgent)) {
      os = 'iOS';
    }
    
    // Detect browser
    if (/edge/i.test(userAgent)) {
      browser = 'Edge';
    } else if (/chrome/i.test(userAgent)) {
      browser = 'Chrome';
    } else if (/firefox/i.test(userAgent)) {
      browser = 'Firefox';
    } else if (/safari/i.test(userAgent)) {
      browser = 'Safari';
    } else if (/msie|trident/i.test(userAgent)) {
      browser = 'Internet Explorer';
    }
    
    return `${deviceType} - ${os} - ${browser}`;
  };
  
  // End a session
  const endUserSession = (sessionId) => {
    if (!currentUser || !currentUser._id) return;
    
    // If ending current session, log out
    if (sessionId === currentSessionId) {
      userLogout(
        "logs",
        currentUser,
        databasePrefix,
        pouchDatabase
      );
      return;
    }
    
    // Otherwise, just remove the session
    setLoading(true);
    try {
      // Remove the session
      const activeSessions = getActiveSessions(currentUser._id);
      const sessionToEnd = activeSessions.find(session => session.id === sessionId);
      
      if (sessionToEnd) {
        // Remove the session
        const updatedSessions = activeSessions.filter(session => session.id !== sessionId);
        localStorage.setItem('ACTIVE_SESSIONS', JSON.stringify({
          ...JSON.parse(localStorage.getItem('ACTIVE_SESSIONS') || '{}'),
          [currentUser._id]: updatedSessions
        }));
        
        // Refresh sessions
        loadSessions();
      }
    } catch (error) {
      console.error('Error ending session:', error);
    } finally {
      setLoading(false);
    }
  };
  
  // Table columns
  const columns = [
    {
      title: 'Device',
      dataIndex: 'deviceInfo',
      key: 'device',
      render: (deviceInfo) => (
        <Space>
          {getDeviceIcon(deviceInfo)}
          <span>{getDeviceName(deviceInfo)}</span>
          {currentSessionId === deviceInfo.sessionId && (
            <Tag color="green">Current Session</Tag>
          )}
        </Space>
      ),
    },
    {
      title: 'Started',
      dataIndex: 'startTime',
      key: 'startTime',
      render: (startTime) => formatDate(startTime),
    },
    {
      title: 'Last Activity',
      dataIndex: 'lastActivity',
      key: 'lastActivity',
      render: (lastActivity) => (
        <Tooltip title={formatDate(lastActivity)}>
          <Space>
            <ClockCircleOutlined />
            {formatTimeAgo(lastActivity)}
          </Space>
        </Tooltip>
      ),
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_, record) => (
        <Popconfirm
          title={record.id === currentSessionId ? "End your current session?" : "End this session?"}
          description={record.id === currentSessionId ? "You will be logged out." : "The user will be logged out from this device."}
          onConfirm={() => endUserSession(record.id)}
          okText="Yes"
          cancelText="No"
        >
          <Button 
            type="link" 
            danger
            icon={<LogoutOutlined />}
          >
            {record.id === currentSessionId ? 'End My Session' : 'End Session'}
          </Button>
        </Popconfirm>
      ),
    },
  ];
  
  // Add session ID to each record for easier identification
  const dataSource = sessions.map(session => ({
    ...session,
    deviceInfo: {
      ...session.deviceInfo,
      sessionId: session.id
    },
    key: session.id
  }));
  
  return (
    <Card title="Active Sessions" className="security-card">
      <div style={{ marginBottom: 16 }}>
        <Text>
          These are your currently active sessions across all devices. You can end any session to force a logout on that device.
        </Text>
      </div>
      
      {dataSource.length > 0 ? (
        <Table 
          columns={columns} 
          dataSource={dataSource} 
          loading={loading}
          pagination={false}
          size="middle"
        />
      ) : (
        <Empty 
          description="No active sessions found" 
          image={Empty.PRESENTED_IMAGE_SIMPLE} 
        />
      )}
      
      <div style={{ marginTop: 16, textAlign: 'right' }}>
        <Button onClick={loadSessions} loading={loading}>
          Refresh
        </Button>
      </div>
    </Card>
  );
};

export default ActiveSessions;
