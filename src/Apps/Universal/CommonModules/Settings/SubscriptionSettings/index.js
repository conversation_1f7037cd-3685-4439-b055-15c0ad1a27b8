import FeatureRegistry from "../../../../../Services/FeatureRegistry";
import React, { useState, useEffect } from 'react';
import SubscriptionService from "../../../../../Services/SubscriptionService";
import moment from "moment";
import { Card, Alert, Descriptions, Tag, Progress, Button, Space, Statistic, Row, Col, Divider, Typography, List, Badge, Tooltip, Modal, message  } from "antd";
import { CrownOutlined, CalendarOutlined, CheckCircleOutlined, WarningOutlined, ExclamationCircleOutlined, RocketOutlined, InfoCircleOutlined, StarOutlined, ClockCircleOutlined, UserOutlined, HddOutlined, CloudOutlined  } from "@ant-design/icons";
import { buffModules  } from "../../../../../Utils/functions";
import { fetchCurrentOrganization  } from "../../../../../Utils/organizationUtils";
import { modules  } from "../../../../mission-control/modules";


const { Title, Text, Paragraph } = Typography;

const SubscriptionSettings = ({ pouchDatabase, databasePrefix, currentUser }) => {
  const [subscriptionData, setSubscriptionData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [features, setFeatures] = useState([]);
  const [currentOrganization, setCurrentOrganization] = useState(null);
  const [subscriptionService] = useState(
    () => new SubscriptionService(pouchDatabase, databasePrefix)
  );

  useEffect(() => {
    loadOrganizationAndSubscription();
  }, []);

  const loadOrganizationAndSubscription = async () => {
    setLoading(true);
    try {
      // Get organization data
      const organizations = buffModules(
        modules,
        pouchDatabase,
        currentUser,
        databasePrefix
      ).organizations;

      const organization = await fetchCurrentOrganization(
        pouchDatabase,
        organizations.collection,
        databasePrefix
      );

      console.log('[SubscriptionSettings] Current organization:', organization);
      console.log('[SubscriptionSettings] Database prefix:', databasePrefix);
      setCurrentOrganization(organization);

      if (organization?._id) {
        console.log('[SubscriptionSettings] Loading subscription for organization ID:', organization._id);
        await loadSubscriptionData(organization._id);
      } else {
        console.warn('[SubscriptionSettings] No organization ID found');
      }
    } catch (error) {
      console.error('[SubscriptionSettings] Error loading organization:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadSubscriptionData = async (organizationId) => {
    if (!organizationId) {
      return;
    }

    try {
      console.log('[SubscriptionSettings] Loading subscription data for organization:', organizationId);
      const validation = await subscriptionService.validateSubscription(organizationId);
      console.log('[SubscriptionSettings] Received validation data:', validation);
      setSubscriptionData(validation);

      // Get features for current tier
      if (validation.plan?.tier) {
        const tierFeatures = FeatureRegistry.getFeaturesForApplication('*', validation.plan.tier);
        setFeatures(tierFeatures);
        console.log('[SubscriptionSettings] Loaded features for tier:', validation.plan.tier, tierFeatures.length);
      }
    } catch (error) {
      console.error('[SubscriptionSettings] Error loading subscription data:', error);
      message.error('Failed to load subscription information');
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'active': return 'success';
      case 'trial': return 'processing';
      case 'expired': return 'error';
      default: return 'default';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'active': return <CheckCircleOutlined />;
      case 'trial': return <ClockCircleOutlined />;
      case 'expired': return <WarningOutlined />;
      default: return <ExclamationCircleOutlined />;
    }
  };

  const getTierIcon = (tier) => {
    switch (tier) {
      case 'bronze': return <StarOutlined style={{ color: '#cd7f32' }} />;
      case 'silver': return <StarOutlined style={{ color: '#c0c0c0' }} />;
      case 'gold': return <CrownOutlined style={{ color: '#ffd700' }} />;
      case 'lifetime': return <CrownOutlined style={{ color: '#722ed1' }} />;
      default: return <InfoCircleOutlined />;
    }
  };

  const getTierColor = (tier) => {
    switch (tier) {
      case 'bronze': return '#cd7f32';
      case 'silver': return '#c0c0c0';
      case 'gold': return '#ffd700';
      case 'lifetime': return '#722ed1';
      default: return '#1890ff';
    }
  };

  const handleUpgrade = () => {
    Modal.info({
      title: 'Upgrade Your Subscription',
      content: (
        <div>
          <Paragraph>
            To upgrade your subscription plan, please contact our sales team or visit our pricing page.
          </Paragraph>
          <Paragraph>
            <strong>Benefits of upgrading:</strong>
          </Paragraph>
          <ul>
            <li>Access to advanced features</li>
            <li>Increased usage limits</li>
            <li>Priority support</li>
            <li>Custom integrations</li>
          </ul>
        </div>
      ),
      width: 500,
    });
  };

  const renderSubscriptionOverview = () => {
    if (!subscriptionData) {
      return (
        <div>
          <Alert
            message="No Active Subscription"
            description="Your organization doesn't have an active subscription. You have access to basic features only."
            type="warning"
            showIcon
            style={{ marginBottom: 16 }}
          />

          <Card
            title={
              <Space>
                <InfoCircleOutlined style={{ color: '#1890ff' }} />
                <span>Basic Access Available</span>
              </Space>
            }
          >
            <Descriptions column={1} size="small">
              <Descriptions.Item label="Current Status">
                <Badge status="default" text="NO SUBSCRIPTION" />
              </Descriptions.Item>
              <Descriptions.Item label="Access Level">
                <Text>Basic features only</Text>
              </Descriptions.Item>
              <Descriptions.Item label="Available Modules">
                <div style={{ marginTop: 8 }}>
                  {['Dashboard', 'Settings', 'Users', 'Roles', 'Organizations', 'Profile'].map(module => (
                    <Tag key={module} color="blue" style={{ marginBottom: 4 }}>
                      {module}
                    </Tag>
                  ))}
                </div>
              </Descriptions.Item>
            </Descriptions>

            <Divider />

            <Alert
              message="Upgrade to Unlock Full Features"
              description="Subscribe to access advanced modules like Invoicing, CRM, HR Management, Analytics, and more."
              type="info"
              showIcon
              action={
                <Button type="primary" icon={<RocketOutlined />} onClick={handleUpgrade}>
                  View Plans
                </Button>
              }
            />
          </Card>
        </div>
      );
    }

    const { subscription, plan, status, isInTrial, daysRemaining, phase, phaseInfo } = subscriptionData;

    return (
      <Card
        title={
          <Space>
            {getTierIcon(plan?.tier)}
            <span>Subscription Overview</span>
          </Space>
        }
        extra={
          <Tag color={getStatusColor(status)} icon={getStatusIcon(status)}>
            {status?.toUpperCase()}
          </Tag>
        }
      >
        <Row gutter={[16, 16]}>
          <Col span={12}>
            <Statistic
              title="Current Plan"
              value={plan?.title || 'Unknown Plan'}
              prefix={getTierIcon(plan?.tier)}
              valueStyle={{ color: getTierColor(plan?.tier) }}
            />
          </Col>
          <Col span={12}>
            <Statistic
              title={
                phase === 'trial' ? "Trial Days Remaining" :
                plan?.type === 'one_time' && plan?.tier === 'lifetime' ? "Subscription Type" :
                "Subscription Days Remaining"
              }
              value={plan?.type === 'one_time' && plan?.tier === 'lifetime' ? "Lifetime" : daysRemaining}
              suffix={plan?.type === 'one_time' && plan?.tier === 'lifetime' ? "" : "days"}
              valueStyle={{
                color: plan?.type === 'one_time' && plan?.tier === 'lifetime' ? '#722ed1' :
                       daysRemaining < 7 ? '#ff4d4f' : daysRemaining < 30 ? '#faad14' : '#52c41a'
              }}
              prefix={plan?.type === 'one_time' && plan?.tier === 'lifetime' ? <CrownOutlined /> : <CalendarOutlined />}
            />
          </Col>
        </Row>

        <Divider />

        <Descriptions column={2} size="small">
          <Descriptions.Item label="Plan Type">
            <Space>
              {getTierIcon(plan?.tier)}
              <Text strong>{plan?.tier?.toUpperCase() || 'Unknown'}</Text>
            </Space>
          </Descriptions.Item>
          <Descriptions.Item label="Billing Cycle">
            {plan?.type || 'Unknown'}
          </Descriptions.Item>
          <Descriptions.Item label="Start Date">
            {subscription?.start ? moment(subscription.start).format('MMMM DD, YYYY') : 'Unknown'}
          </Descriptions.Item>
          <Descriptions.Item label="User Limit">
            {plan?.users || 'Unlimited'}
          </Descriptions.Item>

          {/* Phase-specific information */}
          {phase === 'trial' && (
            <>
              <Descriptions.Item label="Current Phase">
                <Badge status="processing" text="Trial Period" />
              </Descriptions.Item>
              <Descriptions.Item label="Trial Duration">
                {subscription?.trial_period_days} days total
              </Descriptions.Item>
              <Descriptions.Item label="Trial Ends">
                {phaseInfo?.trialEndDate ? moment(phaseInfo.trialEndDate).format('MMMM DD, YYYY') : 'Unknown'}
              </Descriptions.Item>
              <Descriptions.Item label="After Trial">
                Subscription period begins with plan-based access
              </Descriptions.Item>
            </>
          )}

          {phase === 'subscription' && (
            <>
              <Descriptions.Item label="Current Phase">
                <Badge status="success" text="Subscription Period" />
              </Descriptions.Item>
              {phaseInfo?.subscriptionStartDate && (
                <Descriptions.Item label="Subscription Started">
                  {moment(phaseInfo.subscriptionStartDate).format('MMMM DD, YYYY')}
                </Descriptions.Item>
              )}
              {subscription?.trial_period_days > 0 && (
                <Descriptions.Item label="Previous Trial">
                  {subscription.trial_period_days} days (completed)
                </Descriptions.Item>
              )}
            </>
          )}

          <Descriptions.Item label="Status">
            <Badge
              status={getStatusColor(status)}
              text={
                phase === 'trial' ? 'Trial Period' :
                phase === 'subscription' ? `Subscription ${status?.toUpperCase()}` :
                status?.toUpperCase()
              }
            />
          </Descriptions.Item>
        </Descriptions>

        {daysRemaining < 30 && status !== 'expired' && !(plan?.type === 'one_time' && plan?.tier === 'lifetime') && (
          <Alert
            message={
              phase === 'trial' ? "Trial Ending Soon" :
              phase === 'subscription' ? "Subscription Ending Soon" :
              "Period Ending Soon"
            }
            description={
              phase === 'trial'
                ? `Your trial period ends in ${daysRemaining} days. After trial ends, you'll transition to subscription period with plan-based access. Subscribe now to ensure continued access to all features.`
                : phase === 'subscription'
                ? `Your subscription period ends in ${daysRemaining} days. Renew to continue using plan features.`
                : `Your current period ends in ${daysRemaining} days. Consider upgrading or renewing to continue using all features.`
            }
            type="warning"
            showIcon
            style={{ marginTop: 16 }}
            action={
              <Button size="small" type="primary" onClick={handleUpgrade}>
                {phase === 'trial' ? 'Subscribe Now' : 'Renew'}
              </Button>
            }
          />
        )}

        {/* Trial period information alert */}
        {phase === 'trial' && (
          <Alert
            message="Trial Period Active"
            description={
              <div>
                <p><strong>Current Access:</strong> Full access to all features regardless of plan restrictions.</p>
                <p><strong>After Trial:</strong> Access will be limited to features included in your {plan?.title || 'subscription plan'}.</p>
                <p><strong>Trial vs Subscription:</strong> These are separate periods - your subscription billing starts after the trial ends.</p>
              </div>
            }
            type="info"
            showIcon
            style={{ marginTop: 16 }}
          />
        )}

        {/* Subscription period information alert */}
        {phase === 'subscription' && subscription?.trial_period_days > 0 && (
          <Alert
            message="Subscription Period Active"
            description={
              <div>
                <p><strong>Current Access:</strong> Access limited to features included in your {plan?.title || 'subscription plan'}.</p>
                <p><strong>Trial Completed:</strong> Your {subscription.trial_period_days}-day trial period has ended.</p>
                <p><strong>Billing Period:</strong> You are now in the paid subscription period.</p>
              </div>
            }
            type="success"
            showIcon
            style={{ marginTop: 16 }}
          />
        )}

        {status === 'expired' && (
          <Alert
            message="Subscription Expired"
            description="Your subscription has expired. Some features may be limited. Please renew your subscription to restore full access."
            type="error"
            showIcon
            style={{ marginTop: 16 }}
            action={
              <Button size="small" type="primary" danger onClick={handleUpgrade}>
                Renew Now
              </Button>
            }
          />
        )}
      </Card>
    );
  };

  const renderFeaturesList = () => {
    if (!subscriptionData) {
      // Show basic features available without subscription
      const basicFeatures = [
        { name: 'Dashboard', description: 'View basic dashboard and statistics', category: 'core' },
        { name: 'User Management', description: 'Manage users and roles', category: 'core' },
        { name: 'Organization Settings', description: 'Basic organization configuration', category: 'core' },
        { name: 'Profile Management', description: 'Manage user profiles', category: 'core' },
        { name: 'Basic Settings', description: 'Access to basic application settings', category: 'core' }
      ];

      return (
        <Card title="Available Features (Basic Access)" style={{ marginTop: 16 }}>
          <Alert
            message="Limited Feature Access"
            description="You have access to basic features only. Subscribe to unlock advanced modules and features."
            type="info"
            showIcon
            style={{ marginBottom: 16 }}
          />

          <List
            size="small"
            dataSource={basicFeatures}
            renderItem={(feature) => (
              <List.Item>
                <List.Item.Meta
                  avatar={<CheckCircleOutlined style={{ color: '#52c41a' }} />}
                  title={feature.name}
                  description={feature.description}
                />
                <Tag color="default">Basic</Tag>
              </List.Item>
            )}
          />

          <Divider />

          <div style={{ textAlign: 'center', padding: '20px 0' }}>
            <Title level={5}>Want More Features?</Title>
            <Paragraph>
              Upgrade to access advanced features like:
            </Paragraph>
            <div style={{ marginBottom: 16 }}>
              {['Invoicing', 'CRM', 'HR Management', 'Analytics', 'Reports', 'API Access'].map(feature => (
                <Tag key={feature} color="orange" style={{ marginBottom: 4 }}>
                  {feature}
                </Tag>
              ))}
            </div>
            <Button type="primary" icon={<RocketOutlined />} onClick={handleUpgrade}>
              View Subscription Plans
            </Button>
          </div>
        </Card>
      );
    }

    if (!features.length) {
      return (
        <Alert
          message="No Features Available"
          description="No features found for your current subscription tier."
          type="info"
          showIcon
        />
      );
    }

    const categorizedFeatures = features.reduce((acc, feature) => {
      const category = feature.category || 'other';
      if (!acc[category]) acc[category] = [];
      acc[category].push(feature);
      return acc;
    }, {});

    const { phase } = subscriptionData || {};

    return (
      <Card
        title={
          <Space>
            <span>Available Features</span>
            {phase === 'trial' && <Tag color="blue">Trial: Full Access</Tag>}
            {phase === 'subscription' && <Tag color="green">Subscription: Plan-Based Access</Tag>}
          </Space>
        }
        style={{ marginTop: 16 }}
      >
        {/* Phase-specific access information */}
        {phase === 'trial' && (
          <Alert
            message="Trial Period: Full Feature Access"
            description="During your trial period, you have access to ALL features regardless of your plan. After trial ends, access will be limited to your plan's features."
            type="info"
            showIcon
            style={{ marginBottom: 16 }}
          />
        )}

        {phase === 'subscription' && (
          <Alert
            message="Subscription Period: Plan-Based Access"
            description="You now have access to features included in your subscription plan. Trial period has ended."
            type="success"
            showIcon
            style={{ marginBottom: 16 }}
          />
        )}

        {Object.entries(categorizedFeatures).map(([category, categoryFeatures]) => (
          <div key={category}>
            <Title level={5} style={{ textTransform: 'capitalize', marginTop: 16 }}>
              {category.replace('_', ' ')} Features
            </Title>
            <List
              size="small"
              dataSource={categoryFeatures}
              renderItem={(feature) => (
                <List.Item>
                  <List.Item.Meta
                    avatar={<CheckCircleOutlined style={{ color: '#52c41a' }} />}
                    title={feature.name}
                    description={feature.description}
                  />
                  <Space>
                    <Tag color="blue">{feature.tier}</Tag>
                    {phase === 'trial' && <Tag color="orange">Trial Access</Tag>}
                    {phase === 'subscription' && <Tag color="green">Plan Access</Tag>}
                  </Space>
                </List.Item>
              )}
            />
          </div>
        ))}
      </Card>
    );
  };

  const renderUsageProgress = () => {
    if (!subscriptionData) {
      return (
        <Card title="Subscription Benefits" style={{ marginTop: 16 }}>
          <Alert
            message="Unlock Advanced Features"
            description="Subscribe to access usage tracking, advanced analytics, and detailed reporting features."
            type="info"
            showIcon
            style={{ marginBottom: 16 }}
          />

          <Row gutter={[16, 16]}>
            <Col span={8}>
              <Card size="small" style={{ textAlign: 'center' }}>
                <Statistic
                  title="User Limit"
                  value="5"
                  suffix="users"
                  prefix={<UserOutlined />}
                  valueStyle={{ color: '#faad14' }}
                />
                <Text type="secondary">Basic Plan</Text>
              </Card>
            </Col>
            <Col span={8}>
              <Card size="small" style={{ textAlign: 'center' }}>
                <Statistic
                  title="Storage"
                  value="1"
                  suffix="GB"
                  prefix={<HddOutlined />}
                  valueStyle={{ color: '#faad14' }}
                />
                <Text type="secondary">Limited</Text>
              </Card>
            </Col>
            <Col span={8}>
              <Card size="small" style={{ textAlign: 'center' }}>
                <Statistic
                  title="API Calls"
                  value="100"
                  suffix="/month"
                  prefix={<CloudOutlined />}
                  valueStyle={{ color: '#faad14' }}
                />
                <Text type="secondary">Basic Tier</Text>
              </Card>
            </Col>
          </Row>
        </Card>
      );
    }

    // This would show usage statistics if available
    // For now, we'll show a placeholder
    return (
      <Card title="Usage Overview" style={{ marginTop: 16 }}>
        <Alert
          message="Usage Tracking"
          description="Detailed usage statistics will be available in a future update. This will show your current usage against plan limits."
          type="info"
          showIcon
        />
      </Card>
    );
  };

  if (loading) {
    return (
      <Card loading={true}>
        <div style={{ height: 200 }} />
      </Card>
    );
  }

  return (
    <div>
      <Alert
        message="Subscription Information"
        description="View your current subscription details, available features, and usage information."
        type="info"
        showIcon
        style={{ marginBottom: 16 }}
      />

      {renderSubscriptionOverview()}
      {renderFeaturesList()}
      {renderUsageProgress()}

      <Card
        title="Need Help?"
        style={{ marginTop: 16 }}
        size="small"
      >
        <Paragraph>
          <Text strong>Questions about your subscription?</Text>
        </Paragraph>
        <Paragraph>
          Contact our support team for assistance with billing, upgrades, or feature questions.
        </Paragraph>
        <Space>
          <Button type="primary" icon={<RocketOutlined />} onClick={handleUpgrade}>
            Upgrade Plan
          </Button>
          <Button icon={<InfoCircleOutlined />}>
            Contact Support
          </Button>
        </Space>
      </Card>
    </div>
  );
};

export default SubscriptionSettings;
