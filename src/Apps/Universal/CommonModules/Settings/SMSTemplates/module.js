

export const smsTemplates = {
  name: 'SMS Templates',
  icon: 'MessageOutlined',
  path: '/settings/sms-templates',
  collection: 'sms_templates',
  singular: 'SMS Template',
  parent: 'settings',
  columns: [
    {
      title: 'Template Name',
      dataIndex: 'name',
      sorter: true,
      valueType: 'text',
      isRequired: true,
    },
    {
      title: 'Category',
      dataIndex: 'category',
      valueType: 'select',
      valueEnum: {
        invoice: { text: 'Invoice', status: 'processing' },
        receipt: { text: 'Receipt', status: 'success' },
        reminder: { text: 'Reminder', status: 'warning' },
        general: { text: 'General', status: 'default' },
        marketing: { text: 'Marketing', status: 'processing' },
      },
      isRequired: true,
    },
    {
      title: 'Description',
      dataIndex: 'description',
      valueType: 'text',
      hideInTable: true,
    },
    {
      title: 'Message Template',
      dataIndex: 'message',
      valueType: 'textarea',
      isRequired: true,
      fieldProps: {
        rows: 4,
        maxLength: 160,
        showCount: true,
      },
    },
    {
      title: 'Created At',
      dataIndex: 'createdAt',
      valueType: 'dateTime',
      hideInForm: true,
      sorter: true,
    },
    {
      title: 'Created By',
      dataIndex: 'createdBy',
      valueType: 'text',
      hideInForm: true,
      hideInTable: true,
    },
    {
      title: 'Updated At',
      dataIndex: 'updatedAt',
      valueType: 'dateTime',
      hideInForm: true,
      hideInTable: true,
    },
    {
      title: 'Updated By',
      dataIndex: 'updatedBy',
      valueType: 'text',
      hideInForm: true,
      hideInTable: true,
    }
  ]
};
