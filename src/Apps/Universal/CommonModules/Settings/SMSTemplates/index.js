import React, { useState, useEffect } from 'react';
import { Card, Table, Button, Modal, Form, Input, Select, Space, message, Popconfirm, Tag, Typography, Row, Col, Alert, Tooltip  } from "antd";
import { PlusOutlined, EditOutlined, DeleteOutlined, CopyOutlined, EyeOutlined, MessageOutlined  } from "@ant-design/icons";


const { TextArea } = Input;
const { Option } = Select;
const { Text, Paragraph } = Typography;

const SMSTemplates = ({ pouchDatabase, databasePrefix, currentUser }) => {
  const [templates, setTemplates] = useState([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingTemplate, setEditingTemplate] = useState(null);
  const [previewVisible, setPreviewVisible] = useState(false);
  const [previewTemplate, setPreviewTemplate] = useState(null);
  const [form] = Form.useForm();

  // Load templates on component mount
  useEffect(() => {
    loadTemplates();
    createDefaultTemplates();
  }, []);

  const loadTemplates = async () => {
    try {
      setLoading(true);
      const result = await pouchDatabase('sms_templates', databasePrefix).getAllData();
      setTemplates(result.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt)));
    } catch (error) {
      console.error('Error loading SMS templates:', error);
      message.error('Failed to load SMS templates');
    } finally {
      setLoading(false);
    }
  };

  const createDefaultTemplates = async () => {
    try {
      const existingTemplates = await pouchDatabase('sms_templates', databasePrefix).getAllData();

      // Only create defaults if no templates exist
      if (existingTemplates.length === 0) {
        const defaultTemplates = [
          {
            _id: 'TEMPLATE_INVOICE_001',
            name: 'Invoice Notification',
            category: 'invoice',
            description: 'Standard invoice notification message',
            message: 'Dear {{customer_name}}, your invoice #{{document_id}} for {{amount}} has been created. Due date: {{due_date}}. Thank you for your business!',
            createdAt: new Date().toISOString(),
            createdBy: 'system',
            updatedAt: new Date().toISOString(),
            updatedBy: 'system'
          },
          {
            _id: 'TEMPLATE_RECEIPT_001',
            name: 'Payment Receipt',
            category: 'receipt',
            description: 'Payment confirmation message',
            message: 'Dear {{customer_name}}, we have received your payment of {{amount}}. Receipt #{{document_id}} has been generated. Thank you!',
            createdAt: new Date().toISOString(),
            createdBy: 'system',
            updatedAt: new Date().toISOString(),
            updatedBy: 'system'
          },
          {
            _id: 'TEMPLATE_REMINDER_001',
            name: 'Payment Reminder',
            category: 'reminder',
            description: 'Gentle payment reminder',
            message: 'Dear {{customer_name}}, this is a friendly reminder that invoice #{{document_id}} for {{amount}} is due on {{due_date}}. Outstanding balance: {{balance}}.',
            createdAt: new Date().toISOString(),
            createdBy: 'system',
            updatedAt: new Date().toISOString(),
            updatedBy: 'system'
          },
          {
            _id: 'TEMPLATE_BALANCE_001',
            name: 'Balance Notification',
            category: 'reminder',
            description: 'Notify tenants about their updated balance',
            message: 'Dear {{customer_name}}, your account balance has been updated. Current balance: {{balance}}. Thank you for your attention.',
            createdAt: new Date().toISOString(),
            createdBy: 'system',
            updatedAt: new Date().toISOString(),
            updatedBy: 'system'
          }
        ];

        for (const template of defaultTemplates) {
          await pouchDatabase('sms_templates', databasePrefix).saveDocument(
            template,
            {
              key: 'system',
              label: 'System',
              value: 'system',
            }
          );
        }

        console.log('Default SMS templates created');
      }
    } catch (error) {
      console.error('Error creating default templates:', error);
    }
  };

  const handleSave = async (values) => {
    try {
      const templateData = {
        ...values,
        createdAt: editingTemplate ? editingTemplate.createdAt : new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        createdBy: editingTemplate ? editingTemplate.createdBy : currentUser._id,
        updatedBy: currentUser._id,
        _id: editingTemplate ? editingTemplate._id : Date.now().toString(36).toUpperCase()
      };

      await pouchDatabase('sms_templates', databasePrefix).saveDocument(
        templateData,
        {
          key: currentUser._id,
          label: `${currentUser.first_name} ${currentUser.last_name}`,
          value: currentUser._id,
        }
      );

      message.success(`Template ${editingTemplate ? 'updated' : 'created'} successfully!`);
      setModalVisible(false);
      setEditingTemplate(null);
      form.resetFields();
      loadTemplates();
    } catch (error) {
      console.error('Error saving template:', error);
      message.error('Failed to save template');
    }
  };

  const handleEdit = (template) => {
    setEditingTemplate(template);
    form.setFieldsValue(template);
    setModalVisible(true);
  };

  const handleDelete = async (template) => {
    try {
      await pouchDatabase('sms_templates', databasePrefix).deleteDocument(template._id);
      message.success('Template deleted successfully!');
      loadTemplates();
    } catch (error) {
      console.error('Error deleting template:', error);
      message.error('Failed to delete template');
    }
  };

  const handleCopy = (template) => {
    navigator.clipboard.writeText(template.message);
    message.success('Template message copied to clipboard!');
  };

  const handlePreview = (template) => {
    setPreviewTemplate(template);
    setPreviewVisible(true);
  };

  const getVariableHelp = () => {
    return (
      <div>
        <Paragraph>
          <strong>Available Variables:</strong>
        </Paragraph>
        <ul>
          <li><code>{'{{customer_name}}'}</code> - Customer/Client name</li>
          <li><code>{'{{amount}}'}</code> - Invoice/Receipt amount</li>
          <li><code>{'{{document_id}}'}</code> - Document ID/Number</li>
          <li><code>{'{{company_name}}'}</code> - Your company name</li>
          <li><code>{'{{date}}'}</code> - Current date</li>
          <li><code>{'{{due_date}}'}</code> - Invoice due date</li>
          <li><code>{'{{balance}}'}</code> - Outstanding balance</li>
        </ul>
        <Paragraph>
          <Text type="secondary">
            Variables will be automatically replaced with actual values when sending SMS.
          </Text>
        </Paragraph>
      </div>
    );
  };

  const columns = [
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
      render: (text, record) => (
        <div>
          <Text strong>{text}</Text>
          <br />
          <Text type="secondary" style={{ fontSize: '12px' }}>
            {record.description}
          </Text>
        </div>
      ),
    },
    {
      title: 'Category',
      dataIndex: 'category',
      key: 'category',
      render: (category) => {
        const colors = {
          invoice: 'blue',
          receipt: 'green',
          reminder: 'orange',
          balance: 'red',
          general: 'purple',
          marketing: 'cyan'
        };
        return <Tag color={colors[category] || 'default'}>{category}</Tag>;
      },
    },
    {
      title: 'Message Preview',
      dataIndex: 'message',
      key: 'message',
      render: (text) => (
        <Text ellipsis style={{ maxWidth: 200 }}>
          {text.length > 50 ? `${text.substring(0, 50)}...` : text}
        </Text>
      ),
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_, record) => (
        <Space>
          <Tooltip title="Preview">
            <Button
              icon={<EyeOutlined />}
              size="small"
              onClick={() => handlePreview(record)}
            />
          </Tooltip>
          <Tooltip title="Copy Message">
            <Button
              icon={<CopyOutlined />}
              size="small"
              onClick={() => handleCopy(record)}
            />
          </Tooltip>
          <Tooltip title="Edit">
            <Button
              icon={<EditOutlined />}
              size="small"
              onClick={() => handleEdit(record)}
            />
          </Tooltip>
          <Popconfirm
            title="Are you sure you want to delete this template?"
            onConfirm={() => handleDelete(record)}
            okText="Yes"
            cancelText="No"
          >
            <Tooltip title="Delete">
              <Button
                icon={<DeleteOutlined />}
                size="small"
                danger
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div>
      <Card
        title={
          <Space>
            <MessageOutlined />
            SMS Templates
          </Space>
        }
        extra={
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => {
              setEditingTemplate(null);
              form.resetFields();
              setModalVisible(true);
            }}
          >
            Add Template
          </Button>
        }
      >
        <Alert
          message="SMS Templates"
          description="Create reusable SMS message templates with variables for invoices, receipts, reminders, and more. Templates help maintain consistent messaging and save time."
          type="info"
          showIcon
          style={{ marginBottom: 16 }}
        />

        <Table
          columns={columns}
          dataSource={templates}
          rowKey="_id"
          loading={loading}
          pagination={{ pageSize: 10 }}
        />
      </Card>

      {/* Add/Edit Template Modal */}
      <Modal
        title={editingTemplate ? 'Edit SMS Template' : 'Add SMS Template'}
        open={modalVisible}
        onCancel={() => {
          setModalVisible(false);
          setEditingTemplate(null);
          form.resetFields();
        }}
        footer={null}
        width={700}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSave}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="name"
                label="Template Name"
                rules={[{ required: true, message: 'Please enter template name' }]}
              >
                <Input placeholder="e.g., Invoice Notification" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="category"
                label="Category"
                rules={[{ required: true, message: 'Please select category' }]}
              >
                <Select placeholder="Select category">
                  <Option value="invoice">Invoice</Option>
                  <Option value="receipt">Receipt</Option>
                  <Option value="reminder">Reminder</Option>
                  <Option value="balance">Balance Notification</Option>
                  <Option value="general">General</Option>
                  <Option value="marketing">Marketing</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="description"
            label="Description"
          >
            <Input placeholder="Brief description of when to use this template" />
          </Form.Item>

          <Form.Item
            name="message"
            label="Message Template"
            rules={[{ required: true, message: 'Please enter message template' }]}
            extra={
              <Button
                type="link"
                size="small"
                onClick={() => {
                  Modal.info({
                    title: 'Template Variables Help',
                    content: getVariableHelp(),
                    width: 500
                  });
                }}
              >
                View Available Variables
              </Button>
            }
          >
            <TextArea
              rows={4}
              placeholder="Dear {{customer_name}}, your invoice #{{document_id}} for {{amount}} has been created..."
              showCount
              maxLength={160}
            />
          </Form.Item>

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                {editingTemplate ? 'Update Template' : 'Create Template'}
              </Button>
              <Button onClick={() => {
                setModalVisible(false);
                setEditingTemplate(null);
                form.resetFields();
              }}>
                Cancel
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* Preview Modal */}
      <Modal
        title="Template Preview"
        open={previewVisible}
        onCancel={() => setPreviewVisible(false)}
        footer={[
          <Button key="close" onClick={() => setPreviewVisible(false)}>
            Close
          </Button>
        ]}
        width={500}
      >
        {previewTemplate && (
          <div>
            <Paragraph>
              <strong>Name:</strong> {previewTemplate.name}
            </Paragraph>
            <Paragraph>
              <strong>Category:</strong> <Tag color="blue">{previewTemplate.category}</Tag>
            </Paragraph>
            {previewTemplate.description && (
              <Paragraph>
                <strong>Description:</strong> {previewTemplate.description}
              </Paragraph>
            )}
            <Paragraph>
              <strong>Message:</strong>
            </Paragraph>
            <div style={{
              background: '#f5f5f5',
              padding: '12px',
              borderRadius: '6px',
              border: '1px solid #d9d9d9'
            }}>
              <Text>{previewTemplate.message}</Text>
            </div>
            <Paragraph style={{ marginTop: 16 }}>
              <Text type="secondary">
                Variables like {'{{customer_name}}'} will be replaced with actual values when sending.
              </Text>
            </Paragraph>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default SMSTemplates;
