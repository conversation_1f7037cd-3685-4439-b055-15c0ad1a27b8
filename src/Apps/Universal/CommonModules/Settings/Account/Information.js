import React, { useEffect, useState } from "react";
import { BetaSchemaForm  } from "@ant-design/pro-form";
import { Divider, message, Card, Typography, Space  } from "antd";
import { LOCAL_STORAGE_USER  } from "../../../../../Utils/constants";
import { UserOutlined, InfoCircleOutlined, CheckCircleOutlined, EditOutlined  } from "@ant-design/icons";


const Information = (props) => {
  const currentUser = JSON.parse(localStorage.getItem(LOCAL_STORAGE_USER));
  const { pouchDatabase, modules, databasePrefix } = props;
  const { columns, collection } = modules.users;
  const [update, setUpdate] = useState(0);
  const layout = {
    labelCol: { span: 4 },
    wrapperCol: { span: 16 },
  };
  const { Title, Text } = Typography;

  useEffect(() => {}, [update]);

  return (
    <div>
      <div className="settings-section-header">
        <Divider orientation="left">
          <Space>
            <UserOutlined />
            User Profile
          </Space>
        </Divider>
      </div>

      {currentUser ? (
        <div className="enhanced-form-container">
          <div className="form-field-group">
            <div className="form-field-group-title">
              <EditOutlined />
              Personal Information
            </div>
            <Text type="secondary" className="form-help-text">
              <InfoCircleOutlined />
              Update your personal details and contact information.
            </Text>

            <BetaSchemaForm
              {...layout}
              layoutType="ProForm"
              submitter={{
                searchConfig: {
                  resetText: "Reset",
                  submitText: (
                    <Space>
                      <CheckCircleOutlined />
                      Update Profile
                    </Space>
                  )
                },
                render: (props, doms) => {
                  return (
                    <div className="form-actions-container">
                      {doms}
                    </div>
                  );
                },
              }}
              onFinish={async (values) => {
                pouchDatabase(collection, databasePrefix)
                  .saveDocument(
                    { ...currentUser, ...values },
                    {
                      key: currentUser._id,
                      label: `${currentUser.first_name} ${currentUser.last_name}`,
                      value: currentUser._id,
                    }
                  )
                  .then((res) => {
                    message.success({
                      content: "Your profile has been updated successfully!",
                      icon: <CheckCircleOutlined style={{ color: '#52c41a' }} />,
                      duration: 4,
                    });
                    setUpdate(update + 1);
                  });
              }}
              initialValues={currentUser}
              columns={columns.filter(
                (r) => r.dataIndex !== "password" && r.dataIndex !== "name"
              )}
            />
          </div>
        </div>
      ) : (
        <Card>
          <div style={{ textAlign: 'center', padding: '40px 20px' }}>
            <UserOutlined style={{ fontSize: '48px', color: '#d9d9d9', marginBottom: '16px' }} />
            <Title level={4} type="secondary">No User Data</Title>
            <Text type="secondary">
              Unable to load user information.
            </Text>
          </div>
        </Card>
      )}
    </div>
  );
};

export default Information;
