

export const smsMessages = {
  name: 'SMS Messages',
  icon: 'MessageOutlined',
  path: '/settings/sms-messages',
  collection: 'sms_messages',
  singular: 'SMS Message',
  parent: 'settings',
  columns: [
    {
      title: 'Date',
      dataIndex: 'sent_at',
      sorter: true,
      valueType: 'dateTime',
      hideInForm: true,
    },
    {
      title: 'Phone Number',
      dataIndex: 'phone_number',
      sorter: true,
      valueType: 'text',
      isRequired: true,
    },
    {
      title: 'Message',
      dataIndex: 'message',
      valueType: 'textarea',
      isRequired: true,
    },
    {
      title: 'Status',
      dataIndex: 'status',
      valueType: 'select',
      valueEnum: {
        pending: { text: 'Pending', status: 'warning' },
        sent: { text: 'Sent', status: 'processing' },
        delivered: { text: 'Delivered', status: 'success' },
        failed: { text: 'Failed', status: 'error' },
      },
      hideInForm: true,
    },
    {
      title: 'Response',
      dataIndex: 'response',
      valueType: 'textarea',
      hideInForm: true,
      hideInTable: true,
    },
    {
      title: 'Response Received At',
      dataIndex: 'response_received_at',
      valueType: 'dateTime',
      hideInForm: true,
      hideInTable: true,
    },
    {
      title: 'Related Document Type',
      dataIndex: 'related_document_type',
      valueType: 'text',
      hideInTable: true,
    },
    {
      title: 'Related Document ID',
      dataIndex: 'related_document_id',
      valueType: 'text',
      hideInTable: true,
    },
    {
      title: 'Provider',
      dataIndex: 'provider',
      valueType: 'text',
      hideInForm: true,
      hideInTable: true,
    },
    {
      title: 'Error',
      dataIndex: 'error',
      valueType: 'textarea',
      hideInForm: true,
      hideInTable: true,
    }
  ]
};
