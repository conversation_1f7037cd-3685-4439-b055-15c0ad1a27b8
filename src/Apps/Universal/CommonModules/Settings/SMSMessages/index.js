import React, { useState, useEffect } from 'react';
import moment from "moment";
import { Card, Table, Tag, Button, Input, DatePicker, Space, Tooltip, Modal, Typography, Badge, Tabs, Alert  } from "antd";
import { SearchOutlined, ReloadOutlined, EyeOutlined, SendOutlined  } from "@ant-design/icons";
import { sendSMSWithDB  } from "../../../../../Utils/functions";


const { Text } = Typography;
const { RangePicker } = DatePicker;
const { TabPane } = Tabs;

const SMSMessages = ({ pouchDatabase, databasePrefix, currentUser, refreshKey }) => {
  const [messages, setMessages] = useState([]);
  const [loading, setLoading] = useState(false);
  const [searchText, setSearchText] = useState('');
  const [dateRange, setDateRange] = useState([]);
  const [viewModalVisible, setViewModalVisible] = useState(false);
  const [currentMessage, setCurrentMessage] = useState(null);
  const [statusFilter, setStatusFilter] = useState('all');

  // Fetch SMS messages
  const fetchMessages = async () => {
    try {
      setLoading(true);
      const result = await pouchDatabase('sms_messages', databasePrefix).getAllData();

      // Sort by date descending (newest first)
      const sortedMessages = result.sort((a, b) =>
        moment(b.sent_at || b.createdAt).diff(moment(a.sent_at || a.createdAt))
      );

      setMessages(sortedMessages);
      setLoading(false);
    } catch (error) {
      console.error('Error fetching SMS messages:', error);
      setLoading(false);
    }
  };

  // Load messages on component mount and when refresh key changes
  useEffect(() => {
    fetchMessages();
  }, [refreshKey]);

  // Handle resending an SMS
  const handleResend = async (record) => {
    try {
      const result = await sendSMSWithDB(
        record.phone_number,
        record.message,
        {},
        pouchDatabase,
        databasePrefix,
        currentUser
      );

      if (result.error) {
        Modal.error({
          title: 'Failed to resend SMS',
          content: result.error
        });
      } else {
        Modal.success({
          title: 'SMS Resent',
          content: 'The message has been resent successfully.'
        });

        // Refresh the list
        fetchMessages();
      }
    } catch (error) {
      console.error('Error resending SMS:', error);
      Modal.error({
        title: 'Error',
        content: 'Failed to resend the SMS. Please try again.'
      });
    }
  };

  // View message details
  const handleViewMessage = (record) => {
    setCurrentMessage(record);
    setViewModalVisible(true);
  };

  // Filter messages based on search text and date range
  const getFilteredMessages = () => {
    let filtered = [...messages];

    // Filter by status
    if (statusFilter !== 'all') {
      filtered = filtered.filter(msg => msg.status === statusFilter);
    }

    // Filter by search text
    if (searchText) {
      const searchLower = searchText.toLowerCase();
      filtered = filtered.filter(msg =>
        (msg.message && msg.message.toLowerCase().includes(searchLower)) ||
        (msg.phone_number && msg.phone_number.toLowerCase().includes(searchLower)) ||
        (msg.related_document_type && msg.related_document_type.toLowerCase().includes(searchLower)) ||
        (msg.related_document_id && msg.related_document_id.toLowerCase().includes(searchLower))
      );
    }

    // Filter by date range
    if (dateRange && dateRange.length === 2) {
      const startDate = dateRange[0].startOf('day');
      const endDate = dateRange[1].endOf('day');

      filtered = filtered.filter(msg => {
        const msgDate = moment(msg.sent_at || msg.createdAt);
        return msgDate.isBetween(startDate, endDate, null, '[]');
      });
    }

    return filtered;
  };

  // Table columns
  const columns = [
    {
      title: 'Date',
      dataIndex: 'sent_at',
      key: 'sent_at',
      render: (text, record) => (
        <span>{moment(text || record.createdAt).format('DD-MMM-YYYY HH:mm:ss')}</span>
      ),
      width: 180,
    },
    {
      title: 'Phone Number',
      dataIndex: 'phone_number',
      key: 'phone_number',
      width: 150,
    },
    {
      title: 'Message',
      dataIndex: 'message',
      key: 'message',
      ellipsis: true,
      render: text => (
        <Tooltip title={text}>
          <span>{text}</span>
        </Tooltip>
      ),
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      width: 120,
      render: status => {
        let color = 'default';
        if (status === 'sent') color = 'blue';
        if (status === 'delivered') color = 'green';
        if (status === 'failed') color = 'red';

        return (
          <Tag color={color}>
            {status ? status.toUpperCase() : 'UNKNOWN'}
          </Tag>
        );
      },
    },
    {
      title: 'Related To',
      dataIndex: 'related_document_type',
      key: 'related_document_type',
      width: 150,
      render: (text, record) => (
        text ? `${text} (${record.related_document_id || 'Unknown'})` : '-'
      ),
    },
    {
      title: 'Actions',
      key: 'actions',
      width: 120,
      render: (_, record) => (
        <Space>
          <Button
            icon={<EyeOutlined />}
            size="small"
            onClick={() => handleViewMessage(record)}
          />
          {record.status === 'failed' && (
            <Button
              icon={<SendOutlined />}
              size="small"
              onClick={() => handleResend(record)}
              danger
            />
          )}
        </Space>
      ),
    },
  ];

  // Render status counts
  const renderStatusCounts = () => {
    const counts = {
      total: messages.length,
      sent: messages.filter(m => m.status === 'sent').length,
      delivered: messages.filter(m => m.status === 'delivered').length,
      failed: messages.filter(m => m.status === 'failed').length,
      pending: messages.filter(m => m.status === 'pending').length,
    };

    return (
      <Space size="large">
        <Badge count={counts.total} showZero style={{ backgroundColor: '#8c8c8c' }}>
          <Text>Total</Text>
        </Badge>
        <Badge count={counts.sent} showZero style={{ backgroundColor: '#1890ff' }}>
          <Text>Sent</Text>
        </Badge>
        <Badge count={counts.delivered} showZero style={{ backgroundColor: '#52c41a' }}>
          <Text>Delivered</Text>
        </Badge>
        <Badge count={counts.failed} showZero style={{ backgroundColor: '#f5222d' }}>
          <Text>Failed</Text>
        </Badge>
        <Badge count={counts.pending} showZero style={{ backgroundColor: '#faad14' }}>
          <Text>Pending</Text>
        </Badge>
      </Space>
    );
  };

  return (
    <div>
      <Alert
        message="SMS Message History"
        description="All SMS messages sent from this application are logged here. You can view message details, check delivery status, and resend failed messages."
        type="info"
        showIcon
        style={{ marginBottom: 16 }}
      />

      <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <div>
          <Space>
            <Input
              placeholder="Search messages"
              value={searchText}
              onChange={e => setSearchText(e.target.value)}
              style={{ width: 250 }}
              prefix={<SearchOutlined />}
              allowClear
            />
            <RangePicker
              onChange={dates => setDateRange(dates)}
              allowClear
            />
            <Button
              type="primary"
              icon={<ReloadOutlined />}
              onClick={fetchMessages}
              loading={loading}
            >
              Refresh
            </Button>
          </Space>
        </div>
        <div>
          {renderStatusCounts()}
        </div>
      </div>

      <Tabs
        defaultActiveKey="all"
        onChange={key => setStatusFilter(key)}
        style={{ marginBottom: 16 }}
      >
        <TabPane tab="All Messages" key="all" />
        <TabPane tab="Sent" key="sent" />
        <TabPane tab="Delivered" key="delivered" />
        <TabPane tab="Failed" key="failed" />
        <TabPane tab="Pending" key="pending" />
      </Tabs>

      <Table
        columns={columns}
        dataSource={getFilteredMessages()}
        rowKey="_id"
        loading={loading}
        pagination={{ pageSize: 10 }}
      />

      {/* Message Detail Modal */}
      <Modal
        title="SMS Message Details"
        visible={viewModalVisible}
        onCancel={() => setViewModalVisible(false)}
        footer={[
          <Button key="close" onClick={() => setViewModalVisible(false)}>
            Close
          </Button>,
          currentMessage && currentMessage.status === 'failed' && (
            <Button
              key="resend"
              type="primary"
              danger
              onClick={() => {
                handleResend(currentMessage);
                setViewModalVisible(false);
              }}
            >
              Resend
            </Button>
          ),
        ]}
        width={700}
      >
        {currentMessage && (
          <div>
            <div style={{ marginBottom: 16 }}>
              <Text strong>Date: </Text>
              <Text>{moment(currentMessage.sent_at || currentMessage.createdAt).format('DD-MMM-YYYY HH:mm:ss')}</Text>
            </div>

            <div style={{ marginBottom: 16 }}>
              <Text strong>Phone Number: </Text>
              <Text>{currentMessage.phone_number}</Text>
            </div>

            <div style={{ marginBottom: 16 }}>
              <Text strong>Status: </Text>
              <Tag color={
                currentMessage.status === 'sent' ? 'blue' :
                  currentMessage.status === 'delivered' ? 'green' :
                    currentMessage.status === 'failed' ? 'red' :
                      currentMessage.status === 'pending' ? 'orange' :
                        'default'
              }>
                {currentMessage.status ? currentMessage.status.toUpperCase() : 'UNKNOWN'}
              </Tag>
            </div>

            <div style={{ marginBottom: 16 }}>
              <Text strong>Message: </Text>
              <div style={{ background: '#f5f5f5', padding: 8, borderRadius: 4, marginTop: 8 }}>
                <Text>{currentMessage.message}</Text>
              </div>
            </div>

            {currentMessage.response && (
              <div style={{ marginBottom: 16 }}>
                <Text strong>Response: </Text>
                <div style={{ background: '#f5f5f5', padding: 8, borderRadius: 4, marginTop: 8 }}>
                  <Text>{currentMessage.response}</Text>
                </div>
                {currentMessage.response_received_at && (
                  <div style={{ marginTop: 4 }}>
                    <Text type="secondary">
                      Received: {moment(currentMessage.response_received_at).format('DD-MMM-YYYY HH:mm:ss')}
                    </Text>
                  </div>
                )}
              </div>
            )}

            {currentMessage.related_document_type && (
              <div style={{ marginBottom: 16 }}>
                <Text strong>Related Document: </Text>
                <Text>{currentMessage.related_document_type} ({currentMessage.related_document_id || 'Unknown'})</Text>
              </div>
            )}

            {currentMessage.provider && (
              <div style={{ marginBottom: 16 }}>
                <Text strong>Provider: </Text>
                <Text>{currentMessage.provider}</Text>
              </div>
            )}

            {currentMessage.error && (
              <div style={{ marginBottom: 16 }}>
                <Text strong>Error: </Text>
                <div style={{ background: '#fff2f0', padding: 8, borderRadius: 4, marginTop: 8 }}>
                  <Text type="danger">{currentMessage.error}</Text>
                </div>
              </div>
            )}
          </div>
        )}
      </Modal>
    </div>
  );
};

export default SMSMessages;
