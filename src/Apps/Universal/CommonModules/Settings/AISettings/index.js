import React, { useState, useEffect } from 'react';
import { AIServiceProvider, useAIService  } from "../../../../../Services/AI/AIServiceContext";
import { Form, Input, Select, Switch, InputNumber, Button, Card, Alert, Divider, Typography, Space, message, Tooltip  } from "antd";
import { SaveOutlined, LockOutlined, ApiOutlined, RobotOutlined, SyncOutlined, InfoCircleOutlined  } from "@ant-design/icons";
import { fetchGoogleModels, clearModelsCache, DEFAULT_MODELS  } from "../../../../../Services/AI/ModelFetchService";
import { getAISettings, saveAISettings  } from "../../../../../Services/AI/AISettingsStorage";


const { Option } = Select;
const { Title, Paragraph, Text } = Typography;

// Wrapper component that provides AI service context
const AISettingsWrapper = (props) => {
  return (
    <AIServiceProvider modulesProperties={props.modulesProperties}>
      <AISettingsContent {...props} />
    </AIServiceProvider>
  );
};

// Main settings content component
const AISettingsContent = (props) => {
  const { aiService, config, error, refreshService } = useAIService();
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [testResult, setTestResult] = useState(null);
  const [models, setModels] = useState(DEFAULT_MODELS.google);
  const [fetchingModels, setFetchingModels] = useState(false);

  const { modulesProperties, updateModuleProperties } = props;

  useEffect(() => {
    // Initialize form with current settings from local storage
    const settings = getAISettings();
    if (form && form.setFieldsValue) {
      try {
        form.setFieldsValue(settings);
      } catch (error) {
        console.warn('Could not set initial form values:', error);
      }
    }

    // Load models when component mounts
    loadModels(settings.apiKey);
  }, [form]);

  // Function to load models from API or cache
  const loadModels = async (apiKey) => {
    setFetchingModels(true);
    try {
      const fetchedModels = await fetchGoogleModels(apiKey);
      setModels(fetchedModels);
    } catch (error) {
      console.error('Error loading models:', error);
      message.error('Failed to load models. Using default list.');
    } finally {
      setFetchingModels(false);
    }
  };

  // Function to refresh models list
  const refreshModels = async () => {
    const apiKey = form.getFieldValue('apiKey');
    if (!apiKey) {
      message.warning('Please enter an API key to fetch available models');
      return;
    }

    // Clear cache and fetch fresh models
    clearModelsCache();
    message.info('Refreshing models list...');
    await loadModels(apiKey);
    message.success('Models list updated successfully');
  };

  const handleSave = async (values) => {
    setLoading(true);
    try {
      
      

      // Get the selected model details for verification
      const selectedModel = models.find(model => model.value === values.modelName);
      

      // Ensure numeric values are properly formatted
      const formattedValues = {
        ...values,
        // Ensure model name is exactly as selected
        modelName: values.modelName,
        temperature: typeof values.temperature === 'string' ? parseFloat(values.temperature) : values.temperature,
        maxTokens: typeof values.maxTokens === 'string' ? parseInt(values.maxTokens, 10) : values.maxTokens
      };

      

      // Save settings to local storage
      const success = saveAISettings(formattedValues);

      if (success) {
        message.success('AI settings saved successfully');
        setTestResult({ success: true, message: 'Settings saved successfully' });

        // Verify settings were saved by reading them back
        const savedSettings = getAISettings();
        

        // Refresh the AI service instead of reloading the page
        if (refreshService) {
          
          refreshService();
        } else {
          console.warn('refreshService not available, falling back to page reload');
          // Fallback to page reload if refreshService is not available
          setTimeout(() => {
            window.location.reload();
          }, 2000);
        }
      } else {
        throw new Error('Failed to save settings to local storage');
      }
    } catch (error) {
      console.error('Error in handleSave:', error);
      setTestResult({ success: false, message: `Error saving settings: ${error.message}` });
    } finally {
      setLoading(false);
    }
  };

  const handleTest = async () => {
    setLoading(true);
    try {
      const values = await form.validateFields();

      // Import the service dynamically to test it
      const { default: AIServiceFactory } = await import('../../../../../Services/AI/AIServiceFactory');

      // Create a service instance with the current form values
      const service = AIServiceFactory.createService(values);

      // Test the service with a simple prompt
      const result = await service.generateText('Hello, please respond with a short greeting.');

      setTestResult({
        success: true,
        message: 'Connection successful! Response: ' + result.substring(0, 100) + (result.length > 100 ? '...' : '')
      });
    } catch (error) {
      setTestResult({
        success: false,
        message: `Connection failed: ${error.message}`
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div>
      <Divider orientation="left">AI Service Configuration</Divider>

      <Paragraph>
        Configure your AI service settings to enable AI-powered features in the application.
      </Paragraph>

      <Alert
        message="Free Models Available"
        description="Gemini 1.0 models are available for free use without billing setup. Select a model from the 'Free Models' section to get started without any costs."
        type="info"
        showIcon
        style={{ marginBottom: 16 }}
      />

      <Alert
        message="Auto-Updated Model List"
        description={
          <span>
            The model list is automatically updated from Google's API. Click the refresh button next to the model selector to get the latest available models.
            <strong>Note:</strong> Model availability may change as Google updates their API. If you encounter errors with a specific model, try switching to one of the free models which tend to be more stable.
          </span>
        }
        type="warning"
        showIcon
        style={{ marginBottom: 16 }}
      />

      {testResult && (
        <Alert
          message={testResult.success ? 'Success' : 'Error'}
          description={testResult.message}
          type={testResult.success ? 'success' : 'error'}
          showIcon
          closable
          onClose={() => setTestResult(null)}
          style={{ marginBottom: 16 }}
        />
      )}

      {error && !testResult && (
        <Alert
          message="AI Service Configuration Error"
          description={error}
          type="error"
          showIcon
          style={{ marginBottom: 16 }}
        />
      )}

      <Form
        form={form}
        layout="vertical"
        onFinish={handleSave}
        initialValues={{
          aiServiceProvider: 'google',
          modelName: 'gemini-pro', // Free model as default (correct name format)
          temperature: 0.7,
          maxTokens: 2000,
          enablePredictiveAnalytics: true,
          enableDocumentAnalysis: true,
          enableSmartRecommendations: true,
          enableIntelligentSearch: true,
        }}
      >
        <Divider orientation="left">Service Provider</Divider>

        <Form.Item
          name="aiServiceProvider"
          label="AI Service Provider"
          rules={[{ required: true, message: 'Please select an AI service provider' }]}
        >
          <Select>
            <Option value="google">Google AI (Gemini)</Option>
            <Option value="openai" disabled>OpenAI (Coming Soon)</Option>
            <Option value="azure" disabled>Azure AI (Coming Soon)</Option>
          </Select>
        </Form.Item>

        <Form.Item
          name="apiKey"
          label="API Key"
          rules={[{ required: true, message: 'Please enter your API key' }]}
          extra="Your API key is stored locally and never shared. Free models still require an API key, but don't require billing setup."
        >
          <Input.Password
            prefix={<LockOutlined />}
            placeholder="Enter your API key"
          />
        </Form.Item>

        <Divider orientation="left">Model Settings</Divider>

        <Form.Item
          name="modelName"
          label={
            <span>
              Model Name
              <Tooltip title="Click the refresh button to update the list of available models">
                <InfoCircleOutlined style={{ marginLeft: 8 }} />
              </Tooltip>
            </span>
          }
          rules={[{ required: true, message: 'Please select a model' }]}
          extra="Models marked with (Free) don't require billing setup. Models marked with (Exp) are experimental and may change."
        >
          <div style={{ display: 'flex', gap: '8px' }}>
            <Select
              optionFilterProp="label"
              showSearch
              placeholder="Select a model"
              style={{ flex: 1 }}
              loading={fetchingModels}
              onChange={(value) => {

                // Update the form field directly to ensure the value is set correctly
                if (form && form.setFieldsValue) {
                  try {
                    form.setFieldsValue({ modelName: value });
                  } catch (error) {
                    console.warn('Could not set modelName field value:', error);
                  }
                }
              }}
              dropdownRender={menu => (
                <div>
                  {menu}
                  <Divider style={{ margin: '8px 0' }} />
                  <div style={{ padding: '0 8px 4px' }}>
                    <Text type="secondary" style={{ fontSize: '12px' }}>
                      {models.length} models available
                    </Text>
                  </div>
                </div>
              )}
            >
              {/* Group models by category */}
              {Array.from(new Set(models.map(model => model.group))).map(group => (
                <Select.OptGroup key={group} label={group}>
                  {models
                    .filter(model => model.group === group)
                    .map(model => (
                      <Option key={model.value} value={model.value} label={model.label}>
                        {model.label}
                        {model.description && (
                          <div>
                            <Text type="secondary" style={{ fontSize: '12px' }}>
                              {model.description.length > 60
                                ? model.description.substring(0, 60) + '...'
                                : model.description}
                            </Text>
                          </div>
                        )}
                      </Option>
                    ))}
                </Select.OptGroup>
              ))}
            </Select>
            <Tooltip title="Refresh models list">
              <Button
                icon={<SyncOutlined />}
                onClick={refreshModels}
                loading={fetchingModels}
              />
            </Tooltip>
          </div>
        </Form.Item>

        <Form.Item
          name="temperature"
          label="Temperature"
          rules={[{ required: true, message: 'Please enter a temperature value' }]}
          extra="Controls randomness: Lower values are more deterministic, higher values more creative."
        >
          <InputNumber min={0} max={1} step={0.1} style={{ width: '100%' }} />
        </Form.Item>

        <Form.Item
          name="maxTokens"
          label="Max Tokens"
          rules={[{ required: true, message: 'Please enter a max tokens value' }]}
          extra="Maximum number of tokens to generate in a response."
        >
          <InputNumber min={100} max={8000} step={100} style={{ width: '100%' }} />
        </Form.Item>

        <Divider orientation="left">Feature Settings</Divider>

        <Form.Item
          name="enablePredictiveAnalytics"
          valuePropName="checked"
          label="Enable Predictive Analytics"
        >
          <Switch />
        </Form.Item>

        <Form.Item
          name="enableDocumentAnalysis"
          valuePropName="checked"
          label="Enable Document Analysis"
        >
          <Switch />
        </Form.Item>

        <Form.Item
          name="enableSmartRecommendations"
          valuePropName="checked"
          label="Enable Smart Recommendations"
        >
          <Switch />
        </Form.Item>

        <Form.Item
          name="enableIntelligentSearch"
          valuePropName="checked"
          label="Enable Intelligent Search"
        >
          <Switch />
        </Form.Item>

        <Form.Item>
          <Space>
            <Button
              type="primary"
              htmlType="submit"
              loading={loading}
              icon={<SaveOutlined />}
            >
              Save Settings
            </Button>
            <Button
              onClick={handleTest}
              loading={loading}
              icon={<ApiOutlined />}
            >
              Test Connection
            </Button>
          </Space>
        </Form.Item>
      </Form>

      <Divider />

      <Title level={5}>Getting an API Key</Title>
      <Paragraph>
        To use Google's Gemini AI, you need to:
        <ol>
          <li>Go to <a href="https://ai.google.dev/" target="_blank" rel="noopener noreferrer">Google AI Studio</a></li>
          <li>Create or sign in to your Google account</li>
          <li>Navigate to the API keys section</li>
          <li>Create a new API key</li>
          <li>Copy and paste the key into the field above</li>
        </ol>
      </Paragraph>

      <Title level={5}>Free vs. Paid Models</Title>
      <Paragraph>
        <Text strong>Free Models:</Text> Gemini 1.0 models (gemini-pro, gemini-pro-vision) are available for free use and don't require billing setup.
        These models provide good performance for most business tasks and are recommended for getting started.
      </Paragraph>

      <Paragraph>
        <Text strong>Paid Models:</Text> Gemini 1.5 and newer models typically require billing setup in Google AI Studio. These models offer improved
        performance but will incur charges based on usage.
      </Paragraph>

      <Paragraph>
        <Text strong>Experimental Models:</Text> Models marked with (Exp) are newer versions that may have limited availability or change without notice.
        If you encounter errors with experimental models, switch to one of the free models which tend to be more stable.
      </Paragraph>

      <Paragraph>
        <Text strong>Auto-Updated Model List:</Text> The application automatically fetches the latest available models from Google's API.
        This ensures you always have access to the most current models, including new experimental ones as they become available.
        You can manually refresh the list by clicking the refresh button next to the model selector.
      </Paragraph>

      <Paragraph type="secondary">
        <Text strong>Note:</Text> API usage for paid models may incur charges from the service provider. Check the
        <a href="https://ai.google.dev/pricing" target="_blank" rel="noopener noreferrer"> pricing details</a> for more information.
      </Paragraph>
    </div>
  );
};

export default AISettingsWrapper;
