

export const activities = {
  name: "Activities",
  icon: "ScheduleOutlined",
  path: "/activities",
  collection: "activities",
  singular: "Activity",
  multi_Branch: true,
  columns: [
    {
      title: "ID",
      dataIndex: "_id",
      hideInForm: true,
      isPrintable: true
    },
    {
      title: "Activity Type",
      dataIndex: "activity_type",
      valueType: "select",
      valueEnum: {
        call: { text: "Call" },
        meeting: { text: "Meeting" },
        email: { text: "Email" },
        task: { text: "Task" },
        note: { text: "Note" },
        other: { text: "Other" },
      },
      isRequired: true,
      colProps: {
        md: 8,
      },
    },
    {
      title: "Subject",
      dataIndex: "subject",
      valueType: "text",
      isRequired: true,
      colProps: {
        md: 16,
      },
    },
    {
      title: "Related To",
      dataIndex: "related_to_type",
      valueType: "select",
      valueEnum: {
        contact: { text: "Contact" },
        deal: { text: "Deal" },
        campaign: { text: "Campaign" },
        bid: { text: "Bid" },
      },
      colProps: {
        md: 8,
      },
    },
    {
      title: "Contact",
      dataIndex: "contact",
      type: "dbSelect",
      collection: "contacts",
      label: ["name"],
      dependencies: ["related_to_type"],
      formItemProps: (form) => {
        const relatedToType = form.getFieldValue("related_to_type");
        if (relatedToType !== "contact" && relatedToType !== undefined) {
          return {
            hidden: true,
          };
        }
        return {
          rules: [{ required: true, message: "Please select a contact" }],
        };
      },
      colProps: {
        md: 16,
      },
    },
    {
      title: "Bid",
      dataIndex: "bid",
      type: "dbSelect",
      collection: "bids",
      label: ["title"],
      dependencies: ["related_to_type"],
      formItemProps: (form) => {
        const relatedToType = form.getFieldValue("related_to_type");
        if (relatedToType !== "bid" && relatedToType !== undefined) {
          return {
            hidden: true,
          };
        }
        return {
          rules: [{ required: true, message: "Please select a bid" }],
        };
      },
      colProps: {
        md: 16,
      },
    },
    {
      title: "Deal",
      dataIndex: "deal",
      type: "dbSelect",
      collection: "deals",
      label: ["name"],
      dependencies: ["related_to_type"],
      formItemProps: (form) => {
        const relatedToType = form.getFieldValue("related_to_type");
        if (relatedToType !== "deal" && relatedToType !== undefined) {
          return {
            hidden: true,
          };
        }
        return {
          rules: [{ required: true, message: "Please select a deal" }],
        };
      },
      colProps: {
        md: 16,
      },
    },
    {
      title: "Campaign",
      dataIndex: "campaign",
      type: "dbSelect",
      collection: "campaigns",
      label: ["name"],
      dependencies: ["related_to_type"],
      formItemProps: (form) => {
        const relatedToType = form.getFieldValue("related_to_type");
        if (relatedToType !== "campaign" && relatedToType !== undefined) {
          return {
            hidden: true,
          };
        }
        return {
          rules: [{ required: true, message: "Please select a campaign" }],
        };
      },
      colProps: {
        md: 16,
      },
    },
    {
      title: "Start Date",
      dataIndex: "start_date",
      valueType: "dateTime",
      isRequired: true,
      colProps: {
        md: 8,
      },
    },
    {
      title: "End Date",
      dataIndex: "end_date",
      valueType: "dateTime",
      dependencies: ["start_date"],
      formItemProps: (form) => {
        const startDate = form.getFieldValue("start_date");
        return {
          rules: [
            {
              validator: (_, value) => {
                if (value && startDate && value < startDate) {
                  return Promise.reject(
                    "End date cannot be earlier than start date"
                  );
                }
                return Promise.resolve();
              },
            },
          ],
        };
      },
      colProps: {
        md: 8,
      },
    },
    {
      title: "Duration (minutes)",
      dataIndex: "duration",
      valueType: "digit",
      min: 1,
      colProps: {
        md: 8,
      },
    },
    {
      title: "Status",
      dataIndex: "status",
      valueType: "select",
      valueEnum: {
        planned: { text: "Planned", status: "Default" },
        in_progress: { text: "In Progress", status: "Processing" },
        completed: { text: "Completed", status: "Success" },
        canceled: { text: "Canceled", status: "Error" },
      },
      isRequired: true,
      colProps: {
        md: 8,
      },
    },
    {
      title: "Priority",
      dataIndex: "priority",
      valueType: "select",
      valueEnum: {
        low: { text: "Low", status: "Default" },
        medium: { text: "Medium", status: "Processing" },
        high: { text: "High", status: "Warning" },
        critical: { text: "Critical", status: "Error" },
      },
      colProps: {
        md: 8,
      },
    },
    {
      title: "Assigned To",
      dataIndex: "assigned_to",
      type: "dbSelect",
      collection: "users",
      label: ["first_name", "last_name"],
      colProps: {
        md: 8,
      },
    },
    {
      title: "Location",
      dataIndex: "location",
      valueType: "text",
      hideInTable: true,
      colProps: {
        md: 12,
      },
    },
    {
      title: "Description",
      dataIndex: "description",
      valueType: "textarea",
      hideInTable: true,
      colProps: {
        span: 24,
      },
    },
    {
      title: "Outcome",
      dataIndex: "outcome",
      valueType: "textarea",
      hideInTable: true,
      colProps: {
        span: 24,
      },
    },
    {
      title: "Created Date",
      dataIndex: "createdAt",
      valueType: "dateTime",
      hideInForm: true,
      sorter: true,
    },
  ],
};
