

export const contacts = {
  name: "Contacts",
  icon: "UserOutlined",
  path: "/contacts",
  collection: "contacts",
  singular: "Contact",
  multi_Branch: true,
  columns: [
    {
      title: "ID",
      dataIndex: "_id",
      hideInForm: true,
      isPrintable: true
    },
    {
      title: "Contact Type",
      dataIndex: "contact_type",
      valueType: "select",
      isRequired: true,
      valueEnum: {
        Individual: {
          text: "Individual",
        },
        Company: {
          text: "Company",
        },
      },
      colProps: {
        md: 8,
      },
      tooltip: "Fundamental field for marketing team",
    },
    {
      title: "Title",
      dataIndex: "title",
      valueType: "select",
      valueEnum: {
        Mr: { text: "Mr" },
        Mrs: { text: "Mrs" },
        Ms: { text: "Ms" },
        Dr: { text: "Dr" },
        Prof: { text: "Prof" },
      },
      colProps: {
        md: 4,
      },
      hideInTable: true,
      dependencies: ["contact_type"],
      formItemProps: (form) => {
        const contactType = form.getFieldValue("contact_type");
        if (contactType === "Company") {
          return {
            hidden: true,
          };
        }
        return {};
      },
    },
    {
      title: "First Name",
      dataIndex: "first_name",
      valueType: "text",
      colProps: {
        md: 8,
      },
      hideInTable: true,
      dependencies: ["contact_type"],
      formItemProps: (form) => {
        const contactType = form.getFieldValue("contact_type");
        if (contactType === "Company") {
          return {
            hidden: true,
          };
        }
        return {
          rules: [{ required: true, message: "Please enter first name" }],
        };
      },
    },
    {
      title: "Last Name",
      dataIndex: "last_name",
      valueType: "text",
      colProps: {
        md: 8,
      },
      hideInTable: true,
      dependencies: ["contact_type"],
      formItemProps: (form) => {
        const contactType = form.getFieldValue("contact_type");
        if (contactType === "Company") {
          return {
            hidden: true,
          };
        }
        return {
          rules: [{ required: true, message: "Please enter last name" }],
        };
      },
    },
    {
      title: "Company Name",
      dataIndex: "company_name",
      valueType: "text",
      colProps: {
        md: 12,
      },
      tooltip: "Name of the company or organization",
      dependencies: ["contact_type"],
      formItemProps: (form) => {
        const contactType = form.getFieldValue("contact_type");
        if (contactType === "Individual") {
          return {
            hidden: true,
          };
        }
        return {
          rules: [{ required: true, message: "Please enter company name" }],
        };
      },
    },
    {
      title: "Industry",
      dataIndex: "industry",
      valueType: "select",
      valueEnum: {
        technology: { text: "Technology" },
        healthcare: { text: "Healthcare" },
        finance: { text: "Finance" },
        education: { text: "Education" },
        manufacturing: { text: "Manufacturing" },
        retail: { text: "Retail" },
        hospitality: { text: "Hospitality" },
        construction: { text: "Construction" },
        transportation: { text: "Transportation" },
        energy: { text: "Energy" },
        agriculture: { text: "Agriculture" },
        other: { text: "Other" },
      },
      colProps: {
        md: 8,
      },
      hideInTable: true,
      dependencies: ["contact_type"],
      formItemProps: (form) => {
        const contactType = form.getFieldValue("contact_type");
        if (contactType !== "Company") {
          return {
            hidden: true,
          };
        }
        return {};
      },
    },
    {
      title: "Annual Revenue",
      dataIndex: "annual_revenue",
      valueType: "money",
      hideInTable: true,
      colProps: {
        md: 8,
      },
      tooltip: "Optional field - not required for marketing purposes",
      dependencies: ["contact_type"],
      formItemProps: (form) => {
        const contactType = form.getFieldValue("contact_type");
        if (contactType !== "Company") {
          return {
            hidden: true,
          };
        }
        return {};
      },
    },
    {
      title: "Number of Employees",
      dataIndex: "employee_count",
      valueType: "digit",
      hideInTable: true,
      colProps: {
        md: 8,
      },
      tooltip: "Optional field - not required for marketing purposes",
      dependencies: ["contact_type"],
      formItemProps: (form) => {
        const contactType = form.getFieldValue("contact_type");
        if (contactType !== "Company") {
          return {
            hidden: true,
          };
        }
        return {};
      },
    },
    {
      title: "Tax ID / VAT Number",
      dataIndex: "tax_id",
      valueType: "text",
      hideInTable: true,
      colProps: {
        md: 8,
      },
      tooltip: "Optional field - not required for marketing purposes",
      dependencies: ["contact_type"],
      formItemProps: (form) => {
        const contactType = form.getFieldValue("contact_type");
        if (contactType !== "Company") {
          return {
            hidden: true,
          };
        }
        return {};
      },
    },
    {
      title: "Name",
      dataIndex: "name",
      valueType: "text",
      sorter: true,
      colProps: {
        md: 8,
      },
      isRequired: true,
      hideInForm: true,
      isPrintable: true,
      tooltip: "Fundamental field for marketing team",
      render: (_, record) => {
        if (record.contact_type === "Company") {
          return record.company_name;
        }
        if (record.contact_type === "Individual") {
          return record.name
            ? record.name
            : `${record.title ? record.title + ". " : ""}${record.first_name} ${record.last_name}`;
        }
      },
    },
    {
      title: "Email",
      dataIndex: "email",
      copyable: true,
      isPrintable: true,
      type: "email",
      colProps: {
        md: 8,
      },
      isRequired: true,
      tooltip: "Fundamental field for marketing team",
    },
    {
      title: "Phone",
      dataIndex: "phone",
      sorter: true,
      type: "phone",
      isPrintable: true,
      colProps: {
        md: 8,
      },
      isRequired: true,
      tooltip: "Fundamental field for marketing team",
    },
    {
      title: "Mobile",
      dataIndex: "mobile",
      sorter: true,
      type: "phone",
      hideInTable: true,
      colProps: {
        md: 8,
      },
    },
    {
      title: "Address",
      dataIndex: "address",
      sorter: true,
      valueType: "text",
      hideInTable: true,
      isPrintable: true,
      colProps: {
        md: 8,
      },
      isRequired: true,
    },
    {
      title: "City",
      dataIndex: "city",
      valueType: "text",
      hideInTable: true,
      colProps: {
        md: 8,
      },
    },
    {
      title: "State/Province",
      dataIndex: "state",
      valueType: "text",
      hideInTable: true,
      colProps: {
        md: 8,
      },
    },
    {
      title: "Postal Code",
      dataIndex: "postal_code",
      valueType: "text",
      hideInTable: true,
      colProps: {
        md: 8,
      },
    },
    {
      title: "Country",
      dataIndex: "country",
      valueType: "text",
      hideInTable: true,
      colProps: {
        md: 8,
      },
    },
    {
      title: "Website",
      dataIndex: "website",
      valueType: "text",
      hideInTable: true,
      colProps: {
        md: 8,
      },
    },
    {
      title: "Source",
      dataIndex: "source",
      valueType: "select",
      valueEnum: {
        website: { text: "Website" },
        referral: { text: "Referral" },
        social_media: { text: "Social Media" },
        event: { text: "Event" },
        cold_call: { text: "Cold Call" },
        other: { text: "Other" },
      },
      hideInTable: true,
      colProps: {
        md: 8,
      },
    },
    {
      title: "Status",
      dataIndex: "status",
      valueType: "select",
      valueEnum: {
        active: { text: "Active", status: "Success" },
        inactive: { text: "Inactive", status: "Error" },
        lead: { text: "Lead", status: "Warning" },
        prospect: { text: "Prospect", status: "Processing" },
        customer: { text: "Customer", status: "Success" },
      },
      colProps: {
        md: 8,
      },
    },
    {
      title: "Tags",
      dataIndex: "tags",
      valueType: "select",
      mode: "tags",
      fieldProps: {
        options: [
          { label: "VIP", value: "vip" },
          { label: "New", value: "new" },
          { label: "Returning", value: "returning" },
          { label: "High Value", value: "high_value" },
        ],
      },
      hideInTable: true,
      colProps: {
        md: 12,
      },
    },
    {
      title: "Notes",
      dataIndex: "notes",
      valueType: "textarea",
      hideInTable: true,
      colProps: {
        span: 24,
      },
    },
    {
      title: "Contact Persons",
      dataIndex: "contact_persons",
      valueType: "formList",
      colProps: {
        span: 24,
      },
      hideInTable: true,
      dependencies: ["contact_type"],
      formItemProps: (form) => {
        const contactType = form.getFieldValue("contact_type");
        if (contactType !== "Company") {
          return {
            hidden: true,
          };
        }
        return {
          rules: [{ required: true, message: "Please add at least one contact person" }],
        };
      },
      columns: [
        {
          title: "Title",
          dataIndex: "title",
          valueType: "select",
          valueEnum: {
            Mr: { text: "Mr" },
            Mrs: { text: "Mrs" },
            Ms: { text: "Ms" },
            Dr: { text: "Dr" },
            Prof: { text: "Prof" },
          },
          colProps: {
            md: 4,
          },
        },
        {
          title: "First Name",
          dataIndex: "first_name",
          valueType: "text",
          colProps: {
            md: 8,
          },
          formItemProps: {
            rules: [{ required: true, message: "Please enter first name" }],
          },
        },
        {
          title: "Last Name",
          dataIndex: "last_name",
          valueType: "text",
          colProps: {
            md: 8,
          },
          formItemProps: {
            rules: [{ required: true, message: "Please enter last name" }],
          },
        },
        {
          title: "Position",
          dataIndex: "position",
          valueType: "text",
          colProps: {
            md: 8,
          },
        },
        {
          title: "Department",
          dataIndex: "department",
          valueType: "text",
          colProps: {
            md: 8,
          },
        },
        {
          title: "Email",
          dataIndex: "email",
          valueType: "text",
          colProps: {
            md: 8,
          },
          formItemProps: {
            rules: [{ required: true, message: "Please enter email" }],
          },
        },
        {
          title: "Phone",
          dataIndex: "phone",
          valueType: "text",
          colProps: {
            md: 8,
          },
          formItemProps: {
            rules: [{ required: true, message: "Please enter phone" }],
          },
        },
        {
          title: "Mobile",
          dataIndex: "mobile",
          valueType: "text",
          colProps: {
            md: 8,
          },
        },
      ],
      render: (_, record) => {
        if (record.contact_type === "Company" && record.contact_persons) {
          return record.contact_persons.map(person =>
            `${person.title ? person.title + ". " : ""}${person.first_name} ${person.last_name}`
          ).join(", ");
        }
        return '';
      },
    },
    {
      title: "Last Contact Date",
      dataIndex: "last_contact_date",
      valueType: "date",
      hideInForm: true,
      sorter: true,
    },
  ],
};
