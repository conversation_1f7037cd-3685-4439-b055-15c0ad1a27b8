

export const campaigns = {
  name: "Campaigns",
  icon: "FundOutlined",
  path: "/campaigns",
  collection: "campaigns",
  singular: "Campaign",
  multi_Branch: true,
  columns: [
    {
      title: "ID",
      dataIndex: "_id",
      hideInForm: true,
      isPrintable: true
    },
    {
      title: "Campaign Name",
      dataIndex: "name",
      valueType: "text",
      sorter: true,
      isRequired: true,
      isPrintable: true,
      colProps: {
        md: 12,
      },
    },
    {
      title: "Campaign Type",
      dataIndex: "campaign_type",
      valueType: "select",
      valueEnum: {
        email: { text: "Email" },
        social_media: { text: "Social Media" },
        event: { text: "Event" },
        webinar: { text: "Webinar" },
        direct_mail: { text: "Direct Mail" },
        telemarketing: { text: "Telemarketing" },
        advertising: { text: "Advertising" },
        other: { text: "Other" },
      },
      isRequired: true,
      colProps: {
        md: 8,
      },
    },
    {
      title: "Status",
      dataIndex: "status",
      valueType: "select",
      valueEnum: {
        planning: { text: "Planning", status: "Default" },
        active: { text: "Active", status: "Processing" },
        completed: { text: "Completed", status: "Success" },
        canceled: { text: "Canceled", status: "Error" },
        on_hold: { text: "On Hold", status: "Warning" },
      },
      isRequired: true,
      colProps: {
        md: 8,
      },
    },
    {
      title: "Start Date",
      dataIndex: "start_date",
      valueType: "date",
      isRequired: true,
      colProps: {
        md: 8,
      },
    },
    {
      title: "End Date",
      dataIndex: "end_date",
      valueType: "date",
      dependencies: ["start_date"],
      formItemProps: (form) => {
        const startDate = form.getFieldValue("start_date");
        return {
          rules: [
            {
              validator: (_, value) => {
                if (value && startDate && value < startDate) {
                  return Promise.reject(
                    "End date cannot be earlier than start date"
                  );
                }
                return Promise.resolve();
              },
            },
          ],
        };
      },
      colProps: {
        md: 8,
      },
    },
    {
      title: "Budget",
      dataIndex: "budget",
      valueType: "money",
      colProps: {
        md: 8,
      },
    },
    {
      title: "Expected Revenue",
      dataIndex: "expected_revenue",
      valueType: "money",
      colProps: {
        md: 8,
      },
    },
    {
      title: "Actual Cost",
      dataIndex: "actual_cost",
      valueType: "money",
      colProps: {
        md: 8,
      },
    },
    {
      title: "Expected Response (%)",
      dataIndex: "expected_response",
      valueType: "digit",
      min: 0,
      max: 100,
      colProps: {
        md: 8,
      },
    },
    {
      title: "Owner",
      dataIndex: "owner",
      type: "dbSelect",
      collection: "users",
      label: ["first_name", "last_name"],
      colProps: {
        md: 8,
      },
    },
    {
      title: "Target Audience",
      dataIndex: "target_audience",
      valueType: "textarea",
      hideInTable: true,
      colProps: {
        span: 24,
      },
    },
    {
      title: "Description",
      dataIndex: "description",
      valueType: "textarea",
      hideInTable: true,
      colProps: {
        span: 24,
      },
    },
    {
      title: "Success Metrics",
      dataIndex: "success_metrics",
      valueType: "textarea",
      hideInTable: true,
      colProps: {
        span: 24,
      },
    },
    {
      title: "Tags",
      dataIndex: "tags",
      valueType: "select",
      mode: "tags",
      fieldProps: {
        options: [
          { label: "Q1", value: "q1" },
          { label: "Q2", value: "q2" },
          { label: "Q3", value: "q3" },
          { label: "Q4", value: "q4" },
          { label: "Lead Generation", value: "lead_generation" },
          { label: "Brand Awareness", value: "brand_awareness" },
          { label: "Product Launch", value: "product_launch" },
        ],
      },
      hideInTable: true,
      colProps: {
        md: 12,
      },
    },
    {
      title: "Created Date",
      dataIndex: "createdAt",
      valueType: "dateTime",
      hideInForm: true,
      sorter: true,
    },
  ],
};
