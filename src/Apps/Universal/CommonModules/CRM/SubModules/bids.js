

export const bids = {
  name: "Bids",
  icon: "FileProtectOutlined",
  path: "/bids",
  collection: "bids",
  singular: "Bid",
  multi_Branch: true,
  columns: [
    {
      title: "ID",
      dataIndex: "_id",
      hideInForm: true,
      isPrintable: true
    },
    {
      title: "Bid Title",
      dataIndex: "title",
      valueType: "text",
      sorter: true,
      isRequired: true,
      isPrintable: true,
      colProps: {
        md: 12,
      },
    },
    {
      title: "Organisation/Company",
      dataIndex: "client",
      type: "dbSelect",
      collection: "contacts",
      label: ["name"],
      isRequired: true,
      colProps: {
        md: 12,
      },
      fieldProps: {
        showSearch: true,
        filterOption: (input, option) =>
          option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0,
        params: {
          contact_type: "Company"
        },
      },
    },
    {
      title: "Contact Person",
      dataIndex: "contact_name",
      hideInTable: true,
      valueType: "text",
      colProps: {
        md: 12,
      },
    },
    {
      title: "Contact Person Phone",
      dataIndex: "contact_phone",
      hideInTable: true,
      valueType: "text",
      colProps: {
        md: 12,
      },
    },
    {
      title: "Cost of Bid",
      dataIndex: "value",
      valueType: "money",
      sorter: true,
      tooltip: "Optional field - not mandatory for marketing purposes",
      colProps: {
        md: 8,
      },
    },
    {
      title: "Currency",
      dataIndex: "currency",
      valueType: "select",
      hideInTable: true,
      valueEnum: {
        USD: { text: "USD" },
        EUR: { text: "EUR" },
        GBP: { text: "GBP" },
        JPY: { text: "JPY" },
        AUD: { text: "AUD" },
        CAD: { text: "CAD" },
        UGX: { text: "UGX" },
      },
      initialValue: "UGX",
      colProps: {
        md: 8,
      },
    },
    {
      title: "Status",
      dataIndex: "status",
      valueType: "select",
      valueEnum: {
        pending: { text: "Pending", status: "Processing" },
        approved: { text: "Approved", status: "Success" },
        rejected: { text: "Rejected", status: "Error" },
      },
      isRequired: true,
      initialValue: "pending",
      colProps: {
        md: 8,
      },
    },
    {
      title: "Bid Type",
      dataIndex: "bid_type",
      valueType: "select",
      valueEnum: {
        framework: { text: "Framework" },
        prequalification: { text: "Prequalification" },
        eoi: { text: "Expression of Interest" },
        rfp: { text: "RFP (Request for Proposal)" },
        rfi: { text: "RFI (Request for Information)" },
        rfq: { text: "RFQ (Request for Quotation)" },
        itb: { text: "ITB (Invitation to Bid)" },
        tender: { text: "Tender" },
        other: { text: "Other" },
      },
      isRequired: true,
      colProps: {
        md: 8,
      },
    },
    {
      title: "Submission Deadline",
      dataIndex: "submission_deadline",
      valueType: "dateTime",
      sorter: true,
      isRequired: true,
      colProps: {
        md: 8,
      },
    },
    {
      title: "Decision Date",
      dataIndex: "decision_date",
      hideInTable: true,
      valueType: "date",
      sorter: true,
      colProps: {
        md: 8,
      },
    },
    {
      title: "Probability (%)",
      dataIndex: "probability",
      valueType: "digit",
      min: 0,
      max: 100,
      colProps: {
        md: 8,
      },
    },
    {
      title: "Bid Officer",
      dataIndex: "bid_officer",
      type: "dbSelect",
      collection: "users",
      label: ["first_name", "last_name"],
      colProps: {
        md: 8,
      },
    },
    {
      title: "Related Deal",
      dataIndex: "deal",
      hideInTable: true,
      type: "dbSelect",
      collection: "deals",
      label: ["name"],
      colProps: {
        md: 12,
      },
    },
    {
      title: "Bid Team",
      dataIndex: "bid_team",
      type: "dbSelect",
      collection: "users",
      label: ["first_name", "last_name"],
      mode: "multiple",
      colProps: {
        md: 12,
      },
      hideInTable: true,
    },
    {
      title: "Competitors",
      dataIndex: "competitors",
      valueType: "select",
      mode: "tags",
      fieldProps: {
        options: [],
      },
      hideInTable: true,
      colProps: {
        md: 12,
      },
    },
    {
      title: "Requirements",
      dataIndex: "requirements",
      valueType: "textarea",
      hideInTable: true,
      colProps: {
        span: 24,
      },
    },
    {
      title: "Scope of Work",
      dataIndex: "scope_of_work",
      valueType: "textarea",
      hideInTable: true,
      colProps: {
        span: 24,
      },
    },
    {
      title: "Notes",
      dataIndex: "notes",
      valueType: "textarea",
      hideInTable: true,
      colProps: {
        span: 24,
      },
    },
    {
      title: "Marketing Feedback",
      dataIndex: "marketing_feedback",
      valueType: "textarea",
      hideInTable: true,
      colProps: {
        span: 24,
      },
      dependencies: ["status", "marketing_person"],
      formItemProps: (form) => {
        const status = form.getFieldValue("status");
        const marketingPerson = form.getFieldValue("marketing_person");
        if (status !== "approved" || !marketingPerson) {
          return {
            hidden: true,
          };
        }
        return {};
      },
    },
    {
      title: "Tags",
      dataIndex: "tags",
      valueType: "select",
      mode: "tags",
      fieldProps: {
        options: [
          { label: "High Priority", value: "high_priority" },
          { label: "Strategic", value: "strategic" },
          { label: "Government", value: "government" },
          { label: "Private Sector", value: "private_sector" },
          { label: "NGO", value: "ngo" },
        ],
      },
      hideInTable: true,
      colProps: {
        md: 12,
      },
    },
  ],
};
