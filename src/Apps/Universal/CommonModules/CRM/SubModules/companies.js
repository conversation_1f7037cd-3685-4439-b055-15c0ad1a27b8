

export const companies = {
  name: "Companies",
  icon: "BankOutlined",
  path: "/companies",
  collection: "companies",
  singular: "Company",
  multi_Branch: true,
  columns: [
    {
      title: "ID",
      dataIndex: "_id",
      hideInForm: true,
      isPrintable: true
    },
    {
      title: "Name",
      dataIndex: "name",
      valueType: "text",
      sorter: true,
      isRequired: true,
      isPrintable: true,
      colProps: {
        md: 12,
      },
    },
    {
      title: "Industry",
      dataIndex: "industry",
      valueType: "select",
      valueEnum: {
        technology: { text: "Technology" },
        healthcare: { text: "Healthcare" },
        finance: { text: "Finance" },
        education: { text: "Education" },
        manufacturing: { text: "Manufacturing" },
        retail: { text: "Retail" },
        hospitality: { text: "Hospitality" },
        construction: { text: "Construction" },
        transportation: { text: "Transportation" },
        energy: { text: "Energy" },
        agriculture: { text: "Agriculture" },
        other: { text: "Other" },
      },
      colProps: {
        md: 8,
      },
    },
    {
      title: "Website",
      dataIndex: "website",
      valueType: "text",
      colProps: {
        md: 8,
      },
    },
    {
      title: "Email",
      dataIndex: "email",
      copyable: true,
      isPrintable: true,
      type: "email",
      colProps: {
        md: 8,
      },
    },
    {
      title: "Phone",
      dataIndex: "phone",
      sorter: true,
      type: "phone",
      isPrintable: true,
      colProps: {
        md: 8,
      },
      isRequired: true,
    },
    {
      title: "Address",
      dataIndex: "address",
      sorter: true,
      valueType: "text",
      hideInTable: true,
      isPrintable: true,
      colProps: {
        md: 8,
      },
      isRequired: true,
    },
    {
      title: "City",
      dataIndex: "city",
      valueType: "text",
      hideInTable: true,
      colProps: {
        md: 8,
      },
    },
    {
      title: "State/Province",
      dataIndex: "state",
      valueType: "text",
      hideInTable: true,
      colProps: {
        md: 8,
      },
    },
    {
      title: "Postal Code",
      dataIndex: "postal_code",
      valueType: "text",
      hideInTable: true,
      colProps: {
        md: 8,
      },
    },
    {
      title: "Country",
      dataIndex: "country",
      valueType: "text",
      hideInTable: true,
      colProps: {
        md: 8,
      },
    },
    {
      title: "Annual Revenue",
      dataIndex: "annual_revenue",
      valueType: "money",
      hideInTable: true,
      colProps: {
        md: 8,
      },
      tooltip: "Optional field - not required for marketing purposes",
    },
    {
      title: "Number of Employees",
      dataIndex: "employee_count",
      valueType: "digit",
      hideInTable: true,
      colProps: {
        md: 8,
      },
      tooltip: "Optional field - not required for marketing purposes",
    },
    {
      title: "Tax ID / VAT Number",
      dataIndex: "tax_id",
      valueType: "text",
      hideInTable: true,
      colProps: {
        md: 8,
      },
      tooltip: "Optional field - not required for marketing purposes",
    },
    {
      title: "LinkedIn",
      dataIndex: "linkedin",
      valueType: "text",
      hideInTable: true,
      colProps: {
        md: 8,
      },
    },
    {
      title: "Twitter",
      dataIndex: "twitter",
      valueType: "text",
      hideInTable: true,
      colProps: {
        md: 8,
      },
    },
    {
      title: "Facebook",
      dataIndex: "facebook",
      valueType: "text",
      hideInTable: true,
      colProps: {
        md: 8,
      },
    },
    {
      title: "Source",
      dataIndex: "source",
      valueType: "select",
      valueEnum: {
        website: { text: "Website" },
        referral: { text: "Referral" },
        social_media: { text: "Social Media" },
        event: { text: "Event" },
        cold_call: { text: "Cold Call" },
        other: { text: "Other" },
      },
      hideInTable: true,
      colProps: {
        md: 8,
      },
    },
    {
      title: "Status",
      dataIndex: "status",
      valueType: "select",
      valueEnum: {
        active: { text: "Active", status: "Success" },
        inactive: { text: "Inactive", status: "Error" },
        lead: { text: "Lead", status: "Warning" },
        prospect: { text: "Prospect", status: "Processing" },
        customer: { text: "Customer", status: "Success" },
      },
      colProps: {
        md: 8,
      },
    },
    {
      title: "Tags",
      dataIndex: "tags",
      valueType: "select",
      mode: "tags",
      fieldProps: {
        options: [
          { label: "VIP", value: "vip" },
          { label: "Enterprise", value: "enterprise" },
          { label: "SMB", value: "smb" },
          { label: "Startup", value: "startup" },
          { label: "Partner", value: "partner" },
        ],
      },
      hideInTable: true,
      colProps: {
        md: 12,
      },
    },
    {
      title: "Notes",
      dataIndex: "notes",
      valueType: "textarea",
      hideInTable: true,
      colProps: {
        span: 24,
      },
    },
    {
      title: "Primary Contact",
      dataIndex: "primary_contact",
      type: "dbSelect",
      collection: "contacts",
      label: ["name"],
      colProps: {
        md: 12,
      },
      isRequired: true,
      render: (_, record) => {
        return record.primary_contact?.label || '';
      },
    },
    {
      title: "Last Contact Date",
      dataIndex: "last_contact_date",
      valueType: "date",
      hideInForm: true,
      sorter: true,
    },
  ],
};
