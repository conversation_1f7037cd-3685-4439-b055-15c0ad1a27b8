

export const deals = {
  name: "Deals",
  icon: "DollarOutlined",
  path: "/deals",
  collection: "deals",
  singular: "Deal",
  multi_Branch: true,
  columns: [
    {
      title: "ID",
      dataIndex: "_id",
      hideInForm: true,
      isPrintable: true
    },
    {
      title: "Deal Name",
      dataIndex: "name",
      valueType: "text",
      sorter: true,
      isRequired: true,
      isPrintable: true,
      colProps: {
        md: 12,
      },
    },
    {
      title: "Contact",
      dataIndex: "contact",
      type: "dbSelect",
      collection: "contacts",
      label: ["name"],
      isRequired: true,
      colProps: {
        md: 12,
      },
    },
    {
      title: "Company",
      dataIndex: "company",
      type: "dbSelect",
      collection: "contacts",
      label: ["name"],
      colProps: {
        md: 12,
      },
      fieldProps: {
        showSearch: true,
        filterOption: (input, option) =>
          option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0,
        params: {
          contact_type: "Company"
        },
      },
    },
    {
      title: "Value",
      dataIndex: "value",
      valueType: "money",
      sorter: true,
      isRequired: true,
      colProps: {
        md: 8,
      },
    },
    {
      title: "Currency",
      dataIndex: "currency",
      valueType: "select",
      valueEnum: {
        USD: { text: "USD" },
        EUR: { text: "EUR" },
        GBP: { text: "GBP" },
        JPY: { text: "JPY" },
        AUD: { text: "AUD" },
        CAD: { text: "CAD" },
        UGX: { text: "UGX" },
      },
      initialValue: "UGX",
      colProps: {
        md: 8,
      },
    },
    {
      title: "Stage",
      dataIndex: "stage",
      valueType: "select",
      valueEnum: {
        lead: { text: "Lead", status: "Default" },
        qualified: { text: "Qualified", status: "Processing" },
        proposal: { text: "Proposal", status: "Warning" },
        negotiation: { text: "Negotiation", status: "Warning" },
        closed_won: { text: "Closed Won", status: "Success" },
        closed_lost: { text: "Closed Lost", status: "Error" },
      },
      isRequired: true,
      colProps: {
        md: 8,
      },
    },
    {
      title: "Probability (%)",
      dataIndex: "probability",
      valueType: "digit",
      min: 0,
      max: 100,
      colProps: {
        md: 8,
      },
    },
    {
      title: "Expected Close Date",
      dataIndex: "expected_close_date",
      valueType: "date",
      sorter: true,
      isRequired: true,
      colProps: {
        md: 8,
      },
    },
    {
      title: "Source",
      dataIndex: "source",
      valueType: "select",
      valueEnum: {
        website: { text: "Website" },
        referral: { text: "Referral" },
        social_media: { text: "Social Media" },
        event: { text: "Event" },
        cold_call: { text: "Cold Call" },
        other: { text: "Other" },
      },
      colProps: {
        md: 8,
      },
    },
    {
      title: "Owner",
      dataIndex: "owner",
      type: "dbSelect",
      collection: "users",
      label: ["first_name", "last_name"],
      colProps: {
        md: 8,
      },
    },
    {
      title: "Priority",
      dataIndex: "priority",
      valueType: "select",
      valueEnum: {
        low: { text: "Low", status: "Default" },
        medium: { text: "Medium", status: "Processing" },
        high: { text: "High", status: "Warning" },
        critical: { text: "Critical", status: "Error" },
      },
      colProps: {
        md: 8,
      },
    },
    {
      title: "Products/Services",
      dataIndex: "products",
      valueType: "select",
      mode: "tags",
      fieldProps: {
        options: [
          { label: "Product A", value: "product_a" },
          { label: "Product B", value: "product_b" },
          { label: "Service X", value: "service_x" },
          { label: "Service Y", value: "service_y" },
        ],
      },
      hideInTable: true,
      colProps: {
        md: 12,
      },
    },
    {
      title: "Tags",
      dataIndex: "tags",
      valueType: "select",
      mode: "tags",
      fieldProps: {
        options: [
          { label: "Hot", value: "hot" },
          { label: "Strategic", value: "strategic" },
          { label: "Renewal", value: "renewal" },
          { label: "Upsell", value: "upsell" },
        ],
      },
      hideInTable: true,
      colProps: {
        md: 12,
      },
    },
    {
      title: "Notes",
      dataIndex: "notes",
      valueType: "textarea",
      hideInTable: true,
      colProps: {
        span: 24,
      },
    },
    {
      title: "Last Activity Date",
      dataIndex: "last_activity_date",
      valueType: "date",
      hideInForm: true,
      sorter: true,
    },
    {
      title: "Created Date",
      dataIndex: "createdAt",
      valueType: "dateTime",
      hideInForm: true,
      sorter: true,
    },
  ],
};
