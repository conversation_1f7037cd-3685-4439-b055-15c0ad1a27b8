import Analytics from "./AdvancedFeatures/Analytics";
import HybridModule from "../../../../Components/HybridModule";
import Pipeline from "./AdvancedFeatures/Pipeline";
import React from "react";
import subModuleProperties from "./subModuleProperties";
import { modules as subModules } from "./SubModules";
import { scheduleAutomations } from "./AdvancedFeatures/Automation";


const CRM = (props) => {
  // Initialize automation schedules
  React.useEffect(() => {
    scheduleAutomations(props);
  }, []);

  const enhancedProps = {
    ...props,
    dashboard: {
      enabled: props.userPermissions?.root || props.userPermissions?.permissions?.some(p =>
        p.module === "crm_dashboard" && p.view
      ),
      components: {
        Pipeline: props.userPermissions?.root || props.userPermissions?.permissions?.some(p =>
          p.module === "crm_pipeline" && p.view
        ) ? Pipeline : null,
        Analytics: props.userPermissions?.root || props.userPermissions?.permissions?.some(p =>
          p.module === "crm_analytics" && p.view
        ) ? Analytics : null
      },
      layout: [
        { component: 'Pipeline', span: 24 },
        { component: 'Analytics', span: 24 }
      ]
    },
    features: {
      emailIntegration: props.userPermissions?.root || props.userPermissions?.permissions?.some(p =>
        p.module === "crm_email" && p.view
      ),
      documentManagement: props.userPermissions?.root || props.userPermissions?.permissions?.some(p =>
        p.module === "crm_documents" && p.view
      ),
      taskAutomation: props.userPermissions?.root || props.userPermissions?.permissions?.some(p =>
        p.module === "crm_automation" && p.view
      ),
      reporting: props.userPermissions?.root || props.userPermissions?.permissions?.some(p =>
        p.module === "crm_reporting" && p.view
      )
    }
  };

  return (
    <HybridModule
      subModules={subModules}
      subModuleProperties={subModuleProperties}
      {...enhancedProps}
    />
  );
};

export default CRM;