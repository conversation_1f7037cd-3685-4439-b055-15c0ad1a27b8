import React, { useState, useEffect } from "react";
import moment from "moment";
import { Card, Row, Col, Select, DatePicker, Spin, Empty  } from "antd";
import { Column, Pie, Line  } from "@ant-design/plots";


const { RangePicker } = DatePicker;
const { Option } = Select;

const Analytics = ({ pouchDatabase, databasePrefix }) => {
  const [loading, setLoading] = useState(true);
  const [data, setData] = useState({
    contacts: [],
    companies: [],
    deals: [],
    activities: [],
    campaigns: [],
  });
  const [dateRange, setDateRange] = useState([
    moment().subtract(6, "months").startOf("month"),
    moment().endOf("month"),
  ]);
  const [viewType, setViewType] = useState("deals"); // deals, contacts, activities, campaigns

  useEffect(() => {
    loadData();
  }, [dateRange]);

  const loadData = async () => {
    setLoading(true);
    try {
      const [contacts, companies, deals, activities, campaigns] = await Promise.all([
        pouchDatabase("contacts", databasePrefix).getAllData(),
        pouchDatabase("companies", databasePrefix).getAllData(),
        pouchDatabase("deals", databasePrefix).getAllData(),
        pouchDatabase("activities", databasePrefix).getAllData(),
        pouchDatabase("campaigns", databasePrefix).getAllData(),
      ]);

      setData({
        contacts,
        companies,
        deals,
        activities,
        campaigns,
      });
      setLoading(false);
    } catch (error) {
      console.error("Error loading data:", error);
      setLoading(false);
    }
  };

  // Filter data by date range
  const filterByDateRange = (items, dateField = "createdAt") => {
    if (!dateRange || dateRange.length !== 2) return items;
    
    return items.filter((item) => {
      const itemDate = moment(item[dateField]);
      return (
        itemDate.isValid() &&
        itemDate.isSameOrAfter(dateRange[0], "day") &&
        itemDate.isSameOrBefore(dateRange[1], "day")
      );
    });
  };

  // Prepare data for deal stage distribution chart
  const getDealStageData = () => {
    const filteredDeals = filterByDateRange(data.deals, "expected_close_date");
    const stageGroups = {};
    
    filteredDeals.forEach((deal) => {
      const stage = deal.stage || "unknown";
      if (!stageGroups[stage]) {
        stageGroups[stage] = {
          stage: formatStage(stage),
          count: 0,
          value: 0,
        };
      }
      stageGroups[stage].count += 1;
      stageGroups[stage].value += deal.value || 0;
    });
    
    return Object.values(stageGroups);
  };

  // Prepare data for deal value by month chart
  const getDealValueByMonthData = () => {
    const filteredDeals = filterByDateRange(data.deals, "expected_close_date");
    const monthlyData = {};
    
    filteredDeals.forEach((deal) => {
      if (!deal.expected_close_date) return;
      
      const month = moment(deal.expected_close_date).format("YYYY-MM");
      const stage = deal.stage || "unknown";
      
      if (!monthlyData[month]) {
        monthlyData[month] = {
          month,
          lead: 0,
          qualified: 0,
          proposal: 0,
          negotiation: 0,
          closed_won: 0,
          closed_lost: 0,
          total: 0,
        };
      }
      
      monthlyData[month][stage] = (monthlyData[month][stage] || 0) + (deal.value || 0);
      monthlyData[month].total += deal.value || 0;
    });
    
    return Object.values(monthlyData).sort((a, b) => a.month.localeCompare(b.month));
  };

  // Prepare data for contact source distribution chart
  const getContactSourceData = () => {
    const filteredContacts = filterByDateRange(data.contacts);
    const sourceGroups = {};
    
    filteredContacts.forEach((contact) => {
      const source = contact.source || "unknown";
      if (!sourceGroups[source]) {
        sourceGroups[source] = {
          source: formatSource(source),
          count: 0,
        };
      }
      sourceGroups[source].count += 1;
    });
    
    return Object.values(sourceGroups);
  };

  // Prepare data for activity type distribution chart
  const getActivityTypeData = () => {
    const filteredActivities = filterByDateRange(data.activities, "start_date");
    const typeGroups = {};
    
    filteredActivities.forEach((activity) => {
      const type = activity.activity_type || "unknown";
      if (!typeGroups[type]) {
        typeGroups[type] = {
          type: formatActivityType(type),
          count: 0,
        };
      }
      typeGroups[type].count += 1;
    });
    
    return Object.values(typeGroups);
  };

  // Prepare data for campaign performance chart
  const getCampaignPerformanceData = () => {
    const filteredCampaigns = filterByDateRange(data.campaigns, "start_date");
    
    return filteredCampaigns.map((campaign) => ({
      name: campaign.name || "Unnamed Campaign",
      budget: campaign.budget || 0,
      actual_cost: campaign.actual_cost || 0,
      expected_revenue: campaign.expected_revenue || 0,
    }));
  };

  // Helper functions to format labels
  const formatStage = (stage) => {
    if (!stage) return "Unknown";
    return stage
      .split("_")
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
      .join(" ");
  };

  const formatSource = (source) => {
    if (!source) return "Unknown";
    return source
      .split("_")
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
      .join(" ");
  };

  const formatActivityType = (type) => {
    if (!type) return "Unknown";
    return type
      .split("_")
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
      .join(" ");
  };

  // Chart configurations
  const dealStageConfig = {
    data: getDealStageData(),
    xField: "stage",
    yField: "value",
    seriesField: "stage",
    legend: { position: "top" },
    label: {
      position: "middle",
      style: {
        fill: "#FFFFFF",
        opacity: 0.6,
      },
    },
    xAxis: {
      label: {
        autoHide: true,
        autoRotate: false,
      },
    },
  };

  const dealValueByMonthConfig = {
    data: getDealValueByMonthData(),
    xField: "month",
    yField: "total",
    seriesField: "month",
    xAxis: {
      type: "time",
      label: {
        formatter: (v) => moment(v).format("MMM YYYY"),
      },
    },
    tooltip: {
      formatter: (datum) => {
        return {
          name: moment(datum.month).format("MMMM YYYY"),
          value: datum.total.toLocaleString(),
        };
      },
    },
  };

  const contactSourceConfig = {
    data: getContactSourceData(),
    angleField: "count",
    colorField: "source",
    radius: 0.8,
    label: {
      type: "outer",
      content: "{name}: {percentage}",
    },
    interactions: [{ type: "pie-legend-active" }, { type: "element-active" }],
  };

  const activityTypeConfig = {
    data: getActivityTypeData(),
    angleField: "count",
    colorField: "type",
    radius: 0.8,
    label: {
      type: "outer",
      content: "{name}: {percentage}",
    },
    interactions: [{ type: "pie-legend-active" }, { type: "element-active" }],
  };

  const campaignPerformanceConfig = {
    data: getCampaignPerformanceData(),
    isGroup: true,
    xField: "name",
    yField: "value",
    seriesField: "type",
    label: {
      position: "middle",
      layout: [
        { type: "interval-adjust-position" },
        { type: "interval-hide-overlap" },
        { type: "adjust-color" },
      ],
    },
    meta: {
      name: {
        alias: "Campaign",
      },
      value: {
        alias: "Amount",
      },
      type: {
        alias: "Type",
      },
    },
  };

  const renderCharts = () => {
    if (loading) {
      return (
        <div style={{ textAlign: "center", padding: 24 }}>
          <Spin size="large" />
        </div>
      );
    }

    switch (viewType) {
      case "deals":
        return (
          <>
            <Row gutter={[16, 16]}>
              <Col span={24}>
                <Card title="Deal Value by Stage">
                  {getDealStageData().length > 0 ? (
                    <Column {...dealStageConfig} height={300} />
                  ) : (
                    <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
                  )}
                </Card>
              </Col>
            </Row>
            <Row gutter={[16, 16]} style={{ marginTop: 16 }}>
              <Col span={24}>
                <Card title="Deal Value by Month">
                  {getDealValueByMonthData().length > 0 ? (
                    <Line {...dealValueByMonthConfig} height={300} />
                  ) : (
                    <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
                  )}
                </Card>
              </Col>
            </Row>
          </>
        );
      case "contacts":
        return (
          <Row gutter={[16, 16]}>
            <Col span={24}>
              <Card title="Contact Source Distribution">
                {getContactSourceData().length > 0 ? (
                  <Pie {...contactSourceConfig} height={400} />
                ) : (
                  <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
                )}
              </Card>
            </Col>
          </Row>
        );
      case "activities":
        return (
          <Row gutter={[16, 16]}>
            <Col span={24}>
              <Card title="Activity Type Distribution">
                {getActivityTypeData().length > 0 ? (
                  <Pie {...activityTypeConfig} height={400} />
                ) : (
                  <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
                )}
              </Card>
            </Col>
          </Row>
        );
      case "campaigns":
        const campaignData = getCampaignPerformanceData();
        // Transform data for column chart
        const transformedData = [];
        campaignData.forEach((campaign) => {
          transformedData.push({
            name: campaign.name,
            type: "Budget",
            value: campaign.budget,
          });
          transformedData.push({
            name: campaign.name,
            type: "Actual Cost",
            value: campaign.actual_cost,
          });
          transformedData.push({
            name: campaign.name,
            type: "Expected Revenue",
            value: campaign.expected_revenue,
          });
        });
        
        return (
          <Row gutter={[16, 16]}>
            <Col span={24}>
              <Card title="Campaign Performance">
                {campaignData.length > 0 ? (
                  <Column
                    data={transformedData}
                    isGroup={true}
                    xField="name"
                    yField="value"
                    seriesField="type"
                    height={400}
                  />
                ) : (
                  <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
                )}
              </Card>
            </Col>
          </Row>
        );
      default:
        return null;
    }
  };

  return (
    <Card title="CRM Analytics" style={{ marginBottom: 24 }}>
      <Row gutter={[16, 16]} style={{ marginBottom: 16 }}>
        <Col span={12}>
          <RangePicker
            value={dateRange}
            onChange={setDateRange}
            style={{ width: "100%" }}
          />
        </Col>
        <Col span={12}>
          <Select
            value={viewType}
            onChange={setViewType}
            style={{ width: 200, float: "right" }}
          >
            <Option value="deals">Deals</Option>
            <Option value="contacts">Contacts</Option>
            <Option value="activities">Activities</Option>
            <Option value="campaigns">Campaigns</Option>
          </Select>
        </Col>
      </Row>

      {renderCharts()}
    </Card>
  );
};

export default Analytics;
