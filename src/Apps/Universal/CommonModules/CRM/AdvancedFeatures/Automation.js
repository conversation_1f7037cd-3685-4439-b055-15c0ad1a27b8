import moment from "moment";
import { message } from "antd";


export const crmAutomationRules = {
  // Follow-up reminder for activities
  activityFollowUp: async ({ pouchDatabase, databasePrefix, CRUD_USER }) => {
    try {
      // Get all activities that are completed in the last 7 days
      const today = moment();
      const sevenDaysAgo = moment().subtract(7, "days");
      
      const activities = await pouchDatabase("activities", databasePrefix).getAllData();
      const completedActivities = activities.filter(
        (activity) =>
          activity.status === "completed" &&
          moment(activity.start_date).isBetween(sevenDaysAgo, today)
      );
      
      for (const activity of completedActivities) {
        // Check if a follow-up activity already exists
        const followUps = activities.filter(
          (a) =>
            a.subject &&
            a.subject.includes(`Follow-up to: ${activity.subject}`)
        );
        
        if (followUps.length === 0) {
          // Create a follow-up activity
          const followUpDate = moment().add(7, "days").toISOString();
          
          // Determine the related entity
          let relatedEntity = {};
          if (activity.related_to_type === "contact" && activity.contact) {
            relatedEntity = {
              related_to_type: "contact",
              contact: activity.contact,
            };
          } else if (activity.related_to_type === "company" && activity.company) {
            relatedEntity = {
              related_to_type: "company",
              company: activity.company,
            };
          } else if (activity.related_to_type === "deal" && activity.deal) {
            relatedEntity = {
              related_to_type: "deal",
              deal: activity.deal,
            };
          } else if (activity.related_to_type === "campaign" && activity.campaign) {
            relatedEntity = {
              related_to_type: "campaign",
              campaign: activity.campaign,
            };
          }
          
          await pouchDatabase("activities", databasePrefix).saveDocument(
            {
              activity_type: "task",
              subject: `Follow-up to: ${activity.subject}`,
              description: `This is an automated follow-up to the previous activity: ${activity.description || activity.subject}`,
              start_date: followUpDate,
              status: "planned",
              priority: "medium",
              ...relatedEntity,
            },
            CRUD_USER
          );
        }
      }
    } catch (error) {
      console.error("Error in activityFollowUp automation:", error);
    }
  },
  
  // Deal stage progression reminder
  dealStageProgression: async ({ pouchDatabase, databasePrefix, CRUD_USER }) => {
    try {
      // Get all active deals
      const deals = await pouchDatabase("deals", databasePrefix).getAllData();
      const activeDeals = deals.filter(
        (deal) =>
          deal.stage !== "closed_won" && deal.stage !== "closed_lost"
      );
      
      const today = moment();
      
      for (const deal of activeDeals) {
        // Check how long the deal has been in the current stage
        const lastActivityDate = deal.last_activity_date
          ? moment(deal.last_activity_date)
          : moment(deal.createdAt);
        
        const daysSinceLastActivity = today.diff(lastActivityDate, "days");
        
        // If it's been more than 14 days since the last activity, create a reminder
        if (daysSinceLastActivity > 14) {
          // Check if a reminder activity already exists
          const activities = await pouchDatabase("activities", databasePrefix).getAllData();
          const reminders = activities.filter(
            (a) =>
              a.related_to_type === "deal" &&
              a.deal &&
              a.deal.value === deal._id &&
              a.subject &&
              a.subject.includes("Deal progression reminder") &&
              moment(a.start_date).isAfter(moment().subtract(7, "days"))
          );
          
          if (reminders.length === 0) {
            // Create a reminder activity
            await pouchDatabase("activities", databasePrefix).saveDocument(
              {
                activity_type: "task",
                subject: `Deal progression reminder: ${deal.name}`,
                description: `This deal has been in the ${deal.stage} stage for ${daysSinceLastActivity} days. Consider following up or updating the stage.`,
                start_date: today.toISOString(),
                status: "planned",
                priority: "high",
                related_to_type: "deal",
                deal: {
                  value: deal._id,
                  label: deal.name,
                  ...deal,
                },
              },
              CRUD_USER
            );
          }
        }
      }
    } catch (error) {
      console.error("Error in dealStageProgression automation:", error);
    }
  },
  
  // Contact engagement scoring
  contactEngagementScoring: async ({ pouchDatabase, databasePrefix, CRUD_USER }) => {
    try {
      // Get all contacts
      const contacts = await pouchDatabase("contacts", databasePrefix).getAllData();
      
      // Get all activities
      const activities = await pouchDatabase("activities", databasePrefix).getAllData();
      
      for (const contact of contacts) {
        // Count recent activities related to this contact
        const contactActivities = activities.filter(
          (a) =>
            a.related_to_type === "contact" &&
            a.contact &&
            a.contact.value === contact._id &&
            moment(a.start_date).isAfter(moment().subtract(90, "days"))
        );
        
        // Calculate engagement score (simple version)
        const engagementScore = contactActivities.length;
        
        // Update contact with engagement score if it's different
        if (contact.engagement_score !== engagementScore) {
          await pouchDatabase("contacts", databasePrefix).saveDocument(
            {
              ...contact,
              engagement_score: engagementScore,
              engagement_level:
                engagementScore === 0
                  ? "inactive"
                  : engagementScore < 3
                  ? "low"
                  : engagementScore < 6
                  ? "medium"
                  : "high",
            },
            CRUD_USER
          );
        }
      }
    } catch (error) {
      console.error("Error in contactEngagementScoring automation:", error);
    }
  },
  
  // Deal win probability update
  dealWinProbabilityUpdate: async ({ pouchDatabase, databasePrefix, CRUD_USER }) => {
    try {
      // Get all active deals
      const deals = await pouchDatabase("deals", databasePrefix).getAllData();
      const activeDeals = deals.filter(
        (deal) =>
          deal.stage !== "closed_won" && deal.stage !== "closed_lost"
      );
      
      // Get all activities
      const activities = await pouchDatabase("activities", databasePrefix).getAllData();
      
      for (const deal of activeDeals) {
        // Count recent activities related to this deal
        const dealActivities = activities.filter(
          (a) =>
            a.related_to_type === "deal" &&
            a.deal &&
            a.deal.value === deal._id &&
            moment(a.start_date).isAfter(moment().subtract(30, "days"))
        );
        
        // Base probability on stage
        let baseProbability = 0;
        switch (deal.stage) {
          case "lead":
            baseProbability = 10;
            break;
          case "qualified":
            baseProbability = 30;
            break;
          case "proposal":
            baseProbability = 50;
            break;
          case "negotiation":
            baseProbability = 70;
            break;
          default:
            baseProbability = 20;
        }
        
        // Adjust probability based on activity level
        const activityAdjustment = Math.min(dealActivities.length * 5, 20);
        
        // Adjust probability based on deal age
        const dealAge = moment().diff(moment(deal.createdAt), "days");
        const ageAdjustment = dealAge > 90 ? -10 : dealAge > 60 ? -5 : 0;
        
        // Calculate final probability
        let adjustedProbability = baseProbability + activityAdjustment + ageAdjustment;
        adjustedProbability = Math.max(5, Math.min(95, adjustedProbability));
        
        // Update deal if probability is significantly different
        if (
          !deal.probability ||
          Math.abs(deal.probability - adjustedProbability) > 10
        ) {
          await pouchDatabase("deals", databasePrefix).saveDocument(
            {
              ...deal,
              probability: adjustedProbability,
            },
            CRUD_USER
          );
        }
      }
    } catch (error) {
      console.error("Error in dealWinProbabilityUpdate automation:", error);
    }
  },
};

export const scheduleAutomations = (context) => {
  // Run activity follow-up daily
  setInterval(() => {
    crmAutomationRules.activityFollowUp(context);
  }, 24 * 60 * 60 * 1000);

  // Check deal stage progression every 2 days
  setInterval(() => {
    crmAutomationRules.dealStageProgression(context);
  }, 2 * 24 * 60 * 60 * 1000);

  // Update contact engagement scores weekly
  setInterval(() => {
    crmAutomationRules.contactEngagementScoring(context);
  }, 7 * 24 * 60 * 60 * 1000);

  // Update deal win probabilities every 3 days
  setInterval(() => {
    crmAutomationRules.dealWinProbabilityUpdate(context);
  }, 3 * 24 * 60 * 60 * 1000);
  
  // Also run once at startup
  setTimeout(() => {
    crmAutomationRules.activityFollowUp(context);
    crmAutomationRules.dealStageProgression(context);
    crmAutomationRules.contactEngagementScoring(context);
    crmAutomationRules.dealWinProbabilityUpdate(context);
  }, 5000);
};
