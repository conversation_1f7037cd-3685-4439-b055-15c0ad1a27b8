import React, { useState, useEffect } from "react";
import moment from "moment";
import { Card, Row, Col, Statistic, DatePicker, Button, Spin, Empty, Tag  } from "antd";
import { DollarOutlined, UserOutlined, BankOutlined, CalendarOutlined, ArrowRightOutlined, FileProtectOutlined,   } from "@ant-design/icons";
import { DragDropContext, Droppable, Draggable  } from "react-beautiful-dnd";


const { RangePicker } = DatePicker;

const Pipeline = ({ pouchDatabase, databasePrefix, CRUD_USER }) => {
  const [loading, setLoading] = useState(true);
  const [deals, setDeals] = useState([]);
  const [bids, setBids] = useState([]);
  const [showBids, setShowBids] = useState(false);
  const [dateRange, setDateRange] = useState([
    moment().startOf("month"),
    moment().endOf("month"),
  ]);
  const [statistics, setStatistics] = useState({
    totalValue: 0,
    avgDealSize: 0,
    totalDeals: 0,
    wonDeals: 0,
    lostDeals: 0,
    totalBids: 0,
    activeBids: 0,
    totalBidValue: 0,
  });

  // Define the deal stages in order
  const dealStages = [
    { id: "lead", name: "Lead" },
    { id: "qualified", name: "Qualified" },
    { id: "proposal", name: "Proposal" },
    { id: "negotiation", name: "Negotiation" },
    { id: "closed_won", name: "Closed Won" },
    { id: "closed_lost", name: "Closed Lost" },
  ];

  // Define the bid stages in order
  const bidStages = [
    { id: "draft", name: "Draft" },
    { id: "submitted", name: "Submitted" },
    { id: "under_review", name: "Under Review" },
    { id: "shortlisted", name: "Shortlisted" },
    { id: "won", name: "Won" },
    { id: "lost", name: "Lost" },
  ];

  useEffect(() => {
    loadData();
  }, [dateRange, showBids]);

  const loadData = async () => {
    setLoading(true);
    try {
      if (showBids) {
        await loadBids();
      } else {
        await loadDeals();
      }
    } catch (error) {
      console.error("Error loading data:", error);
      setLoading(false);
    }
  };

  const loadDeals = async () => {
    try {
      const allDeals = await pouchDatabase("deals", databasePrefix).getAllData();

      // Filter deals by date range if provided
      let filteredDeals = allDeals;
      if (dateRange && dateRange.length === 2) {
        filteredDeals = allDeals.filter((deal) => {
          const dealDate = moment(deal.expected_close_date);
          return (
            dealDate.isValid() &&
            dealDate.isSameOrAfter(dateRange[0], "day") &&
            dealDate.isSameOrBefore(dateRange[1], "day")
          );
        });
      }

      // Calculate statistics
      const totalValue = filteredDeals.reduce(
        (sum, deal) => sum + (deal.value || 0),
        0
      );
      const wonDeals = filteredDeals.filter((deal) => deal.stage === "closed_won");
      const lostDeals = filteredDeals.filter((deal) => deal.stage === "closed_lost");

      setStatistics({
        totalValue,
        avgDealSize: filteredDeals.length ? totalValue / filteredDeals.length : 0,
        totalDeals: filteredDeals.length,
        wonDeals: wonDeals.length,
        lostDeals: lostDeals.length,
        totalBids: 0,
        activeBids: 0,
        totalBidValue: 0,
      });

      setDeals(filteredDeals);
      setBids([]);
      setLoading(false);
    } catch (error) {
      console.error("Error loading deals:", error);
      setLoading(false);
    }
  };

  const loadBids = async () => {
    try {
      const allBids = await pouchDatabase("bids", databasePrefix).getAllData();

      // Filter bids by date range if provided
      let filteredBids = allBids;
      if (dateRange && dateRange.length === 2) {
        filteredBids = allBids.filter((bid) => {
          const bidDate = moment(bid.submission_deadline);
          return (
            bidDate.isValid() &&
            bidDate.isSameOrAfter(dateRange[0], "day") &&
            bidDate.isSameOrBefore(dateRange[1], "day")
          );
        });
      }

      // Calculate statistics
      const totalBidValue = filteredBids.reduce(
        (sum, bid) => sum + (bid.value || 0),
        0
      );
      const activeBids = filteredBids.filter(
        (bid) => bid.status !== "won" && bid.status !== "lost" && bid.status !== "canceled"
      );
      const wonBids = filteredBids.filter((bid) => bid.status === "won");
      const lostBids = filteredBids.filter((bid) => bid.status === "lost");

      setStatistics({
        totalValue: 0,
        avgDealSize: 0,
        totalDeals: 0,
        wonDeals: 0,
        lostDeals: 0,
        totalBids: filteredBids.length,
        activeBids: activeBids.length,
        totalBidValue,
        wonBids: wonBids.length,
        lostBids: lostBids.length,
        avgBidValue: filteredBids.length ? totalBidValue / filteredBids.length : 0,
      });

      setBids(filteredBids);
      setDeals([]);
      setLoading(false);
    } catch (error) {
      console.error("Error loading bids:", error);
      setLoading(false);
    }
  };

  const onDragEnd = async (result) => {
    const { destination, source, draggableId } = result;

    // If dropped outside a droppable area
    if (!destination) return;

    // If dropped in the same place
    if (
      destination.droppableId === source.droppableId &&
      destination.index === source.index
    ) {
      return;
    }

    if (showBids) {
      // Handle bid drag and drop
      const bid = bids.find((b) => b._id === draggableId);
      if (!bid) return;

      // Update the bid's status
      const updatedBid = {
        ...bid,
        status: destination.droppableId,
        last_updated: new Date().toISOString(),
      };

      // Update probability based on new status
      switch (destination.droppableId) {
        case "draft":
          updatedBid.probability = 10;
          break;
        case "submitted":
          updatedBid.probability = 30;
          break;
        case "under_review":
          updatedBid.probability = 50;
          break;
        case "shortlisted":
          updatedBid.probability = 70;
          break;
        case "won":
          updatedBid.probability = 100;
          break;
        case "lost":
          updatedBid.probability = 0;
          break;
        default:
          break;
      }

      try {
        // Save the updated bid
        await pouchDatabase("bids", databasePrefix).saveDocument(
          updatedBid,
          CRUD_USER
        );

        // Create an activity to log the status change
        await pouchDatabase("activities", databasePrefix).saveDocument(
          {
            activity_type: "note",
            subject: `Bid status updated to ${destination.droppableId}`,
            related_to_type: "bid",
            bid: {
              value: bid._id,
              label: bid.title,
              ...bid,
            },
            start_date: new Date().toISOString(),
            status: "completed",
            description: `Status changed from ${source.droppableId} to ${destination.droppableId}`,
          },
          CRUD_USER
        );

        // If the bid is linked to a deal and the status is won/lost, update the deal too
        if ((destination.droppableId === "won" || destination.droppableId === "lost") && bid.deal && bid.deal.value) {
          try {
            const dealDoc = await pouchDatabase("deals", databasePrefix).getDocument(bid.deal.value);
            if (dealDoc) {
              const newStage = destination.droppableId === "won" ? "closed_won" : "closed_lost";
              await pouchDatabase("deals", databasePrefix).saveDocument(
                {
                  ...dealDoc,
                  stage: newStage,
                  probability: destination.droppableId === "won" ? 100 : 0,
                  last_activity_date: new Date().toISOString().split("T")[0],
                },
                CRUD_USER
              );
            }
          } catch (dealError) {
            console.error("Error updating related deal:", dealError);
          }
        }

        // Update the local state
        setBids(
          bids.map((b) => (b._id === draggableId ? updatedBid : b))
        );
      } catch (error) {
        console.error("Error updating bid:", error);
      }
    } else {
      // Handle deal drag and drop
      const deal = deals.find((d) => d._id === draggableId);
      if (!deal) return;

      // Update the deal's stage
      const updatedDeal = {
        ...deal,
        stage: destination.droppableId,
        last_activity_date: new Date().toISOString().split("T")[0],
      };

      // Update probability based on new stage
      switch (destination.droppableId) {
        case "lead":
          updatedDeal.probability = 10;
          break;
        case "qualified":
          updatedDeal.probability = 30;
          break;
        case "proposal":
          updatedDeal.probability = 50;
          break;
        case "negotiation":
          updatedDeal.probability = 70;
          break;
        case "closed_won":
          updatedDeal.probability = 100;
          break;
        case "closed_lost":
          updatedDeal.probability = 0;
          break;
        default:
          break;
      }

      try {
        // Save the updated deal
        await pouchDatabase("deals", databasePrefix).saveDocument(
          updatedDeal,
          CRUD_USER
        );

        // Create an activity to log the stage change
        await pouchDatabase("activities", databasePrefix).saveDocument(
          {
            activity_type: "note",
            subject: `Deal stage updated to ${destination.droppableId}`,
            related_to_type: "deal",
            deal: {
              value: deal._id,
              label: deal.name,
              ...deal,
            },
            start_date: new Date().toISOString(),
            status: "completed",
            description: `Stage changed from ${source.droppableId} to ${destination.droppableId}`,
          },
          CRUD_USER
        );

        // Update the local state
        setDeals(
          deals.map((d) => (d._id === draggableId ? updatedDeal : d))
        );
      } catch (error) {
        console.error("Error updating deal:", error);
      }
    }
  };

  const getStageDeals = (stageId) => {
    return deals.filter((deal) => deal.stage === stageId);
  };

  const getStageBids = (stageId) => {
    return bids.filter((bid) => bid.status === stageId);
  };

  const getStageValue = (stageId) => {
    return getStageDeals(stageId).reduce(
      (sum, deal) => sum + (deal.value || 0),
      0
    );
  };

  const getStageBidValue = (stageId) => {
    return getStageBids(stageId).reduce(
      (sum, bid) => sum + (bid.value || 0),
      0
    );
  };

  return (
    <Card
      title={showBids ? "Bid Pipeline" : "Sales Pipeline"}
      style={{ marginBottom: 24 }}
      extra={
        <Button
          type={showBids ? "default" : "primary"}
          icon={<FileProtectOutlined />}
          onClick={() => setShowBids(!showBids)}
        >
          {showBids ? "Show Deals" : "Show Bids"}
        </Button>
      }
    >
      <Row gutter={[16, 16]} style={{ marginBottom: 16 }}>
        <Col span={12}>
          <RangePicker
            value={dateRange}
            onChange={setDateRange}
            style={{ width: "100%" }}
          />
        </Col>
        <Col span={12} style={{ textAlign: "right" }}>
          <Button type="primary" onClick={loadData}>
            Refresh
          </Button>
        </Col>
      </Row>

      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        {showBids ? (
          // Bid statistics
          <>
            <Col xs={24} sm={12} md={8} lg={6}>
              <Card>
                <Statistic
                  title="Total Bid Value"
                  value={statistics.totalBidValue}
                  precision={0}
                  valueStyle={{ color: "#3f8600" }}
                  prefix={<DollarOutlined />}
                  suffix=""
                />
              </Card>
            </Col>
            <Col xs={24} sm={12} md={8} lg={6}>
              <Card>
                <Statistic
                  title="Average Bid Size"
                  value={statistics.avgBidValue}
                  precision={0}
                  valueStyle={{ color: "#1890ff" }}
                  prefix={<DollarOutlined />}
                  suffix=""
                />
              </Card>
            </Col>
            <Col xs={24} sm={12} md={8} lg={6}>
              <Card>
                <Statistic
                  title="Total Bids"
                  value={statistics.totalBids}
                  valueStyle={{ color: "#722ed1" }}
                />
              </Card>
            </Col>
            <Col xs={24} sm={12} md={8} lg={6}>
              <Card>
                <Statistic
                  title="Win Rate"
                  value={
                    (statistics.wonBids || statistics.lostBids)
                      ? (
                        (statistics.wonBids /
                          (statistics.wonBids + statistics.lostBids)) *
                        100
                      ).toFixed(1)
                      : 0
                  }
                  precision={1}
                  valueStyle={{ color: "#cf1322" }}
                  suffix="%"
                />
              </Card>
            </Col>
          </>
        ) : (
          // Deal statistics
          <>
            <Col xs={24} sm={12} md={8} lg={6}>
              <Card>
                <Statistic
                  title="Total Pipeline Value"
                  value={statistics.totalValue}
                  precision={0}
                  valueStyle={{ color: "#3f8600" }}
                  prefix={<DollarOutlined />}
                  suffix=""
                />
              </Card>
            </Col>
            <Col xs={24} sm={12} md={8} lg={6}>
              <Card>
                <Statistic
                  title="Average Deal Size"
                  value={statistics.avgDealSize}
                  precision={0}
                  valueStyle={{ color: "#1890ff" }}
                  prefix={<DollarOutlined />}
                  suffix=""
                />
              </Card>
            </Col>
            <Col xs={24} sm={12} md={8} lg={6}>
              <Card>
                <Statistic
                  title="Total Deals"
                  value={statistics.totalDeals}
                  valueStyle={{ color: "#722ed1" }}
                />
              </Card>
            </Col>
            <Col xs={24} sm={12} md={8} lg={6}>
              <Card>
                <Statistic
                  title="Win Rate"
                  value={
                    (statistics.wonDeals || statistics.lostDeals)
                      ? (
                        (statistics.wonDeals /
                          (statistics.wonDeals + statistics.lostDeals)) *
                        100
                      ).toFixed(1)
                      : 0
                  }
                  precision={1}
                  valueStyle={{ color: "#cf1322" }}
                  suffix="%"
                />
              </Card>
            </Col>
          </>
        )}
      </Row>

      {loading ? (
        <div style={{ textAlign: "center", padding: 24 }}>
          <Spin size="large" />
        </div>
      ) : (
        <DragDropContext onDragEnd={onDragEnd}>
          <Row gutter={[8, 16]} style={{ overflowX: "auto" }}>
            {(showBids ? bidStages : dealStages).map((stage) => (
              <Col key={stage.id} xs={24} sm={12} md={8} lg={4}>
                <Card
                  title={
                    <div>
                      {stage.name}{" "}
                      <Tag color="blue">{showBids ? getStageBids(stage.id).length : getStageDeals(stage.id).length}</Tag>
                    </div>
                  }
                  extra={
                    <span style={{ fontSize: "12px" }}>
                      {showBids ? getStageBidValue(stage.id).toLocaleString() : getStageValue(stage.id).toLocaleString()}
                    </span>
                  }
                  style={{ height: "100%" }}
                  bodyStyle={{ padding: "0 8px", height: 400, overflowY: "auto" }}
                >
                  <Droppable droppableId={stage.id}>
                    {(provided, snapshot) => (
                      <div
                        ref={provided.innerRef}
                        {...provided.droppableProps}
                        style={{
                          minHeight: "100%",
                          backgroundColor: snapshot.isDraggingOver
                            ? "#f0f5ff"
                            : "transparent",
                          padding: 8,
                        }}
                      >
                        {showBids ? (
                          getStageBids(stage.id).length === 0 ? (
                            <Empty
                              image={Empty.PRESENTED_IMAGE_SIMPLE}
                              description="No bids"
                              style={{ margin: "40px 0" }}
                            />
                          ) : (
                            getStageBids(stage.id).map((bid, index) => (
                              <Draggable
                                key={bid._id}
                                draggableId={bid._id}
                                index={index}
                              >
                                {(provided, snapshot) => (
                                  <Card
                                    size="small"
                                    style={{
                                      marginBottom: 8,
                                      backgroundColor: snapshot.isDragging
                                        ? "#e6f7ff"
                                        : "#fff",
                                      ...provided.draggableProps.style,
                                    }}
                                    ref={provided.innerRef}
                                    {...provided.draggableProps}
                                    {...provided.dragHandleProps}
                                  >
                                    <div style={{ fontWeight: "bold" }}>
                                      {bid.title}
                                    </div>
                                    <div>
                                      <DollarOutlined />{" "}
                                      {bid.value ? bid.value.toLocaleString() : 0}{" "}
                                      {bid.currency || ""}
                                    </div>
                                    {bid.client && bid.client.label && (
                                      <div>
                                        <BankOutlined /> {bid.client.label}
                                      </div>
                                    )}
                                    {bid.contact && bid.contact.label && (
                                      <div>
                                        <UserOutlined /> {bid.contact.label}
                                      </div>
                                    )}
                                    {bid.submission_deadline && (
                                      <div>
                                        <CalendarOutlined />{" "}
                                        {moment(bid.submission_deadline).format(
                                          "MMM D, YYYY"
                                        )}
                                      </div>
                                    )}
                                    {bid.deal && bid.deal.label && (
                                      <div>
                                        <FileProtectOutlined /> {bid.deal.label}
                                      </div>
                                    )}
                                  </Card>
                                )}
                              </Draggable>
                            ))
                          )
                        ) : (
                          getStageDeals(stage.id).length === 0 ? (
                            <Empty
                              image={Empty.PRESENTED_IMAGE_SIMPLE}
                              description="No deals"
                              style={{ margin: "40px 0" }}
                            />
                          ) : (
                            getStageDeals(stage.id).map((deal, index) => (
                              <Draggable
                                key={deal._id}
                                draggableId={deal._id}
                                index={index}
                              >
                                {(provided, snapshot) => (
                                  <Card
                                    size="small"
                                    style={{
                                      marginBottom: 8,
                                      backgroundColor: snapshot.isDragging
                                        ? "#e6f7ff"
                                        : "#fff",
                                      ...provided.draggableProps.style,
                                    }}
                                    ref={provided.innerRef}
                                    {...provided.draggableProps}
                                    {...provided.dragHandleProps}
                                  >
                                    <div style={{ fontWeight: "bold" }}>
                                      {deal.name}
                                    </div>
                                    <div>
                                      <DollarOutlined />{" "}
                                      {deal.value ? deal.value.toLocaleString() : 0}{" "}
                                      {deal.currency || ""}
                                    </div>
                                    {deal.contact && deal.contact.label && (
                                      <div>
                                        <UserOutlined /> {deal.contact.label}
                                      </div>
                                    )}
                                    {deal.company && deal.company.label && (
                                      <div>
                                        <BankOutlined /> {deal.company.label}
                                      </div>
                                    )}
                                    {deal.expected_close_date && (
                                      <div>
                                        <CalendarOutlined />{" "}
                                        {moment(deal.expected_close_date).format(
                                          "MMM D, YYYY"
                                        )}
                                      </div>
                                    )}
                                  </Card>
                                )}
                              </Draggable>
                            ))
                          )
                        )}
                        {provided.placeholder}
                      </div>
                    )}
                  </Droppable>
                </Card>
              </Col>
            ))}
          </Row>
        </DragDropContext>
      )}
    </Card>
  );
};

export default Pipeline;
