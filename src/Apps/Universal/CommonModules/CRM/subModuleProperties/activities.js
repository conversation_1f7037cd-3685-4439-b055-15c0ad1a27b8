import React from "react";


export default {
  buffResults: (data) => {
    // Any data transformations needed before display
    return data;
  },

  afterSave: async (data, { pouchDatabase, databasePrefix, CRUD_USER }) => {
    // Update the last_contact_date or last_activity_date for the related entity
    try {
      const today = new Date().toISOString().split("T")[0];

      if (data.related_to_type === "contact" && data.contact && data.contact.value) {
        const contactDoc = await pouchDatabase("contacts", databasePrefix).getDocument(data.contact.value);
        if (contactDoc) {
          await pouchDatabase("contacts", databasePrefix).saveDocument(
            {
              ...contactDoc,
              last_contact_date: today,
            },
            CRUD_USER
          );
        }
      } else if (data.related_to_type === "bid" && data.bid && data.bid.value) {
        const bidDoc = await pouchDatabase("bids", databasePrefix).getDocument(data.bid.value);
        if (bidDoc) {
          await pouchDatabase("bids", databasePrefix).saveDocument(
            {
              ...bidDoc,
              last_updated: new Date().toISOString(),
            },
            CRUD_USER
          );
        }
      } else if (data.related_to_type === "deal" && data.deal && data.deal.value) {
        const dealDoc = await pouchDatabase("deals", databasePrefix).getDocument(data.deal.value);
        if (dealDoc) {
          await pouchDatabase("deals", databasePrefix).saveDocument(
            {
              ...dealDoc,
              last_activity_date: today,
            },
            CRUD_USER
          );
        }
      }
    } catch (error) {
      console.error("Error updating related entity:", error);
    }
  },
};
