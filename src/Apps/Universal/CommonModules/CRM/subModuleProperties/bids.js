import Bid from "./CustomViews/Bid";
import React, { useRef } from "react";
import moment from "moment";
import { BetaSchemaForm, TableDropdown  } from "@ant-design/pro-components";
import { message  } from "antd";


export default {
  CustomView: (data) => <Bid {...data} />,

  buffResults: (data) => {
    // Any data transformations needed before display
    return data;
  },

  beforeSave: (data, context) => {
    // If client is selected and it's a company, try to get contact persons
    if (data.client && data.client.value) {
      // We'll handle contact persons in the form directly
    }
    return data;
  },

  MoreActions: (props) => {
    const action = useRef();

    const {
      pouchDatabase,
      collection,
      databasePrefix,
      record,
      CRUD_USER,
      singular,
      modules,
    } = props;

    return (
      <TableDropdown
        key="actionGroup"
        onSelect={async (key) => {
          if (key === "add_activity") {
            // Handle adding activity
          } else if (key === "update_status") {
            // Handle updating status
          } else if (key === "add_marketing_feedback") {
            // Handle adding marketing feedback
          }
        }}
        menus={[
          {
            key: "add_activity",
            name: (
              <BetaSchemaForm
                formRef={action}
                submitter={{
                  searchConfig: {
                    resetText: "Cancel",
                    submitText: "Save",
                  },
                }}
                grid={true}
                trigger={
                  <a key="button" type="primary">
                    Add Activity
                  </a>
                }
                title={"Add Activity"}
                destroyOnClose={true}
                layoutType="ModalForm"
                onFinish={async (values) => {
                  try {
                    // Create a new activity linked to this bid
                    await pouchDatabase("activities", databasePrefix).saveDocument(
                      {
                        ...values,
                        related_to_type: "bid",
                        bid: {
                          value: record._id,
                          label: record.title,
                          ...record,
                        },
                        status: values.status || "planned",
                      },
                      CRUD_USER
                    );

                    // Update the last_updated for the bid
                    await pouchDatabase(collection, databasePrefix).saveDocument(
                      {
                        ...record,
                        last_updated: new Date().toISOString(),
                      },
                      CRUD_USER
                    );

                    message.success("Activity added successfully");
                    return true;
                  } catch (error) {
                    message.error("Failed to add activity: " + error.message);
                    return false;
                  }
                }}
                columns={[
                  {
                    title: "Activity Type",
                    dataIndex: "activity_type",
                    valueType: "select",
                    valueEnum: {
                      call: { text: "Call" },
                      meeting: { text: "Meeting" },
                      email: { text: "Email" },
                      task: { text: "Task" },
                      note: { text: "Note" },
                      other: { text: "Other" },
                    },
                    required: true,
                    colProps: { span: 12 },
                  },
                  {
                    title: "Subject",
                    dataIndex: "subject",
                    valueType: "text",
                    required: true,
                    colProps: { span: 12 },
                    initialValue: `Activity for ${record.title}`,
                  },
                  {
                    title: "Start Date",
                    dataIndex: "start_date",
                    valueType: "dateTime",
                    required: true,
                    colProps: { span: 12 },
                    initialValue: moment().format(),
                  },
                  {
                    title: "End Date",
                    dataIndex: "end_date",
                    valueType: "dateTime",
                    colProps: { span: 12 },
                  },
                  {
                    title: "Status",
                    dataIndex: "status",
                    valueType: "select",
                    valueEnum: {
                      planned: { text: "Planned", status: "Default" },
                      in_progress: { text: "In Progress", status: "Processing" },
                      completed: { text: "Completed", status: "Success" },
                      canceled: { text: "Canceled", status: "Error" },
                    },
                    required: true,
                    colProps: { span: 12 },
                    initialValue: "planned",
                  },
                  {
                    title: "Description",
                    dataIndex: "description",
                    valueType: "textarea",
                    colProps: { span: 24 },
                  },
                ]}
              />
            ),
          },
          {
            key: "update_status",
            name: (
              <BetaSchemaForm
                formRef={action}
                submitter={{
                  searchConfig: {
                    resetText: "Cancel",
                    submitText: "Update",
                  },
                }}
                grid={true}
                trigger={
                  <a key="button" type="primary">
                    Update Status
                  </a>
                }
                title={"Update Bid Status"}
                destroyOnClose={true}
                layoutType="ModalForm"
                onFinish={async (values) => {
                  try {
                    // Update the bid status
                    const updatedBid = {
                      ...record,
                      status: values.status,
                      notes: record.notes
                        ? `${record.notes}\n\n${values.notes}`
                        : values.notes,
                      last_updated: new Date().toISOString(),
                    };

                    // If status is approved, add marketing person
                    if (values.status === "approved" && values.marketing_person) {
                      updatedBid.marketing_person = values.marketing_person;
                    }

                    await pouchDatabase(collection, databasePrefix).saveDocument(
                      updatedBid,
                      CRUD_USER
                    );

                    // Create an activity to log the status change
                    const activityData = {
                      activity_type: "note",
                      subject: `Bid status updated to ${values.status}`,
                      related_to_type: "bid",
                      bid: {
                        value: record._id,
                        label: record.title,
                        ...record,
                      },
                      start_date: new Date().toISOString(),
                      status: "completed",
                      description: values.notes || `Status changed from ${record.status} to ${values.status}`,
                    };

                    // If marketing person is assigned, add to activity description
                    if (values.status === "approved" && values.marketing_person) {
                      activityData.description += `\nMarketing person assigned: ${values.marketing_person.label}`;
                    }

                    await pouchDatabase("activities", databasePrefix).saveDocument(
                      activityData,
                      CRUD_USER
                    );

                    // If status is approved, notify the marketing person
                    if (values.status === "approved" && values.marketing_person) {
                      message.success(`Bid approved and assigned to ${values.marketing_person.label}`);
                    } else if (values.status === "rejected") {
                      message.info("Bid has been rejected");
                    } else {
                      message.success("Bid status updated successfully");
                    }

                    return true;
                  } catch (error) {
                    message.error("Failed to update status: " + error.message);
                    return false;
                  }
                }}
                columns={[
                  {
                    title: "Current Status",
                    dataIndex: "current_status",
                    valueType: "text",
                    readonly: true,
                    initialValue: record?.status,
                    colProps: { span: 12 },
                  },
                  {
                    title: "New Status",
                    dataIndex: "status",
                    valueType: "select",
                    valueEnum: {
                      pending: { text: "Pending", status: "Processing" },
                      approved: { text: "Approved", status: "Success" },
                      rejected: { text: "Rejected", status: "Error" },
                    },
                    required: true,
                    colProps: { span: 12 },
                  },
                  {
                    title: "Marketing Person",
                    dataIndex: "marketing_person",
                    type: "dbSelect",
                    collection: "users",
                    label: ["first_name", "last_name"],
                    colProps: { span: 12 },
                    dependencies: ["status"],
                    formItemProps: (form) => {
                      const status = form.getFieldValue("status");
                      if (status !== "approved") {
                        return {
                          hidden: true,
                        };
                      }
                      return {
                        rules: [{ required: true, message: "Please assign a marketing person for approved bids" }],
                      };
                    },
                  },
                  {
                    title: "Notes",
                    dataIndex: "notes",
                    valueType: "textarea",
                    colProps: { span: 24 },
                  },
                ]}
              />
            ),
          },
          {
            key: "add_marketing_feedback",
            name: (
              <BetaSchemaForm
                formRef={action}
                submitter={{
                  searchConfig: {
                    resetText: "Cancel",
                    submitText: "Save Feedback",
                  },
                }}
                grid={true}
                trigger={
                  <a key="button" type="primary">
                    Add Marketing Feedback
                  </a>
                }
                title={"Add Marketing Feedback"}
                destroyOnClose={true}
                layoutType="ModalForm"
                onFinish={async (values) => {
                  try {
                    // Only allow if bid is approved and has a marketing person
                    if (record.status !== "approved" || !record.marketing_person) {
                      message.error("Feedback can only be added to approved bids with an assigned marketing person");
                      return false;
                    }

                    // Update the bid with marketing feedback
                    const updatedBid = {
                      ...record,
                      marketing_feedback: record.marketing_feedback
                        ? `${record.marketing_feedback}\n\n${new Date().toLocaleDateString()}: ${values.feedback}`
                        : `${new Date().toLocaleDateString()}: ${values.feedback}`,
                      last_updated: new Date().toISOString(),
                    };

                    await pouchDatabase(collection, databasePrefix).saveDocument(
                      updatedBid,
                      CRUD_USER
                    );

                    // Create an activity to log the feedback
                    await pouchDatabase("activities", databasePrefix).saveDocument(
                      {
                        activity_type: "note",
                        subject: "Marketing feedback added",
                        related_to_type: "bid",
                        bid: {
                          value: record._id,
                          label: record.title,
                          ...record,
                        },
                        start_date: new Date().toISOString(),
                        status: "completed",
                        description: `Marketing feedback: ${values.feedback}`,
                      },
                      CRUD_USER
                    );

                    message.success("Marketing feedback added successfully");
                    return true;
                  } catch (error) {
                    message.error("Failed to add feedback: " + error.message);
                    return false;
                  }
                }}
                columns={[
                  {
                    title: "Feedback",
                    dataIndex: "feedback",
                    valueType: "textarea",
                    required: true,
                    colProps: { span: 24 },
                  },
                ]}
              />
            ),
            // Only show for approved bids with a marketing person
            hideInDropdown: (record) => {
              return record.status !== "approved" || !record.marketing_person;
            },
          },
        ]}
      />
    );
  },
};
