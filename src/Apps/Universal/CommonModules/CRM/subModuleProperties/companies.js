import Company from "./CustomViews/Company";
import React, { useRef } from "react";
import { BetaSchemaForm, TableDropdown  } from "@ant-design/pro-components";
import { message  } from "antd";


export default {
  CustomView: (data) => <Company {...data} />,
  
  buffResults: (data) => {
    // Any data transformations needed before display
    return data;
  },
  
  MoreActions: (props) => {
    const action = useRef();
    
    const {
      pouchDatabase,
      collection,
      databasePrefix,
      record,
      CRUD_USER,
      singular,
      modules,
    } = props;
    
    return (
      <TableDropdown
        key="actionGroup"
        onSelect={async (key) => {
          if (key === "add_activity") {
            // Handle adding activity
          } else if (key === "add_contact") {
            // <PERSON><PERSON> adding contact
          } else if (key === "add_deal") {
            // <PERSON>le adding deal
          }
        }}
        menus={[
          {
            key: "add_activity",
            name: (
              <BetaSchemaForm
                formRef={action}
                submitter={{
                  searchConfig: {
                    resetText: "Cancel",
                    submitText: "Save",
                  },
                }}
                grid={true}
                trigger={
                  <a key="button" type="primary">
                    Add Activity
                  </a>
                }
                title={"Add Activity"}
                destroyOnClose={true}
                layoutType="ModalForm"
                onFinish={async (values) => {
                  try {
                    // Create a new activity linked to this company
                    await pouchDatabase("activities", databasePrefix).saveDocument(
                      {
                        ...values,
                        related_to_type: "company",
                        company: {
                          value: record._id,
                          label: record.name,
                          ...record,
                        },
                        status: values.status || "planned",
                      },
                      CRUD_USER
                    );
                    
                    // Update the last_contact_date for the company
                    await pouchDatabase(collection, databasePrefix).saveDocument(
                      {
                        ...record,
                        last_contact_date: new Date().toISOString().split("T")[0],
                      },
                      CRUD_USER
                    );
                    
                    message.success("Activity added successfully");
                    return true;
                  } catch (error) {
                    message.error("Failed to add activity: " + error.message);
                    return false;
                  }
                }}
                columns={[
                  {
                    title: "Activity Type",
                    dataIndex: "activity_type",
                    valueType: "select",
                    valueEnum: {
                      call: { text: "Call" },
                      meeting: { text: "Meeting" },
                      email: { text: "Email" },
                      task: { text: "Task" },
                      note: { text: "Note" },
                      other: { text: "Other" },
                    },
                    required: true,
                    colProps: { span: 12 },
                  },
                  {
                    title: "Subject",
                    dataIndex: "subject",
                    valueType: "text",
                    required: true,
                    colProps: { span: 12 },
                  },
                  {
                    title: "Start Date",
                    dataIndex: "start_date",
                    valueType: "dateTime",
                    required: true,
                    colProps: { span: 12 },
                    initialValue: new Date().toISOString(),
                  },
                  {
                    title: "Status",
                    dataIndex: "status",
                    valueType: "select",
                    valueEnum: {
                      planned: { text: "Planned", status: "Default" },
                      in_progress: { text: "In Progress", status: "Processing" },
                      completed: { text: "Completed", status: "Success" },
                      canceled: { text: "Canceled", status: "Error" },
                    },
                    required: true,
                    colProps: { span: 12 },
                    initialValue: "planned",
                  },
                  {
                    title: "Description",
                    dataIndex: "description",
                    valueType: "textarea",
                    colProps: { span: 24 },
                  },
                ]}
              />
            ),
          },
          {
            key: "add_contact",
            name: (
              <BetaSchemaForm
                formRef={action}
                submitter={{
                  searchConfig: {
                    resetText: "Cancel",
                    submitText: "Save",
                  },
                }}
                grid={true}
                trigger={
                  <a key="button" type="primary">
                    Add Contact
                  </a>
                }
                title={"Add Contact"}
                destroyOnClose={true}
                layoutType="ModalForm"
                onFinish={async (values) => {
                  try {
                    // Create a new contact linked to this company
                    const contactData = {
                      ...values,
                      contact_type: "Individual",
                      associated_company: {
                        value: record._id,
                        label: record.name,
                        ...record,
                      },
                      name: `${values.title ? values.title + ". " : ""}${values.first_name} ${values.last_name}`,
                    };
                    
                    await pouchDatabase("contacts", databasePrefix).saveDocument(
                      contactData,
                      CRUD_USER
                    );
                    
                    message.success("Contact added successfully");
                    return true;
                  } catch (error) {
                    message.error("Failed to add contact: " + error.message);
                    return false;
                  }
                }}
                columns={[
                  {
                    title: "Title",
                    dataIndex: "title",
                    valueType: "select",
                    valueEnum: {
                      Mr: { text: "Mr" },
                      Mrs: { text: "Mrs" },
                      Ms: { text: "Ms" },
                      Dr: { text: "Dr" },
                      Prof: { text: "Prof" },
                    },
                    colProps: { span: 6 },
                  },
                  {
                    title: "First Name",
                    dataIndex: "first_name",
                    valueType: "text",
                    required: true,
                    colProps: { span: 9 },
                  },
                  {
                    title: "Last Name",
                    dataIndex: "last_name",
                    valueType: "text",
                    required: true,
                    colProps: { span: 9 },
                  },
                  {
                    title: "Email",
                    dataIndex: "email",
                    valueType: "text",
                    colProps: { span: 12 },
                  },
                  {
                    title: "Phone",
                    dataIndex: "phone",
                    valueType: "text",
                    required: true,
                    colProps: { span: 12 },
                  },
                  {
                    title: "Job Title",
                    dataIndex: "job_title",
                    valueType: "text",
                    colProps: { span: 12 },
                  },
                  {
                    title: "Department",
                    dataIndex: "department",
                    valueType: "text",
                    colProps: { span: 12 },
                  },
                ]}
              />
            ),
          },
          {
            key: "add_deal",
            name: (
              <BetaSchemaForm
                formRef={action}
                submitter={{
                  searchConfig: {
                    resetText: "Cancel",
                    submitText: "Save",
                  },
                }}
                grid={true}
                trigger={
                  <a key="button" type="primary">
                    Add Deal
                  </a>
                }
                title={"Add Deal"}
                destroyOnClose={true}
                layoutType="ModalForm"
                onFinish={async (values) => {
                  try {
                    // Create a new deal linked to this company
                    await pouchDatabase("deals", databasePrefix).saveDocument(
                      {
                        ...values,
                        company: {
                          value: record._id,
                          label: record.name,
                          ...record,
                        },
                      },
                      CRUD_USER
                    );
                    
                    message.success("Deal added successfully");
                    return true;
                  } catch (error) {
                    message.error("Failed to add deal: " + error.message);
                    return false;
                  }
                }}
                columns={[
                  {
                    title: "Deal Name",
                    dataIndex: "name",
                    valueType: "text",
                    required: true,
                    colProps: { span: 12 },
                  },
                  {
                    title: "Value",
                    dataIndex: "value",
                    valueType: "money",
                    required: true,
                    colProps: { span: 12 },
                  },
                  {
                    title: "Stage",
                    dataIndex: "stage",
                    valueType: "select",
                    valueEnum: {
                      lead: { text: "Lead", status: "Default" },
                      qualified: { text: "Qualified", status: "Processing" },
                      proposal: { text: "Proposal", status: "Warning" },
                      negotiation: { text: "Negotiation", status: "Warning" },
                      closed_won: { text: "Closed Won", status: "Success" },
                      closed_lost: { text: "Closed Lost", status: "Error" },
                    },
                    required: true,
                    colProps: { span: 12 },
                    initialValue: "lead",
                  },
                  {
                    title: "Expected Close Date",
                    dataIndex: "expected_close_date",
                    valueType: "date",
                    required: true,
                    colProps: { span: 12 },
                  },
                  {
                    title: "Contact",
                    dataIndex: "contact",
                    type: "dbSelect",
                    collection: "contacts",
                    label: ["name"],
                    required: true,
                    colProps: { span: 24 },
                  },
                ]}
              />
            ),
          },
        ]}
      />
    );
  },
};
