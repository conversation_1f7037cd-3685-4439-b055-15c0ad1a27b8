import Contact from "./CustomViews/Contact";
import React, { useRef } from "react";
import moment from "moment";
import { BetaSchemaForm, TableDropdown  } from "@ant-design/pro-components";
import { message  } from "antd";


export default {
  CustomView: (data) => <Contact {...data} />,

  beforeSave: (data, context) => {
    // Generate the name field based on contact type
    if (data.contact_type === "Individual") {
      data.name = `${data.title ? data.title + ". " : ""}${data.first_name} ${data.last_name}`;
    } else if (data.contact_type === "Company") {
      data.name = data.company_name;

      // Process contact persons if present
      if (data.contact_persons && data.contact_persons.length > 0) {
        // Format each contact person's name
        data.contact_persons = data.contact_persons.map(person => {
          return {
            ...person,
            name: `${person.title ? person.title + ". " : ""}${person.first_name} ${person.last_name}`
          };
        });
      }
    }
    return data;
  },

  buffResults: (data) => {
    // Any data transformations needed before display
    return data;
  },

  MoreActions: (props) => {
    const action = useRef();

    const {
      pouchDatabase,
      collection,
      databasePrefix,
      record,
      CRUD_USER,
      singular,
      modules,
    } = props;

    return (
      <TableDropdown
        key="actionGroup"
        onSelect={async (key) => {
          if (key === "add_activity") {
            // Handle adding activity
          } else if (key === "add_deal") {
            // Handle adding deal
          }
        }}
        menus={[

          {
            key: "add_activity",
            name: (
              <BetaSchemaForm
                formRef={action}
                submitter={{
                  searchConfig: {
                    resetText: "Cancel",
                    submitText: "Save",
                  },
                }}
                grid={true}
                trigger={
                  <a key="button" type="primary">
                    Add Activity
                  </a>
                }
                title={"Add Activity"}
                destroyOnClose={true}
                layoutType="ModalForm"
                onFinish={async (values) => {
                  try {
                    // Create a new activity linked to this contact
                    await pouchDatabase("activities", databasePrefix).saveDocument(
                      {
                        ...values,
                        related_to_type: "contact",
                        contact: {
                          value: record._id,
                          label: record.name,
                          ...record,
                        },
                        status: values.status || "planned",
                      },
                      CRUD_USER
                    );

                    // Update the last_contact_date for the contact
                    await pouchDatabase(collection, databasePrefix).saveDocument(
                      {
                        ...record,
                        last_contact_date: new Date().toISOString().split("T")[0],
                      },
                      CRUD_USER
                    );

                    message.success("Activity added successfully");
                    return true;
                  } catch (error) {
                    message.error("Failed to add activity: " + error.message);
                    return false;
                  }
                }}
                columns={[
                  {
                    title: "Activity Type",
                    dataIndex: "activity_type",
                    valueType: "select",
                    valueEnum: {
                      call: { text: "Call" },
                      meeting: { text: "Meeting" },
                      email: { text: "Email" },
                      task: { text: "Task" },
                      note: { text: "Note" },
                      other: { text: "Other" },
                    },
                    required: true,
                    colProps: { span: 12 },
                  },
                  {
                    title: "Subject",
                    dataIndex: "subject",
                    valueType: "text",
                    required: true,
                    colProps: { span: 12 },
                  },
                  {
                    title: "Start Date",
                    dataIndex: "start_date",
                    valueType: "dateTime",
                    required: true,
                    colProps: { span: 12 },
                    initialValue: new Date().toISOString(),
                  },
                  {
                    title: "Status",
                    dataIndex: "status",
                    valueType: "select",
                    valueEnum: {
                      planned: { text: "Planned", status: "Default" },
                      in_progress: { text: "In Progress", status: "Processing" },
                      completed: { text: "Completed", status: "Success" },
                      canceled: { text: "Canceled", status: "Error" },
                    },
                    required: true,
                    colProps: { span: 12 },
                    initialValue: "planned",
                  },
                  {
                    title: "Description",
                    dataIndex: "description",
                    valueType: "textarea",
                    colProps: { span: 24 },
                  },
                ]}
              />
            ),
          },
          {
            key: "add_deal",
            name: (
              <BetaSchemaForm
                formRef={action}
                submitter={{
                  searchConfig: {
                    resetText: "Cancel",
                    submitText: "Save",
                  },
                }}
                grid={true}
                trigger={
                  <a key="button" type="primary">
                    Add Deal
                  </a>
                }
                title={"Add Deal"}
                destroyOnClose={true}
                layoutType="ModalForm"
                onFinish={async (values) => {
                  try {
                    // Create a new deal linked to this contact
                    await pouchDatabase("deals", databasePrefix).saveDocument(
                      {
                        ...values,
                        contact: {
                          value: record._id,
                          label: record.name,
                          ...record,
                        },
                        // If contact is associated with a company, link the deal to that company too
                        ...(record.associated_company ? {
                          company: record.associated_company
                        } : {}),
                      },
                      CRUD_USER
                    );

                    // Update the contact status if it's a lead
                    if (record.status === "lead") {
                      await pouchDatabase(collection, databasePrefix).saveDocument(
                        {
                          ...record,
                          status: "prospect",
                          last_contact_date: new Date().toISOString().split("T")[0],
                        },
                        CRUD_USER
                      );
                    }

                    message.success("Deal added successfully");
                    return true;
                  } catch (error) {
                    message.error("Failed to add deal: " + error.message);
                    return false;
                  }
                }}
                columns={[
                  {
                    title: "Deal Name",
                    dataIndex: "name",
                    valueType: "text",
                    required: true,
                    colProps: { span: 12 },
                    initialValue: `Deal with ${record.name}`,
                  },
                  {
                    title: "Value",
                    dataIndex: "value",
                    valueType: "money",
                    required: true,
                    colProps: { span: 12 },
                  },
                  {
                    title: "Stage",
                    dataIndex: "stage",
                    valueType: "select",
                    valueEnum: {
                      lead: { text: "Lead", status: "Default" },
                      qualified: { text: "Qualified", status: "Processing" },
                      proposal: { text: "Proposal", status: "Warning" },
                      negotiation: { text: "Negotiation", status: "Warning" },
                      closed_won: { text: "Closed Won", status: "Success" },
                      closed_lost: { text: "Closed Lost", status: "Error" },
                    },
                    required: true,
                    colProps: { span: 12 },
                    initialValue: "lead",
                  },
                  {
                    title: "Expected Close Date",
                    dataIndex: "expected_close_date",
                    valueType: "date",
                    required: true,
                    colProps: { span: 12 },
                    initialValue: moment().add(30, "days").format("YYYY-MM-DD"),
                  },
                  {
                    title: "Source",
                    dataIndex: "source",
                    valueType: "select",
                    valueEnum: {
                      website: { text: "Website" },
                      referral: { text: "Referral" },
                      social_media: { text: "Social Media" },
                      event: { text: "Event" },
                      cold_call: { text: "Cold Call" },
                      other: { text: "Other" },
                    },
                    colProps: { span: 12 },
                    initialValue: record.source,
                  },
                  {
                    title: "Products/Services",
                    dataIndex: "products",
                    valueType: "select",
                    mode: "tags",
                    fieldProps: {
                      options: [
                        { label: "Product A", value: "product_a" },
                        { label: "Product B", value: "product_b" },
                        { label: "Service X", value: "service_x" },
                        { label: "Service Y", value: "service_y" },
                      ],
                    },
                    colProps: { span: 12 },
                  },
                  {
                    title: "Notes",
                    dataIndex: "notes",
                    valueType: "textarea",
                    colProps: { span: 24 },
                  },
                ]}
              />
            ),
          },
        ]}
      />
    );
  },
};
