import Deal from "./CustomViews/Deal";
import React, { useRef } from "react";
import moment from "moment";
import { BetaSchemaForm, TableDropdown  } from "@ant-design/pro-components";
import { message  } from "antd";


export default {
  CustomView: (data) => <Deal {...data} />,

  buffResults: (data) => {
    // Any data transformations needed before display
    return data;
  },

  MoreActions: (props) => {
    const action = useRef();

    const {
      pouchDatabase,
      collection,
      databasePrefix,
      record,
      CRUD_USER,
      singular,
      modules,
    } = props;

    return (
      <TableDropdown
        key="actionGroup"
        onSelect={async (key) => {
          if (key === "add_activity") {
            // Handle adding activity
          } else if (key === "update_stage") {
            // Handle updating stage
          } else if (key === "create_bid") {
            // <PERSON>le creating bid
          }
        }}
        menus={[
          {
            key: "add_activity",
            name: (
              <BetaSchemaForm
                formRef={action}
                submitter={{
                  searchConfig: {
                    resetText: "Cancel",
                    submitText: "Save",
                  },
                }}
                grid={true}
                trigger={
                  <a key="button" type="primary">
                    Add Activity
                  </a>
                }
                title={"Add Activity"}
                destroyOnClose={true}
                layoutType="ModalForm"
                onFinish={async (values) => {
                  try {
                    // Create a new activity linked to this deal
                    await pouchDatabase("activities", databasePrefix).saveDocument(
                      {
                        ...values,
                        related_to_type: "deal",
                        deal: {
                          value: record._id,
                          label: record.name,
                          ...record,
                        },
                        status: values.status || "planned",
                      },
                      CRUD_USER
                    );

                    // Update the last_activity_date for the deal
                    await pouchDatabase(collection, databasePrefix).saveDocument(
                      {
                        ...record,
                        last_activity_date: new Date().toISOString().split("T")[0],
                      },
                      CRUD_USER
                    );

                    message.success("Activity added successfully");
                    return true;
                  } catch (error) {
                    message.error("Failed to add activity: " + error.message);
                    return false;
                  }
                }}
                columns={[
                  {
                    title: "Activity Type",
                    dataIndex: "activity_type",
                    valueType: "select",
                    valueEnum: {
                      call: { text: "Call" },
                      meeting: { text: "Meeting" },
                      email: { text: "Email" },
                      task: { text: "Task" },
                      note: { text: "Note" },
                      other: { text: "Other" },
                    },
                    required: true,
                    colProps: { span: 12 },
                  },
                  {
                    title: "Subject",
                    dataIndex: "subject",
                    valueType: "text",
                    required: true,
                    colProps: { span: 12 },
                  },
                  {
                    title: "Start Date",
                    dataIndex: "start_date",
                    valueType: "dateTime",
                    required: true,
                    colProps: { span: 12 },
                    initialValue: new Date().toISOString(),
                  },
                  {
                    title: "Status",
                    dataIndex: "status",
                    valueType: "select",
                    valueEnum: {
                      planned: { text: "Planned", status: "Default" },
                      in_progress: { text: "In Progress", status: "Processing" },
                      completed: { text: "Completed", status: "Success" },
                      canceled: { text: "Canceled", status: "Error" },
                    },
                    required: true,
                    colProps: { span: 12 },
                    initialValue: "planned",
                  },
                  {
                    title: "Description",
                    dataIndex: "description",
                    valueType: "textarea",
                    colProps: { span: 24 },
                  },
                ]}
              />
            ),
          },
          {
            key: "update_stage",
            name: (
              <BetaSchemaForm
                formRef={action}
                submitter={{
                  searchConfig: {
                    resetText: "Cancel",
                    submitText: "Update",
                  },
                }}
                grid={true}
                trigger={
                  <a key="button" type="primary">
                    Update Stage
                  </a>
                }
                title={"Update Deal Stage"}
                destroyOnClose={true}
                layoutType="ModalForm"
                onFinish={async (values) => {
                  try {
                    // Update the deal stage
                    await pouchDatabase(collection, databasePrefix).saveDocument(
                      {
                        ...record,
                        stage: values.stage,
                        probability: values.probability,
                        last_activity_date: new Date().toISOString().split("T")[0],
                      },
                      CRUD_USER
                    );

                    // Create an activity to log the stage change
                    await pouchDatabase("activities", databasePrefix).saveDocument(
                      {
                        activity_type: "note",
                        subject: `Deal stage updated to ${values.stage}`,
                        related_to_type: "deal",
                        deal: {
                          value: record._id,
                          label: record.name,
                          ...record,
                        },
                        start_date: new Date().toISOString(),
                        status: "completed",
                        description: values.notes || `Stage changed from ${record.stage} to ${values.stage}`,
                      },
                      CRUD_USER
                    );

                    message.success("Deal stage updated successfully");
                    return true;
                  } catch (error) {
                    message.error("Failed to update deal stage: " + error.message);
                    return false;
                  }
                }}
                columns={[
                  {
                    title: "Current Stage",
                    dataIndex: "current_stage",
                    valueType: "text",
                    readonly: true,
                    initialValue: record?.stage,
                    colProps: { span: 12 },
                  },
                  {
                    title: "New Stage",
                    dataIndex: "stage",
                    valueType: "select",
                    valueEnum: {
                      lead: { text: "Lead", status: "Default" },
                      qualified: { text: "Qualified", status: "Processing" },
                      proposal: { text: "Proposal", status: "Warning" },
                      negotiation: { text: "Negotiation", status: "Warning" },
                      closed_won: { text: "Closed Won", status: "Success" },
                      closed_lost: { text: "Closed Lost", status: "Error" },
                    },
                    required: true,
                    colProps: { span: 12 },
                  },
                  {
                    title: "Probability (%)",
                    dataIndex: "probability",
                    valueType: "digit",
                    min: 0,
                    max: 100,
                    colProps: { span: 12 },
                    dependencies: ["stage"],
                    formItemProps: (form) => {
                      const stage = form.getFieldValue("stage");
                      let initialValue = 0;

                      switch (stage) {
                        case "lead":
                          initialValue = 10;
                          break;
                        case "qualified":
                          initialValue = 30;
                          break;
                        case "proposal":
                          initialValue = 50;
                          break;
                        case "negotiation":
                          initialValue = 70;
                          break;
                        case "closed_won":
                          initialValue = 100;
                          break;
                        case "closed_lost":
                          initialValue = 0;
                          break;
                        default:
                          initialValue = 0;
                      }

                      return {
                        initialValue,
                      };
                    },
                  },
                  {
                    title: "Notes",
                    dataIndex: "notes",
                    valueType: "textarea",
                    colProps: { span: 24 },
                  },
                ]}
              />
            ),
          },
          {
            key: "create_bid",
            name: (
              <BetaSchemaForm
                formRef={action}
                submitter={{
                  searchConfig: {
                    resetText: "Cancel",
                    submitText: "Create",
                  },
                }}
                grid={true}
                trigger={
                  <a key="button" type="primary">
                    Create Bid
                  </a>
                }
                title={"Create Bid"}
                destroyOnClose={true}
                layoutType="ModalForm"
                onFinish={async (values) => {
                  try {
                    // Create a new bid linked to this deal
                    const bidData = {
                      ...values,
                      deal: {
                        value: record._id,
                        label: record.name,
                        ...record,
                      },
                      // If deal is associated with a company, link the bid to that company too
                      ...(record.company ? {
                        client: record.company
                      } : {}),
                      // If deal is associated with a contact, link the bid to that contact too
                      ...(record.contact ? {
                        contact: record.contact
                      } : {}),
                      status: "draft",
                      last_updated: new Date().toISOString(),
                    };

                    const savedBid = await pouchDatabase("bids", databasePrefix).saveDocument(
                      bidData,
                      CRUD_USER
                    );

                    // Update the deal's last_activity_date
                    await pouchDatabase(collection, databasePrefix).saveDocument(
                      {
                        ...record,
                        last_activity_date: new Date().toISOString().split("T")[0],
                      },
                      CRUD_USER
                    );

                    // Create an activity to log the bid creation
                    await pouchDatabase("activities", databasePrefix).saveDocument(
                      {
                        activity_type: "note",
                        subject: `Bid created: ${values.title}`,
                        related_to_type: "deal",
                        deal: {
                          value: record._id,
                          label: record.name,
                          ...record,
                        },
                        bid: {
                          value: savedBid._id,
                          label: savedBid.title,
                          ...savedBid,
                        },
                        start_date: new Date().toISOString(),
                        status: "completed",
                        description: `New bid "${values.title}" created for this deal.`,
                      },
                      CRUD_USER
                    );

                    message.success("Bid created successfully");
                    return true;
                  } catch (error) {
                    message.error("Failed to create bid: " + error.message);
                    return false;
                  }
                }}
                columns={[
                  {
                    title: "Bid Title",
                    dataIndex: "title",
                    valueType: "text",
                    required: true,
                    colProps: { span: 24 },
                    initialValue: `Bid for ${record.name}`,
                  },
                  {
                    title: "Bid Type",
                    dataIndex: "bid_type",
                    valueType: "select",
                    valueEnum: {
                      rfp: { text: "RFP (Request for Proposal)" },
                      rfi: { text: "RFI (Request for Information)" },
                      rfq: { text: "RFQ (Request for Quotation)" },
                      itb: { text: "ITB (Invitation to Bid)" },
                      eoi: { text: "EOI (Expression of Interest)" },
                      tender: { text: "Tender" },
                      other: { text: "Other" },
                    },
                    required: true,
                    colProps: { span: 12 },
                    initialValue: "rfp",
                  },
                  {
                    title: "Bid Value",
                    dataIndex: "value",
                    valueType: "money",
                    required: true,
                    colProps: { span: 12 },
                    initialValue: record.value,
                  },
                  {
                    title: "Currency",
                    dataIndex: "currency",
                    valueType: "select",
                    valueEnum: {
                      USD: { text: "USD" },
                      EUR: { text: "EUR" },
                      GBP: { text: "GBP" },
                      JPY: { text: "JPY" },
                      AUD: { text: "AUD" },
                      CAD: { text: "CAD" },
                      UGX: { text: "UGX" },
                    },
                    colProps: { span: 12 },
                    initialValue: record.currency || "UGX",
                  },
                  {
                    title: "Submission Deadline",
                    dataIndex: "submission_deadline",
                    valueType: "dateTime",
                    required: true,
                    colProps: { span: 12 },
                    initialValue: moment().add(14, 'days').format(),
                  },
                  {
                    title: "Probability (%)",
                    dataIndex: "probability",
                    valueType: "digit",
                    min: 0,
                    max: 100,
                    colProps: { span: 12 },
                    initialValue: 30,
                  },
                  {
                    title: "Requirements",
                    dataIndex: "requirements",
                    valueType: "textarea",
                    colProps: { span: 24 },
                  },
                  {
                    title: "Scope of Work",
                    dataIndex: "scope_of_work",
                    valueType: "textarea",
                    colProps: { span: 24 },
                  },
                ]}
              />
            ),
          },
        ]}
      />
    );
  },
};
