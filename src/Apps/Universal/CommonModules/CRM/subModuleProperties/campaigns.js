import React, { useRef } from "react";
import { BetaSchemaForm, TableDropdown  } from "@ant-design/pro-components";
import { message  } from "antd";


export default {
  buffResults: (data) => {
    // Any data transformations needed before display
    return data;
  },
  
  MoreActions: (props) => {
    const action = useRef();
    
    const {
      pouchDatabase,
      collection,
      databasePrefix,
      record,
      CRUD_USER,
      singular,
      modules,
    } = props;
    
    return (
      <TableDropdown
        key="actionGroup"
        onSelect={async (key) => {
          if (key === "add_activity") {
            // Handle adding activity
          } else if (key === "update_status") {
            // Handle updating status
          }
        }}
        menus={[
          {
            key: "add_activity",
            name: (
              <BetaSchemaForm
                formRef={action}
                submitter={{
                  searchConfig: {
                    resetText: "Cancel",
                    submitText: "Save",
                  },
                }}
                grid={true}
                trigger={
                  <a key="button" type="primary">
                    Add Activity
                  </a>
                }
                title={"Add Campaign Activity"}
                destroyOnClose={true}
                layoutType="ModalForm"
                onFinish={async (values) => {
                  try {
                    // Create a new activity linked to this campaign
                    await pouchDatabase("activities", databasePrefix).saveDocument(
                      {
                        ...values,
                        related_to_type: "campaign",
                        campaign: {
                          value: record._id,
                          label: record.name,
                          ...record,
                        },
                        status: values.status || "planned",
                      },
                      CRUD_USER
                    );
                    
                    message.success("Activity added successfully");
                    return true;
                  } catch (error) {
                    message.error("Failed to add activity: " + error.message);
                    return false;
                  }
                }}
                columns={[
                  {
                    title: "Activity Type",
                    dataIndex: "activity_type",
                    valueType: "select",
                    valueEnum: {
                      call: { text: "Call" },
                      meeting: { text: "Meeting" },
                      email: { text: "Email" },
                      task: { text: "Task" },
                      note: { text: "Note" },
                      other: { text: "Other" },
                    },
                    required: true,
                    colProps: { span: 12 },
                  },
                  {
                    title: "Subject",
                    dataIndex: "subject",
                    valueType: "text",
                    required: true,
                    colProps: { span: 12 },
                  },
                  {
                    title: "Start Date",
                    dataIndex: "start_date",
                    valueType: "dateTime",
                    required: true,
                    colProps: { span: 12 },
                    initialValue: new Date().toISOString(),
                  },
                  {
                    title: "Status",
                    dataIndex: "status",
                    valueType: "select",
                    valueEnum: {
                      planned: { text: "Planned", status: "Default" },
                      in_progress: { text: "In Progress", status: "Processing" },
                      completed: { text: "Completed", status: "Success" },
                      canceled: { text: "Canceled", status: "Error" },
                    },
                    required: true,
                    colProps: { span: 12 },
                    initialValue: "planned",
                  },
                  {
                    title: "Description",
                    dataIndex: "description",
                    valueType: "textarea",
                    colProps: { span: 24 },
                  },
                ]}
              />
            ),
          },
          {
            key: "update_status",
            name: (
              <BetaSchemaForm
                formRef={action}
                submitter={{
                  searchConfig: {
                    resetText: "Cancel",
                    submitText: "Update",
                  },
                }}
                grid={true}
                trigger={
                  <a key="button" type="primary">
                    Update Status
                  </a>
                }
                title={"Update Campaign Status"}
                destroyOnClose={true}
                layoutType="ModalForm"
                onFinish={async (values) => {
                  try {
                    // Update the campaign status
                    await pouchDatabase(collection, databasePrefix).saveDocument(
                      {
                        ...record,
                        status: values.status,
                        actual_cost: values.actual_cost || record.actual_cost,
                      },
                      CRUD_USER
                    );
                    
                    // Create an activity to log the status change
                    await pouchDatabase("activities", databasePrefix).saveDocument(
                      {
                        activity_type: "note",
                        subject: `Campaign status updated to ${values.status}`,
                        related_to_type: "campaign",
                        campaign: {
                          value: record._id,
                          label: record.name,
                          ...record,
                        },
                        start_date: new Date().toISOString(),
                        status: "completed",
                        description: values.notes || `Status changed from ${record.status} to ${values.status}`,
                      },
                      CRUD_USER
                    );
                    
                    message.success("Campaign status updated successfully");
                    return true;
                  } catch (error) {
                    message.error("Failed to update campaign status: " + error.message);
                    return false;
                  }
                }}
                columns={[
                  {
                    title: "Current Status",
                    dataIndex: "current_status",
                    valueType: "text",
                    readonly: true,
                    initialValue: record?.status,
                    colProps: { span: 12 },
                  },
                  {
                    title: "New Status",
                    dataIndex: "status",
                    valueType: "select",
                    valueEnum: {
                      planning: { text: "Planning", status: "Default" },
                      active: { text: "Active", status: "Processing" },
                      completed: { text: "Completed", status: "Success" },
                      canceled: { text: "Canceled", status: "Error" },
                      on_hold: { text: "On Hold", status: "Warning" },
                    },
                    required: true,
                    colProps: { span: 12 },
                  },
                  {
                    title: "Actual Cost",
                    dataIndex: "actual_cost",
                    valueType: "money",
                    colProps: { span: 12 },
                    initialValue: record?.actual_cost,
                  },
                  {
                    title: "Notes",
                    dataIndex: "notes",
                    valueType: "textarea",
                    colProps: { span: 24 },
                  },
                ]}
              />
            ),
          },
        ]}
      />
    );
  },
};
