import React from "react";
import ViewTable from "../../../../../../Components/ViewTable";
import { Descriptions, Divider, Tabs, Layout, Tag, Progress  } from "antd";
import { DollarOutlined, UserOutlined, BankOutlined, ScheduleOutlined, CalendarOutlined, FlagOutlined, FileProtectOutlined,   } from "@ant-design/icons";
import { PageHeader  } from "@ant-design/pro-layout";


const { Content } = Layout;

const Deal = (props) => {
  const { data, pouchDatabase, databasePrefix, CRUD_USER, modules, modulesProperties } = props;

  // Shared props for ViewTable components
  const sharedProps = {
    pouchDatabase,
    databasePrefix,
    CRUD_USER,
    modules,
    modulesProperties,
    tableFilter: {
      deal: {
        value: data._id,
        label: data.name,
      },
    },
  };

  // Function to determine progress color based on stage
  const getProgressColor = (stage) => {
    switch (stage) {
      case "lead":
        return "#bfbfbf";
      case "qualified":
        return "#1890ff";
      case "proposal":
        return "#faad14";
      case "negotiation":
        return "#fa8c16";
      case "closed_won":
        return "#52c41a";
      case "closed_lost":
        return "#f5222d";
      default:
        return "#1890ff";
    }
  };

  // Function to determine progress percentage based on stage
  const getProgressPercentage = (stage) => {
    switch (stage) {
      case "lead":
        return 20;
      case "qualified":
        return 40;
      case "proposal":
        return 60;
      case "negotiation":
        return 80;
      case "closed_won":
        return 100;
      case "closed_lost":
        return 100;
      default:
        return 0;
    }
  };

  // Format stage name for display
  const formatStage = (stage) => {
    if (!stage) return "";
    return stage
      .split("_")
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
      .join(" ");
  };

  return (
    <>
      <PageHeader
        backIcon={<DollarOutlined style={{ fontSize: 30 }} />}
        onBack={() => null}
        title={`${data.name}`}
        subTitle={`${data.value ? data.value.toLocaleString() : ""} ${data.currency || ""}`}
        tags={
          data.tags &&
          data.tags.map((tag) => (
            <Tag color="blue" key={tag}>
              {tag}
            </Tag>
          ))
        }
      />
      <Content>
        <Layout.Content style={{ marginTop: 30 }}>
          {data.stage && (
            <div style={{ marginBottom: 24 }}>
              <h3>Deal Stage: {formatStage(data.stage)}</h3>
              <Progress
                percent={getProgressPercentage(data.stage)}
                status={data.stage === "closed_lost" ? "exception" : "active"}
                strokeColor={getProgressColor(data.stage)}
              />
            </div>
          )}

          <Descriptions title="Deal Details" column={2}>
            {data.contact && data.contact.label && (
              <Descriptions.Item label="Contact">
                {data.contact.label}
              </Descriptions.Item>
            )}
            {data.company && data.company.label && (
              <Descriptions.Item label="Company">
                {data.company.label}
              </Descriptions.Item>
            )}
            {data.value && (
              <Descriptions.Item label="Value">
                {data.value.toLocaleString()} {data.currency || ""}
              </Descriptions.Item>
            )}
            {data.probability && (
              <Descriptions.Item label="Probability">
                {data.probability}%
              </Descriptions.Item>
            )}
            {data.expected_close_date && (
              <Descriptions.Item label="Expected Close Date">
                {data.expected_close_date}
              </Descriptions.Item>
            )}
            {data.source && (
              <Descriptions.Item label="Source">{data.source}</Descriptions.Item>
            )}
            {data.owner && data.owner.label && (
              <Descriptions.Item label="Owner">{data.owner.label}</Descriptions.Item>
            )}
            {data.priority && (
              <Descriptions.Item label="Priority">{data.priority}</Descriptions.Item>
            )}
            {data.products && data.products.length > 0 && (
              <Descriptions.Item label="Products/Services" span={2}>
                {data.products.map((product) => (
                  <Tag key={product}>{product}</Tag>
                ))}
              </Descriptions.Item>
            )}
            {data.last_activity_date && (
              <Descriptions.Item label="Last Activity">
                {data.last_activity_date}
              </Descriptions.Item>
            )}
            {data.createdAt && (
              <Descriptions.Item label="Created Date">
                {new Date(data.createdAt).toLocaleDateString()}
              </Descriptions.Item>
            )}
          </Descriptions>

          {data.notes && (
            <>
              <Divider orientation="left">Notes</Divider>
              <p>{data.notes}</p>
            </>
          )}
        </Layout.Content>
        <Divider />
        <Tabs defaultActiveKey="activities">
          <Tabs.TabPane
            tab={
              <span>
                <ScheduleOutlined /> Activities
              </span>
            }
            key="activities"
          >
            <ViewTable
              {...sharedProps}
              {...modules.activities}
              {...modulesProperties.activities}
            />
          </Tabs.TabPane>
          <Tabs.TabPane
            tab={
              <span>
                <FileProtectOutlined /> Bids
              </span>
            }
            key="bids"
          >
            <ViewTable
              {...sharedProps}
              {...modules.bids}
              {...modulesProperties.bids}
            />
          </Tabs.TabPane>
        </Tabs>
      </Content>
    </>
  );
};

export default Deal;
