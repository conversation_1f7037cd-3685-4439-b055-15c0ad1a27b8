import React from "react";
import ViewTable from "../../../../../../Components/ViewTable";
import { Descriptions, Divider, Tabs, Layout, Tag, Card, List, Avatar  } from "antd";
import { PageHeader  } from "@ant-design/pro-layout";
import { UserOutlined, MailOutlined, PhoneOutlined, BankOutlined, ScheduleOutlined, DollarOutlined, TeamOutlined, GlobalOutlined, EnvironmentOutlined, IdcardOutlined,   } from "@ant-design/icons";


const { Content } = Layout;

const Contact = (props) => {
  const { data, pouchDatabase, databasePrefix, CRUD_USER, modules, modulesProperties } = props;

  // Shared props for ViewTable components
  const sharedProps = {
    pouchDatabase,
    databasePrefix,
    CRUD_USER,
    modules,
    modulesProperties,
    tableFilter: {
      contact: {
        value: data._id,
        label: data.name,
      },
    },
  };

  // No need for company contacts ViewTable as we're using embedded contact persons

  return (
    <>
      <PageHeader
        backIcon={
          data.contact_type === "Company" ? (
            <BankOutlined style={{ fontSize: 30 }} />
          ) : (
            <UserOutlined style={{ fontSize: 30 }} />
          )
        }
        onBack={() => null}
        title={`${data.name}`}
        subTitle={`${data.phone}`}
        tags={
          data.tags &&
          data.tags.map((tag) => (
            <Tag color="blue" key={tag}>
              {tag}
            </Tag>
          ))
        }
      />
      <Content>
        <Layout.Content style={{ marginTop: 30 }}>
          {data.contact_type === "Company" ? (
            // Company details
            <>
              <Descriptions title="Company Details" column={2}>
                {data.industry && (
                  <Descriptions.Item label="Industry">
                    {data.industry}
                  </Descriptions.Item>
                )}
                {data.email && (
                  <Descriptions.Item label="Email">
                    <a href={`mailto:${data.email}`}>{data.email}</a>
                  </Descriptions.Item>
                )}
                {data.phone && (
                  <Descriptions.Item label="Phone">
                    <a href={`tel:${data.phone}`}>{data.phone}</a>
                  </Descriptions.Item>
                )}
                {data.website && (
                  <Descriptions.Item label="Website">
                    <a href={data.website} target="_blank" rel="noopener noreferrer">
                      {data.website}
                    </a>
                  </Descriptions.Item>
                )}
                {data.address && (
                  <Descriptions.Item label="Address">{data.address}</Descriptions.Item>
                )}
                {data.city && data.state && (
                  <Descriptions.Item label="City/State">
                    {data.city}, {data.state}
                  </Descriptions.Item>
                )}
                {data.postal_code && (
                  <Descriptions.Item label="Postal Code">
                    {data.postal_code}
                  </Descriptions.Item>
                )}
                {data.country && (
                  <Descriptions.Item label="Country">{data.country}</Descriptions.Item>
                )}
                {data.tax_id && (
                  <Descriptions.Item label="Tax ID / VAT Number">
                    {data.tax_id}
                  </Descriptions.Item>
                )}
                {data.annual_revenue && (
                  <Descriptions.Item label="Annual Revenue">
                    {data.annual_revenue.toLocaleString()}
                  </Descriptions.Item>
                )}
                {data.employee_count && (
                  <Descriptions.Item label="Number of Employees">
                    {data.employee_count}
                  </Descriptions.Item>
                )}
                {data.status && (
                  <Descriptions.Item label="Status">{data.status}</Descriptions.Item>
                )}
                {data.source && (
                  <Descriptions.Item label="Source">{data.source}</Descriptions.Item>
                )}
                {data.last_contact_date && (
                  <Descriptions.Item label="Last Contact">
                    {data.last_contact_date}
                  </Descriptions.Item>
                )}
              </Descriptions>

              {data.contact_persons && data.contact_persons.length > 0 && (
                <>
                  <Divider orientation="left">Contact Persons</Divider>
                  <List
                    itemLayout="horizontal"
                    dataSource={data.contact_persons}
                    renderItem={person => (
                      <List.Item>
                        <List.Item.Meta
                          avatar={<Avatar icon={<UserOutlined />} />}
                          title={`${person.title ? person.title + ". " : ""}${person.first_name} ${person.last_name}`}
                          description={
                            <>
                              {person.position && <div><strong>Position:</strong> {person.position}</div>}
                              {person.department && <div><strong>Department:</strong> {person.department}</div>}
                              {person.email && <div><strong>Email:</strong> <a href={`mailto:${person.email}`}>{person.email}</a></div>}
                              {person.phone && <div><strong>Phone:</strong> <a href={`tel:${person.phone}`}>{person.phone}</a></div>}
                              {person.mobile && <div><strong>Mobile:</strong> <a href={`tel:${person.mobile}`}>{person.mobile}</a></div>}
                            </>
                          }
                        />
                      </List.Item>
                    )}
                  />
                </>
              )}
            </>
          ) : (
            // Individual contact details
            <>
              <Descriptions title="Contact Details" column={2}>
                <Descriptions.Item label="Contact Type">
                  Individual
                </Descriptions.Item>
                {data.email && (
                  <Descriptions.Item label="Email">
                    <a href={`mailto:${data.email}`}>{data.email}</a>
                  </Descriptions.Item>
                )}
                {data.phone && (
                  <Descriptions.Item label="Phone">
                    <a href={`tel:${data.phone}`}>{data.phone}</a>
                  </Descriptions.Item>
                )}
                {data.mobile && (
                  <Descriptions.Item label="Mobile">
                    <a href={`tel:${data.mobile}`}>{data.mobile}</a>
                  </Descriptions.Item>
                )}
                {data.position && (
                  <Descriptions.Item label="Position">
                    {data.position}
                  </Descriptions.Item>
                )}
                {data.address && (
                  <Descriptions.Item label="Address">{data.address}</Descriptions.Item>
                )}
                {data.city && data.state && (
                  <Descriptions.Item label="City/State">
                    {data.city}, {data.state}
                  </Descriptions.Item>
                )}
                {data.postal_code && (
                  <Descriptions.Item label="Postal Code">
                    {data.postal_code}
                  </Descriptions.Item>
                )}
                {data.country && (
                  <Descriptions.Item label="Country">{data.country}</Descriptions.Item>
                )}
                {data.website && (
                  <Descriptions.Item label="Website">
                    <a href={data.website} target="_blank" rel="noopener noreferrer">
                      {data.website}
                    </a>
                  </Descriptions.Item>
                )}
                {data.source && (
                  <Descriptions.Item label="Source">{data.source}</Descriptions.Item>
                )}
                {data.status && (
                  <Descriptions.Item label="Status">{data.status}</Descriptions.Item>
                )}
                {data.last_contact_date && (
                  <Descriptions.Item label="Last Contact">
                    {data.last_contact_date}
                  </Descriptions.Item>
                )}
              </Descriptions>
            </>
          )}

          {data.notes && (
            <>
              <Divider orientation="left">Notes</Divider>
              <p>{data.notes}</p>
            </>
          )}
        </Layout.Content>
        <Divider />
        <Tabs defaultActiveKey="activities">
          <Tabs.TabPane
            tab={
              <span>
                <ScheduleOutlined /> Activities
              </span>
            }
            key="activities"
          >
            <ViewTable
              {...sharedProps}
              {...modules.activities}
              {...modulesProperties.activities}
            />
          </Tabs.TabPane>
          <Tabs.TabPane
            tab={
              <span>
                <DollarOutlined /> Deals
              </span>
            }
            key="deals"
          >
            <ViewTable
              {...sharedProps}
              {...modules.deals}
              {...modulesProperties.deals}
            />
          </Tabs.TabPane>

        </Tabs>
      </Content>
    </>
  );
};

export default Contact;
