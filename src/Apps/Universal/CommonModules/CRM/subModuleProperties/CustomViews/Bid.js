import React from "react";
import ViewTable from "../../../../../../Components/ViewTable";
import moment from "moment";
import { Descriptions, Divider, Tabs, Layout, Tag, Progress, Badge  } from "antd";
import { FileProtectOutlined, DollarOutlined, UserOutlined, BankOutlined, ScheduleOutlined, CalendarOutlined, TeamOutlined,   } from "@ant-design/icons";
import { PageHeader  } from "@ant-design/pro-layout";


const { Content } = Layout;

const Bid = (props) => {
  const { data, pouchDatabase, databasePrefix, CRUD_USER, modules, modulesProperties } = props;

  // Shared props for ViewTable components
  const sharedProps = {
    pouchDatabase,
    databasePrefix,
    CRUD_USER,
    modules,
    modulesProperties,
    tableFilter: {
      bid: {
        value: data._id,
        label: data.title,
      },
    },
  };

  // Function to determine progress color based on status
  const getProgressColor = (status) => {
    switch (status) {
      case "pending":
        return "#1890ff";
      case "approved":
        return "#52c41a";
      case "rejected":
        return "#f5222d";
      default:
        return "#1890ff";
    }
  };

  // Function to determine progress percentage based on status
  const getProgressPercentage = (status) => {
    switch (status) {
      case "pending":
        return 33;
      case "approved":
        return 100;
      case "rejected":
        return 100;
      default:
        return 0;
    }
  };

  // Format status name for display
  const formatStatus = (status) => {
    if (!status) return "";
    return status
      .split("_")
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
      .join(" ");
  };

  // Calculate days remaining until submission deadline
  const getDaysRemaining = () => {
    if (!data.submission_deadline) return null;

    const deadline = moment(data.submission_deadline);
    const now = moment();
    const daysRemaining = deadline.diff(now, 'days');

    if (daysRemaining < 0) {
      return <Badge status="error" text="Deadline passed" />;
    } else if (daysRemaining === 0) {
      return <Badge status="warning" text="Due today" />;
    } else if (daysRemaining <= 3) {
      return <Badge status="warning" text={`${daysRemaining} days remaining`} />;
    } else {
      return <Badge status="success" text={`${daysRemaining} days remaining`} />;
    }
  };

  return (
    <>
      <PageHeader
        backIcon={<FileProtectOutlined style={{ fontSize: 30 }} />}
        onBack={() => null}
        title={`${data.title}`}
        subTitle={`${data.value ? data.value.toLocaleString() : ""} ${data.currency || ""}`}
        tags={
          data.tags &&
          data.tags.map((tag) => (
            <Tag color="blue" key={tag}>
              {tag}
            </Tag>
          ))
        }
      />
      <Content>
        <Layout.Content style={{ marginTop: 30 }}>
          {data.status && (
            <div style={{ marginBottom: 24 }}>
              <h3>Bid Status: {formatStatus(data.status)}</h3>
              <Progress
                percent={getProgressPercentage(data.status)}
                status={data.status === "lost" || data.status === "canceled" ? "exception" : "active"}
                strokeColor={getProgressColor(data.status)}
              />
            </div>
          )}

          <Descriptions title="Bid Details" column={2}>
            {data.bid_type && (
              <Descriptions.Item label="Bid Type">
                {data.bid_type}
              </Descriptions.Item>
            )}
            {data.client && data.client.label && (
              <Descriptions.Item label="Client">
                <Tag icon={<BankOutlined />} color="blue">{data.client.label}</Tag>
              </Descriptions.Item>
            )}
            {data.contact && data.contact.label && (
              <Descriptions.Item label="Contact Person">
                <Tag icon={<UserOutlined />} color="green">{data.contact.label}</Tag>
              </Descriptions.Item>
            )}
            {data.deal && data.deal.label && (
              <Descriptions.Item label="Related Deal">
                {data.deal.label}
              </Descriptions.Item>
            )}
            {data.value && (
              <Descriptions.Item label="Bid Value">
                {data.value.toLocaleString()} {data.currency || ""}
              </Descriptions.Item>
            )}
            {data.probability && (
              <Descriptions.Item label="Probability">
                {data.probability}%
              </Descriptions.Item>
            )}
            {data.submission_deadline && (
              <Descriptions.Item label="Submission Deadline">
                {moment(data.submission_deadline).format("MMMM D, YYYY h:mm A")} {getDaysRemaining()}
              </Descriptions.Item>
            )}
            {data.decision_date && (
              <Descriptions.Item label="Decision Date">
                {moment(data.decision_date).format("MMMM D, YYYY")}
              </Descriptions.Item>
            )}
            {data.owner && data.owner.label && (
              <Descriptions.Item label="Owner">{data.owner.label}</Descriptions.Item>
            )}
            {data.status === "approved" && data.marketing_person && data.marketing_person.label && (
              <Descriptions.Item label="Marketing Person">
                <Tag color="green" icon={<UserOutlined />}>{data.marketing_person.label}</Tag>
              </Descriptions.Item>
            )}
            {data.bid_team && data.bid_team.length > 0 && (
              <Descriptions.Item label="Bid Team" span={2}>
                {data.bid_team.map((member) => (
                  <Tag key={member.value} icon={<TeamOutlined />}>{member.label}</Tag>
                ))}
              </Descriptions.Item>
            )}
            {data.competitors && data.competitors.length > 0 && (
              <Descriptions.Item label="Competitors" span={2}>
                {data.competitors.map((competitor) => (
                  <Tag key={competitor}>{competitor}</Tag>
                ))}
              </Descriptions.Item>
            )}
            {data.last_updated && (
              <Descriptions.Item label="Last Updated">
                {moment(data.last_updated).format("MMMM D, YYYY h:mm A")}
              </Descriptions.Item>
            )}
            {data.createdAt && (
              <Descriptions.Item label="Created Date">
                {moment(data.createdAt).format("MMMM D, YYYY h:mm A")}
              </Descriptions.Item>
            )}
          </Descriptions>

          {data.requirements && (
            <>
              <Divider orientation="left">Requirements</Divider>
              <p>{data.requirements}</p>
            </>
          )}

          {data.scope_of_work && (
            <>
              <Divider orientation="left">Scope of Work</Divider>
              <p>{data.scope_of_work}</p>
            </>
          )}

          {data.notes && (
            <>
              <Divider orientation="left">Notes</Divider>
              <p>{data.notes}</p>
            </>
          )}

          {data.status === "approved" && data.marketing_feedback && (
            <>
              <Divider orientation="left">Marketing Feedback</Divider>
              <p>{data.marketing_feedback}</p>
            </>
          )}
        </Layout.Content>
        <Divider />
        <Tabs defaultActiveKey="activities">
          <Tabs.TabPane
            tab={
              <span>
                <ScheduleOutlined /> Activities
              </span>
            }
            key="activities"
          >
            <ViewTable
              {...sharedProps}
              {...modules.activities}
              {...modulesProperties.activities}
            />
          </Tabs.TabPane>
        </Tabs>
      </Content>
    </>
  );
};

export default Bid;
