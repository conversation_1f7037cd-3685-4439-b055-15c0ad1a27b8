import React from "react";
import ViewTable from "../../../../../../Components/ViewTable";
import { BankOutlined, UserOutlined, MailOutlined, PhoneOutlined, ScheduleOutlined, DollarOutlined, GlobalOutlined,   } from "@ant-design/icons";
import { Descriptions, Divider, Tabs, Layout, Tag  } from "antd";
import { PageHeader  } from "@ant-design/pro-layout";


const { Content } = Layout;

const Company = (props) => {
  const { data, pouchDatabase, databasePrefix, CRUD_USER, modules, modulesProperties } = props;

  // Shared props for ViewTable components
  const sharedProps = {
    pouchDatabase,
    databasePrefix,
    CRUD_USER,
    modules,
    modulesProperties,
    tableFilter: {
      company: {
        value: data._id,
        label: data.name,
      },
    },
  };

  return (
    <>
      <PageHeader
        backIcon={<BankOutlined style={{ fontSize: 30 }} />}
        onBack={() => null}
        title={`${data.name}`}
        subTitle={`${data.phone}`}
        tags={
          data.tags &&
          data.tags.map((tag) => (
            <Tag color="blue" key={tag}>
              {tag}
            </Tag>
          ))
        }
      />
      <Content>
        <Layout.Content style={{ marginTop: 30 }}>
          <Descriptions title="Company Details" column={2}>
            {data.industry && (
              <Descriptions.Item label="Industry">{data.industry}</Descriptions.Item>
            )}
            {data.email && (
              <Descriptions.Item label="Email">
                <a href={`mailto:${data.email}`}>{data.email}</a>
              </Descriptions.Item>
            )}
            {data.phone && (
              <Descriptions.Item label="Phone">
                <a href={`tel:${data.phone}`}>{data.phone}</a>
              </Descriptions.Item>
            )}
            {data.website && (
              <Descriptions.Item label="Website">
                <a href={data.website} target="_blank" rel="noopener noreferrer">
                  {data.website}
                </a>
              </Descriptions.Item>
            )}
            {data.address && (
              <Descriptions.Item label="Address">{data.address}</Descriptions.Item>
            )}
            {data.city && data.state && (
              <Descriptions.Item label="City/State">
                {data.city}, {data.state}
              </Descriptions.Item>
            )}
            {data.postal_code && (
              <Descriptions.Item label="Postal Code">
                {data.postal_code}
              </Descriptions.Item>
            )}
            {data.country && (
              <Descriptions.Item label="Country">{data.country}</Descriptions.Item>
            )}
            {data.annual_revenue && (
              <Descriptions.Item label="Annual Revenue">
                {data.annual_revenue.toLocaleString()}
              </Descriptions.Item>
            )}
            {data.employee_count && (
              <Descriptions.Item label="Employees">
                {data.employee_count}
              </Descriptions.Item>
            )}
            {data.tax_id && (
              <Descriptions.Item label="Tax ID / VAT">
                {data.tax_id}
              </Descriptions.Item>
            )}
            {data.source && (
              <Descriptions.Item label="Source">{data.source}</Descriptions.Item>
            )}
            {data.status && (
              <Descriptions.Item label="Status">{data.status}</Descriptions.Item>
            )}
            {data.primary_contact && data.primary_contact.label && (
              <Descriptions.Item label="Primary Contact">
                {data.primary_contact.label}
              </Descriptions.Item>
            )}
            {data.last_contact_date && (
              <Descriptions.Item label="Last Contact">
                {data.last_contact_date}
              </Descriptions.Item>
            )}
          </Descriptions>

          {data.notes && (
            <>
              <Divider orientation="left">Notes</Divider>
              <p>{data.notes}</p>
            </>
          )}
        </Layout.Content>
        <Divider />
        <Tabs defaultActiveKey="contacts">
          <Tabs.TabPane
            tab={
              <span>
                <UserOutlined /> Contacts
              </span>
            }
            key="contacts"
          >
            <ViewTable
              {...sharedProps}
              {...modules.contacts}
              {...modulesProperties.contacts}
            />
          </Tabs.TabPane>
          <Tabs.TabPane
            tab={
              <span>
                <DollarOutlined /> Deals
              </span>
            }
            key="deals"
          >
            <ViewTable
              {...sharedProps}
              {...modules.deals}
              {...modulesProperties.deals}
            />
          </Tabs.TabPane>
          <Tabs.TabPane
            tab={
              <span>
                <ScheduleOutlined /> Activities
              </span>
            }
            key="activities"
          >
            <ViewTable
              {...sharedProps}
              {...modules.activities}
              {...modulesProperties.activities}
            />
          </Tabs.TabPane>
        </Tabs>
      </Content>
    </>
  );
};

export default Company;
