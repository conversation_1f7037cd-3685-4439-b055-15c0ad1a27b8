import ProcurementReport from "./ProcurementReport";
import ProcurementStatusReport from "./ProcurementStatusReport";
import React from "react";
import { Tabs  } from "antd";


const Reports = (props) => {
  const { pouchDatabase, databasePrefix } = props;

  const items = [
    {
      key: "1",
      label: `Procurement Report`,
      children: (
        <ProcurementReport
          pouchDatabase={pouchDatabase}
          databasePrefix={databasePrefix}
        />
      ),
    },
    {
      key: "2",
      label: `Procurement Status Report`,
      children: (
        <ProcurementStatusReport
          pouchDatabase={pouchDatabase}
          databasePrefix={databasePrefix}
        />
      ),
    }
  ];

  return (
    <Tabs defaultActiveKey="2" items={items} />
  );
};

export default Reports;
