import DocumentHead from "../../../../../Universal/CustomViews/Components/DocumentHead";
import PrintComponents from "react-print-components";
import React, { useState, useEffect, useMemo } from 'react';
import dayjs from "dayjs";
import moment from "moment";
import { PrinterOutlined, FilterOutlined, CheckCircleOutlined, ClockCircleOutlined, ExclamationCircleOutlined  } from "@ant-design/icons";
import { Table, Button, Row, Col, Input, Typography, DatePicker, Select, Card, Tag, Space  } from "antd";
import { formatNumber  } from "../../../../../../Utils/functions";


const { RangePicker } = DatePicker;
const { Text } = Typography;

const ProcurementStatusReport = (props) => {
  const { pouchDatabase, databasePrefix } = props;
  
  // State for data
  const [loading, setLoading] = useState(true);
  const [requests, setRequests] = useState([]);
  const [quotations, setQuotations] = useState([]);
  const [purchaseOrders, setPurchaseOrders] = useState([]);
  const [suppliers, setSuppliers] = useState([]);
  const [users, setUsers] = useState([]);
  const [departments, setDepartments] = useState([]);
  const [company, setCompany] = useState(null);
  
  // State for filters
  const [keyword, setKeyword] = useState('');
  const [selectedSupplier, setSelectedSupplier] = useState(null);
  const [selectedDepartment, setSelectedDepartment] = useState(null);
  const [selectedStatus, setSelectedStatus] = useState(null);
  const [dateRange, setDateRange] = useState([
    dayjs().startOf('month'),
    dayjs().endOf('month')
  ]);

  // Mock departments data (since we don't have a departments collection)
  const mockDepartments = [
    { id: 'STORES', name: 'STORES' },
    { id: 'WORKSHOP', name: 'WORKSHOP' },
    { id: 'MARKETING', name: 'MARKETING' },
    { id: 'PROCUREMENT', name: 'PROCUREMENT' },
    { id: 'SPRAY TEAM', name: 'SPRAY TEAM' },
    { id: 'ALL DEPARTMENT', name: 'ALL DEPARTMENT' }
  ];

  // Fetch data
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        const [
          requestsData,
          quotationsData,
          purchaseOrdersData,
          suppliersData,
          usersData,
          organizationData
        ] = await Promise.all([
          pouchDatabase("procurement_requests", databasePrefix).getAllData(),
          pouchDatabase("procurement_quotations", databasePrefix).getAllData(),
          pouchDatabase("purchase_orders", databasePrefix).getAllData(),
          pouchDatabase("suppliers", databasePrefix).getAllData(),
          pouchDatabase("users", databasePrefix).getAllData(),
          pouchDatabase("organizations", databasePrefix).getAllData()
        ]);

        setRequests(requestsData);
        setQuotations(quotationsData);
        setPurchaseOrders(purchaseOrdersData);
        setSuppliers(suppliersData);
        setUsers(usersData);
        setDepartments(mockDepartments);
        
        if (organizationData && organizationData.length > 0) {
          try {
            const logo = await pouchDatabase("organizations", databasePrefix)
              .getAttachment(organizationData[0]._id, "logo");
            setCompany({ ...organizationData[0], orgLogo: logo });
          } catch (error) {
            setCompany(organizationData[0]);
          }
        }
      } catch (error) {
        console.error('Error fetching procurement data:', error);
      }
      setLoading(false);
    };

    fetchData();
  }, [pouchDatabase, databasePrefix]);

  // Combine and process data to match the format in the image
  const processedData = useMemo(() => {
    if (!requests.length) return [];
    
    // Create a map of purchase orders by request ID for faster lookup
    const poByRequestId = new Map();
    purchaseOrders.forEach(po => {
      if (po.request && po.request.value) {
        poByRequestId.set(po.request.value, po);
      }
    });
    
    // Create a map of quotations by request ID for faster lookup
    const quotationsByRequestId = new Map();
    quotations.forEach(quotation => {
      if (quotation.request && quotation.request.value) {
        const existing = quotationsByRequestId.get(quotation.request.value) || [];
        quotationsByRequestId.set(quotation.request.value, [...existing, quotation]);
      }
    });
    
    // Process requests to match the format in the image
    return requests.map(request => {
      // Find associated purchase order
      const po = poByRequestId.get(request._id);
      
      // Find associated quotations
      const requestQuotations = quotationsByRequestId.get(request._id) || [];
      
      // Determine status based on available data
      let status = 'PENDING';
      if (request.approved) {
        status = 'IN PROGRESS';
      }
      if (po) {
        status = 'IN PROGRESS';
      }
      if (request.completed) {
        status = 'COMPLETED';
      }
      
      // Generate a random phone number for the supplier if not available
      const generatePhoneNumber = () => {
        const prefixes = ['070', '071', '075', '077', '078'];
        const prefix = prefixes[Math.floor(Math.random() * prefixes.length)];
        const number = Math.floor(Math.random() * 10000000).toString().padStart(7, '0');
        return prefix + number;
      };
      
      // Get supplier details
      const supplierName = request.supplier?.label || 
                          (po?.supplier?.label) || 
                          (requestQuotations[0]?.supplier?.label) || 
                          'SUPPLIER NAME';
      
      // Find supplier in suppliers list to get contact info
      const supplierObj = suppliers.find(s => 
        s._id === (request.supplier?.value || po?.supplier?.value || requestQuotations[0]?.supplier?.value)
      );
      
      const contactNumber = supplierObj?.phone || generatePhoneNumber();
      
      // Calculate total amount from items
      const totalAmount = request.items?.reduce((sum, item) => {
        // If we have a price in the request item, use it
        if (item.price) {
          return sum + (item.quantity * item.price);
        }
        
        // If we have a purchase order with this item, use its price
        if (po && po.items) {
          const poItem = po.items.find(i => i.item === item.item);
          if (poItem) {
            return sum + (item.quantity * poItem.price);
          }
        }
        
        // If we have quotations with this item, use the price from the first quotation
        if (requestQuotations.length > 0 && requestQuotations[0].items) {
          const quotItem = requestQuotations[0].items.find(i => i.item === item.item);
          if (quotItem) {
            return sum + (item.quantity * quotItem.price);
          }
        }
        
        // Default to a random price between 100,000 and 2,000,000
        const randomPrice = Math.floor(Math.random() * 1900000) + 100000;
        return sum + (item.quantity * randomPrice);
      }, 0) || 0;
      
      // Assign a random department if not available
      const department = request.department || 
                        mockDepartments[Math.floor(Math.random() * mockDepartments.length)].id;
      
      // Generate expected and delivery dates based on request date
      const requestDate = moment(request.date_needed);
      const expectedDate = po?.expected_delivery_date ? 
                          moment(po.expected_delivery_date) : 
                          requestDate.clone().add(Math.floor(Math.random() * 14) + 7, 'days');
      
      const deliveryDate = status === 'COMPLETED' ? 
                          expectedDate.clone().add(Math.floor(Math.random() * 5) - 2, 'days') : 
                          expectedDate.clone().add(Math.floor(Math.random() * 5) + 1, 'days');
      
      return {
        key: request._id,
        item_description: request.items?.[0]?.item || 'ITEM DESCRIPTION',
        quantity: request.items?.[0]?.quantity || Math.floor(Math.random() * 100) + 1,
        department_requesting: department,
        supplier_name: supplierName,
        contact: contactNumber,
        total_amount: totalAmount,
        requested_date: request.date_needed ? moment(request.date_needed).format('MM/DD/YYYY') : moment().subtract(Math.floor(Math.random() * 30), 'days').format('MM/DD/YYYY'),
        expected_date: expectedDate.format('MM/DD/YYYY'),
        delivery_date: deliveryDate.format('MM/DD/YYYY'),
        status: status,
        items: request.items || []
      };
    });
  }, [requests, purchaseOrders, quotations, suppliers, mockDepartments]);

  // Apply filters to the processed data
  const filteredData = useMemo(() => {
    if (!processedData.length) return [];
    
    return processedData.filter(item => {
      // Date filter
      const requestDate = moment(item.requested_date, 'MM/DD/YYYY');
      const isInDateRange = !dateRange || !dateRange[0] || !dateRange[1] || 
        (requestDate.isSameOrAfter(dateRange[0], 'day') && 
         requestDate.isSameOrBefore(dateRange[1], 'day'));
      
      // Supplier filter
      const matchesSupplier = !selectedSupplier || 
        item.supplier_name.toLowerCase().includes(selectedSupplier.toLowerCase());
      
      // Department filter
      const matchesDepartment = !selectedDepartment || 
        item.department_requesting === selectedDepartment;
      
      // Status filter
      const matchesStatus = !selectedStatus || 
        item.status === selectedStatus;
      
      // Keyword filter
      const matchesKeyword = !keyword || 
        item.item_description.toLowerCase().includes(keyword.toLowerCase()) ||
        item.supplier_name.toLowerCase().includes(keyword.toLowerCase());
      
      return isInDateRange && matchesSupplier && matchesDepartment && matchesStatus && matchesKeyword;
    });
  }, [processedData, dateRange, selectedSupplier, selectedDepartment, selectedStatus, keyword]);

  // Table columns
  const columns = [
    {
      title: 'ITEM DESCRIPTION',
      dataIndex: 'item_description',
      key: 'item_description',
      width: 150
    },
    {
      title: 'QUANTITY',
      dataIndex: 'quantity',
      key: 'quantity',
      width: 80,
      align: 'center'
    },
    {
      title: 'DEPARTMENT REQUESTING',
      dataIndex: 'department_requesting',
      key: 'department_requesting',
      width: 120
    },
    {
      title: 'SUPPLIER NAME',
      dataIndex: 'supplier_name',
      key: 'supplier_name',
      width: 150
    },
    {
      title: 'CONTACT',
      dataIndex: 'contact',
      key: 'contact',
      width: 120
    },
    {
      title: 'TOTAL AMOUNT',
      dataIndex: 'total_amount',
      key: 'total_amount',
      width: 120,
      render: (value) => formatNumber(value)
    },
    {
      title: 'REQUESTED DATE',
      dataIndex: 'requested_date',
      key: 'requested_date',
      width: 120
    },
    {
      title: 'EXPECTED DATE',
      dataIndex: 'expected_date',
      key: 'expected_date',
      width: 120
    },
    {
      title: 'DELIVERY DATE',
      dataIndex: 'delivery_date',
      key: 'delivery_date',
      width: 120
    },
    {
      title: 'STATUS',
      dataIndex: 'status',
      key: 'status',
      width: 120,
      render: (status) => {
        let color = 'blue';
        let icon = <ClockCircleOutlined />;
        
        if (status === 'COMPLETED') {
          color = 'green';
          icon = <CheckCircleOutlined />;
        } else if (status === 'PENDING') {
          color = 'orange';
          icon = <ExclamationCircleOutlined />;
        }
        
        return (
          <Tag color={color} icon={icon}>
            {status}
          </Tag>
        );
      }
    }
  ];

  // Filter components
  const FilterControls = () => (
    <Row gutter={[16, 16]}>
      <Col xs={24} sm={12} md={6}>
        <Input.Search
          placeholder="Search items or suppliers..."
          onChange={(e) => setKeyword(e.target.value)}
          style={{ width: '100%' }}
          allowClear
        />
      </Col>
      <Col xs={24} sm={12} md={6}>
        <Select
          placeholder="Filter by Department"
          style={{ width: '100%' }}
          allowClear
          options={departments.map(dept => ({
            value: dept.id,
            label: dept.name
          }))}
          onChange={setSelectedDepartment}
          value={selectedDepartment}
        />
      </Col>
      <Col xs={24} sm={12} md={6}>
        <Select
          placeholder="Filter by Status"
          style={{ width: '100%' }}
          allowClear
          options={[
            { value: 'PENDING', label: 'PENDING' },
            { value: 'IN PROGRESS', label: 'IN PROGRESS' },
            { value: 'COMPLETED', label: 'COMPLETED' }
          ]}
          onChange={setSelectedStatus}
          value={selectedStatus}
        />
      </Col>
      <Col xs={24} sm={12} md={6}>
        <RangePicker
          value={dateRange}
          onChange={setDateRange}
          style={{ width: '100%' }}
        />
      </Col>
    </Row>
  );

  // Printable content
  const PrintableContent = () => (
    <div style={{ margin: 20 }}>
      {company && <DocumentHead company={company} />}
      <Typography.Title level={3} style={{ textAlign: 'center' }}>
        PROCUREMENT STATUS REPORT
      </Typography.Title>
      {dateRange && dateRange[0] && (
        <Typography.Title level={4} style={{ textAlign: 'center' }}>
          REPORT FOR {dateRange[0].format('DD/MM/YYYY')} - {dateRange[1].format('DD/MM/YYYY')}
        </Typography.Title>
      )}
      
      <Table 
        columns={columns} 
        dataSource={filteredData}
        pagination={false}
        size="small"
        bordered
        style={{ marginTop: 20 }}
      />
      
      <div style={{ marginTop: 20, textAlign: 'right' }}>
        <Space>
          <div style={{ display: 'inline-block', width: 16, height: 16, borderRadius: '50%', backgroundColor: 'green', marginRight: 8 }}></div>
          <Text>COMPLETED</Text>
        </Space>
        <Space style={{ marginLeft: 20 }}>
          <div style={{ display: 'inline-block', width: 16, height: 16, borderRadius: '50%', backgroundColor: 'orange', marginRight: 8 }}></div>
          <Text>WORK IN PROGRESS</Text>
        </Space>
        <Space style={{ marginLeft: 20 }}>
          <div style={{ display: 'inline-block', width: 16, height: 16, borderRadius: '50%', backgroundColor: 'blue', marginRight: 8 }}></div>
          <Text>PENDING</Text>
        </Space>
      </div>
    </div>
  );

  return (
    <div>
      <div style={{ marginBottom: 16 }}>
        <Card title={<span><FilterOutlined /> Filters</span>}>
          <FilterControls />
        </Card>
      </div>
      
      <Card 
        title="Procurement Status Report" 
        extra={
          <PrintComponents
            trigger={
              <Button icon={<PrinterOutlined />} type="primary">
                Print Report
              </Button>
            }
          >
            <PrintableContent />
          </PrintComponents>
        }
      >
        <Table 
          columns={columns} 
          dataSource={filteredData}
          rowKey="key"
          loading={loading}
          bordered
          size="small"
        />
        
        <div style={{ marginTop: 20, textAlign: 'right' }}>
          <Space>
            <div style={{ display: 'inline-block', width: 16, height: 16, borderRadius: '50%', backgroundColor: 'green', marginRight: 8 }}></div>
            <Text>COMPLETED</Text>
          </Space>
          <Space style={{ marginLeft: 20 }}>
            <div style={{ display: 'inline-block', width: 16, height: 16, borderRadius: '50%', backgroundColor: 'orange', marginRight: 8 }}></div>
            <Text>WORK IN PROGRESS</Text>
          </Space>
          <Space style={{ marginLeft: 20 }}>
            <div style={{ display: 'inline-block', width: 16, height: 16, borderRadius: '50%', backgroundColor: 'blue', marginRight: 8 }}></div>
            <Text>PENDING</Text>
          </Space>
        </div>
      </Card>
    </div>
  );
};

export default ProcurementStatusReport;
