import DocumentHead from "../../../../../Universal/CustomViews/Components/DocumentHead";
import PrintComponents from "react-print-components";
import React, { useState, useEffect, useMemo } from 'react';
import dayjs from "dayjs";
import moment from "moment";
import { PrinterOutlined, FilterOutlined, FileSearchOutlined, ShoppingCartOutlined, FileTextOutlined  } from "@ant-design/icons";
import { Table, Button, Row, Col, Input, Typography, DatePicker, Select, Card, Tabs, Statistic  } from "antd";
import { formatNumber  } from "../../../../../../Utils/functions";


const { RangePicker } = DatePicker;
const { TabPane } = Tabs;

const ProcurementReport = (props) => {
  const { pouchDatabase, databasePrefix } = props;
  
  // State for data
  const [loading, setLoading] = useState(true);
  const [requests, setRequests] = useState([]);
  const [quotations, setQuotations] = useState([]);
  const [purchaseOrders, setPurchaseOrders] = useState([]);
  const [suppliers, setSuppliers] = useState([]);
  const [users, setUsers] = useState([]);
  const [company, setCompany] = useState(null);
  
  // State for filters
  const [keyword, setKeyword] = useState('');
  const [selectedSupplier, setSelectedSupplier] = useState(null);
  const [dateRange, setDateRange] = useState([
    dayjs().startOf('month'),
    dayjs().endOf('month')
  ]);
  const [activeTab, setActiveTab] = useState('1');

  // Fetch data
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        const [
          requestsData,
          quotationsData,
          purchaseOrdersData,
          suppliersData,
          usersData,
          organizationData
        ] = await Promise.all([
          pouchDatabase("procurement_requests", databasePrefix).getAllData(),
          pouchDatabase("procurement_quotations", databasePrefix).getAllData(),
          pouchDatabase("purchase_orders", databasePrefix).getAllData(),
          pouchDatabase("suppliers", databasePrefix).getAllData(),
          pouchDatabase("users", databasePrefix).getAllData(),
          pouchDatabase("organizations", databasePrefix).getAllData()
        ]);

        setRequests(requestsData);
        setQuotations(quotationsData);
        setPurchaseOrders(purchaseOrdersData);
        setSuppliers(suppliersData);
        setUsers(usersData);
        
        if (organizationData && organizationData.length > 0) {
          try {
            const logo = await pouchDatabase("organizations", databasePrefix)
              .getAttachment(organizationData[0]._id, "logo");
            setCompany({ ...organizationData[0], orgLogo: logo });
          } catch (error) {
            setCompany(organizationData[0]);
          }
        }
      } catch (error) {
        console.error('Error fetching procurement data:', error);
      }
      setLoading(false);
    };

    fetchData();
  }, [pouchDatabase, databasePrefix]);

  // Filter data based on date range, supplier, and keyword
  const filteredRequests = useMemo(() => {
    if (!requests.length) return [];
    
    return requests.filter(request => {
      // Date filter
      const requestDate = moment(request.date_needed);
      const isInDateRange = !dateRange || !dateRange[0] || !dateRange[1] || 
        (requestDate.isSameOrAfter(dateRange[0], 'day') && 
         requestDate.isSameOrBefore(dateRange[1], 'day'));
      
      // Keyword filter
      const matchesKeyword = !keyword || 
        (request.title && request.title.toLowerCase().includes(keyword.toLowerCase())) ||
        (request.description && request.description.toLowerCase().includes(keyword.toLowerCase()));
      
      return isInDateRange && matchesKeyword;
    });
  }, [requests, dateRange, keyword]);

  const filteredQuotations = useMemo(() => {
    if (!quotations.length) return [];
    
    return quotations.filter(quotation => {
      // Date filter
      const quotationDate = moment(quotation.date);
      const isInDateRange = !dateRange || !dateRange[0] || !dateRange[1] || 
        (quotationDate.isSameOrAfter(dateRange[0], 'day') && 
         quotationDate.isSameOrBefore(dateRange[1], 'day'));
      
      // Supplier filter
      const matchesSupplier = !selectedSupplier || 
        (quotation.supplier && quotation.supplier.value === selectedSupplier);
      
      // Keyword filter
      const matchesKeyword = !keyword || 
        (quotation.title && quotation.title.toLowerCase().includes(keyword.toLowerCase())) ||
        (quotation.description && quotation.description.toLowerCase().includes(keyword.toLowerCase()));
      
      return isInDateRange && matchesSupplier && matchesKeyword;
    });
  }, [quotations, dateRange, selectedSupplier, keyword]);

  const filteredPurchaseOrders = useMemo(() => {
    if (!purchaseOrders.length) return [];
    
    return purchaseOrders.filter(order => {
      // Date filter
      const orderDate = moment(order.date);
      const isInDateRange = !dateRange || !dateRange[0] || !dateRange[1] || 
        (orderDate.isSameOrAfter(dateRange[0], 'day') && 
         orderDate.isSameOrBefore(dateRange[1], 'day'));
      
      // Supplier filter
      const matchesSupplier = !selectedSupplier || 
        (order.supplier && order.supplier.value === selectedSupplier);
      
      // Keyword filter
      const matchesKeyword = !keyword || 
        (order.title && order.title.toLowerCase().includes(keyword.toLowerCase())) ||
        (order.description && order.description.toLowerCase().includes(keyword.toLowerCase()));
      
      return isInDateRange && matchesSupplier && matchesKeyword;
    });
  }, [purchaseOrders, dateRange, selectedSupplier, keyword]);

  // Calculate statistics
  const statistics = useMemo(() => {
    const totalRequests = filteredRequests.length;
    const totalQuotations = filteredQuotations.length;
    const totalPurchaseOrders = filteredPurchaseOrders.length;
    
    // Calculate total value of purchase orders
    const totalValue = filteredPurchaseOrders.reduce((sum, order) => {
      const itemsTotal = order.items ? order.items.reduce((itemSum, item) => {
        return itemSum + (item.quantity * item.price);
      }, 0) : 0;
      return sum + itemsTotal;
    }, 0);
    
    return {
      totalRequests,
      totalQuotations,
      totalPurchaseOrders,
      totalValue
    };
  }, [filteredRequests, filteredQuotations, filteredPurchaseOrders]);

  // Table columns
  const requestColumns = [
    {
      title: 'Date Needed',
      dataIndex: 'date_needed',
      key: 'date_needed',
      render: (text) => moment(text).format('DD MMM YYYY')
    },
    {
      title: 'Title',
      dataIndex: 'title',
      key: 'title'
    },
    {
      title: 'Requested By',
      dataIndex: 'receiver',
      key: 'receiver',
      render: (receiver) => receiver?.label || '-'
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status) => status || 'Pending'
    },
    {
      title: 'Description',
      dataIndex: 'description',
      key: 'description'
    }
  ];

  const quotationColumns = [
    {
      title: 'Date',
      dataIndex: 'date',
      key: 'date',
      render: (text) => moment(text).format('DD MMM YYYY')
    },
    {
      title: 'Supplier',
      dataIndex: 'supplier',
      key: 'supplier',
      render: (supplier) => supplier?.label || '-'
    },
    {
      title: 'Request',
      dataIndex: 'request',
      key: 'request',
      render: (request) => request?.label || '-'
    },
    {
      title: 'Total Amount',
      dataIndex: 'total',
      key: 'total',
      render: (_, record) => {
        const total = record.items ? record.items.reduce((sum, item) => sum + (item.quantity * item.price), 0) : 0;
        return formatNumber(total);
      }
    }
  ];

  const purchaseOrderColumns = [
    {
      title: 'Date',
      dataIndex: 'date',
      key: 'date',
      render: (text) => moment(text).format('DD MMM YYYY')
    },
    {
      title: 'Supplier',
      dataIndex: 'supplier',
      key: 'supplier',
      render: (supplier) => supplier?.label || '-'
    },
    {
      title: 'Request',
      dataIndex: 'request',
      key: 'request',
      render: (request) => request?.label || '-'
    },
    {
      title: 'Total Amount',
      dataIndex: 'total',
      key: 'total',
      render: (_, record) => {
        const total = record.items ? record.items.reduce((sum, item) => sum + (item.quantity * item.price), 0) : 0;
        return formatNumber(total);
      }
    }
  ];

  // Filter components
  const FilterControls = () => (
    <Row gutter={[16, 16]}>
      <Col xs={24} sm={8}>
        <Input.Search
          placeholder="Search..."
          onChange={(e) => setKeyword(e.target.value)}
          style={{ width: '100%' }}
          allowClear
        />
      </Col>
      <Col xs={24} sm={8}>
        <Select
          placeholder="Filter by Supplier"
          style={{ width: '100%' }}
          allowClear
          options={suppliers.map(supplier => ({
            value: supplier._id,
            label: supplier.name
          }))}
          onChange={setSelectedSupplier}
          value={selectedSupplier}
        />
      </Col>
      <Col xs={24} sm={8}>
        <RangePicker
          value={dateRange}
          onChange={setDateRange}
          style={{ width: '100%' }}
        />
      </Col>
    </Row>
  );

  // Printable content
  const PrintableContent = () => (
    <div style={{ margin: 20 }}>
      {company && <DocumentHead company={company} />}
      <Typography.Title level={3}>Procurement Report</Typography.Title>
      {dateRange && dateRange[0] && (
        <Typography.Text>
          Period: {dateRange[0].format('DD/MM/YYYY')} - {dateRange[1].format('DD/MM/YYYY')}
        </Typography.Text>
      )}
      
      <Row gutter={[16, 16]} style={{ marginTop: 16, marginBottom: 16 }}>
        <Col span={6}>
          <Card>
            <Statistic title="Total Requests" value={statistics.totalRequests} />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic title="Total Quotations" value={statistics.totalQuotations} />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic title="Total Purchase Orders" value={statistics.totalPurchaseOrders} />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic 
              title="Total Value" 
              value={statistics.totalValue} 
              formatter={(value) => formatNumber(value)}
            />
          </Card>
        </Col>
      </Row>
      
      <Tabs activeKey={activeTab}>
        <TabPane tab="Requests" key="1">
          <Table 
            columns={requestColumns} 
            dataSource={filteredRequests}
            pagination={false}
            rowKey="_id"
          />
        </TabPane>
        <TabPane tab="Quotations" key="2">
          <Table 
            columns={quotationColumns} 
            dataSource={filteredQuotations}
            pagination={false}
            rowKey="_id"
          />
        </TabPane>
        <TabPane tab="Purchase Orders" key="3">
          <Table 
            columns={purchaseOrderColumns} 
            dataSource={filteredPurchaseOrders}
            pagination={false}
            rowKey="_id"
          />
        </TabPane>
      </Tabs>
    </div>
  );

  return (
    <div>
      <div style={{ marginBottom: 16 }}>
        <Row gutter={[16, 16]}>
          <Col span={24}>
            <Card title={<span><FilterOutlined /> Filters</span>}>
              <FilterControls />
            </Card>
          </Col>
        </Row>
      </div>
      
      <Row gutter={[16, 16]} style={{ marginBottom: 16 }}>
        <Col span={6}>
          <Card>
            <Statistic 
              title="Total Requests" 
              value={statistics.totalRequests} 
              prefix={<FileSearchOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic 
              title="Total Quotations" 
              value={statistics.totalQuotations}
              prefix={<FileTextOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic 
              title="Total Purchase Orders" 
              value={statistics.totalPurchaseOrders}
              prefix={<ShoppingCartOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic 
              title="Total Value" 
              value={statistics.totalValue}
              formatter={(value) => formatNumber(value)}
            />
          </Card>
        </Col>
      </Row>
      
      <Card 
        title="Procurement Report" 
        extra={
          <PrintComponents
            trigger={
              <Button icon={<PrinterOutlined />} type="primary">
                Print Report
              </Button>
            }
          >
            <PrintableContent />
          </PrintComponents>
        }
      >
        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane tab="Requests" key="1">
            <Table 
              columns={requestColumns} 
              dataSource={filteredRequests}
              rowKey="_id"
              loading={loading}
            />
          </TabPane>
          <TabPane tab="Quotations" key="2">
            <Table 
              columns={quotationColumns} 
              dataSource={filteredQuotations}
              rowKey="_id"
              loading={loading}
            />
          </TabPane>
          <TabPane tab="Purchase Orders" key="3">
            <Table 
              columns={purchaseOrderColumns} 
              dataSource={filteredPurchaseOrders}
              rowKey="_id"
              loading={loading}
            />
          </TabPane>
        </Tabs>
      </Card>
    </div>
  );
};

export default ProcurementReport;
