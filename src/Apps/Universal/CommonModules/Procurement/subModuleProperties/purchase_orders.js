import PurchaseOrder from "./CustomViews/PurchaseOrder";
import { BetaSchemaForm, TableDropdown  } from "@ant-design/pro-components";
import { message  } from "antd";
import { refreshRecordData  } from "../../../../../Utils/RecordRefreshUtility";
import { useRef  } from "react";


export default {
    CustomView:(data)=><PurchaseOrder {...data}/>,
    MoreActions: (props)=>{
        const currentUser = JSON.parse(localStorage.getItem("LOCAL_STORAGE_USER"));

                const APP_USER_PERMISSIONS = JSON.parse(
                  localStorage.getItem("APP_USER_PERMISSIONS")
                );

                const action = useRef();

                const {
                  pouchDatabase,
                  collection,
                  databasePrefix,
                  record,
                  CRUD_USER,
                  singular,
                  modules,
                  updateOptimisticRecord,
                } = props;

                // Use the utility function for refreshing record data
                const refreshRecord = async (updatedRecord) => {
                  return await refreshRecordData(
                    updatedRecord,
                    updateOptimisticRecord,
                    pouchDatabase,
                    collection,
                    databasePrefix
                  );
                };

                //log modules


                return (
                    <TableDropdown
                      key="actionGroup"
                      menus={[
                        {
                            key:"Generate Requisition",
                            name:(
                                <BetaSchemaForm
                                formRef={action}
                        submitter={{
                          searchConfig: {
                            resetText: "Close",
                            submitText: "Save",
                          },
                        }}
                        modalProps={{ centered: true }}
                                grid={true}
                                trigger={
                                  <a key="button" type="primary">
                                    <i className="fa fa-plus" /> Generate Requisition
                                  </a>
                                }
                                title={"Generate Requisition"}
                        destroyOnHidden={true}
                        layoutType="ModalForm"
                        onFinish={async (values) => {


                            await pouchDatabase(
                              modules.requisitions.collection,
                              databasePrefix
                            ).saveDocument({ ...values,request:record.request} , CRUD_USER);

                            // Refresh purchase order record to show updated status
                            await refreshRecord(record);

                            message.success(`Requisition Generated.`);

                          return true;
                        }}
                                columns={modules?.requisitions?.columns || []}
                                initialValues={{
                                    purpose: "Purchase of "+record.items.map(i=>i.item).join(', '),
                                  supplier: record.supplier,
                                  amount:record.items.reduce((pv,cv)=>pv+(cv.price*cv.quantity),0)
                                //   receiver:record.receiver
                                }
                                }
                              />
                            )
                        }
                      ]}
                      />)
    }
}