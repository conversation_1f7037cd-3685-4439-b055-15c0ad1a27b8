import React, { useEffect, useState } from 'react'
import moment from "moment";
import { Table, Card, Row, Col, Statistic, Tag, Button, Space, Alert, Tooltip, Progress, Divider, Typography, Select, Switch, Badge, Dropdown, message  } from "antd";
import { TrophyOutlined, DollarOutlined, PercentageOutlined, ClockCircleOutlined, CheckCircleOutlined, ExclamationCircleOutlined, InfoCircleOutlined, StarOutlined, ThunderboltOutlined, FileTextOutlined, PrinterOutlined, DownloadOutlined, FileExcelOutlined, MoreOutlined  } from "@ant-design/icons";
import { formatMoney, numberFormat  } from "../../../../../../Utils/functions";


const { Column, ColumnGroup } = Table;
const { Title, Text } = Typography;

const QuotComparisonView = (props) => {
    const [data, setData] = useState([]);
    const [quotations, setQuotations] = useState([]);
    const [comparisonStats, setComparisonStats] = useState({});
    const [showPercentages, setShowPercentages] = useState(false);
    const [highlightBest, setHighlightBest] = useState(true);
    const [sortBy, setSortBy] = useState('total'); // 'total', 'supplier', 'date'
    const [loading, setLoading] = useState(true);

    useEffect(() => {
        const fetchAndProcessData = async () => {
            try {
                setLoading(true);
                const { pouchDatabase, databasePrefix } = props;

                // Use quotations from props if available, otherwise fetch
                let quotationData = props.quotations;
                if (!quotationData) {
                    const procurementQuotationDB = pouchDatabase('procurement_quotations', databasePrefix);
                    const allQuotations = await procurementQuotationDB.getAllData();
                    quotationData = allQuotations.filter(q => q.request?.value === props.data._id);
                }

                if (quotationData.length === 0) {
                    setLoading(false);
                    return;
                }

                // Sort quotations based on selected criteria
                const sortedQuotations = [...quotationData].sort((a, b) => {
                    switch (sortBy) {
                        case 'total':
                            const totalA = a.items?.reduce((sum, item) => sum + (item.price * item.quantity), 0) || 0;
                            const totalB = b.items?.reduce((sum, item) => sum + (item.price * item.quantity), 0) || 0;
                            return totalA - totalB;
                        case 'supplier':
                            return (a.supplier?.label || '').localeCompare(b.supplier?.label || '');
                        case 'date':
                            return moment(a.date).diff(moment(b.date));
                        default:
                            return 0;
                    }
                });

                setQuotations(sortedQuotations);

                // Create comprehensive item list (union of all items from all quotations)
                const allItems = new Map();

                // Add items from original request
                props.data.items?.forEach(item => {
                    allItems.set(item.item, {
                        item: item.item,
                        requestedQuantity: item.quantity,
                        description: item.description,
                        justification: item.justification
                    });
                });

                // Add items from quotations that might not be in original request
                sortedQuotations.forEach(quot => {
                    quot.items?.forEach(item => {
                        if (!allItems.has(item.item)) {
                            allItems.set(item.item, {
                                item: item.item,
                                requestedQuantity: 0,
                                description: 'Added by supplier',
                                isAdditional: true
                            });
                        }
                    });
                });

                // Process data for comparison table
                const processedData = Array.from(allItems.values()).map(baseItem => {
                    const rowData = {
                        key: baseItem.item,
                        item: baseItem.item,
                        requestedQuantity: baseItem.requestedQuantity,
                        description: baseItem.description,
                        justification: baseItem.justification,
                        isAdditional: baseItem.isAdditional
                    };

                    // Add supplier data
                    sortedQuotations.forEach(quot => {
                        const supplierItem = quot.items?.find(i => i.item === baseItem.item);
                        const supplierId = quot.supplier.value;

                        rowData[`supplier_${supplierId}`] = supplierItem?.price || null;
                        rowData[`supplier_qty_${supplierId}`] = supplierItem?.quantity || 0;
                        rowData[`supplier_total_${supplierId}`] = supplierItem ?
                            (supplierItem.price * supplierItem.quantity) : 0;
                        rowData[`supplier_info_${supplierId}`] = {
                            supplier: quot.supplier,
                            date: quot.date,
                            status: quot.status,
                            quotationId: quot._id
                        };
                    });

                    return rowData;
                });

                setData(processedData);

                // Calculate comparison statistics
                const stats = calculateComparisonStats(sortedQuotations, processedData);
                setComparisonStats(stats);

            } catch (error) {
                console.error('Error processing quotation data:', error);
            } finally {
                setLoading(false);
            }
        };

        fetchAndProcessData();
    }, [props.data._id, props.quotations, sortBy]);

    const calculateComparisonStats = (quotations, processedData) => {
        const supplierTotals = {};
        const supplierStats = {};

        quotations.forEach(quot => {
            const supplierId = quot.supplier.value;
            const supplierTotal = processedData.reduce((sum, item) => {
                return sum + (item[`supplier_total_${supplierId}`] || 0);
            }, 0);

            supplierTotals[supplierId] = supplierTotal;
            supplierStats[supplierId] = {
                total: supplierTotal,
                itemCount: quot.items?.length || 0,
                averageItemPrice: quot.items?.length ?
                    quot.items.reduce((sum, item) => sum + item.price, 0) / quot.items.length : 0,
                quotationDate: quot.date,
                status: quot.status,
                supplier: quot.supplier
            };
        });

        const totals = Object.values(supplierTotals);
        const bestPrice = Math.min(...totals);
        const worstPrice = Math.max(...totals);
        const averagePrice = totals.reduce((a, b) => a + b, 0) / totals.length;

        return {
            supplierTotals,
            supplierStats,
            bestPrice,
            worstPrice,
            averagePrice,
            potentialSavings: worstPrice - bestPrice,
            savingsPercentage: worstPrice > 0 ? ((worstPrice - bestPrice) / worstPrice * 100) : 0
        };
    };

    const getBestSupplierForItem = (item) => {
        const prices = quotations.map(q => {
            const supplierItem = q.items?.find(i => i.item === item.item);
            return {
                supplierId: q.supplier.value,
                price: supplierItem?.price || Infinity,
                total: supplierItem ? supplierItem.price * supplierItem.quantity : Infinity
            };
        }).filter(p => p.price !== Infinity);

        return prices.length > 0 ? prices.reduce((best, current) =>
            current.total < best.total ? current : best
        ) : null;
    };

    const getSupplierRanking = () => {
        return Object.entries(comparisonStats.supplierStats || {})
            .sort(([,a], [,b]) => a.total - b.total)
            .map(([supplierId, stats], index) => ({
                supplierId,
                ...stats,
                rank: index + 1,
                isWinner: index === 0
            }));
    };

    // Export Functions
    const generateCSVData = () => {
        const supplierRanking = getSupplierRanking();
        const csvData = [];

        // Header with request information
        csvData.push(['Quotation Comparison Report']);
        csvData.push(['Request ID:', props.data._id]);
        csvData.push(['Requested By:', props.data.receiver?.label || 'Unknown']);
        csvData.push(['Date Needed:', moment(props.data.date_needed).format('YYYY-MM-DD')]);
        csvData.push(['Report Generated:', moment().format('YYYY-MM-DD HH:mm:ss')]);
        csvData.push([]);

        // Summary statistics
        csvData.push(['SUMMARY STATISTICS']);
        csvData.push(['Total Quotations:', quotations.length]);
        csvData.push(['Best Quote:', numberFormat(comparisonStats.bestPrice)]);
        csvData.push(['Worst Quote:', numberFormat(comparisonStats.worstPrice)]);
        csvData.push(['Average Quote:', numberFormat(comparisonStats.averagePrice)]);
        csvData.push(['Potential Savings:', numberFormat(comparisonStats.potentialSavings)]);
        csvData.push(['Savings Percentage:', `${comparisonStats.savingsPercentage?.toFixed(2)}%`]);
        csvData.push([]);

        // Supplier ranking
        csvData.push(['SUPPLIER RANKING']);
        csvData.push(['Rank', 'Supplier', 'Total Amount', 'Items Count', 'Average Item Price', 'Quote Date', 'Status']);
        supplierRanking.forEach(supplier => {
            csvData.push([
                supplier.rank,
                supplier.supplier.label,
                numberFormat(supplier.total),
                supplier.itemCount,
                numberFormat(supplier.averageItemPrice),
                moment(supplier.quotationDate).format('YYYY-MM-DD'),
                supplier.status || 'pending'
            ]);
        });
        csvData.push([]);

        // Detailed comparison table
        csvData.push(['DETAILED ITEM COMPARISON']);
        const headerRow = ['Item', 'Requested Qty'];
        quotations.forEach(quot => {
            headerRow.push(`${quot.supplier.label} - Qty`);
            headerRow.push(`${quot.supplier.label} - Unit Price`);
            headerRow.push(`${quot.supplier.label} - Total`);
        });
        headerRow.push('Best Supplier');
        headerRow.push('Best Price');
        csvData.push(headerRow);

        data.forEach(item => {
            const row = [item.item, item.requestedQuantity || 0];
            let bestPrice = Infinity;
            let bestSupplier = '';

            quotations.forEach(quot => {
                const supplierId = quot.supplier.value;
                const quantity = item[`supplier_qty_${supplierId}`] || 0;
                const price = item[`supplier_${supplierId}`] || 0;
                const total = item[`supplier_total_${supplierId}`] || 0;

                row.push(quantity);
                row.push(price ? numberFormat(price) : 'Not quoted');
                row.push(total ? numberFormat(total) : 'Not quoted');

                if (total > 0 && total < bestPrice) {
                    bestPrice = total;
                    bestSupplier = quot.supplier.label;
                }
            });

            row.push(bestSupplier);
            row.push(bestPrice !== Infinity ? numberFormat(bestPrice) : 'N/A');
            csvData.push(row);
        });

        // Add totals row
        const totalsRow = ['TOTALS', ''];
        quotations.forEach(quot => {
            const supplierId = quot.supplier.value;
            const total = data.reduce((sum, item) => sum + (item[`supplier_total_${supplierId}`] || 0), 0);
            totalsRow.push(''); // Qty column
            totalsRow.push(''); // Unit price column
            totalsRow.push(numberFormat(total)); // Total column
        });
        totalsRow.push('Best Overall');
        totalsRow.push(numberFormat(comparisonStats.bestPrice));
        csvData.push(totalsRow);

        return csvData;
    };

    const downloadCSV = () => {
        try {
            const csvData = generateCSVData();
            const csvContent = csvData.map(row =>
                row.map(cell => `"${cell}"`).join(',')
            ).join('\n');

            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', `quotation-comparison-${props.data._id}-${moment().format('YYYY-MM-DD')}.csv`);
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            message.success('CSV file downloaded successfully!');
        } catch (error) {
            console.error('Error generating CSV:', error);
            message.error('Failed to generate CSV file');
        }
    };

    const handlePrint = () => {
        try {
            const supplierRanking = getSupplierRanking();

            // Try to open print window, with fallback for popup blockers
            let printWindow = null;
            try {
                printWindow = window.open('', '_blank', 'width=800,height=600,scrollbars=yes,resizable=yes');
            } catch (e) {
                console.warn('Popup blocked, using alternative print method');
            }

            // If popup is blocked, use inline printing
            if (!printWindow || printWindow.closed || typeof printWindow.closed === 'undefined') {
                handleInlinePrint();
                return;
            }

            const printContent = `
                <!DOCTYPE html>
                <html>
                <head>
                    <title>Quotation Comparison Report - ${props.data._id}</title>
                    <style>
                        body {
                            font-family: Arial, sans-serif;
                            margin: 20px;
                            color: #333;
                            line-height: 1.4;
                        }
                        .header {
                            text-align: center;
                            margin-bottom: 30px;
                            border-bottom: 2px solid #1890ff;
                            padding-bottom: 20px;
                        }
                        .header h1 {
                            color: #1890ff;
                            margin: 0;
                            font-size: 24px;
                        }
                        .header p {
                            margin: 5px 0;
                            color: #666;
                        }
                        .section {
                            margin: 30px 0;
                        }
                        .section h2 {
                            color: #1890ff;
                            border-bottom: 1px solid #d9d9d9;
                            padding-bottom: 10px;
                            font-size: 18px;
                        }
                        .stats-grid {
                            display: grid;
                            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                            gap: 15px;
                            margin: 20px 0;
                        }
                        .stat-card {
                            border: 1px solid #d9d9d9;
                            padding: 15px;
                            border-radius: 6px;
                            background: #fafafa;
                        }
                        .stat-label {
                            font-weight: bold;
                            color: #666;
                            font-size: 12px;
                            text-transform: uppercase;
                        }
                        .stat-value {
                            font-size: 18px;
                            font-weight: bold;
                            color: #1890ff;
                            margin-top: 5px;
                        }
                        .winner {
                            color: #52c41a !important;
                        }
                        table {
                            width: 100%;
                            border-collapse: collapse;
                            margin: 20px 0;
                            font-size: 12px;
                        }
                        th, td {
                            border: 1px solid #d9d9d9;
                            padding: 8px;
                            text-align: left;
                        }
                        th {
                            background-color: #f5f5f5;
                            font-weight: bold;
                            color: #333;
                        }
                        .best-price {
                            background-color: #f6ffed;
                            font-weight: bold;
                            color: #52c41a;
                        }
                        .supplier-header {
                            background-color: #e6f7ff;
                            text-align: center;
                            font-weight: bold;
                        }
                        .totals-row {
                            background-color: #fafafa;
                            font-weight: bold;
                        }
                        .ranking-table td:first-child {
                            text-align: center;
                            font-weight: bold;
                        }
                        .rank-1 {
                            background-color: #f6ffed;
                        }
                        .footer {
                            margin-top: 40px;
                            text-align: center;
                            color: #666;
                            font-size: 12px;
                            border-top: 1px solid #d9d9d9;
                            padding-top: 20px;
                        }
                        @media print {
                            body { margin: 0; }
                            .section { page-break-inside: avoid; }
                        }
                    </style>
                </head>
                <body>
                    <div class="header">
                        <h1>Quotation Comparison Report</h1>
                        <p><strong>Request ID:</strong> ${props.data._id}</p>
                        <p><strong>Requested By:</strong> ${props.data.receiver?.label || 'Unknown'}</p>
                        <p><strong>Date Needed:</strong> ${moment(props.data.date_needed).format('MMMM DD, YYYY')}</p>
                        <p><strong>Report Generated:</strong> ${moment().format('MMMM DD, YYYY [at] HH:mm')}</p>
                    </div>

                    <div class="section">
                        <h2>Executive Summary</h2>
                        <div class="stats-grid">
                            <div class="stat-card">
                                <div class="stat-label">Total Quotations</div>
                                <div class="stat-value">${quotations.length}</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-label">Best Quote</div>
                                <div class="stat-value winner">${numberFormat(comparisonStats.bestPrice)}</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-label">Potential Savings</div>
                                <div class="stat-value winner">${numberFormat(comparisonStats.potentialSavings)}</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-label">Savings Percentage</div>
                                <div class="stat-value winner">${comparisonStats.savingsPercentage?.toFixed(1)}%</div>
                            </div>
                        </div>
                    </div>

                    <div class="section">
                        <h2>Supplier Ranking</h2>
                        <table class="ranking-table">
                            <thead>
                                <tr>
                                    <th>Rank</th>
                                    <th>Supplier</th>
                                    <th>Total Amount</th>
                                    <th>Items</th>
                                    <th>Avg. Item Price</th>
                                    <th>Quote Date</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${supplierRanking.map(supplier => `
                                    <tr class="${supplier.rank === 1 ? 'rank-1' : ''}">
                                        <td>${supplier.rank}${supplier.rank === 1 ? ' 🏆' : ''}</td>
                                        <td><strong>${supplier.supplier.label}</strong></td>
                                        <td class="${supplier.rank === 1 ? 'winner' : ''}">${numberFormat(supplier.total)}</td>
                                        <td>${supplier.itemCount}</td>
                                        <td>${numberFormat(supplier.averageItemPrice)}</td>
                                        <td>${moment(supplier.quotationDate).format('MMM DD, YYYY')}</td>
                                        <td>${supplier.status || 'pending'}</td>
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>
                    </div>

                    <div class="section">
                        <h2>Detailed Item Comparison</h2>
                        <table>
                            <thead>
                                <tr>
                                    <th rowspan="2">Item</th>
                                    <th rowspan="2">Req. Qty</th>
                                    ${quotations.map(quot => `
                                        <th colspan="3" class="supplier-header">${quot.supplier.label}</th>
                                    `).join('')}
                                </tr>
                                <tr>
                                    ${quotations.map(() => `
                                        <th>Qty</th>
                                        <th>Unit Price</th>
                                        <th>Total</th>
                                    `).join('')}
                                </tr>
                            </thead>
                            <tbody>
                                ${data.map(item => {
                                    const bestForItem = getBestSupplierForItem(item);
                                    return `
                                        <tr>
                                            <td><strong>${item.item}</strong>${item.isAdditional ? ' <em>(Additional)</em>' : ''}</td>
                                            <td>${item.requestedQuantity || 0}</td>
                                            ${quotations.map(quot => {
                                                const supplierId = quot.supplier.value;
                                                const quantity = item[`supplier_qty_${supplierId}`] || 0;
                                                const price = item[`supplier_${supplierId}`] || 0;
                                                const total = item[`supplier_total_${supplierId}`] || 0;
                                                const isBest = bestForItem?.supplierId === supplierId;

                                                return `
                                                    <td>${quantity || 'N/A'}</td>
                                                    <td>${price ? numberFormat(price) : 'Not quoted'}</td>
                                                    <td class="${isBest ? 'best-price' : ''}">${total ? numberFormat(total) : 'Not quoted'}${isBest ? ' ⭐' : ''}</td>
                                                `;
                                            }).join('')}
                                        </tr>
                                    `;
                                }).join('')}
                                <tr class="totals-row">
                                    <td colspan="2"><strong>TOTALS</strong></td>
                                    ${quotations.map(quot => {
                                        const supplierId = quot.supplier.value;
                                        const total = data.reduce((sum, item) => sum + (item[`supplier_total_${supplierId}`] || 0), 0);
                                        const isBest = total === comparisonStats.bestPrice;
                                        return `
                                            <td></td>
                                            <td></td>
                                            <td class="${isBest ? 'best-price' : ''}">${numberFormat(total)}${isBest ? ' 🏆' : ''}</td>
                                        `;
                                    }).join('')}
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    ${quotations.length > 1 ? `
                        <div class="section">
                            <h2>Recommendation</h2>
                            <p><strong>Recommended Supplier:</strong> ${supplierRanking[0]?.supplier?.label}</p>
                            <p><strong>Total Cost:</strong> ${numberFormat(comparisonStats.bestPrice)}</p>
                            <p><strong>Savings:</strong> ${numberFormat(comparisonStats.potentialSavings)} (${comparisonStats.savingsPercentage?.toFixed(1)}% compared to highest quote)</p>
                            <p><strong>Justification:</strong> This supplier offers the lowest total cost while meeting all requirements.</p>
                        </div>
                    ` : ''}

                    <div class="footer">
                        <p>This report was generated automatically by the Procurement Management System</p>
                        <p>Generated on ${moment().format('MMMM DD, YYYY [at] HH:mm:ss')}</p>
                    </div>
                </body>
                </html>
            `;

            printWindow.document.write(printContent);
            printWindow.document.close();

            // Wait for content to load then print
            printWindow.onload = () => {
                setTimeout(() => {
                    printWindow.print();
                    printWindow.close();
                }, 500);
            };

            message.success('Print dialog opened successfully!');
        } catch (error) {
            console.error('Error generating print view:', error);
            message.error('Failed to generate print view. Please try the inline print option.');
            // Fallback to inline print
            handleInlinePrint();
        }
    };

    const handleInlinePrint = () => {
        try {
            const supplierRanking = getSupplierRanking();

            // Create a temporary div for printing
            const printDiv = document.createElement('div');
            printDiv.innerHTML = `
                <div id="print-content" style="display: none;">
                    <style>
                        @media print {
                            body * { visibility: hidden; }
                            #print-content, #print-content * { visibility: visible; }
                            #print-content { position: absolute; left: 0; top: 0; width: 100%; }
                            .no-print { display: none !important; }
                        }
                        .print-header {
                            text-align: center;
                            margin-bottom: 30px;
                            border-bottom: 2px solid #1890ff;
                            padding-bottom: 20px;
                        }
                        .print-header h1 {
                            color: #1890ff;
                            margin: 0;
                            font-size: 24px;
                        }
                        .print-section {
                            margin: 30px 0;
                            page-break-inside: avoid;
                        }
                        .print-section h2 {
                            color: #1890ff;
                            border-bottom: 1px solid #d9d9d9;
                            padding-bottom: 10px;
                        }
                        .print-stats {
                            display: grid;
                            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                            gap: 15px;
                            margin: 20px 0;
                        }
                        .print-stat-card {
                            border: 1px solid #d9d9d9;
                            padding: 15px;
                            border-radius: 6px;
                        }
                        .print-table {
                            width: 100%;
                            border-collapse: collapse;
                            margin: 20px 0;
                            font-size: 12px;
                        }
                        .print-table th, .print-table td {
                            border: 1px solid #d9d9d9;
                            padding: 8px;
                            text-align: left;
                        }
                        .print-table th {
                            background-color: #f5f5f5;
                            font-weight: bold;
                        }
                        .print-best {
                            background-color: #f6ffed;
                            font-weight: bold;
                            color: #52c41a;
                        }
                        .print-winner {
                            color: #52c41a;
                            font-weight: bold;
                        }
                    </style>

                    <div class="print-header">
                        <h1>Quotation Comparison Report</h1>
                        <p><strong>Request ID:</strong> ${props.data._id}</p>
                        <p><strong>Requested By:</strong> ${props.data.receiver?.label || 'Unknown'}</p>
                        <p><strong>Date Needed:</strong> ${moment(props.data.date_needed).format('MMMM DD, YYYY')}</p>
                        <p><strong>Report Generated:</strong> ${moment().format('MMMM DD, YYYY [at] HH:mm')}</p>
                    </div>

                    <div class="print-section">
                        <h2>Executive Summary</h2>
                        <div class="print-stats">
                            <div class="print-stat-card">
                                <div style="font-weight: bold; color: #666; font-size: 12px;">TOTAL QUOTATIONS</div>
                                <div style="font-size: 18px; font-weight: bold; color: #1890ff;">${quotations.length}</div>
                            </div>
                            <div class="print-stat-card">
                                <div style="font-weight: bold; color: #666; font-size: 12px;">BEST QUOTE</div>
                                <div style="font-size: 18px; font-weight: bold;" class="print-winner">${numberFormat(comparisonStats.bestPrice)}</div>
                            </div>
                            <div class="print-stat-card">
                                <div style="font-weight: bold; color: #666; font-size: 12px;">POTENTIAL SAVINGS</div>
                                <div style="font-size: 18px; font-weight: bold;" class="print-winner">${numberFormat(comparisonStats.potentialSavings)}</div>
                            </div>
                            <div class="print-stat-card">
                                <div style="font-weight: bold; color: #666; font-size: 12px;">SAVINGS PERCENTAGE</div>
                                <div style="font-size: 18px; font-weight: bold;" class="print-winner">${comparisonStats.savingsPercentage?.toFixed(1)}%</div>
                            </div>
                        </div>
                    </div>

                    <div class="print-section">
                        <h2>Supplier Ranking</h2>
                        <table class="print-table">
                            <thead>
                                <tr>
                                    <th>Rank</th>
                                    <th>Supplier</th>
                                    <th>Total Amount</th>
                                    <th>Items</th>
                                    <th>Quote Date</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${supplierRanking.map(supplier => `
                                    <tr ${supplier.rank === 1 ? 'style="background-color: #f6ffed;"' : ''}>
                                        <td style="text-align: center; font-weight: bold;">${supplier.rank}${supplier.rank === 1 ? ' 🏆' : ''}</td>
                                        <td><strong>${supplier.supplier.label}</strong></td>
                                        <td class="${supplier.rank === 1 ? 'print-winner' : ''}">${numberFormat(supplier.total)}</td>
                                        <td>${supplier.itemCount}</td>
                                        <td>${moment(supplier.quotationDate).format('MMM DD, YYYY')}</td>
                                        <td>${supplier.status || 'pending'}</td>
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>
                    </div>

                    <div class="print-section">
                        <h2>Detailed Item Comparison</h2>
                        <table class="print-table">
                            <thead>
                                <tr>
                                    <th rowspan="2">Item</th>
                                    <th rowspan="2">Req. Qty</th>
                                    ${quotations.map(quot => `
                                        <th colspan="3" style="background-color: #e6f7ff; text-align: center;">${quot.supplier.label}</th>
                                    `).join('')}
                                </tr>
                                <tr>
                                    ${quotations.map(() => `
                                        <th>Qty</th>
                                        <th>Unit Price</th>
                                        <th>Total</th>
                                    `).join('')}
                                </tr>
                            </thead>
                            <tbody>
                                ${data.map(item => {
                                    const bestForItem = getBestSupplierForItem(item);
                                    return `
                                        <tr>
                                            <td><strong>${item.item}</strong>${item.isAdditional ? ' <em>(Additional)</em>' : ''}</td>
                                            <td>${item.requestedQuantity || 0}</td>
                                            ${quotations.map(quot => {
                                                const supplierId = quot.supplier.value;
                                                const quantity = item[`supplier_qty_${supplierId}`] || 0;
                                                const price = item[`supplier_${supplierId}`] || 0;
                                                const total = item[`supplier_total_${supplierId}`] || 0;
                                                const isBest = bestForItem?.supplierId === supplierId;

                                                return `
                                                    <td>${quantity || 'N/A'}</td>
                                                    <td>${price ? numberFormat(price) : 'Not quoted'}</td>
                                                    <td class="${isBest ? 'print-best' : ''}">${total ? numberFormat(total) : 'Not quoted'}${isBest ? ' ⭐' : ''}</td>
                                                `;
                                            }).join('')}
                                        </tr>
                                    `;
                                }).join('')}
                                <tr style="background-color: #fafafa; font-weight: bold;">
                                    <td colspan="2"><strong>TOTALS</strong></td>
                                    ${quotations.map(quot => {
                                        const supplierId = quot.supplier.value;
                                        const total = data.reduce((sum, item) => sum + (item[`supplier_total_${supplierId}`] || 0), 0);
                                        const isBest = total === comparisonStats.bestPrice;
                                        return `
                                            <td></td>
                                            <td></td>
                                            <td class="${isBest ? 'print-best' : ''}">${numberFormat(total)}${isBest ? ' 🏆' : ''}</td>
                                        `;
                                    }).join('')}
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    ${quotations.length > 1 ? `
                        <div class="print-section">
                            <h2>Recommendation</h2>
                            <p><strong>Recommended Supplier:</strong> ${supplierRanking[0]?.supplier?.label}</p>
                            <p><strong>Total Cost:</strong> ${numberFormat(comparisonStats.bestPrice)}</p>
                            <p><strong>Savings:</strong> ${numberFormat(comparisonStats.potentialSavings)} (${comparisonStats.savingsPercentage?.toFixed(1)}% compared to highest quote)</p>
                            <p><strong>Justification:</strong> This supplier offers the lowest total cost while meeting all requirements.</p>
                        </div>
                    ` : ''}

                    <div style="margin-top: 40px; text-align: center; color: #666; font-size: 12px; border-top: 1px solid #d9d9d9; padding-top: 20px;">
                        <p>This report was generated automatically by the Procurement Management System</p>
                        <p>Generated on ${moment().format('MMMM DD, YYYY [at] HH:mm:ss')}</p>
                    </div>
                </div>
            `;

            // Add to document
            document.body.appendChild(printDiv);

            // Print
            window.print();

            // Clean up
            setTimeout(() => {
                document.body.removeChild(printDiv);
            }, 1000);

            message.success('Print dialog opened successfully!');
        } catch (error) {
            console.error('Error with inline print:', error);
            message.error('Print functionality is not available in this browser');
        }
    };

    if (loading) {
        return <Card loading={true} style={{ minHeight: 400 }} />;
    }

    if (quotations.length === 0) {
        return (
            <Card>
                <div style={{ textAlign: 'center', padding: '60px 20px' }}>
                    <ExclamationCircleOutlined style={{ fontSize: 48, color: '#faad14', marginBottom: 16 }} />
                    <Title level={3}>No Quotations Available</Title>
                    <Text type="secondary">
                        No supplier quotations have been submitted for this request yet.
                        <br />
                        Add quotations to enable comparison analysis.
                    </Text>
                </div>
            </Card>
        );
    }

    const supplierRanking = getSupplierRanking();

    return (
        <div>
            {/* Comparison Summary Cards */}
            <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
                <Col xs={24} sm={12} lg={6}>
                    <Card size="small">
                        <Statistic
                            title="Best Quote"
                            value={comparisonStats.bestPrice}
                            prefix={<TrophyOutlined style={{ color: '#52c41a' }} />}
                            formatter={(value) => numberFormat(value)}
                            valueStyle={{ color: '#52c41a' }}
                        />
                        <div style={{ marginTop: 8 }}>
                            <Tag color="success" icon={<CheckCircleOutlined />}>
                                {supplierRanking[0]?.supplier?.label}
                            </Tag>
                        </div>
                    </Card>
                </Col>

                <Col xs={24} sm={12} lg={6}>
                    <Card size="small">
                        <Statistic
                            title="Potential Savings"
                            value={comparisonStats.potentialSavings}
                            prefix={<DollarOutlined style={{ color: '#1890ff' }} />}
                            formatter={(value) => numberFormat(value)}
                            valueStyle={{ color: '#1890ff' }}
                        />
                        <div style={{ marginTop: 8 }}>
                            <Tag color="blue">
                                {comparisonStats.savingsPercentage?.toFixed(1)}% savings
                            </Tag>
                        </div>
                    </Card>
                </Col>

                <Col xs={24} sm={12} lg={6}>
                    <Card size="small">
                        <Statistic
                            title="Average Quote"
                            value={comparisonStats.averagePrice}
                            prefix={<PercentageOutlined />}
                            formatter={(value) => numberFormat(value)}
                        />
                    </Card>
                </Col>

                <Col xs={24} sm={12} lg={6}>
                    <Card size="small">
                        <Statistic
                            title="Quotations"
                            value={quotations.length}
                            prefix={<FileTextOutlined />}
                            suffix="received"
                        />
                    </Card>
                </Col>
            </Row>

            {/* Supplier Ranking */}
            <Card
                title={
                    <Space>
                        <StarOutlined />
                        Supplier Ranking
                    </Space>
                }
                size="small"
                style={{ marginBottom: 24 }}
            >
                <Row gutter={[16, 8]}>
                    {supplierRanking.map((supplier, index) => (
                        <Col xs={24} sm={12} lg={8} key={supplier.supplierId}>
                            <Card
                                size="small"
                                style={{
                                    border: supplier.isWinner ? '2px solid #52c41a' : '1px solid #d9d9d9',
                                    backgroundColor: supplier.isWinner ? '#f6ffed' : 'white'
                                }}
                            >
                                <Space direction="vertical" size="small" style={{ width: '100%' }}>
                                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                                        <Text strong>{supplier.supplier.label}</Text>
                                        <Badge
                                            count={`#${supplier.rank}`}
                                            style={{
                                                backgroundColor: supplier.isWinner ? '#52c41a' : '#1890ff'
                                            }}
                                        />
                                    </div>
                                    <div>
                                        <Text type="secondary">Total: </Text>
                                        <Text strong style={{ color: supplier.isWinner ? '#52c41a' : 'inherit' }}>
                                            {numberFormat(supplier.total)}
                                        </Text>
                                    </div>
                                    <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                                        <span>
                                            <Text type="secondary">Items: </Text>
                                            <Text>{supplier.itemCount}</Text>
                                        </span>
                                        <span>
                                            <Text type="secondary">Date: </Text>
                                            <Text>{moment(supplier.quotationDate).format('MMM DD')}</Text>
                                        </span>
                                    </div>
                                    {supplier.isWinner && (
                                        <Tag color="success" icon={<TrophyOutlined />} style={{ margin: 0 }}>
                                            Best Quote
                                        </Tag>
                                    )}
                                </Space>
                            </Card>
                        </Col>
                    ))}
                </Row>
            </Card>

            {/* Controls */}
            <Card size="small" style={{ marginBottom: 16 }}>
                <Row gutter={[16, 8]} align="middle" justify="space-between">
                    <Col xs={24} lg={12}>
                        <Space wrap>
                            <Text strong>View Options:</Text>
                            <Switch
                                checked={showPercentages}
                                onChange={setShowPercentages}
                                checkedChildren="Show %"
                                unCheckedChildren="Hide %"
                            />
                            <Switch
                                checked={highlightBest}
                                onChange={setHighlightBest}
                                checkedChildren="Highlight Best"
                                unCheckedChildren="No Highlight"
                            />
                            <Space>
                                <Text>Sort by:</Text>
                                <Select
                                    value={sortBy}
                                    onChange={setSortBy}
                                    style={{ width: 120 }}
                                    options={[
                                        { value: 'total', label: 'Total Price' },
                                        { value: 'supplier', label: 'Supplier' },
                                        { value: 'date', label: 'Date' }
                                    ]}
                                />
                            </Space>
                        </Space>
                    </Col>
                    <Col xs={24} lg={12}>
                        <div style={{ display: 'flex', justifyContent: 'flex-end', gap: 8 }}>
                            <Button
                                type="default"
                                icon={<PrinterOutlined />}
                                onClick={handlePrint}
                                size="small"
                            >
                                Print Report
                            </Button>
                            <Button
                                type="default"
                                icon={<FileExcelOutlined />}
                                onClick={downloadCSV}
                                size="small"
                            >
                                Export CSV
                            </Button>
                            <Dropdown
                                menu={{
                                    items: [
                                        {
                                            key: 'print',
                                            label: 'Print Report',
                                            icon: <PrinterOutlined />,
                                            onClick: handlePrint
                                        },
                                        {
                                            key: 'csv',
                                            label: 'Export to CSV',
                                            icon: <FileExcelOutlined />,
                                            onClick: downloadCSV
                                        },
                                        {
                                            type: 'divider'
                                        },
                                        {
                                            key: 'print-summary',
                                            label: 'Print Summary Only',
                                            icon: <PrinterOutlined />,
                                            onClick: () => {
                                                // Create a simplified print version with fallback
                                                const simplifiedPrint = () => {
                                                    try {
                                                        const supplierRanking = getSupplierRanking();
                                                        let printWindow = null;

                                                        try {
                                                            printWindow = window.open('', '_blank', 'width=800,height=600');
                                                        } catch (e) {
                                                            console.warn('Popup blocked, using inline print for summary');
                                                        }

                                                        if (!printWindow || printWindow.closed || typeof printWindow.closed === 'undefined') {
                                                            // Fallback to inline print for summary
                                                            const summaryDiv = document.createElement('div');
                                                            summaryDiv.innerHTML = `
                                                                <div id="summary-print" style="display: none;">
                                                                    <style>
                                                                        @media print {
                                                                            body * { visibility: hidden; }
                                                                            #summary-print, #summary-print * { visibility: visible; }
                                                                            #summary-print { position: absolute; left: 0; top: 0; width: 100%; }
                                                                        }
                                                                        .summary-header { text-align: center; margin-bottom: 30px; }
                                                                        .summary-stats { display: grid; grid-template-columns: repeat(2, 1fr); gap: 20px; margin: 20px 0; }
                                                                        .summary-card { border: 1px solid #ddd; padding: 15px; }
                                                                        .summary-table { width: 100%; border-collapse: collapse; }
                                                                        .summary-table th, .summary-table td { border: 1px solid #ddd; padding: 8px; }
                                                                        .summary-winner { background-color: #f6ffed; font-weight: bold; }
                                                                    </style>
                                                                    <div class="summary-header">
                                                                        <h1>Quotation Comparison Summary</h1>
                                                                        <p>Request ID: ${props.data._id} | Generated: ${moment().format('YYYY-MM-DD HH:mm')}</p>
                                                                    </div>
                                                                    <div class="summary-stats">
                                                                        <div class="summary-card">
                                                                            <h3>Best Quote</h3>
                                                                            <p style="font-size: 24px; color: #52c41a; font-weight: bold;">${numberFormat(comparisonStats.bestPrice)}</p>
                                                                        </div>
                                                                        <div class="summary-card">
                                                                            <h3>Potential Savings</h3>
                                                                            <p style="font-size: 24px; color: #1890ff; font-weight: bold;">${numberFormat(comparisonStats.potentialSavings)} (${comparisonStats.savingsPercentage?.toFixed(1)}%)</p>
                                                                        </div>
                                                                    </div>
                                                                    <h2>Supplier Ranking</h2>
                                                                    <table class="summary-table">
                                                                        <thead>
                                                                            <tr><th>Rank</th><th>Supplier</th><th>Total</th><th>Status</th></tr>
                                                                        </thead>
                                                                        <tbody>
                                                                            ${supplierRanking.map(s => `
                                                                                <tr class="${s.rank === 1 ? 'summary-winner' : ''}">
                                                                                    <td>${s.rank}</td>
                                                                                    <td>${s.supplier.label}</td>
                                                                                    <td>${numberFormat(s.total)}</td>
                                                                                    <td>${s.status || 'pending'}</td>
                                                                                </tr>
                                                                            `).join('')}
                                                                        </tbody>
                                                                    </table>
                                                                </div>
                                                            `;
                                                            document.body.appendChild(summaryDiv);
                                                            window.print();
                                                            setTimeout(() => document.body.removeChild(summaryDiv), 1000);
                                                            message.success('Summary print dialog opened!');
                                                            return;
                                                        }

                                                        const printContent = `
                                                            <!DOCTYPE html>
                                                            <html>
                                                            <head>
                                                                <title>Quotation Summary - ${props.data._id}</title>
                                                                <style>
                                                                    body { font-family: Arial, sans-serif; margin: 20px; }
                                                                    .header { text-align: center; margin-bottom: 30px; }
                                                                    .stats { display: grid; grid-template-columns: repeat(2, 1fr); gap: 20px; margin: 20px 0; }
                                                                    .stat-card { border: 1px solid #ddd; padding: 15px; }
                                                                    table { width: 100%; border-collapse: collapse; }
                                                                    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                                                                    th { background-color: #f5f5f5; }
                                                                    .winner { background-color: #f6ffed; font-weight: bold; }
                                                                </style>
                                                            </head>
                                                            <body>
                                                                <div class="header">
                                                                    <h1>Quotation Comparison Summary</h1>
                                                                    <p>Request ID: ${props.data._id} | Generated: ${moment().format('YYYY-MM-DD HH:mm')}</p>
                                                                </div>
                                                                <div class="stats">
                                                                    <div class="stat-card">
                                                                        <h3>Best Quote</h3>
                                                                        <p style="font-size: 24px; color: #52c41a; font-weight: bold;">${numberFormat(comparisonStats.bestPrice)}</p>
                                                                    </div>
                                                                    <div class="stat-card">
                                                                        <h3>Potential Savings</h3>
                                                                        <p style="font-size: 24px; color: #1890ff; font-weight: bold;">${numberFormat(comparisonStats.potentialSavings)} (${comparisonStats.savingsPercentage?.toFixed(1)}%)</p>
                                                                    </div>
                                                                </div>
                                                                <h2>Supplier Ranking</h2>
                                                                <table>
                                                                    <thead>
                                                                        <tr><th>Rank</th><th>Supplier</th><th>Total</th><th>Status</th></tr>
                                                                    </thead>
                                                                    <tbody>
                                                                        ${supplierRanking.map(s => `
                                                                            <tr class="${s.rank === 1 ? 'winner' : ''}">
                                                                                <td>${s.rank}</td>
                                                                                <td>${s.supplier.label}</td>
                                                                                <td>${numberFormat(s.total)}</td>
                                                                                <td>${s.status || 'pending'}</td>
                                                                            </tr>
                                                                        `).join('')}
                                                                    </tbody>
                                                                </table>
                                                            </body>
                                                            </html>
                                                        `;
                                                        printWindow.document.write(printContent);
                                                        printWindow.document.close();
                                                        printWindow.onload = () => {
                                                            setTimeout(() => { printWindow.print(); printWindow.close(); }, 500);
                                                        };
                                                        message.success('Summary print dialog opened!');
                                                    } catch (error) {
                                                        console.error('Error with summary print:', error);
                                                        message.error('Summary print failed');
                                                    }
                                                };
                                                simplifiedPrint();
                                            }
                                        }
                                    ]
                                }}
                                trigger={['click']}
                            >
                                <Button size="small" icon={<MoreOutlined />}>
                                    More Actions
                                </Button>
                            </Dropdown>
                        </div>
                    </Col>
                </Row>
            </Card>

            {/* Enhanced Comparison Table */}
            <Card>
                <Table
                    dataSource={data}
                    size="small"
                    bordered
                    pagination={false}
                    scroll={{ x: 'max-content' }}
                    summary={(pageData) => (
                        <Table.Summary>
                            <Table.Summary.Row style={{ backgroundColor: '#fafafa' }}>
                                <Table.Summary.Cell colSpan={2}>
                                    <Text strong style={{ fontSize: 16 }}>TOTAL</Text>
                                </Table.Summary.Cell>
                                {quotations.map((quot, index) => {
                                    const total = pageData.reduce((sum, item) =>
                                        sum + (item[`supplier_total_${quot.supplier.value}`] || 0), 0
                                    );
                                    const isLowest = total === comparisonStats.bestPrice;

                                    return (
                                        <Table.Summary.Cell key={index}>
                                            <div style={{ textAlign: 'center' }}>
                                                <Text
                                                    strong
                                                    style={{
                                                        fontSize: 16,
                                                        color: isLowest && highlightBest ? '#52c41a' : 'inherit'
                                                    }}
                                                >
                                                    {numberFormat(total)}
                                                </Text>
                                                {isLowest && highlightBest && (
                                                    <div>
                                                        <Tag color="success" size="small" icon={<TrophyOutlined />}>
                                                            BEST
                                                        </Tag>
                                                    </div>
                                                )}
                                                {showPercentages && comparisonStats.bestPrice > 0 && (
                                                    <div style={{ fontSize: 12, color: '#666' }}>
                                                        {((total / comparisonStats.bestPrice - 1) * 100).toFixed(1)}%
                                                    </div>
                                                )}
                                            </div>
                                        </Table.Summary.Cell>
                                    );
                                })}
                            </Table.Summary.Row>
                        </Table.Summary>
                    )}
                >
                    <Column
                        title="#"
                        dataIndex="index"
                        render={(text, record, index) => index + 1}
                        width={50}
                        fixed="left"
                    />

                    <Column
                        title="Item"
                        dataIndex="item"
                        width={200}
                        fixed="left"
                        render={(text, record) => (
                            <div>
                                <Text strong>{text}</Text>
                                {record.isAdditional && (
                                    <Tag color="orange" size="small" style={{ marginLeft: 8 }}>
                                        Additional
                                    </Tag>
                                )}
                                <br />
                                <Text type="secondary" style={{ fontSize: 12 }}>
                                    Req: {record.requestedQuantity}
                                </Text>
                            </div>
                        )}
                    />

                    <ColumnGroup title="Supplier Quotations">
                        {quotations.map((quot, index) => {
                            const supplierId = quot.supplier.value;
                            const isWinningSupplier = supplierRanking[0]?.supplierId === supplierId;

                            return (
                                <Column
                                    key={index}
                                    title={
                                        <div style={{ textAlign: 'center' }}>
                                            <div style={{ fontWeight: 'bold' }}>
                                                {quot.supplier.label}
                                                {isWinningSupplier && (
                                                    <TrophyOutlined style={{ color: '#52c41a', marginLeft: 4 }} />
                                                )}
                                            </div>
                                            <div style={{ fontSize: 11, color: '#666' }}>
                                                {moment(quot.date).format('MMM DD, YYYY')}
                                            </div>
                                            <Tag
                                                color={quot.status === 'approved' ? 'success' : 'processing'}
                                                size="small"
                                            >
                                                {quot.status || 'pending'}
                                            </Tag>
                                        </div>
                                    }
                                    dataIndex={`supplier_${supplierId}`}
                                    width={180}
                                    render={(price, record) => {
                                        const quantity = record[`supplier_qty_${supplierId}`] || 0;
                                        const total = record[`supplier_total_${supplierId}`] || 0;
                                        const bestForItem = getBestSupplierForItem(record);
                                        const isBestForItem = bestForItem?.supplierId === supplierId && highlightBest;

                                        if (!price) {
                                            return (
                                                <div style={{ textAlign: 'center', color: '#ccc' }}>
                                                    <Text type="secondary">Not quoted</Text>
                                                </div>
                                            );
                                        }

                                        return (
                                            <div
                                                style={{
                                                    textAlign: 'center',
                                                    backgroundColor: isBestForItem ? '#f6ffed' : 'transparent',
                                                    padding: '4px',
                                                    borderRadius: '4px',
                                                    border: isBestForItem ? '1px solid #b7eb8f' : 'none'
                                                }}
                                            >
                                                <div>
                                                    <Text strong>Qty: {quantity}</Text>
                                                </div>
                                                <div>
                                                    <Text>Unit: {numberFormat(price)}</Text>
                                                </div>
                                                <Divider style={{ margin: '4px 0' }} />
                                                <div>
                                                    <Text
                                                        strong
                                                        style={{
                                                            color: isBestForItem ? '#52c41a' : 'inherit',
                                                            fontSize: 14
                                                        }}
                                                    >
                                                        {numberFormat(total)}
                                                    </Text>
                                                </div>
                                                {isBestForItem && (
                                                    <Tag color="success" size="small" icon={<ThunderboltOutlined />}>
                                                        Best
                                                    </Tag>
                                                )}
                                                {showPercentages && bestForItem && bestForItem.total > 0 && (
                                                    <div style={{ fontSize: 11, color: '#666' }}>
                                                        +{((total / bestForItem.total - 1) * 100).toFixed(1)}%
                                                    </div>
                                                )}
                                            </div>
                                        );
                                    }}
                                />
                            );
                        })}
                    </ColumnGroup>
                </Table>
            </Card>

            {/* Analysis Insights */}
            {quotations.length > 1 && (
                <Card
                    title={
                        <Space>
                            <InfoCircleOutlined />
                            Analysis Insights
                        </Space>
                    }
                    style={{ marginTop: 16 }}
                    size="small"
                >
                    <Row gutter={[16, 8]}>
                        <Col span={24}>
                            <Alert
                                message={`Best Overall Quote: ${supplierRanking[0]?.supplier?.label}`}
                                description={
                                    <div>
                                        <p>
                                            Choosing {supplierRanking[0]?.supplier?.label} would save{' '}
                                            <Text strong style={{ color: '#52c41a' }}>
                                                {numberFormat(comparisonStats.potentialSavings)}
                                            </Text>{' '}
                                            ({comparisonStats.savingsPercentage?.toFixed(1)}%) compared to the highest quote.
                                        </p>
                                        {comparisonStats.potentialSavings > 0 && (
                                            <Progress
                                                percent={comparisonStats.savingsPercentage}
                                                strokeColor="#52c41a"
                                                format={(percent) => `${percent?.toFixed(1)}% savings`}
                                            />
                                        )}
                                    </div>
                                }
                                type="success"
                                showIcon
                            />
                        </Col>
                    </Row>
                </Card>
            )}
        </div>
    );
}

export default QuotComparisonView;