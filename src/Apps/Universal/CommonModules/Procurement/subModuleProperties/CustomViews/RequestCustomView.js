import QuotComparisonView from "./QuotComparisonView";
import React, { useState, useEffect } from 'react'
import ViewTable from "../../../../../../Components/ViewTable";
import moment from "moment/moment";
import { Descriptions, Table, Tabs, Card, Row, Col, Statistic, Badge, Tag, Space, Button, Tooltip  } from "antd";
import { ShoppingCartOutlined, DollarOutlined, ClockCircleOutlined, UserOutlined, FileTextOutlined, SwapOutlined, CheckCircleOutlined, ExclamationCircleOutlined  } from "@ant-design/icons";
import { default as subModuleProperties  } from "..";
import { modules as subModules  } from "../../SubModules";
import { numberFormat  } from "../../../../../../Utils/functions";


const RequestCustomView = (props) => {
  const { data, pouchDatabase, databasePrefix } = props;
  const [quotations, setQuotations] = useState([]);
  const [purchaseOrders, setPurchaseOrders] = useState([]);
  const [loading, setLoading] = useState(true);
  const [requestStats, setRequestStats] = useState({
    totalQuotations: 0,
    totalPurchaseOrders: 0,
    lowestQuotation: null,
    highestQuotation: null,
    averageQuotation: 0,
    totalRequestValue: 0
  });

  useEffect(() => {
    const fetchRelatedData = async () => {
      try {
        setLoading(true);

        // Fetch quotations
        const quotationDB = pouchDatabase('procurement_quotations', databasePrefix);
        const quotationData = await quotationDB.getAllData();
        const relatedQuotations = quotationData.filter(q => q.request?.value === data._id);

        // Fetch purchase orders
        const purchaseOrderDB = pouchDatabase('purchase_orders', databasePrefix);
        const purchaseOrderData = await purchaseOrderDB.getAllData();
        const relatedPurchaseOrders = purchaseOrderData.filter(po => po.request?.value === data._id);

        setQuotations(relatedQuotations);
        setPurchaseOrders(relatedPurchaseOrders);

        // Calculate statistics
        const quotationTotals = relatedQuotations.map(q => {
          return q.items?.reduce((sum, item) => sum + (item.price * item.quantity), 0) || 0;
        });

        const stats = {
          totalQuotations: relatedQuotations.length,
          totalPurchaseOrders: relatedPurchaseOrders.length,
          lowestQuotation: quotationTotals.length > 0 ? Math.min(...quotationTotals) : 0,
          highestQuotation: quotationTotals.length > 0 ? Math.max(...quotationTotals) : 0,
          averageQuotation: quotationTotals.length > 0 ? quotationTotals.reduce((a, b) => a + b, 0) / quotationTotals.length : 0,
          totalRequestValue: data.items?.reduce((sum, item) => sum + (item.estimatedCost || 0), 0) || 0
        };

        setRequestStats(stats);
      } catch (error) {
        console.error('Error fetching related data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchRelatedData();
  }, [data._id, pouchDatabase, databasePrefix]);

  const getRequestStatus = () => {
    if (purchaseOrders.length > 0) {
      return { status: 'Ordered', color: 'success', icon: <CheckCircleOutlined /> };
    } else if (quotations.length > 0) {
      return { status: 'Quoted', color: 'processing', icon: <FileTextOutlined /> };
    } else {
      return { status: 'Pending', color: 'warning', icon: <ExclamationCircleOutlined /> };
    }
  };

  const getUrgencyLevel = () => {
    const daysUntilNeeded = moment(data.date_needed).diff(moment(), 'days');
    if (daysUntilNeeded < 0) {
      return { level: 'Overdue', color: 'error' };
    } else if (daysUntilNeeded <= 3) {
      return { level: 'Urgent', color: 'warning' };
    } else if (daysUntilNeeded <= 7) {
      return { level: 'Soon', color: 'processing' };
    } else {
      return { level: 'Normal', color: 'default' };
    }
  };

  const requestStatus = getRequestStatus();
  const urgencyLevel = getUrgencyLevel();

  return (
    <div style={{ padding: '0 24px' }}>
      {/* Enhanced Header Section */}
      <Card style={{ marginBottom: 24 }}>
        <Row gutter={[24, 16]}>
          <Col xs={24} lg={16}>
            <Space direction="vertical" size="small" style={{ width: '100%' }}>
              <div style={{ display: 'flex', alignItems: 'center', gap: 12 }}>
                <h2 style={{ margin: 0, fontSize: '24px' }}>Request #{data._id}</h2>
                <Badge
                  status={requestStatus.color}
                  text={
                    <span style={{ fontSize: '14px', fontWeight: 500 }}>
                      {requestStatus.icon} {requestStatus.status}
                    </span>
                  }
                />
                <Tag color={urgencyLevel.color}>{urgencyLevel.level}</Tag>
              </div>

              <Descriptions column={2} size="small">
                <Descriptions.Item label={<><UserOutlined /> Requested By</>}>
                  <strong>{data.receiver?.label || 'Unknown'}</strong>
                </Descriptions.Item>
                <Descriptions.Item label={<><ClockCircleOutlined /> Date Needed</>}>
                  <strong style={{ color: urgencyLevel.color === 'error' ? '#ff4d4f' : 'inherit' }}>
                    {moment(data.date_needed).format("Do MMM YYYY")}
                  </strong>
                  <span style={{ marginLeft: 8, fontSize: '12px', color: '#666' }}>
                    ({moment(data.date_needed).fromNow()})
                  </span>
                </Descriptions.Item>
                <Descriptions.Item label="Request Date">
                  {moment(data.createdAt).format("Do MMM YYYY")}
                </Descriptions.Item>
                <Descriptions.Item label="Items Count">
                  <strong>{data.items?.length || 0} items</strong>
                </Descriptions.Item>
              </Descriptions>

              {data.particulars && (
                <div>
                  <strong>Description:</strong>
                  <p style={{ margin: '8px 0 0 0', color: '#666' }}>{data.particulars}</p>
                </div>
              )}
            </Space>
          </Col>

          <Col xs={24} lg={8}>
            <Card size="small" title="Request Statistics" style={{ height: '100%' }}>
              <Row gutter={[16, 16]}>
                <Col span={12}>
                  <Statistic
                    title="Quotations"
                    value={requestStats.totalQuotations}
                    prefix={<FileTextOutlined />}
                    valueStyle={{ color: '#1890ff' }}
                  />
                </Col>
                <Col span={12}>
                  <Statistic
                    title="Purchase Orders"
                    value={requestStats.totalPurchaseOrders}
                    prefix={<ShoppingCartOutlined />}
                    valueStyle={{ color: '#52c41a' }}
                  />
                </Col>
                {requestStats.totalQuotations > 0 && (
                  <>
                    <Col span={24}>
                      <Statistic
                        title="Best Quote"
                        value={requestStats.lowestQuotation}
                        prefix={<DollarOutlined />}
                        formatter={(value) => numberFormat(value)}
                        valueStyle={{ color: '#52c41a', fontSize: '16px' }}
                      />
                    </Col>
                    {requestStats.totalQuotations > 1 && (
                      <Col span={24}>
                        <div style={{ fontSize: '12px', color: '#666' }}>
                          Savings: {numberFormat(requestStats.highestQuotation - requestStats.lowestQuotation)}
                          ({((requestStats.highestQuotation - requestStats.lowestQuotation) / requestStats.highestQuotation * 100).toFixed(1)}%)
                        </div>
                      </Col>
                    )}
                  </>
                )}
              </Row>
            </Card>
          </Col>
        </Row>
      </Card>

      <Tabs
        defaultActiveKey={requestStats.totalQuotations > 1 ? "3" : "1"}
        items={[
          {
            key: "1",
            label: (
              <span>
                <FileTextOutlined />
                Items ({data.items?.length || 0})
              </span>
            ),
            children: (
              <Card>
                <Table
                  dataSource={data.items}
                  columns={[
                    {
                      title: "#",
                      dataIndex: "no",
                      render: (text, record, index) => index + 1,
                      width: 50
                    },
                    {
                      title: "Item",
                      dataIndex: "item",
                      render: (text) => <strong>{text}</strong>
                    },
                    {
                      title: "Quantity",
                      dataIndex: "quantity",
                      render: (text) => <Tag color="blue">{text}</Tag>,
                      width: 100
                    },
                    {
                      title: "Specification",
                      dataIndex: "description",
                      ellipsis: true
                    },
                    {
                      title: "Justification",
                      dataIndex: "justification",
                      ellipsis: true
                    }
                  ]}
                  pagination={false}
                  size="small"
                  bordered
                />
              </Card>
            )
          },
          {
            key: "2",
            label: (
              <span>
                <FileTextOutlined />
                Quotations ({requestStats.totalQuotations})
                {requestStats.totalQuotations > 0 && <Badge count={requestStats.totalQuotations} style={{ marginLeft: 8 }} />}
              </span>
            ),
            children: (
              <ViewTable {
                ...{
                  modules: {...props.modules, ...subModules},
                  modulesProperties: props.modulesProperties,
                  databasePrefix: props.databasePrefix,
                  pouchDatabase: props.pouchDatabase,
                  userPermissions: props.userPermissions,
                  filterID: { column: "request", id: props.data._id },
                }
              }
              {...subModules.procurement_quotations}
              {...subModuleProperties.procurement_quotations}
              />
            )
          },
          {
            key: "3",
            label: (
              <span>
                <SwapOutlined />
                Quotation Comparison
                {requestStats.totalQuotations > 1 && (
                  <Tooltip title="Multiple quotations available for comparison">
                    <Badge status="processing" style={{ marginLeft: 8 }} />
                  </Tooltip>
                )}
              </span>
            ),
            children: (
              <QuotComparisonView
                {...props}
                quotations={quotations}
                requestStats={requestStats}
              />
            )
          },
          {
            key: "4",
            label: (
              <span>
                <ShoppingCartOutlined />
                Purchase Orders ({requestStats.totalPurchaseOrders})
                {requestStats.totalPurchaseOrders > 0 && <Badge count={requestStats.totalPurchaseOrders} style={{ marginLeft: 8 }} />}
              </span>
            ),
            children: (
              <ViewTable {
                ...{
                  modules: {...props.modules, ...subModules.modules},
                  modulesProperties: props.modulesProperties,
                  databasePrefix: props.databasePrefix,
                  pouchDatabase: props.pouchDatabase,
                  userPermissions: props.userPermissions,
                  filterID: { column: "request", id: props.data._id },
                }
              }
              {...subModules.purchase_orders}
              {...subModuleProperties.purchase_orders}
              />
            )
          }
        ]}
      />
    </div>
  )
}

export default RequestCustomView;