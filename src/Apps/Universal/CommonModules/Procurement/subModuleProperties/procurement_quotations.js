import QuotationView from "./CustomViews/QuotationView";
import { BetaSchemaForm, TableDropdown  } from "@ant-design/pro-components";
import { message  } from "antd";
import { useRef  } from "react";


export default {
  CustomView: (data) => <QuotationView data={data} />,
  MoreActions: (props) => {
    const currentUser = JSON.parse(localStorage.getItem("LOCAL_STORAGE_USER"));

    const APP_USER_PERMISSIONS = JSON.parse(
      localStorage.getItem("APP_USER_PERMISSIONS")
    );

    const action = useRef();

    const {
      pouchDatabase,
      collection,
      databasePrefix,
      record,
      CRUD_USER,
      singular,
      modules,
    } = props;

    //log modules


    return (
      <TableDropdown
        key="actionGroup"
        menus={[
          {
            key: "Generate Purchase Order",
            name: (
              <BetaSchemaForm
                formRef={action}
                submitter={{
                  searchConfig: {
                    resetText: "Close",
                    submitText: "Save",
                  },
                }}
                modalProps={{ centered: true }}
                grid={true}
                trigger={
                  <a key="button" type="primary">
                    <i className="fa fa-plus" /> Generate Purchase Order
                  </a>
                }
                title={"Generate Purchase Order"}
                destroyOnHidden={true}
                layoutType="ModalForm"
                onFinish={async (values) => {


                  await pouchDatabase(
                    modules.purchase_orders.collection,
                    databasePrefix
                  ).saveDocument({ ...values, quotation: record }, CRUD_USER);
                  message.success(`Purchase Order Generated.`);

                  return true;
                }}
                columns={modules?.purchase_orders?.columns || []}
                initialValues={{
                  request: record.request,
                  items: record.items,
                  supplier: record.supplier,

                }
                }
              />
            )
          }
        ]}
      />)
  }
}