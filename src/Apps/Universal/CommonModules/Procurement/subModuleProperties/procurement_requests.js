import RequestCustomView from "./CustomViews/RequestCustomView";
import { BetaSchemaForm, TableDropdown  } from "@ant-design/pro-components";
import { CheckCircleOutlined, CloseCircleOutlined, PaperClipOutlined  } from "@ant-design/icons";
import { message, Popconfirm  } from "antd";
import { useRef  } from "react";


export default {
  CustomView: (data) => <RequestCustomView {...data} />,
  MoreActions: (props) => {

    const currentUser = JSON.parse(localStorage.getItem("LOCAL_STORAGE_USER"));

    const APP_USER_PERMISSIONS = JSON.parse(
      localStorage.getItem("APP_USER_PERMISSIONS")
    );

    const action = useRef();

    const {
      pouchDatabase,
      collection,
      databasePrefix,
      record,
      CRUD_USER,
      singular,
      modules,
    } = props;

    // Modules are now properly buffed and available via HybridModule



    return (
      <TableDropdown
        key="actionGroup"
        menus={[
          {
            key: "Approve Request",
            name: (
              <Popconfirm
                title="Approve Request"
                description={`Authorize request sent by ${record.receiver.label}?`}
                // open={open}
                // onOpenChange={handleOpenChange}
                onConfirm={async () => {
                  await pouchDatabase(
                    modules.requests.collection,
                    databasePrefix
                  ).saveDocument(
                    {
                      ...record,
                      approvedAt: Date(),
                      approvedBy: {
                        ...currentUser,
                        value: currentUser._id,
                        label: `${currentUser.first_name} ${currentUser.last_name}`,
                      },
                      approved: true,
                    },
                    CRUD_USER
                  );
                  message.success(`Approval Successful.`);
                  return true;
                }}
                // onCancel={cancel}
                okText="Yes"
                cancelText="No"
              >
                <a key="button" type="primary">
                  <CheckCircleOutlined /> Approve Request
                </a>
              </Popconfirm>
            ),
          },
          {
            key: "Reject Request",
            name: (
              <Popconfirm
                title="Reject Request"
                description={`Reject request sent by ${record.receiver.label}?`}
                // open={open}
                // onOpenChange={handleOpenChange}
                onConfirm={async () => {
                  await pouchDatabase(
                    modules.requests.collection,
                    databasePrefix
                  ).saveDocument(
                    {
                      ...record,
                      rejectedAt: Date(),
                      rejectedBy: {
                        ...currentUser,
                        value: currentUser._id,
                        label: `${currentUser.first_name} ${currentUser.last_name}`,
                      },
                      rejected: true,
                    },
                    CRUD_USER
                  );
                  message.success(`Rejection Successful.`);
                  return true;
                }}
                // onCancel={cancel}
                okText="Yes"
                cancelText="No"
              >
                <a key="button" type="primary">
                  <CloseCircleOutlined /> Reject Request
                </a>
              </Popconfirm>
            ),
          },

          {
            key: "Add Supplier Quotation",
            name: (
              <BetaSchemaForm
                formRef={action}
                submitter={{
                  searchConfig: {
                    resetText: "Close",
                    submitText: "Save",
                  },
                }}
                modalProps={{ centered: true }}
                grid={true}
                trigger={
                  <a key="button" type="primary">
                    <PaperClipOutlined /> Add Supplier Quotation
                  </a>
                }
                title={"Supplier Quotation"}
                destroyOnHidden={true}
                layoutType="ModalForm"
                onFinish={async (values) => {


                  await pouchDatabase(
                    modules.procurement_quotations.collection,
                    databasePrefix
                  ).saveDocument({ ...values }, CRUD_USER);
                  message.success(`Quotation Saved.`);

                  return true;
                }}
                columns={modules?.procurement_quotations?.columns || []}
                initialValues={{
                  date: new Date().toISOString().split('T')[0],
                  request: {
                    value: record._id,
                    label: `${record.receiver?.label || record.receiver?.name || 'Unknown Receiver'} - ${record._id}`
                  },
                  items: record.items || [{}]
                }}
              />
            ),
          }

        ]}
      ></TableDropdown>
    );
  },
}
