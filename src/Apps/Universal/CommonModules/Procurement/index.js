import HybridModule from "../../../../Components/HybridModule";
import Reports from "./subModuleProperties/Reports";
import subModuleProperties from "./subModuleProperties";
import { Tabs } from "antd";
import { modules as subModules } from "./SubModules";
import { useState } from "react";


const Procurement = (props) => {
  const [activeTab, setActiveTab] = useState("1");

  const items = [
    {
      key: "1",
      label: "Procurement",
      children: <HybridModule subModules={subModules} subModuleProperties={subModuleProperties} {...props} />
    },
    {
      key: "2",
      label: "Reports",
      children: <Reports {...props} />
    }
  ];

  return (
    <Tabs
      activeKey={activeTab}
      onChange={setActiveTab}
      items={items}
      type="card"
    />
  );
};

export default Procurement;
