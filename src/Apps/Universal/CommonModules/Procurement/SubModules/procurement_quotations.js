

const procurement_quotations = {
    name: "Procurement Quotations",
    icon: "FileTextOutlined",
    path: "/procurement_quotations",
    collection: "procurement_quotations",
    singular: "Procurement Quotation",
    multi_Branch: true,
    columns: [
      {
        valueType: "date",
        dataIndex: "date",
        title: "Date",
        isRequired: true,
        colProps: {
          span: 8
        }
      },
      {
        type: "dbSelect",
        dataIndex: "supplier",
        title: "Supplier",
        collection: "suppliers",
        label: ["name"],
        isRequired: true,
        colProps: {
          span: 8
        }
      },
      {
        type: "dbSelect",
        dataIndex: "request",
        title: "Request",
        collection: "procurement_requests",
        label: ["title"],
        isRequired: true,
        colProps: {
          span: 8
        }
      },
       {
        title: "Items",
        dataIndex: "items",
        valueType: "formList",
        isRequired: true,
        fieldProps: {
          initialValue: [{}],
          style: { width: '100%' },
          creatorButtonProps: {
            block: true,
            style: { width: '100%' },
            creatorButtonText: 'Add Item',
          }
        },
        render: (text, record) => record.items?.map((item) => item.item).join(', ') || '',
        columns: [
          {
            valueType: 'group',
            columns: [
              {
                valueType: 'text',
                dataIndex: 'item',
                title: 'Item',
                isRequired: true,
                colProps: {
                  span: 8
                }
              },
              {
                valueType: 'digit',
                dataIndex: 'quantity',
                title: 'Quantity',
                isRequired: true,
                colProps: {
                  span: 8
                }
              },
              {
                valueType: 'digit',
                dataIndex: 'price',
                title: 'Unit Price',
                isRequired: true,
                colProps: {
                  span: 8
                }
              },
            ],
          },
        ],
       },
       {
        title: "Discounted",
        dataIndex: "discounted",
        valueType: "switch",
        fieldProps: {
          unCheckedChildren: "No",
          checkedChildren: "Yes",
        },
        colProps: {
          md: 2,
          offset: 14,
        },
        hideInTable: true,
      },
       {
        title: "Taxable",
        dataIndex: "taxable",
        valueType: "switch",
        fieldProps: {
          unCheckedChildren: "No",
          checkedChildren: "Yes",
        },
        colProps: {
          md: 2,
        },
        hideInTable: true,
      },
      {
        valueType: "dependency",
        fieldProps: {
          name: ["items"],
        },

        render: (record, { items }) => {
          let total = 0;
          items &&
            items.map(
              (item) =>
                (total = item
                  ? total +
                    (item.price ? item.price : 0) *
                      (item.quantity ? item.quantity : 0)
                  : total)
            );
          return total.toLocaleString();
        },
        columns: ({ items, taxable, discounted, discount }) => {
          const itemValue = items.reduce(
            (pv, cv) =>
              pv + (cv.price ? cv.price : 0) * (cv.quantity ? cv.quantity : 0),
            0
          );

          const docDiscount = discounted ? discount : 0;

          const tax = taxable ? itemValue * 0.18 : 0;

          return [
            {
              title: "Total",
              dataIndex: "total",
              colProps: {
                md: 6,
                // offset: 18,
              },
              fieldProps: {
                disabled: true,
                value: itemValue,
              },
              // valueType: "digit",
            },
          ];
        },
      },
      {
        valueType: "dependency",
        hideInTable: true,
        name: ["taxable", "items", "discounted"],
        columns: ({ taxable, items, discounted }) => {
          let columnsToRender = [];

          if (discounted && items.length > 0) {
            columnsToRender.push({
              dataIndex: "discount_description",
              title: "Discount Description",
              colProps: {
                md: 6,
                offset: !taxable ? 12 : 6,
              },
            });
            columnsToRender.push({
              dataIndex: "discount",
              title: "Discount",
              colProps: {
                md: 6,
                // offset: !taxable ? 18 : 12,
              },
            });
          }

          if (taxable && items.length > 0) {
            columnsToRender.push({
              dataIndex: "tax",
              title: "Tax",
              colProps: {
                md: 6,
                offset: !discounted ? 18 : null,
              },
              fieldProps: {
                disabled: true,
                value:
                  items.reduce(
                    (pv, cv) =>
                      pv +
                      (cv.price ? cv.price : 0) * (cv.quantity ? cv.quantity : 0),
                    0
                  ) * 0.18,
              },
            });
          }

          return columnsToRender;
        },
      },

      {
        valueType: "textarea",
        dataIndex: "description",
        title: "Description",
        colProps: {
          span: 24
        }
      },
      {
        valueType: "select",
        dataIndex: "status",
        title: "Status",
        valueEnum: {
          pending: { text: "Pending", status: "Processing" },
          approved: { text: "Approved", status: "Success" },
          rejected: { text: "Rejected", status: "Error" },
        },
        initialValue: "pending",
        colProps: {
          span: 8
        }
      },
    ],
}

export default procurement_quotations