import moment from "moment";


const procurement_requests = {
    name: 'Requests',
    icon: 'FlagOutlined',
    path: '/requests',
    collection: 'procurement_requests',
    singular: 'Request',
    columns: [
      {
        title: "Date Needed",
        dataIndex: "date_needed",
        valueType: "date",
        isRequired: true,
        initialValue: moment(),
      },
          {
            title: "Requested By",
            dataIndex: "receiver",
            type: "dbSelect",
            valueType: "select",
            collection: "users",
            label: ["first_name", "last_name"],
            sorter: true,
            isRequired: true,
            initialValueAsCurrentUser: true,
            // initialValue: requester,
          },
          {
            title:"Items",
            dataIndex: "items",
            // hideInTable: true,
            valueType: "formList",
            fieldProps: {
              initialValue: [{}],
              creatorButtonProps: {
                block: true,
                style: {
                  width: "100%",
                },
                copyIconProps: false,
                creatorButtonText: "Add Item",
              },
            },
            render: (text, record) => record.items?.map((item) => item.item).join(', ') || '',
            columns: [
                {
                    valueType: "group",
                    colProps: {
                      md: 24,
                    },
                    columns: [
                      {
                        title: "Item",
                        dataIndex: "item",
                        valueType: "text",
                        isRequired: true,
                        colProps: {
                          md: 5,
                        },
                      },
                      {
                        title: "Quantity",
                        dataIndex: "quantity",
                        valueType: "digit",
                        isRequired: true,
                        colProps: {
                          md: 3,
                        },
                      },
                      {
                        title: "Specification",
                        dataIndex: "description",
                        valueType: "text",
                        // isRequired: true,
                        colProps: {
                          md: 8,
                        },
                      },
                      {
                        title: "Justification",
                        dataIndex: "justification",
                        valueType: "text",
                        // isRequired: true,
                        colProps: {
                          md: 8,
                        },
                      },
                    ],
                }
            ]
          },
          {
            title: "Description",
            dataIndex: "particulars",
            valueType: "text",
            // isRequired: true,
            colProps: {
              md: 8,
            },
          },
    ]

}

export { procurement_requests };
