

const purchase_orders = {
    name: 'Purchase Orders',
    icon: 'FlagOutlined',
    path: '/purchase_orders',
    collection: 'purchase_orders',
    singular: 'Purchase Order',
    multi_Branch: true,
    columns: [
      {
        valueType: "date",
        dataIndex: "date",
        title: "Date",
        isRequired: true,
        colProps: {
          span: 8
        }
      },
      {
        type: "dbSelect",
        dataIndex: "supplier",
        title: "Supplier",
        collection: "suppliers",
        label: ["name"],
        isRequired: true,
        colProps: {
          span: 8
        }
      },
      {
        type: "dbSelect",
        dataIndex: "request",
        title: "Request",
        collection: "procurement_requests",
        label: ["title"],
        isRequired: true,
        colProps: {
          span: 8
        }
      },
       {
        title: "Items",
        dataIndex: "items",
        valueType: "formList",
        isRequired: true,
        fieldProps: {
          initialValue: [{}],
          style: { width: '100%' },
          creatorButtonProps: {
            block: true,
            style: { width: '100%' },
            creatorButtonText: 'Add Item',
          }
        },
        render: (text, record) => record.items?.map((item) => item.item).join(', ') || '',
        columns: [
          {
            valueType: 'group',
            columns: [
              {
                valueType: 'text',
                dataIndex: 'item',
                title: 'Item',
                isRequired: true,
                colProps: {
                  span: 8
                }
              },
              {
                valueType: 'digit',
                dataIndex: 'quantity',
                title: 'Quantity',
                isRequired: true,
                colProps: {
                  span: 8
                }
              },
              {
                valueType: 'digit',
                dataIndex: 'price',
                title: 'Unit Price',
                isRequired: true,
                colProps: {
                  span: 8
                }
              },
            ],
          },
        ],
       },
      {
        valueType: "textarea",
        dataIndex: "description",
        title: "Description",
        colProps: {
          span: 24
        }
      },
      {
        valueType: "select",
        dataIndex: "status",
        title: "Status",
        valueEnum: {
          pending: { text: "Pending", status: "Processing" },
          approved: { text: "Approved", status: "Success" },
          completed: { text: "Completed", status: "Success" },
          cancelled: { text: "Cancelled", status: "Error" },
        },
        initialValue: "pending",
        colProps: {
          span: 8
        }
      },
    ],
}

export default purchase_orders