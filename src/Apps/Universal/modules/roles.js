

export const roles = {
  name: "Roles",
  icon: "KeyOutlined",
  path: "/user_management/roles",
  parent: "user_management",
  collection: "roles",
  singular: "Role",
  multi_Branch: true,
  columns: [
    {
      title: "Name",
      dataIndex: "name",
      valueType: "text",
    },
    {
      title: "Super User",
      dataIndex: "root",
      valueType: "switch",
      fieldProps: {
        checkedChildren: "Yes",
        unCheckedChildren: "No",
      },
    },
    {
      title: "Advanced Dashboard",
      dataIndex: "advanced_dashboard",
      valueType: "switch",
      fieldProps: {
        checkedChildren: "Yes",
        unCheckedChildren: "No",
      },
    },
    {
      title: "Permissions",
      valueType: "formList",
      dataIndex: "permissions",
      hideInTable: true,
      fieldProps: {
        creatorButtonProps: false,
        copyIconProps: false,
        deleteIconProps: false,
      },
      columns: [
        {
          valueType: "group",
          colProps: {
            md: 24,
          },
          columns: [
            {
              title: "Module",
              dataIndex: "module",
              valueType: "select",
              fieldProps: {
                disabled: true,
              },
              colProps: {
                md: 13,
              },
            },
            {
              title: "View",
              dataIndex: "view",
              valueType: "switch",
              fieldProps: {
                checkedChildren: "Yes",
                unCheckedChildren: "No",
              },
              colProps: {
                md: 2,
              },
            },
            {
              title: "Create",
              dataIndex: "create",
              valueType: "switch",
              fieldProps: {
                checkedChildren: "Yes",
                unCheckedChildren: "No",
              },
              colProps: {
                md: 2,
              },
            },
            {
              title: "Update",
              dataIndex: "update",
              valueType: "switch",
              fieldProps: {
                checkedChildren: "Yes",
                unCheckedChildren: "No",
              },
              colProps: {
                md: 2,
              },
            },
            {
              title: "Delete",
              dataIndex: "delete",
              valueType: "switch",
              fieldProps: {
                checkedChildren: "Yes",
                unCheckedChildren: "No",
              },
              colProps: {
                md: 2,
              },
            },
            {
              title: "Personalized",
              dataIndex: "personalized",
              valueType: "switch",
              fieldProps: {
                checkedChildren: "Yes",
                unCheckedChildren: "No",
              },
              colProps: {
                md: 3,
              },
            },
          ],
        },
      ],
    },
  ],
};
