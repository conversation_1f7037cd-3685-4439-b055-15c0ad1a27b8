import moment from "moment";


export const fixed_assets = {
    name: "Fixed Assets",
    icon: "BuildOutlined",
    path: "/fixed_assets",
    collection: "fixed_assets",
    singular: "Fixed Asset",
    parent: "finance",
    columns: [
        {
            title: "Asset Name",
            dataIndex: "name",
            isRequired: true,
            sorter: true,
        },
        {
            title: "Asset Code",
            dataIndex: "code",
            isRequired: true,
            sorter: true,
            tooltip: "Unique identifier for this asset"
        },
        {
            title: "Asset Type",
            dataIndex: "asset_type",
            valueType: "select",
            valueEnum: {
                building: "Building",
                land: "Land",
                vehicle: "Vehicle",
                equipment: "Equipment",
                furniture: "Furniture & Fixtures",
                computer: "Computer & IT Equipment",
                leasehold: "Leasehold Improvements",
                other: "Other"
            },
            isRequired: true,
            filters: true,
            onFilter: true,
        },
        {
            title: "Purchase Date",
            dataIndex: "purchase_date",
            valueType: "date",
            isRequired: true,
            sorter: true,
        },
        {
            title: "Purchase Cost",
            dataIndex: "purchase_cost",
            valueType: "money",
            isRequired: true,
        },
        {
            title: "Salvage Value",
            dataIndex: "salvage_value",
            valueType: "money",
            isRequired: true,
            initialValue: 0,
            tooltip: "Estimated value at the end of useful life"
        },
        {
            title: "Useful Life (Years)",
            dataIndex: "useful_life_years",
            valueType: "digit",
            isRequired: true,
            initialValue: 5,
        },
        {
            title: "Depreciation Method",
            dataIndex: "depreciation_method",
            valueType: "select",
            valueEnum: {
                straight_line: "Straight Line",
                declining_balance: "Declining Balance",
                units_of_production: "Units of Production",
                sum_of_years_digits: "Sum of Years' Digits"
            },
            isRequired: true,
            initialValue: "straight_line",
        },
        {
            title: "Depreciation Rate (%)",
            dataIndex: "depreciation_rate",
            valueType: "digit",
            tooltip: "Only used for declining balance method",
            hideInTable: true,
            dependencies: ['depreciation_method'],
            hideInForm: ({ depreciation_method }) => depreciation_method !== 'declining_balance',
        },
        {
            title: "Asset Account",
            dataIndex: "asset_account",
            type: "dbSelect",
            collection: "enhanced_accounts",
            label: ["code", " - ", "name"],
            isRequired: true,
            tooltip: "The asset account where this fixed asset is recorded",
            filters: {
                category: "asset",
                type: "fixed_asset"
            }
        },
        {
            title: "Accumulated Depreciation Account",
            dataIndex: "accumulated_depreciation_account",
            type: "dbSelect",
            collection: "enhanced_accounts",
            label: ["code", " - ", "name"],
            isRequired: true,
            tooltip: "The contra-asset account for accumulated depreciation",
            filters: {
                category: "asset",
                type: "contra_asset",
                reporting_group: "accumulated_depreciation"
            }
        },
        {
            title: "Depreciation Expense Account",
            dataIndex: "depreciation_expense_account",
            type: "dbSelect",
            collection: "enhanced_accounts",
            label: ["code", " - ", "name"],
            isRequired: true,
            tooltip: "The expense account for depreciation",
            filters: {
                category: "expense",
                reporting_group: "depreciation_expense"
            }
        },
        {
            title: "Current Book Value",
            dataIndex: "current_book_value",
            valueType: "money",
            readonly: true,
            hideInForm: true,
        },
        {
            title: "Accumulated Depreciation",
            dataIndex: "accumulated_depreciation",
            valueType: "money",
            readonly: true,
            hideInForm: true,
        },
        {
            title: "Last Depreciation Date",
            dataIndex: "last_depreciation_date",
            valueType: "date",
            readonly: true,
            hideInForm: true,
        },
        {
            title: "Location",
            dataIndex: "location",
            valueType: "text",
            hideInTable: true,
        },
        {
            title: "Serial Number",
            dataIndex: "serial_number",
            valueType: "text",
            hideInTable: true,
        },
        {
            title: "Warranty Expiry",
            dataIndex: "warranty_expiry",
            valueType: "date",
            hideInTable: true,
        },
        {
            title: "Status",
            dataIndex: "status",
            valueType: "select",
            valueEnum: {
                active: { text: "Active", status: "Success" },
                fully_depreciated: { text: "Fully Depreciated", status: "Default" },
                disposed: { text: "Disposed", status: "Error" },
                impaired: { text: "Impaired", status: "Warning" }
            },
            filters: true,
            onFilter: true,
            initialValue: "active",
        },
        {
            title: "Notes",
            dataIndex: "notes",
            valueType: "textarea",
            hideInTable: true,
        },
        {
            title: "Created At",
            dataIndex: "createdAt",
            valueType: "dateTime",
            hideInForm: true,
            hideInTable: true,
            initialValue: moment().format(),
        },
        {
            title: "Updated At",
            dataIndex: "updatedAt",
            valueType: "dateTime",
            hideInForm: true,
            hideInTable: true,
        }
    ],
    
    // Initialize the asset with calculated values
    afterSave: async (data, { pouchDatabase, databasePrefix, CRUD_USER }) => {
        if (!data._id) return; // Skip if no ID (shouldn't happen)
        
        // Calculate initial book value if it's a new asset
        if (!data.current_book_value) {
            const assetDoc = await pouchDatabase("fixed_assets", databasePrefix).get(data._id);
            
            // Set initial values
            await pouchDatabase("fixed_assets", databasePrefix).put({
                ...assetDoc,
                current_book_value: data.purchase_cost,
                accumulated_depreciation: 0,
                last_depreciation_date: null,
                updatedAt: new Date().toISOString()
            });
        }
    }
};
