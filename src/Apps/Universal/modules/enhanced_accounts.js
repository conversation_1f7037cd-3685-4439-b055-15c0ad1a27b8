import moment from "moment";


export const enhanced_accounts = {
    name: "Chart of Accounts",
    icon: "BankOutlined",
    path: "/enhanced_accounts",
    collection: "enhanced_accounts",
    singular: "Account",
    parent: "finance",
    columns: [
        {
            title: "Account Code",
            dataIndex: "code",
            isRequired: true,
            sorter: true,
            tooltip: "Unique code for this account (e.g., 1000, 2000, etc.)"
        },
        {
            title: "Account Name",
            dataIndex: "name",
            isRequired: true,
            sorter: true,
        },
        {
            title: "Account Category",
            dataIndex: "category",
            valueType: "select",
            valueEnum: {
                asset: "Asset",
                liability: "Liability",
                equity: "Equity",
                revenue: "Revenue",
                expense: "Expense"
            },
            isRequired: true,
            filters: true,
            onFilter: true,
        },
        {
            title: "Account Type",
            dataIndex: "type",
            valueType: "select",
            valueEnum: {
                // Asset types
                current_asset: "Current Asset",
                non_current_asset: "Non-Current Asset",
                fixed_asset: "Fixed Asset",
                intangible_asset: "Intangible Asset",
                contra_asset: "Contra Asset",
                
                // Liability types
                current_liability: "Current Liability",
                non_current_liability: "Non-Current Liability",
                
                // Equity types
                capital: "Capital",
                retained_earnings: "Retained Earnings",
                reserves: "Reserves",
                
                // Revenue types
                operating_revenue: "Operating Revenue",
                non_operating_revenue: "Non-Operating Revenue",
                
                // Expense types
                operating_expense: "Operating Expense",
                non_operating_expense: "Non-Operating Expense",
                cost_of_goods_sold: "Cost of Goods Sold"
            },
            isRequired: true,
            filters: true,
            onFilter: true,
            dependencies: ['category'],
            fieldProps: (form) => {
                const category = form.getFieldValue('category');
                let options = {};
                
                switch(category) {
                    case 'asset':
                        options = {
                            current_asset: "Current Asset",
                            non_current_asset: "Non-Current Asset",
                            fixed_asset: "Fixed Asset",
                            intangible_asset: "Intangible Asset",
                            contra_asset: "Contra Asset"
                        };
                        break;
                    case 'liability':
                        options = {
                            current_liability: "Current Liability",
                            non_current_liability: "Non-Current Liability"
                        };
                        break;
                    case 'equity':
                        options = {
                            capital: "Capital",
                            retained_earnings: "Retained Earnings",
                            reserves: "Reserves"
                        };
                        break;
                    case 'revenue':
                        options = {
                            operating_revenue: "Operating Revenue",
                            non_operating_revenue: "Non-Operating Revenue"
                        };
                        break;
                    case 'expense':
                        options = {
                            operating_expense: "Operating Expense",
                            non_operating_expense: "Non-Operating Expense",
                            cost_of_goods_sold: "Cost of Goods Sold"
                        };
                        break;
                    default:
                        options = {};
                }
                
                return {
                    options: Object.entries(options).map(([value, label]) => ({
                        value,
                        label
                    }))
                };
            }
        },
        {
            title: "Reporting Group",
            dataIndex: "reporting_group",
            valueType: "select",
            valueEnum: {
                // Asset groups
                cash_and_equivalents: "Cash and Cash Equivalents",
                accounts_receivable: "Accounts Receivable",
                inventory: "Inventory",
                prepaid_expenses: "Prepaid Expenses",
                investments: "Investments",
                property_plant_equipment: "Property, Plant & Equipment",
                accumulated_depreciation: "Accumulated Depreciation",
                intangible_assets: "Intangible Assets",
                accumulated_amortization: "Accumulated Amortization",
                other_assets: "Other Assets",
                
                // Liability groups
                accounts_payable: "Accounts Payable",
                accrued_liabilities: "Accrued Liabilities",
                short_term_debt: "Short-term Debt",
                deferred_revenue: "Deferred Revenue",
                long_term_debt: "Long-term Debt",
                other_liabilities: "Other Liabilities",
                
                // Equity groups
                share_capital: "Share Capital",
                additional_paid_in_capital: "Additional Paid-in Capital",
                treasury_stock: "Treasury Stock",
                retained_earnings: "Retained Earnings",
                other_comprehensive_income: "Other Comprehensive Income",
                
                // Revenue groups
                sales_revenue: "Sales Revenue",
                service_revenue: "Service Revenue",
                interest_income: "Interest Income",
                other_income: "Other Income",
                
                // Expense groups
                cost_of_goods_sold: "Cost of Goods Sold",
                selling_expenses: "Selling Expenses",
                administrative_expenses: "Administrative Expenses",
                depreciation_expense: "Depreciation Expense",
                amortization_expense: "Amortization Expense",
                interest_expense: "Interest Expense",
                tax_expense: "Tax Expense",
                other_expenses: "Other Expenses"
            },
            isRequired: true,
            filters: true,
            onFilter: true,
            dependencies: ['category', 'type'],
            fieldProps: (form) => {
                const category = form.getFieldValue('category');
                const type = form.getFieldValue('type');
                let options = {};
                
                // Filter reporting groups based on category and type
                if (category === 'asset') {
                    if (type === 'current_asset') {
                        options = {
                            cash_and_equivalents: "Cash and Cash Equivalents",
                            accounts_receivable: "Accounts Receivable",
                            inventory: "Inventory",
                            prepaid_expenses: "Prepaid Expenses",
                            other_assets: "Other Assets"
                        };
                    } else if (type === 'non_current_asset' || type === 'fixed_asset') {
                        options = {
                            property_plant_equipment: "Property, Plant & Equipment",
                            investments: "Investments",
                            other_assets: "Other Assets"
                        };
                    } else if (type === 'intangible_asset') {
                        options = {
                            intangible_assets: "Intangible Assets"
                        };
                    } else if (type === 'contra_asset') {
                        options = {
                            accumulated_depreciation: "Accumulated Depreciation",
                            accumulated_amortization: "Accumulated Amortization"
                        };
                    }
                } else if (category === 'liability') {
                    if (type === 'current_liability') {
                        options = {
                            accounts_payable: "Accounts Payable",
                            accrued_liabilities: "Accrued Liabilities",
                            short_term_debt: "Short-term Debt",
                            deferred_revenue: "Deferred Revenue",
                            other_liabilities: "Other Liabilities"
                        };
                    } else if (type === 'non_current_liability') {
                        options = {
                            long_term_debt: "Long-term Debt",
                            other_liabilities: "Other Liabilities"
                        };
                    }
                } else if (category === 'equity') {
                    if (type === 'capital') {
                        options = {
                            share_capital: "Share Capital",
                            additional_paid_in_capital: "Additional Paid-in Capital",
                            treasury_stock: "Treasury Stock"
                        };
                    } else if (type === 'retained_earnings') {
                        options = {
                            retained_earnings: "Retained Earnings"
                        };
                    } else if (type === 'reserves') {
                        options = {
                            other_comprehensive_income: "Other Comprehensive Income"
                        };
                    }
                } else if (category === 'revenue') {
                    if (type === 'operating_revenue') {
                        options = {
                            sales_revenue: "Sales Revenue",
                            service_revenue: "Service Revenue"
                        };
                    } else if (type === 'non_operating_revenue') {
                        options = {
                            interest_income: "Interest Income",
                            other_income: "Other Income"
                        };
                    }
                } else if (category === 'expense') {
                    if (type === 'cost_of_goods_sold') {
                        options = {
                            cost_of_goods_sold: "Cost of Goods Sold"
                        };
                    } else if (type === 'operating_expense') {
                        options = {
                            selling_expenses: "Selling Expenses",
                            administrative_expenses: "Administrative Expenses",
                            depreciation_expense: "Depreciation Expense",
                            amortization_expense: "Amortization Expense"
                        };
                    } else if (type === 'non_operating_expense') {
                        options = {
                            interest_expense: "Interest Expense",
                            tax_expense: "Tax Expense",
                            other_expenses: "Other Expenses"
                        };
                    }
                }
                
                return {
                    options: Object.entries(options).map(([value, label]) => ({
                        value,
                        label
                    }))
                };
            }
        },
        {
            title: "Parent Account",
            dataIndex: "parent_account",
            type: "dbSelect",
            collection: "enhanced_accounts",
            label: ["code", " - ", "name"],
            hideInTable: true,
            tooltip: "Parent account for hierarchical structure"
        },
        {
            title: "Opening Balance",
            dataIndex: "opening_balance",
            valueType: "money",
            isRequired: true,
            initialValue: 0,
        },
        {
            title: "Current Balance",
            dataIndex: "current_balance",
            valueType: "money",
            readonly: true,
            hideInForm: true,
        },
        {
            title: "Is Contra Account",
            dataIndex: "is_contra",
            valueType: "switch",
            initialValue: false,
            tooltip: "Contra accounts have balances that offset their parent account (e.g., Accumulated Depreciation)",
            hideInTable: true,
        },
        {
            title: "Is Reconcilable",
            dataIndex: "is_reconcilable",
            valueType: "switch",
            initialValue: true,
            tooltip: "Indicates if this account can be reconciled (typically bank accounts)",
            hideInTable: true,
        },
        {
            title: "Status",
            dataIndex: "status",
            valueType: "select",
            valueEnum: {
                active: { text: "Active", status: "Success" },
                inactive: { text: "Inactive", status: "Default" },
                archived: { text: "Archived", status: "Error" }
            },
            filters: true,
            onFilter: true,
            initialValue: "active",
        },
        {
            title: "Description",
            dataIndex: "description",
            valueType: "textarea",
            hideInTable: true,
        },
        {
            title: "Created At",
            dataIndex: "createdAt",
            valueType: "dateTime",
            hideInForm: true,
            hideInTable: true,
            initialValue: moment().format(),
        },
        {
            title: "Updated At",
            dataIndex: "updatedAt",
            valueType: "dateTime",
            hideInForm: true,
            hideInTable: true,
        }
    ]
};
