

export const logs = {
  name: "Logs",
  icon: "FlagOutlined",
  path: "/user_management/logs",
  parent: "user_management",
  collection: "logs",
  singular: "Log",
  removeCreate: true,
  removeDelete: true,
  removeUpdate: true,
  multi_Branch: true,
  columns: [
    {
      title: "User",
      dataIndex: "user",
      type: "dbSelect",
      valueType: "select",
      collection: "users",
      label: ["first_name", "last_name"],
      render: (_, record) =>
        record.user && record.user.label && record.user.label,
      // hideInTable: true,
    },
    {
      valueType: "select",
      dataIndex: "action",
      title: "Action",
      width: "lg",
      // filters: true,
      // onFilter: true,
      defaultValue: "default",
      valueEnum: {
        auth: { text: "Authentication", status: "default" },
        rollback: { text: "Rollback", status: "processing" },
        create: { text: "Create", status: "success" },
        edit: { text: "Edit", status: "warning" },
        delete: { text: "Delete", status: "error" },
      },
    },
    {
      title: "Description",
      dataIndex: "description",
      search: true,
    },
  ],
};
