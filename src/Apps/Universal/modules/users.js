

export const users = {
  name: "Users",
  icon: "TeamOutlined",
  path: "/user_management/users",
  parent: "user_management",
  collection: "users",
  singular: "User",
  multi_Branch: true,
  columns: [
    {
      title: "First Name",
      dataIndex: "first_name",
      valueType: "text",
    },
    {
      title: "Last Name",
      dataIndex: "last_name",
      valueType: "text",
    },
    {
      title: "Email",
      dataIndex: "email",
      copyable: true,
      valueType: "text",
    },
    {
      title: "Phone",
      dataIndex: "phone",
      valueType: "text",
    },
    {
      title: "Mobile",
      dataIndex: "mobile",
      valueType: "text",
    },
    {
      title: "Role",
      dataIndex: "role",
      type: "dbSelect",
      sorter: true,
      collection: "roles",
      label: ["name"],
    },
    {
      title: "Branch",
      dataIndex: "branch",
      type: "dbSelect",
      sorter: true,
      collection: "branches",
      label: ["name", "location"],
    },
    {
      title: "ID Type",
      dataIndex: "id_type",
      initialValue: "National ID",
      filters: true,
      onFilter: true,
      valueType: "select",
      hideInTable: true,
      valueEnum: {
        "National ID": { text: "National ID" },
        "Driving Permit": { text: "Driving Permit" },
        Passport: { text: "Passport" },
      },
    },
    {
      title: "ID Number",
      dataIndex: "id_number",
      hideInTable: true,
      valueType: "text",
    },
    {
      title: "Password",
      dataIndex: "password",
      valueType: "password",
      hideInTable: true,
    },
    {
      title: "Avatar",
      dataIndex: "avatar",
      valueType: "image",
      type: "attachment",
      attachmentType: "image/*",
      hideInTable: true,
    },
  ],
};
