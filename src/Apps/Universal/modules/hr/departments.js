

const departments = {
  name: "Departments",
  icon: "ApartmentOutlined",
  path: "/hr/departments",
  parent: "hr",
  collection: "departments",
  singular: "Department",
  multi_Branch: true,
  columns: [
    {
      title: "Name",
      dataIndex: "name",
      valueType: "text",
      sorter: true,
      isRequired: true,
    },
    {
      title: "Code",
      dataIndex: "code",
      valueType: "text",
      sorter: true,
      isRequired: true,
    },
    {
      title: "Department Head",
      dataIndex: "department_head",
      type: "dbSelect",
      collection: "employees",
      label: ["first_name", "last_name"],
      sorter: true,
    },
    {
      title: "Parent Department",
      dataIndex: "parent_department",
      type: "dbSelect",
      collection: "departments",
      label: ["name"],
      hideInTable: true,
    },
    {
      title: "Description",
      dataIndex: "description",
      valueType: "textarea",
      hideInTable: true,
    },
    {
      title: "Budget",
      dataIndex: "budget",
      valueType: "money",
      hideInTable: true,
    },
    {
      title: "Location",
      dataIndex: "location",
      valueType: "text",
      hideInTable: true,
    },
    {
      title: "Branch",
      dataIndex: "branch",
      type: "dbSelect",
      sorter: true,
      collection: "branches",
      label: ["name", "location"],
    },
    {
      title: "Status",
      dataIndex: "status",
      valueType: "select",
      valueEnum: {
        "active": { text: "Active", status: "Success" },
        "inactive": { text: "Inactive", status: "Error" },
      },
      initialValue: "active",
      sorter: true,
    },
    {
      title: "Created Date",
      dataIndex: "createdAt",
      valueType: "dateTime",
      sorter: true,
      hideInForm: true,
    },
  ],
};

export default departments;
