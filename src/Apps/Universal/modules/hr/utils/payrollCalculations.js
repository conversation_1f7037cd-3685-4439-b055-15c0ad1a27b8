

 /**
  * Uganda Payroll Calculation Utilities
 * 
 * This module provides comprehensive payroll calculations including:
 * - PAYE (Pay As You Earn) tax calculations
 * - NSSF (National Social Security Fund) contributions
 * - Gross and net salary calculations
 * - Allowances and deductions
 */

/**
 * Calculate Uganda PAYE tax based on current tax brackets
 * @param {number} grossSalary - Monthly gross salary
 * @returns {number} - Monthly PAYE tax amount
 */
export const calculatePAYE = (grossSalary) => {
  if (!grossSalary || grossSalary <= 0) return 0;

  // Uganda PAYE tax brackets (2024 rates)
  const taxFreeThreshold = 235000; // UGX 235,000 per month
  
  if (grossSalary <= taxFreeThreshold) {
    return 0;
  }

  const taxableAmount = grossSalary - taxFreeThreshold;
  let tax = 0;

  // Tax brackets
  if (taxableAmount <= 135000) {
    // 10% on first UGX 135,000
    tax = taxableAmount * 0.10;
  } else if (taxableAmount <= 545000) {
    // 10% on first UGX 135,000 + 20% on next UGX 410,000
    tax = (135000 * 0.10) + ((taxableAmount - 135000) * 0.20);
  } else {
    // 10% on first UGX 135,000 + 20% on next UGX 410,000 + 30% on remainder
    tax = (135000 * 0.10) + (410000 * 0.20) + ((taxableAmount - 545000) * 0.30);
  }

  return Math.round(tax);
};

/**
 * Calculate Uganda NSSF contribution
 * @param {number} grossSalary - Monthly gross salary
 * @returns {Object} - NSSF contribution details
 */
export const calculateNSSF = (grossSalary) => {
  if (!grossSalary || grossSalary <= 0) {
    return {
      employee: 0,
      employer: 0,
      total: 0
    };
  }

  // NSSF rates: 5% employee + 10% employer on gross salary
  // Maximum contribution ceiling: UGX 200,000 per month
  const maxContributionBase = 200000;
  const contributionBase = Math.min(grossSalary, maxContributionBase);
  
  const employeeContribution = Math.round(contributionBase * 0.05); // 5%
  const employerContribution = Math.round(contributionBase * 0.10); // 10%
  
  return {
    employee: employeeContribution,
    employer: employerContribution,
    total: employeeContribution + employerContribution,
    contributionBase: contributionBase
  };
};

/**
 * Calculate gross salary from basic salary and allowances
 * @param {Object} salaryComponents - Salary components
 * @returns {number} - Gross salary
 */
export const calculateGrossSalary = (salaryComponents) => {
  const {
    basic_salary = 0,
    housing_allowance = 0,
    transport_allowance = 0,
    medical_allowance = 0,
    other_allowances = 0,
    overtime_amount = 0
  } = salaryComponents;

  return Math.round(
    parseFloat(basic_salary) +
    parseFloat(housing_allowance) +
    parseFloat(transport_allowance) +
    parseFloat(medical_allowance) +
    parseFloat(other_allowances) +
    parseFloat(overtime_amount)
  );
};

/**
 * Calculate total deductions
 * @param {Object} deductionComponents - Deduction components
 * @returns {number} - Total deductions
 */
export const calculateTotalDeductions = (deductionComponents) => {
  const {
    tax_deduction = 0,
    nssf_deduction = 0,
    insurance_deduction = 0,
    loan_deduction = 0,
    advance_deduction = 0,
    other_deductions = 0
  } = deductionComponents;

  return Math.round(
    parseFloat(tax_deduction) +
    parseFloat(nssf_deduction) +
    parseFloat(insurance_deduction) +
    parseFloat(loan_deduction) +
    parseFloat(advance_deduction) +
    parseFloat(other_deductions)
  );
};

/**
 * Calculate net salary
 * @param {number} grossSalary - Gross salary
 * @param {number} totalDeductions - Total deductions
 * @returns {number} - Net salary
 */
export const calculateNetSalary = (grossSalary, totalDeductions) => {
  return Math.round(parseFloat(grossSalary) - parseFloat(totalDeductions));
};

/**
 * Comprehensive payroll calculation
 * @param {Object} payrollData - Payroll input data
 * @returns {Object} - Complete payroll calculation results
 */
export const calculatePayroll = (payrollData) => {
  try {
    // Extract salary components
    const salaryComponents = {
      basic_salary: payrollData.basic_salary || 0,
      housing_allowance: payrollData.housing_allowance || 0,
      transport_allowance: payrollData.transport_allowance || 0,
      medical_allowance: payrollData.medical_allowance || 0,
      other_allowances: payrollData.other_allowances || 0,
      overtime_amount: payrollData.overtime_amount || 0
    };

    // Calculate gross salary
    const grossSalary = calculateGrossSalary(salaryComponents);

    // Calculate automatic deductions
    const payeTax = calculatePAYE(grossSalary);
    const nssfContribution = calculateNSSF(grossSalary);

    // Manual deductions (provided by user)
    const manualDeductions = {
      insurance_deduction: payrollData.insurance_deduction || 0,
      loan_deduction: payrollData.loan_deduction || 0,
      advance_deduction: payrollData.advance_deduction || 0,
      other_deductions: payrollData.other_deductions || 0
    };

    // All deductions
    const deductionComponents = {
      tax_deduction: payeTax,
      nssf_deduction: nssfContribution.employee,
      ...manualDeductions
    };

    const totalDeductions = calculateTotalDeductions(deductionComponents);
    const netSalary = calculateNetSalary(grossSalary, totalDeductions);

    return {
      // Salary components
      ...salaryComponents,
      gross_salary: grossSalary,

      // Automatic deductions
      tax_deduction: payeTax,
      nssf_deduction: nssfContribution.employee,
      
      // Manual deductions
      ...manualDeductions,

      // Totals
      total_deductions: totalDeductions,
      net_salary: netSalary,

      // Additional NSSF info
      nssf_employer_contribution: nssfContribution.employer,
      nssf_total_contribution: nssfContribution.total,
      nssf_contribution_base: nssfContribution.contributionBase,

      // Tax info
      tax_free_threshold: 235000,
      taxable_amount: Math.max(0, grossSalary - 235000),

      // Calculation metadata
      calculated_at: new Date().toISOString(),
      calculation_method: 'automatic'
    };
  } catch (error) {
    console.error('Error in payroll calculation:', error);
    throw new Error(`Payroll calculation failed: ${error.message}`);
  }
};

/**
 * Calculate overtime amount
 * @param {number} basicSalary - Basic monthly salary
 * @param {number} overtimeHours - Number of overtime hours
 * @param {number} workingDaysPerMonth - Working days in the month (default 22)
 * @param {number} hoursPerDay - Working hours per day (default 8)
 * @param {number} overtimeRate - Overtime multiplier (default 1.5)
 * @returns {number} - Overtime amount
 */
export const calculateOvertimeAmount = (
  basicSalary, 
  overtimeHours, 
  workingDaysPerMonth = 22, 
  hoursPerDay = 8, 
  overtimeRate = 1.5
) => {
  if (!basicSalary || !overtimeHours || overtimeHours <= 0) return 0;

  const hourlyRate = basicSalary / (workingDaysPerMonth * hoursPerDay);
  const overtimeHourlyRate = hourlyRate * overtimeRate;
  
  return Math.round(overtimeHours * overtimeHourlyRate);
};

/**
 * Calculate attendance-based deductions
 * @param {number} basicSalary - Basic monthly salary
 * @param {number} workingDays - Total working days in month
 * @param {number} absentDays - Number of absent days
 * @returns {number} - Attendance deduction amount
 */
export const calculateAttendanceDeduction = (basicSalary, workingDays, absentDays) => {
  if (!basicSalary || !workingDays || !absentDays || absentDays <= 0) return 0;

  const dailyRate = basicSalary / workingDays;
  return Math.round(dailyRate * absentDays);
};

/**
 * Validate payroll data
 * @param {Object} payrollData - Payroll data to validate
 * @returns {Object} - Validation result
 */
export const validatePayrollData = (payrollData) => {
  const errors = [];
  const warnings = [];

  // Required fields
  if (!payrollData.employee) {
    errors.push('Employee is required');
  }

  if (!payrollData.basic_salary || payrollData.basic_salary <= 0) {
    errors.push('Basic salary must be greater than 0');
  }

  if (!payrollData.month) {
    errors.push('Pay period month is required');
  }

  // Warnings for unusual values
  if (payrollData.basic_salary > 50000000) { // 50M UGX
    warnings.push('Basic salary seems unusually high');
  }

  if (payrollData.overtime_amount > payrollData.basic_salary) {
    warnings.push('Overtime amount exceeds basic salary');
  }

  const totalAllowances = (payrollData.housing_allowance || 0) + 
                         (payrollData.transport_allowance || 0) + 
                         (payrollData.medical_allowance || 0) + 
                         (payrollData.other_allowances || 0);

  if (totalAllowances > payrollData.basic_salary * 2) {
    warnings.push('Total allowances exceed 200% of basic salary');
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
};

/**
 * Format currency for display
 * @param {number} amount - Amount to format
 * @param {string} currency - Currency code (default 'UGX')
 * @returns {string} - Formatted currency string
 */
export const formatCurrency = (amount, currency = 'UGX') => {
  if (!amount && amount !== 0) return 'UGX 0';
  
  return new Intl.NumberFormat('en-UG', {
    style: 'currency',
    currency: currency,
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(amount);
};

export default {
  calculatePAYE,
  calculateNSSF,
  calculateGrossSalary,
  calculateTotalDeductions,
  calculateNetSalary,
  calculatePayroll,
  calculateOvertimeAmount,
  calculateAttendanceDeduction,
  validatePayrollData,
  formatCurrency
};
