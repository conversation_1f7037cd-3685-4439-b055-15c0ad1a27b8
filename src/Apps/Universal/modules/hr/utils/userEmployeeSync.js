

 /**
  * User-Employee Synchronization Utilities
 * 
 * This module provides utilities to maintain data consistency between
 * the users collection and employees collection, minimizing data duplication
 * and ensuring data integrity.
 */

/**
 * Synchronize employee data with linked user data
 * @param {Object} employeeData - The employee data to sync
 * @param {Function} pouchDatabase - Database function
 * @param {String} databasePrefix - Database prefix
 * @returns {Object} - Synchronized employee data
 */
export const syncEmployeeWithUser = async (employeeData, pouchDatabase, databasePrefix) => {
  if (!employeeData.user_id) {
    throw new Error('Employee must be linked to a user account');
  }

  try {
    console.log('Syncing employee with user ID:', employeeData.user_id);

    // Get the linked user data
    const usersDb = pouchDatabase('users', databasePrefix);
    const userData = await usersDb.get(employeeData.user_id);

    console.log('Retrieved user data:', userData);

    if (!userData) {
      throw new Error(`Linked user account not found (ID: ${employeeData.user_id})`);
    }

    // Sync user data to employee record
    const syncedEmployeeData = {
      ...employeeData,
      // Sync basic user information
      first_name: userData.first_name,
      last_name: userData.last_name,
      email: userData.email,
      phone: userData.phone,
      mobile: userData.mobile,
      role: userData.role,
      branch: userData.branch,
      id_type: userData.id_type,
      id_number: userData.id_number,
      avatar: userData.avatar,
      
      // Add reference to user
      user_reference: {
        key: userData._id,
        label: `${userData.first_name} ${userData.last_name}`,
        value: userData._id,
        email: userData.email,
      },
      
      // Maintain sync timestamp
      last_user_sync: new Date().toISOString(),
    };

    return syncedEmployeeData;
  } catch (error) {
    console.error('Error syncing employee with user:', error);
    throw error;
  }
};

/**
 * Update user data when employee data changes
 * @param {Object} employeeData - The updated employee data
 * @param {Function} pouchDatabase - Database function
 * @param {String} databasePrefix - Database prefix
 * @param {Object} currentUser - Current user making the change
 * @returns {Boolean} - Success status
 */
export const updateUserFromEmployee = async (employeeData, pouchDatabase, databasePrefix, currentUser) => {
  if (!employeeData.user_id) {
    return false; // No linked user to update
  }

  try {
    const usersDb = pouchDatabase('users', databasePrefix);
    const userData = await usersDb.get(employeeData.user_id);

    if (!userData) {
      console.warn('Linked user account not found for employee:', employeeData._id);
      return false;
    }

    // Update user data with employee changes (only specific fields)
    const updatedUserData = {
      ...userData,
      // Update fields that might change in HR context
      phone: employeeData.phone || userData.phone,
      mobile: employeeData.mobile || userData.mobile,
      branch: employeeData.branch || userData.branch,
      avatar: employeeData.avatar || userData.avatar,
      
      // Add HR context
      hr_employee_id: employeeData._id,
      hr_employee_number: employeeData.employee_id,
      hr_department: employeeData.department,
      hr_position: employeeData.position,
      hr_employment_date: employeeData.employment_date,
      hr_status: employeeData.status,
      
      // Update metadata
      updated_at: new Date().toISOString(),
      updated_by: currentUser._id,
    };

    await usersDb.save(updatedUserData, currentUser);
    return true;
  } catch (error) {
    console.error('Error updating user from employee:', error);
    return false;
  }
};

/**
 * Create employee from existing user
 * @param {String} userId - User ID to create employee from
 * @param {Object} hrData - Additional HR-specific data
 * @param {Function} pouchDatabase - Database function
 * @param {String} databasePrefix - Database prefix
 * @param {Object} currentUser - Current user making the change
 * @returns {Object} - Created employee data
 */
export const createEmployeeFromUser = async (userId, hrData, pouchDatabase, databasePrefix, currentUser) => {
  try {
    // Get user data
    const usersDb = pouchDatabase('users', databasePrefix);
    const userData = await usersDb.get(userId);

    if (!userData) {
      throw new Error('User not found');
    }

    // Check if employee already exists for this user
    const employeesDb = pouchDatabase('employees', databasePrefix);
    const allEmployees = await employeesDb.getAll();
    const existingEmployee = allEmployees.find(emp => emp.user_id === userId);

    if (existingEmployee) {
      throw new Error('Employee record already exists for this user');
    }

    // Generate employee ID
    const employeeId = await generateEmployeeId(employeesDb);

    // Create employee data
    const employeeData = {
      // Link to user
      user_id: userId,
      
      // Sync user data
      first_name: userData.first_name,
      last_name: userData.last_name,
      email: userData.email,
      phone: userData.phone,
      mobile: userData.mobile,
      role: userData.role,
      branch: userData.branch,
      id_type: userData.id_type,
      id_number: userData.id_number,
      avatar: userData.avatar,
      
      // HR-specific data
      employee_id: employeeId,
      ...hrData,
      
      // Status
      status: hrData.status || 'active',
      
      // Reference
      user_reference: {
        key: userData._id,
        label: `${userData.first_name} ${userData.last_name}`,
        value: userData._id,
        email: userData.email,
      },
      
      // Metadata
      created_at: new Date().toISOString(),
      created_by: currentUser._id,
      last_user_sync: new Date().toISOString(),
    };

    // Save employee
    const savedEmployee = await employeesDb.save(employeeData, currentUser);

    // Update user with HR reference
    await updateUserFromEmployee(savedEmployee, pouchDatabase, databasePrefix, currentUser);

    return savedEmployee;
  } catch (error) {
    console.error('Error creating employee from user:', error);
    throw error;
  }
};

/**
 * Generate unique employee ID
 * @param {Object} employeesDb - Employees database instance
 * @returns {String} - Generated employee ID
 */
const generateEmployeeId = async (employeesDb) => {
  try {
    // Get the latest employee to determine next ID
    const allEmployees = await employeesDb.getAll();
    const employeesWithIds = allEmployees.filter(emp => emp.employee_id);

    let nextNumber = 1;
    if (employeesWithIds.length > 0) {
      // Sort by employee_id to get the latest
      employeesWithIds.sort((a, b) => {
        const aNum = parseInt(a.employee_id.replace('EMP', '')) || 0;
        const bNum = parseInt(b.employee_id.replace('EMP', '')) || 0;
        return bNum - aNum; // Descending order
      });

      const lastId = employeesWithIds[0].employee_id;
      const match = lastId.match(/EMP(\d+)/);
      if (match) {
        nextNumber = parseInt(match[1]) + 1;
      }
    }

    return `EMP${nextNumber.toString().padStart(4, '0')}`;
  } catch (error) {
    console.error('Error generating employee ID:', error);
    // Fallback to timestamp-based ID
    return `EMP${Date.now().toString().slice(-4)}`;
  }
};

/**
 * Validate user-employee relationship
 * @param {Object} employeeData - Employee data to validate
 * @param {Function} pouchDatabase - Database function
 * @param {String} databasePrefix - Database prefix
 * @returns {Object} - Validation result
 */
export const validateUserEmployeeRelationship = async (employeeData, pouchDatabase, databasePrefix) => {
  const validation = {
    isValid: true,
    errors: [],
    warnings: [],
  };

  try {
    if (!employeeData.user_id) {
      validation.isValid = false;
      validation.errors.push('Employee must be linked to a user account');
      return validation;
    }

    // Check if user exists
    const usersDb = pouchDatabase('users', databasePrefix);
    const userData = await usersDb.get(employeeData.user_id);

    if (!userData) {
      validation.isValid = false;
      validation.errors.push('Linked user account not found');
      return validation;
    }

    // Check for data inconsistencies
    if (employeeData.email && employeeData.email !== userData.email) {
      validation.warnings.push('Email mismatch between employee and user records');
    }

    if (employeeData.first_name && employeeData.first_name !== userData.first_name) {
      validation.warnings.push('First name mismatch between employee and user records');
    }

    if (employeeData.last_name && employeeData.last_name !== userData.last_name) {
      validation.warnings.push('Last name mismatch between employee and user records');
    }

    // Check if another employee is linked to the same user
    const employeesDb = pouchDatabase('employees', databasePrefix);
    const allEmployees = await employeesDb.getAll();
    const duplicateEmployee = allEmployees.find(emp =>
      emp.user_id === employeeData.user_id &&
      emp._id !== (employeeData._id || 'new')
    );

    if (duplicateEmployee) {
      validation.isValid = false;
      validation.errors.push('Another employee is already linked to this user account');
    }

  } catch (error) {
    validation.isValid = false;
    validation.errors.push(`Validation error: ${error.message}`);
  }

  return validation;
};

/**
 * Get employee data with synchronized user information
 * @param {String} employeeId - Employee ID
 * @param {Function} pouchDatabase - Database function
 * @param {String} databasePrefix - Database prefix
 * @returns {Object} - Employee data with user info
 */
export const getEmployeeWithUserData = async (employeeId, pouchDatabase, databasePrefix) => {
  try {
    const employeesDb = pouchDatabase('employees', databasePrefix);
    const employeeData = await employeesDb.get(employeeId);

    if (!employeeData) {
      throw new Error('Employee not found');
    }

    if (employeeData.user_id) {
      // Sync with latest user data
      const syncedData = await syncEmployeeWithUser(employeeData, pouchDatabase, databasePrefix);
      return syncedData;
    }

    return employeeData;
  } catch (error) {
    console.error('Error getting employee with user data:', error);
    throw error;
  }
};

export default {
  syncEmployeeWithUser,
  updateUserFromEmployee,
  createEmployeeFromUser,
  validateUserEmployeeRelationship,
  getEmployeeWithUserData,
};
