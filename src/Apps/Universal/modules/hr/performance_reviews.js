

const performance_reviews = {
  name: "Performance Reviews",
  icon: "TrophyOutlined",
  path: "/hr/performance_reviews",
  parent: "hr",
  collection: "performance_reviews",
  singular: "Performance Review",
  multi_Branch: true,
  columns: [
    {
      title: "Employee",
      dataIndex: "employee",
      type: "dbSelect",
      collection: "employees",
      label: ["first_name", "last_name"],
      sorter: true,
      isRequired: true,
    },
    {
      title: "Review Period",
      dataIndex: "review_period",
      valueType: "text",
      sorter: true,
      isRequired: true,
    },
    {
      title: "Review Type",
      dataIndex: "review_type",
      valueType: "select",
      valueEnum: {
        "annual": { text: "Annual Review" },
        "quarterly": { text: "Quarterly Review" },
        "probation": { text: "Probation Review" },
        "promotion": { text: "Promotion Review" },
        "disciplinary": { text: "Disciplinary Review" },
      },
      sorter: true,
      isRequired: true,
    },
    {
      title: "Start Date",
      dataIndex: "start_date",
      valueType: "date",
      sorter: true,
      isRequired: true,
    },
    {
      title: "End Date",
      dataIndex: "end_date",
      valueType: "date",
      sorter: true,
      isRequired: true,
    },
    {
      title: "Reviewer",
      dataIndex: "reviewer",
      type: "dbSelect",
      collection: "employees",
      label: ["first_name", "last_name"],
      sorter: true,
      isRequired: true,
    },
    {
      title: "Overall Rating",
      dataIndex: "overall_rating",
      valueType: "select",
      valueEnum: {
        "5": { text: "Excellent (5)" },
        "4": { text: "Good (4)" },
        "3": { text: "Satisfactory (3)" },
        "2": { text: "Needs Improvement (2)" },
        "1": { text: "Unsatisfactory (1)" },
      },
      sorter: true,
    },
    {
      title: "Goals Achievement",
      dataIndex: "goals_achievement",
      valueType: "digit",
      suffix: "%",
      hideInTable: true,
    },
    {
      title: "Strengths",
      dataIndex: "strengths",
      valueType: "textarea",
      hideInTable: true,
    },
    {
      title: "Areas for Improvement",
      dataIndex: "areas_for_improvement",
      valueType: "textarea",
      hideInTable: true,
    },
    {
      title: "Goals for Next Period",
      dataIndex: "goals_next_period",
      valueType: "textarea",
      hideInTable: true,
    },
    {
      title: "Training Needs",
      dataIndex: "training_needs",
      valueType: "textarea",
      hideInTable: true,
    },
    {
      title: "Employee Comments",
      dataIndex: "employee_comments",
      valueType: "textarea",
      hideInTable: true,
    },
    {
      title: "Manager Comments",
      dataIndex: "manager_comments",
      valueType: "textarea",
      hideInTable: true,
    },
    {
      title: "Status",
      dataIndex: "status",
      valueType: "select",
      valueEnum: {
        "draft": { text: "Draft", status: "Default" },
        "in_progress": { text: "In Progress", status: "Processing" },
        "completed": { text: "Completed", status: "Success" },
        "approved": { text: "Approved", status: "Success" },
      },
      initialValue: "draft",
      sorter: true,
    },
    {
      title: "Review Date",
      dataIndex: "review_date",
      valueType: "date",
      sorter: true,
    },
    {
      title: "Next Review Date",
      dataIndex: "next_review_date",
      valueType: "date",
      hideInTable: true,
    },
    {
      title: "Branch",
      dataIndex: "branch",
      type: "dbSelect",
      sorter: true,
      collection: "branches",
      label: ["name", "location"],
    },
  ],
};

export default performance_reviews;
