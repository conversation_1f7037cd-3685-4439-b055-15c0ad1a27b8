

const payroll = {
  name: "Payroll",
  icon: "DollarOutlined",
  path: "/hr/payroll",
  parent: "hr",
  collection: "payroll",
  singular: "Payroll Record",
  multi_Branch: true,
  columns: [
    {
      title: "Employee",
      dataIndex: "employee",
      type: "dbSelect",
      collection: "employees",
      label: ["first_name", "last_name"],
      sorter: true,
      isRequired: true,
    },
    {
      title: "Pay Period",
      dataIndex: "pay_period",
      valueType: "text",
      sorter: true,
      isRequired: true,
    },
    {
      title: "Month",
      dataIndex: "month",
      valueType: "dateMonth",
      sorter: true,
      isRequired: true,
    },
    {
      title: "Basic Salary",
      dataIndex: "basic_salary",
      valueType: "money",
      sorter: true,
    },
    {
      title: "Housing Allowance",
      dataIndex: "housing_allowance",
      valueType: "money",
      hideInTable: true,
    },
    {
      title: "Transport Allowance",
      dataIndex: "transport_allowance",
      valueType: "money",
      hideInTable: true,
    },
    {
      title: "Medical Allowance",
      dataIndex: "medical_allowance",
      valueType: "money",
      hideInTable: true,
    },
    {
      title: "Other Allowances",
      dataIndex: "other_allowances",
      valueType: "money",
      hideInTable: true,
    },
    {
      title: "Overtime Amount",
      dataIndex: "overtime_amount",
      valueType: "money",
      hideInTable: true,
    },
    {
      valueType: "dependency",
      fieldProps: {
        name: ["basic_salary", "housing_allowance", "transport_allowance", "medical_allowance", "other_allowances", "overtime_amount"],
      },
      columns: ({ basic_salary, housing_allowance, transport_allowance, medical_allowance, other_allowances, overtime_amount }) => {
        const grossSalary = (parseFloat(basic_salary) || 0) +
                           (parseFloat(housing_allowance) || 0) +
                           (parseFloat(transport_allowance) || 0) +
                           (parseFloat(medical_allowance) || 0) +
                           (parseFloat(other_allowances) || 0) +
                           (parseFloat(overtime_amount) || 0);

        return [
          {
            title: "Gross Salary",
            dataIndex: "gross_salary",
            valueType: "money",
            sorter: true,
            fieldProps: {
              disabled: true,
              value: grossSalary,
              style: { backgroundColor: '#f5f5f5' }
            },
            tooltip: "Automatically calculated: Basic Salary + All Allowances + Overtime"
          }
        ];
      }
    },
    {
      valueType: "dependency",
      fieldProps: {
        name: ["basic_salary", "housing_allowance", "transport_allowance", "medical_allowance", "other_allowances", "overtime_amount"],
      },
      columns: ({ basic_salary, housing_allowance, transport_allowance, medical_allowance, other_allowances, overtime_amount }) => {
        const grossSalary = (parseFloat(basic_salary) || 0) +
                           (parseFloat(housing_allowance) || 0) +
                           (parseFloat(transport_allowance) || 0) +
                           (parseFloat(medical_allowance) || 0) +
                           (parseFloat(other_allowances) || 0) +
                           (parseFloat(overtime_amount) || 0);

        // Calculate PAYE tax
        let taxDeduction = 0;
        if (grossSalary > 235000) {
          const taxableAmount = grossSalary - 235000;
          if (taxableAmount <= 135000) {
            taxDeduction = taxableAmount * 0.10;
          } else if (taxableAmount <= 545000) {
            taxDeduction = (135000 * 0.10) + ((taxableAmount - 135000) * 0.20);
          } else {
            taxDeduction = (135000 * 0.10) + (410000 * 0.20) + ((taxableAmount - 545000) * 0.30);
          }
        }

        // Calculate NSSF (5% of gross, max 200,000)
        const nssfDeduction = Math.min(grossSalary * 0.05, 200000);

        return [
          {
            title: "Tax Deduction (PAYE)",
            dataIndex: "tax_deduction",
            valueType: "money",
            hideInTable: true,
            fieldProps: {
              disabled: true,
              value: Math.round(taxDeduction),
              style: { backgroundColor: '#f5f5f5' }
            },
            tooltip: "Automatically calculated based on Uganda PAYE tax brackets"
          },
          {
            title: "NSSF Deduction",
            dataIndex: "nssf_deduction",
            valueType: "money",
            hideInTable: true,
            fieldProps: {
              disabled: true,
              value: Math.round(nssfDeduction),
              style: { backgroundColor: '#f5f5f5' }
            },
            tooltip: "Automatically calculated as 5% of gross salary (max UGX 200,000)"
          }
        ];
      }
    },
    {
      title: "Insurance Deduction",
      dataIndex: "insurance_deduction",
      valueType: "money",
      hideInTable: true,
    },
    {
      title: "Loan Deduction",
      dataIndex: "loan_deduction",
      valueType: "money",
      hideInTable: true,
    },
    {
      title: "Advance Deduction",
      dataIndex: "advance_deduction",
      valueType: "money",
      hideInTable: true,
    },
    {
      title: "Other Deductions",
      dataIndex: "other_deductions",
      valueType: "money",
      hideInTable: true,
    },
    {
      valueType: "dependency",
      fieldProps: {
        name: ["basic_salary", "housing_allowance", "transport_allowance", "medical_allowance", "other_allowances", "overtime_amount", "insurance_deduction", "loan_deduction", "advance_deduction", "other_deductions"],
      },
      columns: ({ basic_salary, housing_allowance, transport_allowance, medical_allowance, other_allowances, overtime_amount, insurance_deduction, loan_deduction, advance_deduction, other_deductions }) => {
        const grossSalary = (parseFloat(basic_salary) || 0) +
                           (parseFloat(housing_allowance) || 0) +
                           (parseFloat(transport_allowance) || 0) +
                           (parseFloat(medical_allowance) || 0) +
                           (parseFloat(other_allowances) || 0) +
                           (parseFloat(overtime_amount) || 0);

        // Calculate PAYE tax
        let taxDeduction = 0;
        if (grossSalary > 235000) {
          const taxableAmount = grossSalary - 235000;
          if (taxableAmount <= 135000) {
            taxDeduction = taxableAmount * 0.10;
          } else if (taxableAmount <= 545000) {
            taxDeduction = (135000 * 0.10) + ((taxableAmount - 135000) * 0.20);
          } else {
            taxDeduction = (135000 * 0.10) + (410000 * 0.20) + ((taxableAmount - 545000) * 0.30);
          }
        }

        // Calculate NSSF (5% of gross, max 200,000)
        const nssfDeduction = Math.min(grossSalary * 0.05, 200000);

        // Calculate total deductions
        const totalDeductions = Math.round(taxDeduction) +
                               Math.round(nssfDeduction) +
                               (parseFloat(insurance_deduction) || 0) +
                               (parseFloat(loan_deduction) || 0) +
                               (parseFloat(advance_deduction) || 0) +
                               (parseFloat(other_deductions) || 0);

        // Calculate net salary
        const netSalary = grossSalary - totalDeductions;

        return [
          {
            title: "Total Deductions",
            dataIndex: "total_deductions",
            valueType: "money",
            sorter: true,
            fieldProps: {
              disabled: true,
              value: totalDeductions,
              style: { backgroundColor: '#f5f5f5' }
            },
            tooltip: "Automatically calculated: Sum of all deductions"
          },
          {
            title: "Net Salary",
            dataIndex: "net_salary",
            valueType: "money",
            sorter: true,
            fieldProps: {
              disabled: true,
              value: Math.round(netSalary),
              style: { backgroundColor: '#f5f5f5', fontWeight: 'bold', color: '#1890ff' }
            },
            tooltip: "Automatically calculated: Gross Salary - Total Deductions"
          }
        ];
      }
    },
    {
      title: "Payment Status",
      dataIndex: "payment_status",
      valueType: "select",
      valueEnum: {
        "pending": { text: "Pending", status: "Processing" },
        "paid": { text: "Paid", status: "Success" },
        "cancelled": { text: "Cancelled", status: "Error" },
        "on_hold": { text: "On Hold", status: "Warning" },
      },
      initialValue: "pending",
      sorter: true,
    },
    {
      title: "Payment Date",
      dataIndex: "payment_date",
      valueType: "date",
      sorter: true,
    },
    {
      title: "Payment Method",
      dataIndex: "payment_method",
      valueType: "select",
      valueEnum: {
        "bank_transfer": { text: "Bank Transfer" },
        "cash": { text: "Cash" },
        "cheque": { text: "Cheque" },
        "mobile_money": { text: "Mobile Money" },
      },
      hideInTable: true,
    },
    {
      title: "Bank Account",
      dataIndex: "bank_account",
      valueType: "text",
      hideInTable: true,
    },
    {
      title: "Notes",
      dataIndex: "notes",
      valueType: "textarea",
      hideInTable: true,
    },
    {
      title: "Branch",
      dataIndex: "branch",
      type: "dbSelect",
      sorter: true,
      collection: "branches",
      label: ["name", "location"],
    },
  ],

  // Calculate and save all computed values before saving
  beforeSave: (data) => {
    // Calculate gross salary
    const grossSalary = (parseFloat(data.basic_salary) || 0) +
                       (parseFloat(data.housing_allowance) || 0) +
                       (parseFloat(data.transport_allowance) || 0) +
                       (parseFloat(data.medical_allowance) || 0) +
                       (parseFloat(data.other_allowances) || 0) +
                       (parseFloat(data.overtime_amount) || 0);

    // Calculate PAYE tax
    let taxDeduction = 0;
    if (grossSalary > 235000) {
      const taxableAmount = grossSalary - 235000;
      if (taxableAmount <= 135000) {
        taxDeduction = taxableAmount * 0.10;
      } else if (taxableAmount <= 545000) {
        taxDeduction = (135000 * 0.10) + ((taxableAmount - 135000) * 0.20);
      } else {
        taxDeduction = (135000 * 0.10) + (410000 * 0.20) + ((taxableAmount - 545000) * 0.30);
      }
    }

    // Calculate NSSF (5% of gross, max 200,000)
    const nssfDeduction = Math.min(grossSalary * 0.05, 200000);

    // Calculate total deductions
    const totalDeductions = Math.round(taxDeduction) +
                           Math.round(nssfDeduction) +
                           (parseFloat(data.insurance_deduction) || 0) +
                           (parseFloat(data.loan_deduction) || 0) +
                           (parseFloat(data.advance_deduction) || 0) +
                           (parseFloat(data.other_deductions) || 0);

    // Calculate net salary
    const netSalary = grossSalary - totalDeductions;

    // Return data with calculated values
    return {
      ...data,
      gross_salary: Math.round(grossSalary),
      tax_deduction: Math.round(taxDeduction),
      nssf_deduction: Math.round(nssfDeduction),
      total_deductions: totalDeductions,
      net_salary: Math.round(netSalary),
      // Add calculation metadata
      calculated_at: new Date().toISOString(),
      calculation_method: 'automatic'
    };
  }
};

export default payroll;
