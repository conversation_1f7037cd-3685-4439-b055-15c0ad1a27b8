      import hrModule from "../../modulesProperties/hr/index.js";


 /**
  * HR System Integration Test
 * This file contains tests to verify that the HR system integrates properly with the user system
 */

// Test data for HR system integration
const testData = {
  // Test employee data that extends user data
  testEmployee: {
    // User fields
    first_name: "<PERSON>",
    last_name: "<PERSON><PERSON>",
    email: "<EMAIL>",
    phone: "+256700123456",
    mobile: "+256700123456",
    role: "employee-role",
    branch: "main-branch",
    status: "active",
    
    // HR-specific fields
    employee_id: "EMP001",
    department: "dept-001",
    position: "Software Developer",
    employment_date: "2024-01-15",
    employment_type: "full_time",
    basic_salary: 2500000,
    housing_allowance: 500000,
    transport_allowance: 200000,
    medical_allowance: 100000,
    manager: "manager-001",
    date_of_birth: "1990-05-15",
    gender: "male",
    address: "123 Main Street, Kampala",
    emergency_contact_name: "<PERSON>",
    emergency_contact_phone: "+256700654321",
    id_type: "National ID",
    id_number: "CM12345678901234",
  },

  // Test department data
  testDepartment: {
    name: "Information Technology",
    code: "IT",
    department_head: "manager-001",
    parent_department: null,
    description: "Responsible for all technology operations",
    budget: 50000000,
    location: "Building A, Floor 3",
    status: "active",
  },

  // Test attendance data
  testAttendance: {
    employee: "emp-001",
    date: "2024-01-15",
    clock_in: "08:00",
    clock_out: "17:00",
    total_hours: 8,
    overtime_hours: 0,
    status: "present",
    location: "Office",
    ip_address: "*************",
    device: "Mozilla/5.0...",
  },

  // Test leave data
  testLeave: {
    employee: "emp-001",
    leave_type: "annual",
    start_date: "2024-02-01",
    end_date: "2024-02-05",
    days_requested: 5,
    reason: "Family vacation",
    status: "pending",
    applied_date: "2024-01-20",
    handover_notes: "All tasks completed, John will cover urgent issues",
    contact_during_leave: "+256700123456",
  },

  // Test payroll data
  testPayroll: {
    employee: "emp-001",
    pay_period: "January 2024",
    month: "2024-01",
    basic_salary: 2500000,
    housing_allowance: 500000,
    transport_allowance: 200000,
    medical_allowance: 100000,
    overtime_amount: 0,
    gross_salary: 3300000,
    tax_deduction: 450000,
    nssf_deduction: 165000,
    insurance_deduction: 33000,
    total_deductions: 648000,
    net_salary: 2652000,
    payment_status: "pending",
    working_days: 22,
    present_days: 22,
    absent_days: 0,
    late_days: 0,
    leave_days: 0,
    overtime_hours: 0,
  },

  // Test performance review data
  testPerformanceReview: {
    employee: "emp-001",
    review_period: "Q4 2023",
    review_type: "quarterly",
    start_date: "2023-10-01",
    end_date: "2023-12-31",
    reviewer: "manager-001",
    overall_rating: "4",
    goals_achievement: 85,
    strengths: "Excellent technical skills, good team collaboration",
    areas_for_improvement: "Could improve time management",
    goals_next_period: "Lead a major project, mentor junior developers",
    training_needs: "Advanced React training, Leadership skills",
    status: "completed",
    review_date: "2024-01-10",
    next_review_date: "2024-04-10",
  },
};

// Integration test functions
const integrationTests = {
  // Test 1: Verify HR modules are properly loaded
  testModuleLoading: () => {
    console.log('🧪 Testing HR module loading...');
    
    try {
      const { modules  } = await import("../index.js"
      
      // Check if HR modules exist
      const hrModules = ['employees', 'departments', 'attendance', 'leaves', 'payroll', 'performance_reviews'];
      const missingModules = hrModules.filter(module => !modules[module]);
      
      if (missingModules.length === 0) {
        console.log('✅ All HR modules loaded successfully');
        return true;
      } else {
        console.log('❌ Missing HR modules:', missingModules);
        return false;
      }
    } catch (error) {
      console.log('❌ Error loading HR modules:', error.message);
      return false;
    }
  },

  // Test 2: Verify HR module properties are properly loaded
  testModuleProperties: () => {
    console.log('🧪 Testing HR module properties...');
    
    try {
      const moduleProperties = await import("../../modulesProperties/index.js"
      
      // Check if HR module properties exist
      const hrProperties = ['hr', 'employees', 'departments', 'attendance', 'leaves', 'payroll', 'performance_reviews'];
      const missingProperties = hrProperties.filter(prop => !moduleProperties[prop]);
      
      if (missingProperties.length === 0) {
        console.log('✅ All HR module properties loaded successfully');
        return true;
      } else {
        console.log('❌ Missing HR module properties:', missingProperties);
        return false;
      }
    } catch (error) {
      console.log('❌ Error loading HR module properties:', error.message);
      return false;
    }
  },

  // Test 3: Verify employee module extends user system properly
  testEmployeeUserIntegration: () => {
    console.log('🧪 Testing employee-user integration...');
    
    try {
      const { employees  } = await import("./employees.js"
      const { users   } = await import("../users.js");
      
      // Check if employee module has user fields
      const userFields = ['first_name', 'last_name', 'email', 'phone', 'mobile', 'role', 'branch'];
      const employeeFields = employees.columns.map(col => col.dataIndex);
      
      const missingUserFields = userFields.filter(field => !employeeFields.includes(field));
      
      if (missingUserFields.length === 0) {
        console.log('✅ Employee module properly extends user system');
        return true;
      } else {
        console.log('❌ Employee module missing user fields:', missingUserFields);
        return false;
      }
    } catch (error) {
      console.log('❌ Error testing employee-user integration:', error.message);
      return false;
    }
  },

  // Test 4: Verify database relationships
  testDatabaseRelationships: () => {
    console.log('🧪 Testing database relationships...');
    
    try {
      const { employees  } = await import("./employees.js"
      const { departments   } = await import("./departments.js");
      const { attendance   } = await import("./attendance.js");
      const { leaves   } = await import("./leaves.js");
      const { payroll   } = await import("./payroll.js");
      const { performance_reviews   } = await import("./performance_reviews.js");
      
      // Check if modules have proper relationships
      const relationships = [
        { module: employees, field: 'department', collection: 'departments' },
        { module: employees, field: 'manager', collection: 'employees' },
        { module: departments, field: 'department_head', collection: 'employees' },
        { module: departments, field: 'parent_department', collection: 'departments' },
        { module: attendance, field: 'employee', collection: 'employees' },
        { module: leaves, field: 'employee', collection: 'employees' },
        { module: payroll, field: 'employee', collection: 'employees' },
        { module: performance_reviews, field: 'employee', collection: 'employees' },
        { module: performance_reviews, field: 'reviewer', collection: 'employees' },
      ];
      
      let allRelationshipsValid = true;
      
      relationships.forEach(rel => {
        const field = rel.module.columns.find(col => col.dataIndex === rel.field);
        if (!field || field.type !== 'dbSelect' || field.collection !== rel.collection) {
          console.log(`❌ Invalid relationship: ${rel.module.name}.${rel.field} -> ${rel.collection}`);
          allRelationshipsValid = false;
        }
      });
      
      if (allRelationshipsValid) {
        console.log('✅ All database relationships are properly configured');
        return true;
      } else {
        return false;
      }
    } catch (error) {
      console.log('❌ Error testing database relationships:', error.message);
      return false;
    }
  },

  // Test 5: Verify HR dashboard integration
  testDashboardIntegration: () => {
    console.log('🧪 Testing HR dashboard integration...');
    
    try {
      const hrDashboard = await import("../../modulesProperties/hr/dashboard.js"
      
      if (hrModule.CustomView && hrDashboard) {
        console.log('✅ HR dashboard integration is properly configured');
        return true;
      } else {
        console.log('❌ HR dashboard integration is not properly configured');
        return false;
      }
    } catch (error) {
      console.log('❌ Error testing HR dashboard integration:', error.message);
      return false;
    }
  },

  // Run all tests
  runAllTests: () => {
    console.log('🚀 Starting HR System Integration Tests...\n');
    
    const tests = [
      integrationTests.testModuleLoading,
      integrationTests.testModuleProperties,
      integrationTests.testEmployeeUserIntegration,
      integrationTests.testDatabaseRelationships,
      integrationTests.testDashboardIntegration,
    ];
    
    let passedTests = 0;
    const totalTests = tests.length;
    
    tests.forEach((test, index) => {
      console.log(`\n--- Test ${index + 1}/${totalTests} ---`);
      if (test()) {
        passedTests++;
      }
    });
    
    console.log('\n' + '='.repeat(50));
    console.log(`📊 Test Results: ${passedTests}/${totalTests} tests passed`);
    
    if (passedTests === totalTests) {
      console.log('🎉 All tests passed! HR system is properly integrated.');
    } else {
      console.log('⚠️  Some tests failed. Please review the issues above.');
    }
    
    return passedTests === totalTests;
  }
};

// Export for use in other files
const moduleExports = {
  testData,
  integrationTests,
};

export default moduleExports;

// Run tests if this file is executed directly
if (require.main === module) {
  integrationTests.runAllTests();
}
