

const leaves = {
  name: "Leave Management",
  icon: "CalendarOutlined",
  path: "/hr/leaves",
  parent: "hr",
  collection: "leaves",
  singular: "Leave Request",
  multi_Branch: true,
  columns: [
    {
      title: "Employee",
      dataIndex: "employee",
      type: "dbSelect",
      collection: "employees",
      label: ["first_name", "last_name"],
      sorter: true,
      isRequired: true,
    },
    {
      title: "Leave Type",
      dataIndex: "leave_type",
      valueType: "select",
      valueEnum: {
        "annual": { text: "Annual Leave" },
        "sick": { text: "Sick Leave" },
        "maternity": { text: "Maternity Leave" },
        "paternity": { text: "Paternity Leave" },
        "emergency": { text: "Emergency Leave" },
        "unpaid": { text: "Unpaid Leave" },
        "study": { text: "Study Leave" },
        "compassionate": { text: "Compassionate Leave" },
      },
      sorter: true,
      isRequired: true,
    },
    {
      title: "Start Date",
      dataIndex: "start_date",
      valueType: "date",
      sorter: true,
      isRequired: true,
    },
    {
      title: "End Date",
      dataIndex: "end_date",
      valueType: "date",
      sorter: true,
      isRequired: true,
    },
    {
      title: "Days Requested",
      dataIndex: "days_requested",
      valueType: "digit",
      sorter: true,
      hideInForm: true,
    },
    {
      title: "Reason",
      dataIndex: "reason",
      valueType: "textarea",
      isRequired: true,
    },
    {
      title: "Status",
      dataIndex: "status",
      valueType: "select",
      valueEnum: {
        "pending": { text: "Pending", status: "Processing" },
        "approved": { text: "Approved", status: "Success" },
        "rejected": { text: "Rejected", status: "Error" },
        "cancelled": { text: "Cancelled", status: "Warning" },
      },
      initialValue: "pending",
      sorter: true,
    },
    {
      title: "Applied Date",
      dataIndex: "applied_date",
      valueType: "date",
      sorter: true,
      hideInForm: true,
    },
    {
      title: "Approved By",
      dataIndex: "approved_by",
      type: "dbSelect",
      collection: "employees",
      label: ["first_name", "last_name"],
      hideInTable: true,
    },
    {
      title: "Approved Date",
      dataIndex: "approved_date",
      valueType: "date",
      hideInTable: true,
    },
    {
      title: "Manager Comments",
      dataIndex: "manager_comments",
      valueType: "textarea",
      hideInTable: true,
    },
    {
      title: "Handover Notes",
      dataIndex: "handover_notes",
      valueType: "textarea",
      hideInTable: true,
    },
    {
      title: "Contact During Leave",
      dataIndex: "contact_during_leave",
      valueType: "text",
      hideInTable: true,
    },
    {
      title: "Return Date",
      dataIndex: "return_date",
      valueType: "date",
      hideInTable: true,
    },
    {
      title: "Branch",
      dataIndex: "branch",
      type: "dbSelect",
      sorter: true,
      collection: "branches",
      label: ["name", "location"],
    },
  ],
};

export default leaves;
