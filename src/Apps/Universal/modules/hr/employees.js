

const EmployeeModuleProperties = {
  name: "Employees",
  icon: "UserOutlined",
  path: "/hr/employees",
  parent: "hr",
  collection: "employees",
  singular: "Employee",
  multi_Branch: true,
  columns: [
    {
      title: "Employee ID",
      dataIndex: "employee_id",
      valueType: "text",
      sorter: true,
      hideInForm: true,
    },
    {
      title: "Link to User Account",
      dataIndex: "user_id",
      type: "dbSelect",
      collection: "users",
      label: ["first_name", "last_name", "email"],
      sorter: true,
      isRequired: true,
      tooltip: "Link this employee to an existing user account to avoid data duplication",
      fieldProps: {
        showSearch: true,
        placeholder: "Search and select user account",
      },
    },
    {
      title: "First Name",
      dataIndex: "first_name",
      valueType: "text",
      sorter: true,
      hideInForm: true, // Will be populated from linked user
      tooltip: "Automatically populated from linked user account",
    },
    {
      title: "Last Name",
      dataIndex: "last_name",
      valueType: "text",
      sorter: true,
      hideInForm: true, // Will be populated from linked user
      tooltip: "Automatically populated from linked user account",
    },
    {
      title: "Email",
      dataIndex: "email",
      copyable: true,
      valueType: "email",
      hideInForm: true, // Will be populated from linked user
      tooltip: "Automatically populated from linked user account",
    },
    {
      title: "Phone",
      dataIndex: "phone",
      valueType: "text",
      hideInForm: true, // Will be populated from linked user
      tooltip: "Automatically populated from linked user account",
    },
    {
      title: "Mobile",
      dataIndex: "mobile",
      valueType: "text",
      hideInForm: true, // Will be populated from linked user
      tooltip: "Automatically populated from linked user account",
    },
    {
      title: "Department",
      dataIndex: "department",
      type: "dbSelect",
      sorter: true,
      collection: "departments",
      label: ["name"],
      isRequired: true,
    },
    {
      title: "Position",
      dataIndex: "position",
      valueType: "text",
      sorter: true,
      isRequired: true,
    },
    {
      title: "Employment Date",
      dataIndex: "employment_date",
      valueType: "date",
      sorter: true,
      isRequired: true,
    },
    {
      title: "Employment Type",
      dataIndex: "employment_type",
      valueType: "select",
      valueEnum: {
        "full_time": { text: "Full Time" },
        "part_time": { text: "Part Time" },
        "contract": { text: "Contract" },
        "intern": { text: "Intern" },
        "consultant": { text: "Consultant" },
      },
      isRequired: true,
    },
    {
      title: "Basic Salary",
      dataIndex: "basic_salary",
      valueType: "money",
      sorter: true,
      hideInTable: true,
    },
    {
      title: "Housing Allowance",
      dataIndex: "housing_allowance",
      valueType: "money",
      hideInTable: true,
    },
    {
      title: "Transport Allowance",
      dataIndex: "transport_allowance",
      valueType: "money",
      hideInTable: true,
    },
    {
      title: "Medical Allowance",
      dataIndex: "medical_allowance",
      valueType: "money",
      hideInTable: true,
    },
    {
      title: "Manager",
      dataIndex: "manager",
      type: "dbSelect",
      collection: "employees",
      label: ["first_name", "last_name"],
      hideInTable: true,
    },
    {
      title: "Role",
      dataIndex: "role",
      type: "dbSelect",
      sorter: true,
      collection: "roles",
      label: ["name"],
      hideInForm: true, // Will be populated from linked user
      tooltip: "Automatically populated from linked user account",
    },
    {
      title: "Branch",
      dataIndex: "branch",
      type: "dbSelect",
      sorter: true,
      collection: "branches",
      label: ["name", "location"],
      hideInForm: true, // Will be populated from linked user
      tooltip: "Automatically populated from linked user account",
    },
    {
      title: "Status",
      dataIndex: "status",
      valueType: "select",
      valueEnum: {
        "active": { text: "Active", status: "Success" },
        "inactive": { text: "Inactive", status: "Warning" },
        "terminated": { text: "Terminated", status: "Error" },
        "on_leave": { text: "On Leave", status: "Processing" },
      },
      initialValue: "active",
      sorter: true,
    },
    {
      title: "Date of Birth",
      dataIndex: "date_of_birth",
      valueType: "date",
      hideInTable: true,
    },
    {
      title: "Gender",
      dataIndex: "gender",
      valueType: "select",
      valueEnum: {
        "male": { text: "Male" },
        "female": { text: "Female" },
        "other": { text: "Other" },
      },
      hideInTable: true,
    },
    {
      title: "Address",
      dataIndex: "address",
      valueType: "textarea",
      hideInTable: true,
    },
    {
      title: "Emergency Contact Name",
      dataIndex: "emergency_contact_name",
      valueType: "text",
      hideInTable: true,
    },
    {
      title: "Emergency Contact Phone",
      dataIndex: "emergency_contact_phone",
      valueType: "text",
      hideInTable: true,
    },
    {
      title: "ID Type",
      dataIndex: "id_type",
      initialValue: "National ID",
      valueType: "select",
      hideInTable: true,
      valueEnum: {
        "National ID": { text: "National ID" },
        "Driving Permit": { text: "Driving Permit" },
        "Passport": { text: "Passport" },
      },
    },
    {
      title: "ID Number",
      dataIndex: "id_number",
      hideInTable: true,
      valueType: "text",
    },
    {
      title: "Avatar",
      dataIndex: "avatar",
      valueType: "image",
      type: "attachment",
      attachmentType: "image/*",
      hideInTable: true,
    },
  ],
};

export default EmployeeModuleProperties;
