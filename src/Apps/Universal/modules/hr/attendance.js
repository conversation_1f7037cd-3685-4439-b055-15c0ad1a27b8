

const attendance = {
  name: "Attendance",
  icon: "ClockCircleOutlined",
  path: "/hr/attendance",
  parent: "hr",
  collection: "attendance",
  singular: "Attendance Record",
  multi_Branch: true,
  columns: [
    {
      title: "Employee",
      dataIndex: "employee",
      type: "dbSelect",
      collection: "employees",
      label: ["first_name", "last_name"],
      sorter: true,
      isRequired: true,
    },
    {
      title: "Date",
      dataIndex: "date",
      valueType: "date",
      sorter: true,
      isRequired: true,
    },
    {
      title: "Clock In",
      dataIndex: "clock_in",
      valueType: "time",
      sorter: true,
    },
    {
      title: "Clock Out",
      dataIndex: "clock_out",
      valueType: "time",
      sorter: true,
    },
    {
      title: "Break Start",
      dataIndex: "break_start",
      valueType: "time",
      hideInTable: true,
    },
    {
      title: "Break End",
      dataIndex: "break_end",
      valueType: "time",
      hideInTable: true,
    },
    {
      title: "Total Hours",
      dataIndex: "total_hours",
      valueType: "digit",
      sorter: true,
      hideInForm: true,
    },
    {
      title: "Overtime Hours",
      dataIndex: "overtime_hours",
      valueType: "digit",
      sorter: true,
      hideInForm: true,
    },
    {
      title: "Status",
      dataIndex: "status",
      valueType: "select",
      valueEnum: {
        "present": { text: "Present", status: "Success" },
        "absent": { text: "Absent", status: "Error" },
        "late": { text: "Late", status: "Warning" },
        "half_day": { text: "Half Day", status: "Processing" },
        "on_leave": { text: "On Leave", status: "Default" },
      },
      sorter: true,
      isRequired: true,
    },
    {
      title: "Location",
      dataIndex: "location",
      valueType: "text",
      hideInTable: true,
    },
    {
      title: "IP Address",
      dataIndex: "ip_address",
      valueType: "text",
      hideInTable: true,
    },
    {
      title: "Device",
      dataIndex: "device",
      valueType: "text",
      hideInTable: true,
    },
    {
      title: "Notes",
      dataIndex: "notes",
      valueType: "textarea",
      hideInTable: true,
    },
    {
      title: "Approved By",
      dataIndex: "approved_by",
      type: "dbSelect",
      collection: "employees",
      label: ["first_name", "last_name"],
      hideInTable: true,
    },
    {
      title: "Branch",
      dataIndex: "branch",
      type: "dbSelect",
      sorter: true,
      collection: "branches",
      label: ["name", "location"],
    },
  ],
};

export default attendance;
