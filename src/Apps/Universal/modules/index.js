import attendance from "./hr/attendance";
import departments from "./hr/departments";
import employees from "./hr/employees";
import leaves from "./hr/leaves";
import payroll from "./hr/payroll";
import performance_reviews from "./hr/performance_reviews";
import { account_reconciliations } from "./account_reconciliations";
import { account_transfers } from "./account_transfers";
import { accounts } from "./accounts";
import { branches } from "./branches";
import { depreciation_entries } from "./depreciation_entries";
import { enhanced_accounts } from "./enhanced_accounts";
import { expense_categories } from "./expense_categories";
import { expenses } from "./expenses";
import { fixed_assets } from "./fixed_assets";
import { inventory_adjustments } from "./inventory_adjustments";
import { inventory_valuations } from "./inventory_valuations";
import { journal_entries } from "./journal_entries";
import { logs } from "./logs";
import { receipts } from "./receipts";
import { requisitions } from "./requisitions";
import { roles } from "./roles";
import { sms_messages } from "./sms_messages";
import { users } from "./users";


export const modules = {
  finance: {
    name: "Finance",
    description: "Finance Management",
    icon: "BankOutlined",
    path: "finance",
    columns: [],
  },
  user_management: {
    name: "User Management",
    description: "User Management",
    icon: "UserOutlined",
    path: "/user_management",
    columns: [],
  },
  hr: {
    name: "Human Resources",
    description: "Human Resources Management",
    icon: "TeamOutlined",
    path: "/hr",
    columns: [],
  },
  administration: {
    name: "Administration",
    description: "Administration",
    icon: "SettingOutlined",
    path: "/administration",
    columns: [],
  },
  expense_categories: expense_categories,
  branches: branches,
  roles: roles,
  users: users,
  logs: logs,
  receipts: receipts,
  expenses: expenses,
  requisitions: requisitions,
  accounts: accounts,
  enhanced_accounts: enhanced_accounts,
  account_transfers: account_transfers,
  account_reconciliations: account_reconciliations,
  fixed_assets: fixed_assets,
  depreciation_entries: depreciation_entries,
  journal_entries: journal_entries,
  inventory_valuations: inventory_valuations,
  inventory_adjustments: inventory_adjustments,
  sms_messages: sms_messages,
  // HR Modules
  employees: employees,
  departments: departments,
  attendance: attendance,
  leaves: leaves,
  payroll: payroll,
  performance_reviews: performance_reviews,
};
