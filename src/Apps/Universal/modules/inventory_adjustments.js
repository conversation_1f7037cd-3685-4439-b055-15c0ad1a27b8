import moment from "moment";


export const inventory_adjustments = {
    name: "Inventory Adjustments",
    icon: "EditOutlined",
    path: "/inventory_adjustments",
    collection: "inventory_adjustments",
    singular: "Inventory Adjustment",
    parent: "inventory",
    columns: [
        {
            title: "Date",
            dataIndex: "date",
            valueType: "date",
            isRequired: true,
            sorter: true,
            initialValue: moment().startOf("day").format(),
        },
        {
            title: "Reference",
            dataIndex: "reference",
            valueType: "text",
            isRequired: true,
        },
        {
            title: "Adjustment Type",
            dataIndex: "adjustment_type",
            valueType: "select",
            valueEnum: {
                physical_count: "Physical Count",
                damaged: "Damaged/Obsolete",
                write_off: "Write-off",
                donation: "Donation",
                internal_use: "Internal Use",
                other: "Other"
            },
            isRequired: true,
            filters: true,
            onFilter: true,
        },
        {
            title: "Items",
            dataIndex: "items",
            valueType: "formList",
            isRequired: true,
            fieldProps: {
                initialValue: [{}],
                creatorButtonProps: {
                    block: true,
                    style: {
                        width: "100%",
                    },
                    creatorButtonText: "Add Item",
                },
            },
            columns: [
                {
                    title: "Product",
                    dataIndex: "product",
                    type: "dbSelect",
                    collection: "products",
                    label: ["name"],
                    isRequired: true,
                    colProps: {
                        md: 8,
                    },
                },
                {
                    title: "Current Quantity",
                    dataIndex: "current_quantity",
                    valueType: "digit",
                    readonly: true,
                    colProps: {
                        md: 4,
                    },
                    fieldProps: {
                        placeholder: "Auto-filled",
                    },
                },
                {
                    title: "New Quantity",
                    dataIndex: "new_quantity",
                    valueType: "digit",
                    isRequired: true,
                    colProps: {
                        md: 4,
                    },
                },
                {
                    title: "Adjustment",
                    dataIndex: "adjustment",
                    valueType: "digit",
                    readonly: true,
                    colProps: {
                        md: 4,
                    },
                    fieldProps: {
                        placeholder: "Auto-calculated",
                    },
                },
                {
                    title: "Unit Cost",
                    dataIndex: "unit_cost",
                    valueType: "money",
                    readonly: true,
                    colProps: {
                        md: 4,
                    },
                    fieldProps: {
                        placeholder: "Auto-filled",
                    },
                },
                {
                    title: "Total Value",
                    dataIndex: "total_value",
                    valueType: "money",
                    readonly: true,
                    colProps: {
                        md: 4,
                    },
                    fieldProps: {
                        placeholder: "Auto-calculated",
                    },
                },
                {
                    title: "Reason",
                    dataIndex: "reason",
                    valueType: "text",
                    colProps: {
                        md: 24,
                    },
                },
            ],
        },
        {
            title: "Inventory Account",
            dataIndex: "inventory_account",
            type: "dbSelect",
            collection: "enhanced_accounts",
            label: ["code", " - ", "name"],
            isRequired: true,
            tooltip: "The asset account where inventory is recorded",
            filters: {
                category: "asset",
                reporting_group: "inventory"
            }
        },
        {
            title: "Adjustment Account",
            dataIndex: "adjustment_account",
            type: "dbSelect",
            collection: "enhanced_accounts",
            label: ["code", " - ", "name"],
            isRequired: true,
            tooltip: "The expense account for inventory adjustments",
            dependencies: ['adjustment_type'],
            fieldProps: (form) => {
                const adjustmentType = form.getFieldValue('adjustment_type');
                let filters = {};
                
                // Set appropriate account filters based on adjustment type
                if (adjustmentType === 'damaged' || adjustmentType === 'write_off') {
                    filters = {
                        category: "expense",
                        reporting_group: "other_expenses"
                    };
                } else if (adjustmentType === 'donation') {
                    filters = {
                        category: "expense",
                        reporting_group: "other_expenses"
                    };
                } else if (adjustmentType === 'internal_use') {
                    filters = {
                        category: "expense",
                        reporting_group: "administrative_expenses"
                    };
                } else {
                    filters = {
                        category: "expense"
                    };
                }
                
                return { filters };
            }
        },
        {
            title: "Status",
            dataIndex: "status",
            valueType: "select",
            valueEnum: {
                draft: { text: "Draft", status: "Default" },
                posted: { text: "Posted", status: "Success" },
                reversed: { text: "Reversed", status: "Error" }
            },
            filters: true,
            onFilter: true,
            initialValue: "draft",
        },
        {
            title: "Notes",
            dataIndex: "notes",
            valueType: "textarea",
            hideInTable: true,
        },
        {
            title: "Created At",
            dataIndex: "createdAt",
            valueType: "dateTime",
            hideInForm: true,
            hideInTable: true,
            initialValue: moment().format(),
        },
        {
            title: "Updated At",
            dataIndex: "updatedAt",
            valueType: "dateTime",
            hideInForm: true,
            hideInTable: true,
        }
    ],
    
    // Auto-fill product details when selected
    onValuesChange: async (changedValues, allValues, form, { pouchDatabase, databasePrefix }) => {
        // Check if a product was selected in any of the items
        if (changedValues.items) {
            const items = allValues.items || [];
            const updatedItems = [...items];
            
            for (let i = 0; i < items.length; i++) {
                const item = items[i];
                
                // If product was just selected or changed
                if (changedValues.items[i]?.product && item.product) {
                    const productId = item.product.value || item.product._id;
                    
                    try {
                        // Get product details
                        const product = await pouchDatabase("products", databasePrefix).get(productId);
                        
                        // Get current quantity from inventory
                        const inventory = await pouchDatabase("inventory", databasePrefix)
                            .find({
                                selector: {
                                    "product.value": productId
                                }
                            });
                        
                        const currentQuantity = inventory.docs.length > 0 
                            ? inventory.docs[0].quantity || 0
                            : 0;
                        
                        // Update the item with product details
                        updatedItems[i] = {
                            ...item,
                            current_quantity: currentQuantity,
                            unit_cost: product.cost || 0
                        };
                        
                        // If new quantity is set, calculate adjustment and total value
                        if (item.new_quantity !== undefined) {
                            const adjustment = item.new_quantity - currentQuantity;
                            const totalValue = Math.abs(adjustment) * (product.cost || 0);
                            
                            updatedItems[i] = {
                                ...updatedItems[i],
                                adjustment,
                                total_value: totalValue
                            };
                        }
                        
                        // Update the form
                        form.setFieldsValue({ items: updatedItems });
                    } catch (error) {
                        console.error("Error fetching product details:", error);
                    }
                }
                
                // If new quantity was changed
                if (changedValues.items[i]?.new_quantity !== undefined && item.current_quantity !== undefined && item.unit_cost !== undefined) {
                    const adjustment = item.new_quantity - item.current_quantity;
                    const totalValue = Math.abs(adjustment) * item.unit_cost;
                    
                    updatedItems[i] = {
                        ...item,
                        adjustment,
                        total_value: totalValue
                    };
                    
                    // Update the form
                    form.setFieldsValue({ items: updatedItems });
                }
            }
        }
    },
    
    // Update inventory and create journal entries when adjustment is posted
    afterSave: async (data, { pouchDatabase, databasePrefix, CRUD_USER }) => {
        if (data.status !== 'posted') return; // Only process posted adjustments
        
        try {
            // Process each item
            if (data.items && Array.isArray(data.items)) {
                // Prepare journal entry
                const journalEntries = [];
                let totalAdjustmentValue = 0;
                
                for (const item of data.items) {
                    const productId = item.product?.value || item.product?._id;
                    if (!productId) continue;
                    
                    // Get the product
                    const productDoc = await pouchDatabase("products", databasePrefix).get(productId);
                    
                    // Get or create inventory record
                    const inventoryResults = await pouchDatabase("inventory", databasePrefix)
                        .find({
                            selector: {
                                "product.value": productId
                            }
                        });
                    
                    let inventoryDoc;
                    if (inventoryResults.docs.length > 0) {
                        inventoryDoc = inventoryResults.docs[0];
                    } else {
                        // Create new inventory record
                        inventoryDoc = {
                            product: {
                                label: productDoc.name,
                                value: productId
                            },
                            quantity: 0,
                            createdAt: new Date().toISOString()
                        };
                    }
                    
                    // Update inventory quantity
                    const newQuantity = item.new_quantity || 0;
                    inventoryDoc.quantity = newQuantity;
                    inventoryDoc.updatedAt = new Date().toISOString();
                    
                    // Save inventory record
                    await pouchDatabase("inventory", databasePrefix).saveDocument(inventoryDoc, CRUD_USER);
                    
                    // Calculate adjustment value for journal entry
                    const adjustmentValue = item.total_value || 0;
                    totalAdjustmentValue += adjustmentValue;
                    
                    // Add to journal entries
                    if (item.adjustment > 0) {
                        // Increase in inventory
                        journalEntries.push({
                            account: data.inventory_account,
                            debit: adjustmentValue,
                            credit: 0,
                            line_description: `Inventory increase for ${productDoc.name}`
                        });
                    } else if (item.adjustment < 0) {
                        // Decrease in inventory
                        journalEntries.push({
                            account: data.adjustment_account,
                            debit: adjustmentValue,
                            credit: 0,
                            line_description: `Inventory decrease for ${productDoc.name}`
                        });
                    }
                }
                
                // Complete the journal entry if there are adjustments
                if (journalEntries.length > 0 && totalAdjustmentValue > 0) {
                    // Add the balancing entry
                    if (data.items.some(item => item.adjustment > 0)) {
                        // For increases, credit the adjustment account
                        journalEntries.push({
                            account: data.adjustment_account,
                            debit: 0,
                            credit: totalAdjustmentValue,
                            line_description: `Balancing entry for inventory adjustment`
                        });
                    } else {
                        // For decreases, credit the inventory account
                        journalEntries.push({
                            account: data.inventory_account,
                            debit: 0,
                            credit: totalAdjustmentValue,
                            line_description: `Balancing entry for inventory adjustment`
                        });
                    }
                    
                    // Create the journal entry
                    await pouchDatabase("journal_entries", databasePrefix).saveDocument({
                        date: data.date,
                        reference: `Inventory Adjustment - ${data.reference}`,
                        description: `Inventory adjustment: ${data.adjustment_type}`,
                        entries: journalEntries,
                        status: 'posted',
                        createdAt: new Date().toISOString(),
                        createdBy: CRUD_USER
                    }, CRUD_USER);
                }
            }
        } catch (error) {
            console.error("Error processing inventory adjustment:", error);
        }
    }
};
