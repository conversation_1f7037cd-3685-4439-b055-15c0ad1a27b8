import moment from "moment";


const baseColumns = [
  {
    valueType: "date",
    dataIndex: "date",
    title: "Date",
    isPrintable: true,
    // noBackDate: true,
    isRequired: true,
    initialValue: moment().startOf("day"),
    render: (_, record) => moment(record.date || record.expense_Date).format("DD MMM YY")
  },
  {
    dataIndex: "expense-category",
    title: "Expense Category",
    type: "dbSelect",
    valueType: "select",
    collection: "expense_categories",
    isPrintable: true,
    isRequired: true,
    label: ["name"],
  },
  {
    dataIndex: "account",
    title: "Account",
    type: "dbSelect",
    valueType: "select",
    collection: "accounts",
    isPrintable: true,
    isRequired: true,
    label: ["name"],
  },
  {
    valueType: "money",
    dataIndex: "amount",
    title: "Amount",
    isPrintable: true,
    isRequired: true,
  },
  {
    valueType: "textarea",
    dataIndex: "description",
    title: "Description",
    isPrintable: true,
  }
];

// Function to inject columns at specific positions
const injectColumns = (baseColumns, injectedColumns) => {
  const result = [...baseColumns];

  injectedColumns.forEach(({ column, position = 'end' }) => {
    if (position === 'end') {
      result.push(column);
    } else if (position === 'start') {
      result.unshift(column);
    } else if (typeof position === 'number') {
      result.splice(position, 0, column);
    } else if (position.after) {
      const index = result.findIndex(col => col.dataIndex === position.after);
      if (index !== -1) {
        result.splice(index + 1, 0, column);
      }
    } else if (position.before) {
      const index = result.findIndex(col => col.dataIndex === position.before);
      if (index !== -1) {
        result.splice(index, 0, column);
      }
    }
  });

  return result;
};

const defaultConfig = {
  name: "Expenses",
  icon: "DollarOutlined",
  path: "/expenses",
  collection: "expenses",
  singular: "Expense",
  parent: "expenditure",
  columns: baseColumns
};

export const createExpensesModule = (config = {}) => {
  const { columns = [], moduleOverrides = {} } = config;

  // Deep clone the default config to avoid mutations
  const finalConfig = JSON.parse(JSON.stringify(defaultConfig));

  // Apply module overrides
  Object.assign(finalConfig, moduleOverrides);

  // Apply column customizations if any
  if (columns.length > 0) {
    finalConfig.columns = injectColumns(baseColumns, columns);
  }

  return finalConfig;
};

// For backwards compatibility
export const expenses = defaultConfig;

// Export base columns for reuse
export const baseExpenseColumns = baseColumns;
