import moment from "moment";


export const inventory_valuations = {
    name: "Inventory Valuations",
    icon: "BarChartOutlined",
    path: "/inventory_valuations",
    collection: "inventory_valuations",
    singular: "Inventory Valuation",
    parent: "inventory",
    columns: [
        {
            title: "Date",
            dataIndex: "date",
            valueType: "date",
            isRequired: true,
            sorter: true,
            initialValue: moment().startOf("day").format(),
        },
        {
            title: "Product",
            dataIndex: "product",
            type: "dbSelect",
            collection: "products",
            label: ["name"],
            isRequired: true,
            filters: true,
            onFilter: true,
        },
        {
            title: "Valuation Method",
            dataIndex: "valuation_method",
            valueType: "select",
            valueEnum: {
                fifo: "First In, First Out (FIFO)",
                lifo: "Last In, First Out (LIFO)",
                weighted_average: "Weighted Average",
                specific_identification: "Specific Identification"
            },
            isRequired: true,
            initialValue: "weighted_average",
            filters: true,
            onFilter: true,
        },
        {
            title: "Quantity",
            dataIndex: "quantity",
            valueType: "digit",
            isRequired: true,
        },
        {
            title: "Unit Cost",
            dataIndex: "unit_cost",
            valueType: "money",
            isRequired: true,
        },
        {
            title: "Total Value",
            dataIndex: "total_value",
            valueType: "money",
            readonly: true,
            hideInForm: true,
        },
        {
            title: "Previous Valuation",
            dataIndex: "previous_valuation",
            valueType: "money",
            readonly: true,
            hideInForm: true,
        },
        {
            title: "Valuation Change",
            dataIndex: "valuation_change",
            valueType: "money",
            readonly: true,
            hideInForm: true,
        },
        {
            title: "Inventory Account",
            dataIndex: "inventory_account",
            type: "dbSelect",
            collection: "enhanced_accounts",
            label: ["code", " - ", "name"],
            isRequired: true,
            tooltip: "The asset account where inventory is recorded",
            filters: {
                category: "asset",
                reporting_group: "inventory"
            }
        },
        {
            title: "COGS Account",
            dataIndex: "cogs_account",
            type: "dbSelect",
            collection: "enhanced_accounts",
            label: ["code", " - ", "name"],
            isRequired: true,
            tooltip: "The expense account for cost of goods sold",
            filters: {
                category: "expense",
                reporting_group: "cost_of_goods_sold"
            }
        },
        {
            title: "Status",
            dataIndex: "status",
            valueType: "select",
            valueEnum: {
                draft: { text: "Draft", status: "Default" },
                posted: { text: "Posted", status: "Success" },
                reversed: { text: "Reversed", status: "Error" }
            },
            filters: true,
            onFilter: true,
            initialValue: "draft",
        },
        {
            title: "Notes",
            dataIndex: "notes",
            valueType: "textarea",
            hideInTable: true,
        },
        {
            title: "Created At",
            dataIndex: "createdAt",
            valueType: "dateTime",
            hideInForm: true,
            hideInTable: true,
            initialValue: moment().format(),
        },
        {
            title: "Updated At",
            dataIndex: "updatedAt",
            valueType: "dateTime",
            hideInForm: true,
            hideInTable: true,
        }
    ],

    // Calculate total value before saving
    beforeSave: (data, context) => {
        if (data.quantity && data.unit_cost) {
            data.total_value = data.quantity * data.unit_cost;
        }
        return data;
    },

    // Update product cost and create journal entries when valuation is posted
    afterSave: async (data, { pouchDatabase, databasePrefix, CRUD_USER }) => {
        if (data.status !== 'posted') return; // Only process posted valuations

        try {
            // Get the product
            const productId = data.product?.value || data.product?._id;
            if (!productId) return;

            const productDoc = await pouchDatabase("products", databasePrefix).get(productId);

            // Get previous valuation
            const previousValuations = await pouchDatabase("inventory_valuations", databasePrefix)
                .find({
                    selector: {
                        "product.value": productId,
                        status: "posted",
                        date: { $lt: data.date }
                    },
                    sort: [{ date: 'desc' }],
                    limit: 1
                });

            const previousValuation = previousValuations.docs.length > 0
                ? previousValuations.docs[0].total_value
                : 0;

            // Calculate valuation change
            const valuationChange = data.total_value - previousValuation;

            // Update the valuation record with previous values
            await pouchDatabase("inventory_valuations", databasePrefix).put({
                ...data,
                previous_valuation: previousValuation,
                valuation_change: valuationChange,
                updatedAt: new Date().toISOString()
            });

            // Update the product cost
            await pouchDatabase("products", databasePrefix).put({
                ...productDoc,
                cost: data.unit_cost,
                valuation_method: data.valuation_method,
                inventory_value: data.total_value,
                updatedAt: new Date().toISOString()
            });

            // Create journal entry if there's a valuation change
            if (Math.abs(valuationChange) > 0.01) {
                await pouchDatabase("journal_entries", databasePrefix).saveDocument({
                    date: data.date,
                    reference: `Inventory Valuation - ${productDoc.name}`,
                    description: `Inventory valuation adjustment for ${productDoc.name}`,
                    entries: valuationChange > 0
                        ? [
                            // Increase in value
                            {
                                account: data.inventory_account,
                                debit: valuationChange,
                                credit: 0,
                                line_description: `Increase in inventory value for ${productDoc.name}`
                            },
                            {
                                account: data.cogs_account,
                                debit: 0,
                                credit: valuationChange,
                                line_description: `Adjustment to COGS for ${productDoc.name}`
                            }
                        ]
                        : [
                            // Decrease in value
                            {
                                account: data.cogs_account,
                                debit: Math.abs(valuationChange),
                                credit: 0,
                                line_description: `Adjustment to COGS for ${productDoc.name}`
                            },
                            {
                                account: data.inventory_account,
                                debit: 0,
                                credit: Math.abs(valuationChange),
                                line_description: `Decrease in inventory value for ${productDoc.name}`
                            }
                        ],
                    status: 'posted',
                    createdAt: new Date().toISOString(),
                    createdBy: CRUD_USER
                }, CRUD_USER);
            }

        } catch (error) {
            console.error("Error processing inventory valuation:", error);
        }
    }
};
