

export const accounts = {
    name: "Accounts",
    icon: "BankOutlined",
    path: "/accounts",
    collection: "accounts",
    singular: "Account",
    parent: "finance",
    columns: [
        {
            title: "Account Name",
            dataIndex: "name",
            isRequired: true,
            sorter: true,
        },
        {
            title: "Account Type",
            dataIndex: "type",
            valueType: "select",
            valueEnum: {
                bank: "Bank Account",
                mobile_money: "Mobile Money",
                petty_cash: "Petty Cash",
                cash_register: "Cash Register",
                other: "Other"
            },
            isRequired: true,
            filters: true,
            onFilter: true,
        },
        {
            title: "Account Number",
            dataIndex: "account_number",
            valueType: "text",
        },
        {
            title: "Bank/Provider",
            dataIndex: "provider",
            valueType: "text",
            hideInTable: true,
        },
        {
            title: "Branch",
            dataIndex: "branch",
            valueType: "text",
            hideInTable: true,
        },
        {
            title: "Balance",
            dataIndex: "balance",
            valueType: "money",
            readonly: true,
        },
        {
            title: "Status",
            dataIndex: "status",
            valueType: "select",
            valueEnum: {
                active: { text: "Active", status: "Success" },
                inactive: { text: "Inactive", status: "Default" },
                frozen: { text: "Frozen", status: "Error" }
            },
            filters: true,
            onFilter: true,
        },
        {
            title: "Description",
            dataIndex: "description",
            valueType: "textarea",
            hideInTable: true,
        }
    ]
};