import moment from "moment";


const baseColumns = [
  {
    title: "Date",
    dataIndex: "date",
    valueType: "date",
    isRequired: true,
    isPrintable: true,
    noBackDate: true,
    initialValue: moment().startOf("day"),
    sorter: true,
    width: 80,
  },
  {
    valueType: "select",
    dataIndex: "pMethod",
    title: "Payment Method",
    width: "lg",
    isRequired: true,
    isPrintable: true,
    valueEnum: {
      cash: { text: "Cash" },
      bank: { text: "Bank Deposit" },
      cheque: { text: "Cheque" },
      mtn: { text: "MTN Mobile Money" },
      airtel: { text: "Airtel Mobile Money" },
      other: { text: "Other" },
    },
  },
  {
    title: "Account",
    dataIndex: "account",
    type: "dbSelect",
    collection: "accounts",
    label: ["name"],
    isPrintable: true,
    width: 100,
  },
  {
    title: "Description",
    dataIndex: "description",
    valueType: "textarea",
    isPrintable: true,
  },
  {
    title: "Amount",
    dataIndex: "amount",
    valueType: "money",
    isRequired: true,
    isPrintable: true,
    sorter: true,
    width: 80,
  },

];

const defaultConfig = {
  name: "Receipts",
  icon: "DollarOutlined",
  path: "/receipts",
  collection: "receipts",
  singular: "Receipt",
  columns: baseColumns,
};

// Function to inject columns at specific positions
const injectColumns = (baseColumns, injectedColumns) => {
  const result = [...baseColumns];

  injectedColumns.forEach(({ column, position = 'end' }) => {
    if (position === 'end') {
      result.push(column);
    } else if (position === 'start') {
      result.unshift(column);
    } else if (typeof position === 'number') {
      result.splice(position, 0, column);
    } else if (position.after) {
      const index = result.findIndex(col => col.dataIndex === position.after);
      if (index !== -1) {
        result.splice(index + 1, 0, column);
      }
    } else if (position.before) {
      const index = result.findIndex(col => col.dataIndex === position.before);
      if (index !== -1) {
        result.splice(index, 0, column);
      }
    }
  });

  return result;
};

export const createReceiptsModule = (config = {}) => {
  const { columns = [], moduleOverrides = {} } = config;

  // Deep clone the default config to avoid mutations
  const finalConfig = JSON.parse(JSON.stringify(defaultConfig));

  // Apply module overrides
  Object.assign(finalConfig, moduleOverrides);

  // Apply column customizations if any
  if (columns.length > 0) {
    finalConfig.columns = injectColumns(baseColumns, columns);
  }

  return finalConfig;
};

// For backwards compatibility
export const receipts = defaultConfig;

// Export base columns for reuse
export const baseReceiptColumns = baseColumns;