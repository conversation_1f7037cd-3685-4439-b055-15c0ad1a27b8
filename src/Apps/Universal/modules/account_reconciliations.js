import moment from "moment";


export const account_reconciliations = {
    name: "Account Reconciliations",
    icon: "ReconciliationOutlined",
    path: "/account_reconciliations",
    collection: "account_reconciliations",
    singular: "Account Reconciliation",
    parent: "finance",
    columns: [
        {
            title: "Date",
            dataIndex: "date",
            valueType: "date",
            isRequired: true,
            isPrintable: true,
            noBackDate: false,
            initialValue: moment().startOf("day"),
            sorter: true,
        },
        {
            title: "Account",
            dataIndex: "account",
            type: "dbSelect",
            collection: "accounts",
            label: ["name"],
            isRequired: true,
            isPrintable: true,
            filters: true,
            onFilter: true,
        },
        {
            title: "Amount",
            dataIndex: "amount",
            valueType: "money",
            isRequired: true,
            isPrintable: true,
            sorter: true,
            tooltip: "Can be positive (credit) or negative (debit)"
        },
        {
            title: "Reason",
            dataIndex: "reason",
            valueType: "textarea",
            isRequired: true,
            isPrintable: true,
        },
        {
            title: "Status",
            dataIndex: "status",
            valueType: "select",
            valueEnum: {
                completed: { text: "Completed", status: "Success" },
                pending: { text: "Pending", status: "Warning" },
                cancelled: { text: "Cancelled", status: "Error" }
            },
            initialValue: "completed",
            filters: true,
            onFilter: true,
        }
    ]
};
