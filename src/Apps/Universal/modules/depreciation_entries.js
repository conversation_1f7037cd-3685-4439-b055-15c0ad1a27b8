import moment from "moment";


export const depreciation_entries = {
    name: "Depreciation Entries",
    icon: "CalculatorOutlined",
    path: "/depreciation_entries",
    collection: "depreciation_entries",
    singular: "Depreciation Entry",
    parent: "finance",
    columns: [
        {
            title: "Date",
            dataIndex: "date",
            valueType: "date",
            isRequired: true,
            sorter: true,
            initialValue: moment().startOf("day").format(),
        },
        {
            title: "Asset",
            dataIndex: "asset",
            type: "dbSelect",
            collection: "fixed_assets",
            label: ["code", " - ", "name"],
            isRequired: true,
            filters: true,
            onFilter: true,
        },
        {
            title: "Depreciation Amount",
            dataIndex: "amount",
            valueType: "money",
            isRequired: true,
        },
        {
            title: "Period Start",
            dataIndex: "period_start",
            valueType: "date",
            isRequired: true,
        },
        {
            title: "Period End",
            dataIndex: "period_end",
            valueType: "date",
            isRequired: true,
        },
        {
            title: "Book Value Before",
            dataIndex: "book_value_before",
            valueType: "money",
            readonly: true,
            hideInForm: true,
        },
        {
            title: "Book Value After",
            dataIndex: "book_value_after",
            valueType: "money",
            readonly: true,
            hideInForm: true,
        },
        {
            title: "Status",
            dataIndex: "status",
            valueType: "select",
            valueEnum: {
                draft: { text: "Draft", status: "Default" },
                posted: { text: "Posted", status: "Success" },
                reversed: { text: "Reversed", status: "Error" }
            },
            filters: true,
            onFilter: true,
            initialValue: "draft",
        },
        {
            title: "Notes",
            dataIndex: "notes",
            valueType: "textarea",
            hideInTable: true,
        },
        {
            title: "Created At",
            dataIndex: "createdAt",
            valueType: "dateTime",
            hideInForm: true,
            hideInTable: true,
            initialValue: moment().format(),
        },
        {
            title: "Updated At",
            dataIndex: "updatedAt",
            valueType: "dateTime",
            hideInForm: true,
            hideInTable: true,
        }
    ],
    
    // Update the fixed asset when a depreciation entry is posted
    afterSave: async (data, { pouchDatabase, databasePrefix, CRUD_USER }) => {
        if (data.status !== 'posted') return; // Only process posted entries
        
        try {
            // Get the fixed asset
            const assetId = data.asset?.value || data.asset?._id;
            if (!assetId) return;
            
            const assetDoc = await pouchDatabase("fixed_assets", databasePrefix).get(assetId);
            
            // Calculate new values
            const currentBookValue = assetDoc.current_book_value || assetDoc.purchase_cost;
            const accumulatedDepreciation = assetDoc.accumulated_depreciation || 0;
            
            const newBookValue = currentBookValue - data.amount;
            const newAccumulatedDepreciation = accumulatedDepreciation + data.amount;
            
            // Update the depreciation entry with book values
            await pouchDatabase("depreciation_entries", databasePrefix).put({
                ...data,
                book_value_before: currentBookValue,
                book_value_after: newBookValue,
                updatedAt: new Date().toISOString()
            });
            
            // Update the fixed asset
            await pouchDatabase("fixed_assets", databasePrefix).put({
                ...assetDoc,
                current_book_value: newBookValue,
                accumulated_depreciation: newAccumulatedDepreciation,
                last_depreciation_date: data.period_end,
                // If fully depreciated, update status
                status: newBookValue <= assetDoc.salvage_value ? 'fully_depreciated' : assetDoc.status,
                updatedAt: new Date().toISOString()
            });
            
            // Create accounting entries
            await pouchDatabase("journal_entries", databasePrefix).saveDocument({
                date: data.date,
                reference: `Depreciation - ${assetDoc.name}`,
                description: `Depreciation for ${assetDoc.name} for period ${moment(data.period_start).format('DD/MM/YYYY')} to ${moment(data.period_end).format('DD/MM/YYYY')}`,
                entries: [
                    {
                        account: assetDoc.depreciation_expense_account,
                        debit: data.amount,
                        credit: 0
                    },
                    {
                        account: assetDoc.accumulated_depreciation_account,
                        debit: 0,
                        credit: data.amount
                    }
                ],
                status: 'posted',
                createdAt: new Date().toISOString(),
                createdBy: CRUD_USER
            }, CRUD_USER);
            
        } catch (error) {
            console.error("Error processing depreciation entry:", error);
        }
    }
};
