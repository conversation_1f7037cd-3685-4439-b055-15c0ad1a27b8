

export const account_transfers = {
    name: "Account Transfers",
    icon: "SwapOutlined",
    path: "/account_transfers",
    collection: "account_transfers",
    singular: "Account Transfer",
    parent: "finance",
    columns: [
        {
            title: "Date",
            dataIndex: "date",
            valueType: "date",
            isRequired: true,
            sorter: true,
        },
        {
            title: "From Account",
            dataIndex: "from_account",
            type: "dbSelect",
            collection: "accounts",
            label: ["name"],
            isRequired: true,
            filters: true,
            onFilter: true,
        },
        {
            title: "To Account",
            dataIndex: "to_account",
            type: "dbSelect",
            collection: "accounts",
            label: ["name"],
            isRequired: true,
            filters: true,
            onFilter: true,
        },
        {
            title: "Amount",
            dataIndex: "amount",
            valueType: "money",
            isRequired: true,
        },
        {
            title: "Transfer Charges",
            dataIndex: "charges",
            valueType: "money",
        },
        {
            title: "Reference",
            dataIndex: "reference",
            valueType: "text",
        },
        {
            title: "Status",
            dataIndex: "status",
            valueType: "select",
            valueEnum: {
                pending: { text: "Pending", status: "Warning" },
                completed: { text: "Completed", status: "Success" },
                failed: { text: "Failed", status: "Error" }
            },
            filters: true,
            onFilter: true,
        },
        {
            title: "Notes",
            dataIndex: "notes",
            valueType: "textarea",
            hideInTable: true,
        }
    ],

    afterSave: async (data, { pouchDatabase, databasePrefix, CRUD_USER }) => {
        if (data.status === 'completed') {
            // Deduct from source account
            const fromAccount = await pouchDatabase('accounts', databasePrefix).get(data.from_account);
            await pouchDatabase('accounts', databasePrefix).saveDocument({
                ...fromAccount,
                balance: fromAccount.balance - data.amount - (data.charges || 0)
            }, CRUD_USER);

            // Add to destination account
            const toAccount = await pouchDatabase('accounts', databasePrefix).get(data.to_account);
            await pouchDatabase('accounts', databasePrefix).saveDocument({
                ...toAccount,
                balance: toAccount.balance + data.amount
            }, CRUD_USER);
        }
    }
};