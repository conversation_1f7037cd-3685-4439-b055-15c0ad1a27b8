import moment from "moment";


const baseColumns = [
  {
    title: "Date",
    dataIndex: "date",
    sorter: true,
    valueType: "date",
    initialValue: moment().startOf("day"),
    isRequired: true,
  },
  {
    valueType: "treeSelect",
    dataIndex: "expense-category",
    title: "Expense Category",
    type: "categoryTreeSelect",
    collection: "expense_categories",
    isPrintable: true,
    isRequired: true,
    label: ["name"],
  },
  {
    title: "Supplier",
    dataIndex: "supplier",
    type: "dbSelect",
    valueType: "select",
    collection: "suppliers",
    label: ["name"],
    isRequired: true,
    sorter: true,
  },
  {
    title: "Requested By",
    dataIndex: "receiver",
    type: "dbSelect",
    valueType: "select",
    collection: "users",
    label: ["first_name", "last_name"],
    sorter: true,
    isRequired: true,
    initialValueAsCurrentUser: true,
  },
  {
    title: "Purpose/Description",
    dataIndex: "purpose",
    valueType: "textarea",
    sorter: true,
    isRequired: true,
  },
  {
    title: "Amount",
    dataIndex: "amount",
    valueType: "digit",
    sorter: true,
    isRequired: true,
  },
];

const defaultConfig = {
  name: "Requisitions",
  icon: "FlagOutlined",
  path: "/requisitions",
  collection: "requisitions",
  singular: "Requisition",
  columns: baseColumns
};

const injectColumns = (baseColumns, customColumns) => {
  let resultColumns = [...baseColumns];

  customColumns.forEach(({ column, position }) => {
    if (position) {
      const targetIndex = position.after
        ? resultColumns.findIndex(col => col.dataIndex === position.after) + 1
        : resultColumns.findIndex(col => col.dataIndex === position.before);

      if (targetIndex !== -1) {
        resultColumns.splice(targetIndex, 0, column);
      } else {
        resultColumns.push(column);
      }
    } else {
      resultColumns.push(column);
    }
  });

  return resultColumns;
};

export const createRequisitionsModule = (config = {}) => {
  const { columns = [], moduleOverrides = {} } = config;

  // Deep clone the default config to avoid mutations
  const finalConfig = JSON.parse(JSON.stringify(defaultConfig));

  // Apply module overrides
  Object.assign(finalConfig, moduleOverrides);

  // Apply column customizations if any
  if (columns.length > 0) {
    finalConfig.columns = injectColumns(baseColumns, columns);
  }

  return finalConfig;
};

// For backwards compatibility
export const requisitions = defaultConfig;

// Export base columns for reuse
export const baseRequisitionColumns = baseColumns;
