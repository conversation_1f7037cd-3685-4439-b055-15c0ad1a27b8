import moment from "moment";


export const journal_entries = {
    name: "Journal Entries",
    icon: "BookOutlined",
    path: "/journal_entries",
    collection: "journal_entries",
    singular: "Journal Entry",
    parent: "finance",
    columns: [
        {
            title: "Date",
            dataIndex: "date",
            valueType: "date",
            isRequired: true,
            sorter: true,
            initialValue: moment().startOf("day").format(),
        },
        {
            title: "Reference",
            dataIndex: "reference",
            valueType: "text",
            isRequired: true,
        },
        {
            title: "Description",
            dataIndex: "description",
            valueType: "textarea",
            isRequired: true,
        },
        {
            title: "Entries",
            dataIndex: "entries",
            valueType: "formList",
            isRequired: true,
            fieldProps: {
                initialValue: [{}],
                creatorButtonProps: {
                    block: true,
                    style: {
                        width: "100%",
                    },
                    creatorButtonText: "Add Line",
                },
            },
            columns: [
                {
                    title: "Account",
                    dataIndex: "account",
                    type: "dbSelect",
                    collection: "enhanced_accounts",
                    label: ["code", " - ", "name"],
                    isRequired: true,
                    colProps: {
                        md: 12,
                    },
                },
                {
                    title: "Debit",
                    dataIndex: "debit",
                    valueType: "money",
                    initialValue: 0,
                    colProps: {
                        md: 6,
                    },
                },
                {
                    title: "Credit",
                    dataIndex: "credit",
                    valueType: "money",
                    initialValue: 0,
                    colProps: {
                        md: 6,
                    },
                },
                {
                    title: "Description",
                    dataIndex: "line_description",
                    valueType: "text",
                    colProps: {
                        md: 24,
                    },
                },
            ],
        },
        {
            title: "Status",
            dataIndex: "status",
            valueType: "select",
            valueEnum: {
                draft: { text: "Draft", status: "Default" },
                posted: { text: "Posted", status: "Success" },
                reversed: { text: "Reversed", status: "Error" }
            },
            filters: true,
            onFilter: true,
            initialValue: "draft",
        },
        {
            title: "Created At",
            dataIndex: "createdAt",
            valueType: "dateTime",
            hideInForm: true,
            hideInTable: true,
            initialValue: moment().format(),
        },
        {
            title: "Updated At",
            dataIndex: "updatedAt",
            valueType: "dateTime",
            hideInForm: true,
            hideInTable: true,
        }
    ],

    // Validate that debits = credits before saving
    beforeSave: (data, context) => {
        // Calculate total debits and credits
        let totalDebits = 0;
        let totalCredits = 0;

        if (data.entries && Array.isArray(data.entries)) {
            data.entries.forEach(entry => {
                totalDebits += Number(entry.debit || 0);
                totalCredits += Number(entry.credit || 0);
            });
        }

        // Check if they balance (within a small tolerance for floating point errors)
        if (Math.abs(totalDebits - totalCredits) > 0.01) {
            throw new Error("Journal entry must balance. Total debits must equal total credits.");
        }

        return data;
    },

    // Update account balances when a journal entry is posted
    afterSave: async (data, { pouchDatabase, databasePrefix, CRUD_USER }) => {
        if (data.status !== 'posted') return; // Only process posted entries

        try {
            // Process each entry line
            if (data.entries && Array.isArray(data.entries)) {
                for (const entry of data.entries) {
                    const accountId = entry.account?.value || entry.account?._id;
                    if (!accountId) continue;

                    // Get the account
                    const accountDoc = await pouchDatabase("enhanced_accounts", databasePrefix).get(accountId);

                    // Calculate the net effect on the account balance
                    const currentBalance = accountDoc.current_balance || accountDoc.opening_balance || 0;
                    let netEffect = 0;

                    // The effect depends on the account type
                    const isDebitNormal = ['asset', 'expense'].includes(accountDoc.category);

                    if (isDebitNormal) {
                        // For asset and expense accounts, debits increase, credits decrease
                        netEffect = (entry.debit || 0) - (entry.credit || 0);
                    } else {
                        // For liability, equity, and revenue accounts, credits increase, debits decrease
                        netEffect = (entry.credit || 0) - (entry.debit || 0);
                    }

                    // For contra accounts, reverse the effect
                    if (accountDoc.is_contra) {
                        netEffect = -netEffect;
                    }

                    // Update the account balance
                    await pouchDatabase("enhanced_accounts", databasePrefix).put({
                        ...accountDoc,
                        current_balance: currentBalance + netEffect,
                        updatedAt: new Date().toISOString()
                    });
                }
            }
        } catch (error) {
            console.error("Error processing journal entry:", error);
        }
    }
};
