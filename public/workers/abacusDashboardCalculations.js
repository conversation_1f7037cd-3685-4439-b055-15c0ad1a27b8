// Abacus Dashboard calculations worker
// This runs in a separate thread to prevent UI blocking during heavy computations

self.onmessage = function(e) {
  const { type, callbackKey, data } = e.data;

  try {
    switch(type) {
      case 'CALCULATE_ABACUS_STATS':
        const stats = calculateAbacusStats(data.invoices, data.expenses, data.selectedBranch);
        self.postMessage({
          type: 'ABACUS_STATS_RESULT',
          callbackKey: callbackKey,
          data: stats,
          success: true
        });
        break;

      case 'CALCULATE_PRODUCT_STATS':
        const productStats = calculateProductStats(data.invoices, data.products, data.selectedBranch);
        self.postMessage({
          type: 'PRODUCT_STATS_RESULT',
          callbackKey: callbackKey,
          data: productStats,
          success: true
        });
        break;

      case 'CALCULATE_DASHBOARD_INSIGHTS':
        const insights = calculateDashboardInsights(data.invoices, data.expenses, data.products, data.purchases);
        self.postMessage({
          type: 'DASHBOARD_INSIGHTS_RESULT',
          callbackKey: callbackKey,
          data: insights,
          success: true
        });
        break;

      default:
        self.postMessage({
          type: 'ERROR',
          callbackKey: callbackKey,
          error: 'Unknown calculation type',
          success: false
        });
    }
  } catch (error) {
    self.postMessage({
      type: 'ERROR',
      callbackKey: callbackKey,
      error: error.message,
      success: false
    });
  }
};

/**
 * Calculate dashboard statistics for abacus (sales and expenses)
 * @param {Array} invoices - Array of invoice documents
 * @param {Array} expenses - Array of expense documents
 * @param {String} selectedBranch - Selected branch ID or null
 * @returns {Object} Object containing all calculated statistics
 */
function calculateAbacusStats(invoices, expenses, selectedBranch) {
  const currentDate = new Date();
  const currentMonth = currentDate.getMonth();
  const currentYear = currentDate.getFullYear();
  const currentWeek = getWeekNumber(currentDate);

  // Previous periods
  const prevMonth = currentMonth === 0 ? 11 : currentMonth - 1;
  const prevYear = currentMonth === 0 ? currentYear - 1 : currentYear;
  const prevWeek = currentWeek - 1;

  // Filter by branch if specified
  const filteredInvoices = selectedBranch && selectedBranch !== "none"
    ? invoices.filter(i => i.branch === selectedBranch)
    : invoices;

  const filteredExpenses = selectedBranch && selectedBranch !== "none"
    ? expenses.filter(e => e.branch === selectedBranch)
    : expenses;

  // Helper function to check if date is in current month
  const isCurrentMonth = (dateStr) => {
    const date = new Date(dateStr);
    return date.getMonth() === currentMonth && date.getFullYear() === currentYear;
  };

  // Helper function to check if date is in previous month
  const isPreviousMonth = (dateStr) => {
    const date = new Date(dateStr);
    return date.getMonth() === prevMonth && date.getFullYear() === prevYear;
  };

  // Helper function to check if date is in current week
  const isCurrentWeek = (dateStr) => {
    const date = new Date(dateStr);
    return getWeekNumber(date) === currentWeek && date.getFullYear() === currentYear;
  };

  // Helper function to check if date is in previous week
  const isPreviousWeek = (dateStr) => {
    const date = new Date(dateStr);
    return getWeekNumber(date) === prevWeek && date.getFullYear() === currentYear;
  };

  // Calculate sales totals
  const calculateSalesTotal = (invoiceList) => {
    return invoiceList.reduce((total, invoice) => {
      if (!invoice.items || !Array.isArray(invoice.items)) return total;
      return total + invoice.items.reduce((itemTotal, item) => {
        const price = parseFloat(item.price) || 0;
        const quantity = parseFloat(item.quantity) || 0;
        return itemTotal + (price * quantity);
      }, 0);
    }, 0);
  };

  // Calculate expense totals
  const calculateExpenseTotal = (expenseList) => {
    return expenseList.reduce((total, expense) => {
      return total + (parseFloat(expense.amount) || 0);
    }, 0);
  };

  // Current month calculations
  const currentMonthInvoices = filteredInvoices.filter(i => i.date && isCurrentMonth(i.date));
  const sales_this_month = calculateSalesTotal(currentMonthInvoices);

  const currentMonthExpenses = filteredExpenses.filter(e => e.date && isCurrentMonth(e.date));
  const expense_this_month = calculateExpenseTotal(currentMonthExpenses);

  // Previous month calculations
  const prevMonthInvoices = filteredInvoices.filter(i => i.date && isPreviousMonth(i.date));
  const sales_prev_month = calculateSalesTotal(prevMonthInvoices);

  const prevMonthExpenses = filteredExpenses.filter(e => e.date && isPreviousMonth(e.date));
  const expense_prev_month = calculateExpenseTotal(prevMonthExpenses);

  // Current week calculations
  const currentWeekInvoices = filteredInvoices.filter(i => i.date && isCurrentWeek(i.date));
  const sales_this_week = calculateSalesTotal(currentWeekInvoices);

  const currentWeekExpenses = filteredExpenses.filter(e => e.date && isCurrentWeek(e.date));
  const expense_this_week = calculateExpenseTotal(currentWeekExpenses);

  // Previous week calculations
  const prevWeekInvoices = filteredInvoices.filter(i => i.date && isPreviousWeek(i.date));
  const sales_prev_week = calculateSalesTotal(prevWeekInvoices);

  const prevWeekExpenses = filteredExpenses.filter(e => e.date && isPreviousWeek(e.date));
  const expense_prev_week = calculateExpenseTotal(prevWeekExpenses);

  // Calculate percentage changes
  const calculatePercentageChange = (current, previous) => {
    if (previous === 0) return current > 0 ? 100 : 0;
    return ((current - previous) / previous) * 100;
  };

  return {
    sales_this_month,
    sales_prev_month,
    sales_month_diff: calculatePercentageChange(sales_this_month, sales_prev_month),
    sales_this_week,
    sales_prev_week,
    sales_week_diff: calculatePercentageChange(sales_this_week, sales_prev_week),
    expense_this_month,
    expense_prev_month,
    expense_month_diff: calculatePercentageChange(expense_this_month, expense_prev_month),
    expense_this_week,
    expense_prev_week,
    expense_week_diff: calculatePercentageChange(expense_this_week, expense_prev_week),
    total_invoices: filteredInvoices.length,
    total_expenses: filteredExpenses.length,
    net_profit_month: sales_this_month - expense_this_month,
    net_profit_week: sales_this_week - expense_this_week
  };
}

/**
 * Calculate product statistics
 * @param {Array} invoices - Array of invoice documents
 * @param {Array} products - Array of product documents
 * @param {String} selectedBranch - Selected branch ID or null
 * @returns {Object} Product statistics
 */
function calculateProductStats(invoices, products, selectedBranch) {
  // Filter invoices by branch if specified
  const filteredInvoices = selectedBranch && selectedBranch !== "none"
    ? invoices.filter(i => i.branch === selectedBranch)
    : invoices;

  // Calculate product sales
  const productSales = {};

  filteredInvoices.forEach(invoice => {
    if (!invoice.items || !Array.isArray(invoice.items)) return;

    invoice.items.forEach(item => {
      if (!item.product || !item.product.value) return;

      const productId = item.product.value;
      const quantity = item.quantity || 0;
      const revenue = (item.price || 0) * quantity;

      if (!productSales[productId]) {
        productSales[productId] = {
          quantity: 0,
          revenue: 0,
          sales_count: 0
        };
      }

      productSales[productId].quantity += quantity;
      productSales[productId].revenue += revenue;
      productSales[productId].sales_count += 1;
    });
  });

  return {
    productSales,
    totalProducts: products.length,
    productsWithSales: Object.keys(productSales).length
  };
}

/**
 * Calculate dashboard insights data
 * @param {Array} invoices - Array of invoice documents
 * @param {Array} expenses - Array of expense documents
 * @param {Array} products - Array of product documents
 * @param {Array} purchases - Array of purchase documents
 * @returns {Object} Dashboard insights data
 */
function calculateDashboardInsights(invoices, expenses, products, purchases) {
  return {
    sales: invoices.map(sale => ({
      ...sale,
      amount: (sale.items && Array.isArray(sale.items)) ?
        sale.items.reduce((pv, cv) => {
          const price = parseFloat(cv.price) || 0;
          const quantity = parseFloat(cv.quantity) || 0;
          return pv + (price * quantity);
        }, 0) : 0
    })),
    purchases: purchases || [],
    inventory: products.map(item => ({
      ...item,
      sales_count: invoices.filter(sale =>
        sale.items && Array.isArray(sale.items) &&
        sale.items.some(i => i.product && i.product.value === item._id)
      ).length
    }))
  };
}

/**
 * Get week number for a given date
 * @param {Date} date - Date object
 * @returns {number} Week number
 */
function getWeekNumber(date) {
  const d = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate()));
  const dayNum = d.getUTCDay() || 7;
  d.setUTCDate(d.getUTCDate() + 4 - dayNum);
  const yearStart = new Date(Date.UTC(d.getUTCFullYear(), 0, 1));
  return Math.ceil((((d - yearStart) / 86400000) + 1) / 7);
}
