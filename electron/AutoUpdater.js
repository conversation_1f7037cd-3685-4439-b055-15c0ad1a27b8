const {
  app,
  <PERSON><PERSON><PERSON><PERSON>indow,
  ipc<PERSON>ain,
  dialog,
} = require("electron");
const log = require("electron-log");
const { autoUpdater } = require("electron-updater");
const https = require("https");

class AutoUpdater {
  constructor(win) {
    this.win = win;
    this.updateCheckInterval = null;
    this.isCheckingForUpdates = false;
    this.setupLogger();
    this.configureAutoUpdater();
    this.setupEventHandlers();

    // Initialize update check when app is ready
    if (app.isReady()) {
      this.initialize();
    } else {
      app.whenReady().then(() => this.initialize());
    }
  }

  checkInternetConnectivity() {
    return new Promise((resolve) => {
      const timeout = setTimeout(() => resolve(false), 5000);

      https.get("https://www.google.com", (res) => {
        clearTimeout(timeout);
        resolve(res.statusCode === 200);
      }).on("error", () => {
        clearTimeout(timeout);
        resolve(false);
      });
    });
  }

  setupLogger() {
    autoUpdater.logger = log;
    autoUpdater.logger.transports.file.level = "info";
    log.info(`AutoUpdater initialized. Current version: ${app.getVersion()}`);
  }

  configureAutoUpdater() {
    autoUpdater.autoDownload = false; // Let user choose when to download
    autoUpdater.disableWebInstaller = true;
    autoUpdater.autoInstallOnAppQuit = true;

    // Security configurations
    autoUpdater.allowPrerelease = false; // Only stable releases
    autoUpdater.allowDowngrade = false; // Prevent downgrade attacks

    // ENHANCED: Enable differential downloads for efficiency
    autoUpdater.disableDifferentialDownload = false;

    // NEW: Configure differential update settings
    autoUpdater.differentialDownloadOptions = {
      useMultipleRangeRequest: true,
      splittingThreshold: 1024 * 1024, // 1MB threshold
    };

    // Set update server URL - electron-updater will automatically detect GitHub releases
    // No need to set feedURL for GitHub provider

    // Additional security: Verify update signatures if code signing is enabled
    // Disabled for now until code signing certificates are set up
    autoUpdater.verifyUpdateCodeSignature = false;
  }

  setupEventHandlers() {
    autoUpdater.on("checking-for-update", () => {
      log.info("Checking for updates...");
      if (this.win && this.win.webContents) {
        this.win.webContents.send("checking-for-update");
      }
    });

    autoUpdater.on("update-available", (info) => {
      log.info(`Update available: ${info.version}`);
      // NEW: Log differential update info
      if (info.files && info.files.length > 0) {
        const totalSize = info.files.reduce((sum, file) => sum + (file.size || 0), 0);
        log.info(`Total download size: ${(totalSize / 1024 / 1024).toFixed(2)} MB`);
      }
      this.showUpdateAvailableDialog(info);
    });

    autoUpdater.on("update-not-available", (info) => {
      log.info(`No update available. Current version: ${app.getVersion()}`);
      if (this.win && this.win.webContents) {
        this.win.webContents.send("update-not-available");
      }
    });

    autoUpdater.on("error", (error) => {
      log.error("Update error:", error);
      this.isCheckingForUpdates = false;

      // Handle different types of errors
      if (error.message && error.message.includes("403")) {
        log.error("Authentication error - check GitHub token");
        this.showErrorDialog(new Error("Update check failed: Authentication error. Please check your GitHub token configuration."));
      } else if (error.message && error.message.includes("ENOTFOUND")) {
        log.error("Network error - no internet connection");
        this.showErrorDialog(new Error("Update check failed: No internet connection."));
      } else {
        this.showErrorDialog(error);
      }
    });

    // NEW: Enhanced download progress with differential info
    autoUpdater.on("download-progress", (progressObj) => {
      const logMessage = `Download progress: ${progressObj.percent.toFixed(2)}% ` +
        `(${(progressObj.transferred / 1024 / 1024).toFixed(2)}MB / ${(progressObj.total / 1024 / 1024).toFixed(2)}MB)`;
      log.info(logMessage);

      if (this.win && this.win.webContents) {
        this.win.webContents.send("download-progress", {
          percent: progressObj.percent,
          transferred: progressObj.transferred,
          total: progressObj.total,
          bytesPerSecond: progressObj.bytesPerSecond
        });
      }
    });

    autoUpdater.on("update-downloaded", (info) => {
      log.info(`Update downloaded: ${info.version}`);
      log.info(`Update file path: ${info.downloadedFile}`);

      // Log update verification info
      if (info.signature) {
        log.info("Update signature verified successfully");
      }

      this.showUpdateDownloadedDialog(info);
    });
  }

  showUpdateAvailableDialog(info) {
    const dialogOpts = {
      type: "info",
      buttons: ["Download Now", "Later"],
      title: "Update Available",
      message: `A new version (${info.version}) is available.`,
      detail: "Would you like to download and install it now?"
    };

    dialog.showMessageBox(this.win, dialogOpts).then((returnValue) => {
      if (returnValue.response === 0) {
        autoUpdater.downloadUpdate();
      }
    });
  }

  showUpdateDownloadedDialog(info) {
    const dialogOpts = {
      type: "info",
      buttons: ["Restart Now", "Later"],
      title: "Update Ready",
      message: `Update ${info.version} has been downloaded.`,
      detail: "The application will restart to apply the update."
    };

    dialog.showMessageBox(this.win, dialogOpts).then((returnValue) => {
      if (returnValue.response === 0) {
        autoUpdater.quitAndInstall();
      }
    });
  }

  showErrorDialog(error) {
    const dialogOpts = {
      type: "error",
      buttons: ["OK"],
      title: "Update Error",
      message: "Failed to check for updates",
      detail: error ? error.toString() : "Unknown error occurred"
    };

    dialog.showMessageBox(this.win, dialogOpts);
  }

  initialize() {
    // Check internet connectivity before checking for updates
    this.checkInternetConnectivity().then((isConnected) => {
      if (isConnected) {
        this.checkForUpdates();
        this.startPeriodicUpdateCheck();
      } else {
        log.info("No internet connection - skipping update check");
      }
    });
  }

  checkForUpdates() {
    if (this.isCheckingForUpdates) {
      log.info("Update check already in progress");
      return;
    }

    this.isCheckingForUpdates = true;

    this.checkInternetConnectivity().then((isConnected) => {
      if (!isConnected) {
        log.error("No internet connection");
        this.isCheckingForUpdates = false;
        return;
      }

      log.info("Checking for updates...");
      autoUpdater.checkForUpdatesAndNotify().catch((error) => {
        log.error("Failed to check for updates:", error);
        this.isCheckingForUpdates = false;
      });
    });
  }

  startPeriodicUpdateCheck() {
    // Check for updates every hour
    const updateCheckInterval = 60 * 60 * 1000; // 1 hour

    this.updateCheckInterval = setInterval(() => {
      this.checkInternetConnectivity().then((isConnected) => {
        if (isConnected) {
          this.checkForUpdates();
        }
      });
    }, updateCheckInterval);
  }

  stopPeriodicUpdateCheck() {
    if (this.updateCheckInterval) {
      clearInterval(this.updateCheckInterval);
      this.updateCheckInterval = null;
    }
  }

  quitAndInstall() {
    autoUpdater.quitAndInstall();
  }

  // Manual update check method for UI
  manualUpdateCheck() {
    log.info("Manual update check requested");
    this.checkForUpdates();
  }

  // Cleanup method
  destroy() {
    this.stopPeriodicUpdateCheck();
  }
}

module.exports = AutoUpdater;
