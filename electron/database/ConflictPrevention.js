/**
 * Simple and Efficient Conflict Prevention Utilities
 * Helps reduce conflicts by improving document versioning and timing
 */

class ConflictPrevention {
  constructor() {
    this.documentLocks = new Map(); // Track documents being modified
    this.lastModificationTimes = new Map(); // Track last modification times
  }

  /**
   * Check if a document is currently being modified
   */
  isDocumentLocked(docId) {
    return this.documentLocks.has(docId);
  }

  /**
   * Lock a document for modification
   */
  lockDocument(docId) {
    this.documentLocks.set(docId, Date.now());
  }

  /**
   * Unlock a document after modification
   */
  unlockDocument(docId) {
    this.documentLocks.delete(docId);
    this.lastModificationTimes.set(docId, Date.now());
  }

  /**
   * Add conflict prevention metadata to a document
   */
  addConflictPreventionMetadata(doc, userId = 'system') {
    const now = new Date().toISOString();
    const timestamp = Date.now();
    
    return {
      ...doc,
      updatedAt: now,
      lastModifiedBy: userId,
      modificationTimestamp: timestamp,
      conflictPreventionVersion: 1
    };
  }

  /**
   * Check if enough time has passed since last modification to safely sync
   */
  isSafeToSync(docId, minimumDelayMs = 2000) {
    const lastModTime = this.lastModificationTimes.get(docId);
    if (!lastModTime) return true;
    
    return (Date.now() - lastModTime) >= minimumDelayMs;
  }

  /**
   * Clean up old tracking data
   */
  cleanup(maxAgeMs = 300000) { // 5 minutes
    const now = Date.now();
    
    // Clean up old locks (in case they weren't properly unlocked)
    for (const [docId, lockTime] of this.documentLocks.entries()) {
      if (now - lockTime > maxAgeMs) {
        this.documentLocks.delete(docId);
      }
    }
    
    // Clean up old modification times
    for (const [docId, modTime] of this.lastModificationTimes.entries()) {
      if (now - modTime > maxAgeMs) {
        this.lastModificationTimes.delete(docId);
      }
    }
  }

  /**
   * Get conflict prevention statistics
   */
  getStats() {
    return {
      lockedDocuments: this.documentLocks.size,
      trackedModifications: this.lastModificationTimes.size,
      oldestLock: this.documentLocks.size > 0 ? 
        Math.min(...Array.from(this.documentLocks.values())) : null,
      oldestModification: this.lastModificationTimes.size > 0 ? 
        Math.min(...Array.from(this.lastModificationTimes.values())) : null
    };
  }
}

// Export singleton instance
const conflictPrevention = new ConflictPrevention();

// Auto-cleanup every 5 minutes
setInterval(() => {
  conflictPrevention.cleanup();
}, 300000);

module.exports = conflictPrevention;
