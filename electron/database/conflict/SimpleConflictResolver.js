/**
 * SIMPLE LAST WRITE WINS CONFLICT RESOLVER
 * 
 * High-performance conflict resolution focused on speed and simplicity.
 * Strategy: Always keep the document with the latest updatedAt timestamp.
 * No complex merging, no UI blocking, minimal database operations.
 * 
 * Key Features:
 * - Last write wins strategy
 * - Minimal database operations
 * - Non-blocking batch processing
 * - No UI interference
 * - High performance
 */

const { EventEmitter } = require('events');

class SimpleConflictResolver extends EventEmitter {
  constructor(eventEmitter) {
    super();
    this.setMaxListeners(50);
    this.eventEmitter = eventEmitter;

    // Simple stats tracking
    this.stats = {
      totalConflicts: 0,
      resolvedConflicts: 0,
      failedResolutions: 0
    };

    // Minimal state tracking
    this.activeResolutions = new Set();
    this.enabled = false; // DISABLED: Conflict detection disabled for maximum performance
    
    // Performance-focused settings (optimized for table performance)
    this.batchQueue = [];
    this.batchProcessing = false;
    this.batchSize = 10; // Smaller batches to avoid UI interference
    this.batchDelay = 5000; // Less frequent processing (5 seconds) to avoid table conflicts
    this.maxRetries = 1; // Minimal retries
    
    console.log('[SimpleConflictResolver] Initialized with Last Write Wins strategy (DISABLED by default)');
  }

  /**
   * Main conflict resolution handler
   */
  async handleConflicts(change, source = 'unknown', databases) {
    if (!this.enabled || !change?.doc?._conflicts?.length) {
      return;
    }

    const docId = change.doc._id;
    const conflicts = change.doc._conflicts;

    // Skip design documents
    if (docId.startsWith('_design/')) {
      return;
    }

    // Skip if already processing
    if (this.activeResolutions.has(docId)) {
      return;
    }

    // Find the database
    const dbKey = this.findDatabaseForDocument(docId, change, databases);
    if (!dbKey) {
      return;
    }

    const dbConfig = databases.get(dbKey);
    if (!dbConfig?.db) {
      return;
    }

    // Add to batch queue
    this.addToBatchQueue(dbConfig.db, docId, conflicts);
    this.stats.totalConflicts++;
  }

  /**
   * Find which database this document belongs to
   */
  findDatabaseForDocument(docId, change, databases) {
    // Simple heuristic: use the database prefix from the document ID
    for (const [dbKey, dbConfig] of databases) {
      if (docId.includes(dbKey) || change.doc._id.includes(dbKey)) {
        return dbKey;
      }
    }
    
    // Fallback: return the first available database
    return databases.keys().next().value;
  }

  /**
   * Add conflict to batch queue
   */
  addToBatchQueue(db, docId, conflicts) {
    this.batchQueue.push({ db, docId, conflicts });
    
    // Start batch processing if not already running
    if (!this.batchProcessing) {
      setTimeout(() => this.processBatchConflicts(), this.batchDelay);
    }
  }

  /**
   * Process conflicts in batches for better performance
   */
  async processBatchConflicts() {
    if (this.batchProcessing || this.batchQueue.length === 0) {
      return;
    }

    this.batchProcessing = true;
    console.log(`[SimpleConflictResolver] Processing ${this.batchQueue.length} conflicts in batch`);

    // Process conflicts in chunks
    while (this.batchQueue.length > 0) {
      const batch = this.batchQueue.splice(0, this.batchSize);
      
      // Process batch in parallel for speed
      const promises = batch.map(({ db, docId, conflicts }) => 
        this.resolveConflictSimple(db, docId, conflicts)
      );
      
      try {
        await Promise.allSettled(promises);
      } catch (error) {
        console.error('[SimpleConflictResolver] Batch processing error:', error);
      }
    }

    this.batchProcessing = false;
    
    // Schedule next batch if queue has new items
    if (this.batchQueue.length > 0) {
      setTimeout(() => this.processBatchConflicts(), this.batchDelay);
    }
  }

  /**
   * SIMPLE LAST WRITE WINS: Resolve conflict with minimal operations
   */
  async resolveConflictSimple(db, docId, conflicts) {
    if (this.activeResolutions.has(docId)) {
      return;
    }

    this.activeResolutions.add(docId);

    try {
      // Get current document with conflicts
      const currentDoc = await db.get(docId, { conflicts: true });
      
      if (!currentDoc._conflicts || currentDoc._conflicts.length === 0) {
        // No conflicts remaining
        this.activeResolutions.delete(docId);
        return;
      }

      // Get all conflicting revisions
      const conflictingDocs = await Promise.all(
        currentDoc._conflicts.map(rev => 
          db.get(docId, { rev }).catch(() => null)
        )
      );

      // Add current document to comparison
      const allDocs = [currentDoc, ...conflictingDocs.filter(doc => doc)];

      // LAST WRITE WINS: Find document with latest updatedAt
      const winningDoc = this.findLatestDocument(allDocs);

      // Prepare winning document (remove conflicts)
      const resolvedDoc = {
        ...winningDoc,
        _rev: currentDoc._rev, // Use current revision
        conflictResolution: {
          resolvedAt: new Date().toISOString(),
          strategy: 'last_write_wins',
          conflictCount: currentDoc._conflicts.length
        }
      };
      delete resolvedDoc._conflicts;

      // Save the winning document
      await db.put(resolvedDoc);

      this.stats.resolvedConflicts++;
      console.log(`[SimpleConflictResolver] Resolved conflict for ${docId} using last write wins`);

    } catch (error) {
      console.error(`[SimpleConflictResolver] Failed to resolve conflict for ${docId}:`, error);
      this.stats.failedResolutions++;
    } finally {
      this.activeResolutions.delete(docId);
    }
  }

  /**
   * Find the document with the latest updatedAt timestamp
   */
  findLatestDocument(docs) {
    return docs.reduce((latest, current) => {
      const latestTime = this.getDocumentTimestamp(latest);
      const currentTime = this.getDocumentTimestamp(current);
      
      return currentTime > latestTime ? current : latest;
    });
  }

  /**
   * Get timestamp from document for comparison
   */
  getDocumentTimestamp(doc) {
    // Try different timestamp fields in order of preference
    const timestampFields = ['updatedAt', 'modifiedAt', 'lastModified', 'createdAt'];
    
    for (const field of timestampFields) {
      if (doc[field]) {
        const timestamp = new Date(doc[field]).getTime();
        if (!isNaN(timestamp)) {
          return timestamp;
        }
      }
    }
    
    // Fallback: use current time (this document becomes "latest")
    return Date.now();
  }

  /**
   * Enable/disable conflict resolution
   */
  setEnabled(enabled) {
    this.enabled = enabled;
    console.log(`[SimpleConflictResolver] ${enabled ? 'Enabled' : 'Disabled'}`);
  }

  /**
   * Get current statistics
   */
  getStats() {
    return {
      ...this.stats,
      activeResolutions: this.activeResolutions.size,
      queueLength: this.batchQueue.length,
      batchProcessing: this.batchProcessing,
      enabled: this.enabled
    };
  }

  /**
   * Clear all pending conflicts (emergency stop)
   */
  clearQueue() {
    this.batchQueue.length = 0;
    this.activeResolutions.clear();
    this.batchProcessing = false;
    console.log('[SimpleConflictResolver] Queue cleared');
  }

  /**
   * Legacy method compatibility
   */
  async resolveConflict(change, db) {
    if (!this.enabled || !change?.doc?._conflicts) {
      return;
    }

    const docId = change.doc._id;
    const conflicts = change.doc._conflicts;

    this.addToBatchQueue(db, docId, conflicts);
    this.stats.totalConflicts++;
  }
}

module.exports = SimpleConflictResolver;
