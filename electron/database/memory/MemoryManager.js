/**
 * Memory Management for Database Operations
 * Handles cleanup, timeout management, and memory optimization
 */

class MemoryManager {
  constructor() {
    this.maxCacheSize = 500;
    this.timeouts = new Set();
    this.intervals = new Set();
    this.isDestroyed = false;
    this.caches = new Map(); // Store multiple cache types
    
    // Setup cleanup interval
    this.setupCleanupInterval();
    
    // Setup process exit handlers
    this.setupExitHandlers();
  }

  /**
   * Setup periodic cleanup
   */
  setupCleanupInterval() {
    const cleanupInterval = setInterval(() => {
      this.performCleanup();
    }, 60000); // Clean up every minute

    this.intervals.add(cleanupInterval);
  }

  /**
   * Setup process exit handlers
   */
  setupExitHandlers() {
    if (process && process.on) {
      const cleanup = () => this.destroy();
      process.on('exit', cleanup);
      process.on('SIGINT', cleanup);
      process.on('SIGTERM', cleanup);
    }
  }

  /**
   * Perform memory cleanup
   */
  performCleanup() {
    if (this.isDestroyed) return;

    try {
      // Clean up caches that exceed max size
      for (const [cacheName, cache] of this.caches) {
        if (cache.size > this.maxCacheSize) {
          console.warn(`[MemoryManager] Clearing ${cache.size} entries from ${cacheName} cache (exceeded ${this.maxCacheSize})`);
          cache.clear();
        }
      }

      // Force garbage collection if available
      if (global && global.gc) {
        global.gc();
      }

      console.log(`[MemoryManager] Memory cleanup completed. Active caches: ${this.caches.size}`);
    } catch (error) {
      console.error('[MemoryManager] Error during memory cleanup:', error);
    }
  }

  /**
   * Create a managed timeout
   */
  createManagedTimeout(callback, delay) {
    const timeoutId = setTimeout(() => {
      this.timeouts.delete(timeoutId);
      if (!this.isDestroyed) {
        callback();
      }
    }, delay);

    this.timeouts.add(timeoutId);
    return timeoutId;
  }

  /**
   * Create a managed interval
   */
  createManagedInterval(callback, delay) {
    const intervalId = setInterval(() => {
      if (!this.isDestroyed) {
        callback();
      }
    }, delay);

    this.intervals.add(intervalId);
    return intervalId;
  }

  /**
   * Clear a managed timeout
   */
  clearManagedTimeout(timeoutId) {
    if (timeoutId) {
      clearTimeout(timeoutId);
      this.timeouts.delete(timeoutId);
    }
  }

  /**
   * Clear a managed interval
   */
  clearManagedInterval(intervalId) {
    if (intervalId) {
      clearInterval(intervalId);
      this.intervals.delete(intervalId);
    }
  }

  /**
   * Register a cache for management
   */
  registerCache(name, cache) {
    this.caches.set(name, cache);
  }

  /**
   * Unregister a cache
   */
  unregisterCache(name) {
    this.caches.delete(name);
  }

  /**
   * Get cache by name
   */
  getCache(name) {
    return this.caches.get(name);
  }

  /**
   * Clear a specific cache
   */
  clearCache(name) {
    const cache = this.caches.get(name);
    if (cache) {
      cache.clear();
      console.log(`[MemoryManager] Cleared cache: ${name}`);
    }
  }

  /**
   * Clear all caches
   */
  clearAllCaches() {
    for (const [name, cache] of this.caches) {
      cache.clear();
      console.log(`[MemoryManager] Cleared cache: ${name}`);
    }
  }

  /**
   * Get memory usage statistics
   */
  getMemoryStats() {
    const cacheStats = {};
    for (const [name, cache] of this.caches) {
      cacheStats[name] = cache.size;
    }

    return {
      managedTimeouts: this.timeouts.size,
      managedIntervals: this.intervals.size,
      caches: cacheStats,
      totalCaches: this.caches.size,
      isDestroyed: this.isDestroyed,
      maxCacheSize: this.maxCacheSize
    };
  }

  /**
   * Set maximum cache size
   */
  setMaxCacheSize(size) {
    this.maxCacheSize = size;
  }

  /**
   * Check if memory manager is healthy
   */
  isHealthy() {
    if (this.isDestroyed) return false;
    
    // Check if we have too many timeouts/intervals
    if (this.timeouts.size > 100 || this.intervals.size > 10) {
      return false;
    }

    // Check if any cache is too large
    for (const cache of this.caches.values()) {
      if (cache.size > this.maxCacheSize * 2) {
        return false;
      }
    }

    return true;
  }

  /**
   * Force cleanup of all resources
   */
  forceCleanup() {
    this.clearAllCaches();
    this.performCleanup();
  }

  /**
   * Comprehensive cleanup and destruction
   */
  destroy() {
    if (this.isDestroyed) return;

    console.log('[MemoryManager] Starting comprehensive cleanup and destruction');
    this.isDestroyed = true;

    try {
      // Clear all timeouts
      for (const timeoutId of this.timeouts) {
        clearTimeout(timeoutId);
      }
      this.timeouts.clear();

      // Clear all intervals
      for (const intervalId of this.intervals) {
        clearInterval(intervalId);
      }
      this.intervals.clear();

      // Clear all caches
      this.clearAllCaches();
      this.caches.clear();

      console.log('[MemoryManager] Comprehensive cleanup completed');
    } catch (error) {
      console.error('[MemoryManager] Error during destruction:', error);
    }
  }

  /**
   * Cleanup method for backward compatibility
   */
  cleanup() {
    this.destroy();
  }
}

module.exports = MemoryManager;
