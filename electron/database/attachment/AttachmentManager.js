/**
 * Attachment Manager for Database Operations
 * Handles all attachment-related operations with simplified logic
 */

const DatabaseUtils = require('../utils/DatabaseUtils');

class AttachmentManager {
  constructor(eventEmitter) {
    this.eventEmitter = eventEmitter;
    this.currentUser = { name: "sys" };
  }

  /**
   * Get an attachment from a document
   */
  async getAttachment(db, dbConfig, id, name) {
    try {
      // Validate input parameters
      if (!DatabaseUtils.validateParams(id, name)) {
        console.log(`[AttachmentManager] Invalid parameters for getAttachment: id='${id}', name='${name}' in ${dbConfig.name}`);
        return null;
      }

      const blob = await db.getAttachment(id, name);

      // Convert blob to base64 in Node.js environment
      if (blob) {
        return this.convertBlobToBase64(blob);
      }

      return null;
    } catch (error) {
      return this.handleAttachmentError(error, 'get', name, id, dbConfig.name);
    }
  }

  /**
   * Put an attachment to a document
   */
  async putAttachment(db, dbConfig, id, name, rev, attachment, type, user) {
    try {
      const result = await db.putAttachment(id, name, rev, attachment, type);

      // Log the operation
      await this.logAttachmentOperation(dbConfig, 'add', id, name, type, user);

      return result;
    } catch (error) {
      console.error(`[AttachmentManager] Put attachment failed for ${dbConfig.name}:`, error);
      throw error;
    }
  }

  /**
   * Remove an attachment from a document
   */
  async removeAttachment(db, dbConfig, id, name, rev, user) {
    try {
      const result = await db.removeAttachment(id, name, rev);

      // Log the operation
      await this.logAttachmentOperation(dbConfig, 'remove', id, name, null, user);

      return result;
    } catch (error) {
      console.error(`[AttachmentManager] Remove attachment failed for ${dbConfig.name}:`, error);
      throw error;
    }
  }

  /**
   * Process attachments from ElectronDB format to PouchDB format
   */
  async processAttachments(data, db) {
    const processedData = { ...data };
    const attachments = {};

    if (data._attachments) {
      for (const [key, attachment] of Object.entries(data._attachments)) {
        try {
          // Convert array back to Buffer for PouchDB
          const buffer = Buffer.from(attachment.data);

          attachments[key] = {
            content_type: attachment.content_type,
            data: buffer
          };
        } catch (error) {
          console.error(`[AttachmentManager] Error processing attachment ${key}:`, error);
        }
      }

      // Replace the _attachments with processed ones
      if (Object.keys(attachments).length > 0) {
        processedData._attachments = attachments;
      } else {
        delete processedData._attachments;
      }
    }

    return processedData;
  }

  /**
   * Convert blob to base64 format
   */
  convertBlobToBase64(blob) {
    try {
      // In Node.js, PouchDB returns a Buffer for attachments
      if (Buffer.isBuffer(blob)) {
        return `data:application/octet-stream;base64,${blob.toString('base64')}`;
      }
      
      // If it's a Blob-like object, convert to buffer first
      if (blob.arrayBuffer) {
        return blob.arrayBuffer().then(arrayBuffer => {
          const buffer = Buffer.from(arrayBuffer);
          return `data:application/octet-stream;base64,${buffer.toString('base64')}`;
        });
      }
      
      // Fallback: try to convert directly
      const buffer = Buffer.from(blob);
      return `data:application/octet-stream;base64,${buffer.toString('base64')}`;
    } catch (error) {
      console.error(`[AttachmentManager] Error converting blob to base64:`, error);
      return null;
    }
  }

  /**
   * Handle attachment errors gracefully
   */
  handleAttachmentError(error, operation, name, id, dbName) {
    // Handle 404 (not found) errors gracefully - this is expected when attachments don't exist
    if (error.status === 404 || error.error === true || error.name === 'not_found') {
      console.log(`[AttachmentManager] Attachment '${name}' not found for document '${id}' in ${dbName}`);
      return null;
    }

    // Log other errors but still return null to prevent crashes
    console.error(`[AttachmentManager] ${operation} attachment failed for ${dbName}:`, error);
    return null;
  }

  /**
   * Log attachment operations
   */
  async logAttachmentOperation(dbConfig, action, id, name, type, user) {
    try {
      // Create log entry
      const logData = {
        description: `${action === 'add' ? 'Added' : 'Removed'} attachment '${name}' ${action === 'add' ? 'to' : 'from'} document ${id} in ${dbConfig.name}`,
        details: { 
          documentId: id, 
          attachmentName: name, 
          ...(type && { attachmentType: type })
        },
        user: user || this.currentUser,
        action: `attachment_${action}`,
        type: "attachment",
        sourceDatabase: dbConfig.name,
        databasePrefix: dbConfig.databasePrefix,
        branch: dbConfig.branch
      };

      // Emit log event (let the main handler deal with actual logging)
      this.eventEmitter.emit('logOperation', {
        databasePrefix: dbConfig.databasePrefix,
        logData
      });

    } catch (logError) {
      // Silent error handling for logging
      console.warn(`[AttachmentManager] Failed to log attachment ${action} operation:`, logError);
    }
  }

  /**
   * Validate attachment parameters
   */
  validateAttachmentParams(id, name, rev) {
    if (!id || typeof id !== 'string') {
      throw new Error('Invalid document ID');
    }
    if (!name || typeof name !== 'string') {
      throw new Error('Invalid attachment name');
    }
    if (rev && typeof rev !== 'string') {
      throw new Error('Invalid revision');
    }
    return true;
  }

  /**
   * Get attachment info without downloading the content
   */
  async getAttachmentInfo(db, id, name) {
    try {
      const doc = await db.get(id, { attachments: false });
      
      if (doc._attachments && doc._attachments[name]) {
        return {
          name,
          content_type: doc._attachments[name].content_type,
          length: doc._attachments[name].length,
          digest: doc._attachments[name].digest,
          stub: doc._attachments[name].stub
        };
      }
      
      return null;
    } catch (error) {
      return this.handleAttachmentError(error, 'info', name, id, 'unknown');
    }
  }

  /**
   * List all attachments for a document
   */
  async listAttachments(db, id) {
    try {
      const doc = await db.get(id, { attachments: false });
      
      if (doc._attachments) {
        return Object.keys(doc._attachments).map(name => ({
          name,
          ...doc._attachments[name]
        }));
      }
      
      return [];
    } catch (error) {
      console.error(`[AttachmentManager] List attachments failed for ${id}:`, error);
      return [];
    }
  }

  /**
   * Check if document has attachments
   */
  async hasAttachments(db, id) {
    try {
      const doc = await db.get(id, { attachments: false });
      return !!(doc._attachments && Object.keys(doc._attachments).length > 0);
    } catch (error) {
      return false;
    }
  }

  /**
   * Get attachment count for a document
   */
  async getAttachmentCount(db, id) {
    try {
      const doc = await db.get(id, { attachments: false });
      return doc._attachments ? Object.keys(doc._attachments).length : 0;
    } catch (error) {
      return 0;
    }
  }
}

module.exports = AttachmentManager;
