# Database Handler - Modular Architecture

This document describes the refactored DatabaseHandler architecture that follows KISS (Keep It Simple, Stupid) and DRY (Don't Repeat Yourself) principles.

## Overview

The DatabaseHandler has been refactored from a monolithic 1975-line file into a modular architecture with 585 lines in the main class and specialized modules for different concerns.

## Architecture

### Main Components

#### 1. DatabaseHandler (Main Class)
- **File**: `DatabaseHandler.js`
- **Purpose**: Coordination and orchestration of database operations
- **Responsibilities**:
  - Database initialization and management
  - Event handling and coordination
  - Public API interface
  - Logging operations

#### 2. DatabaseUtils
- **File**: `utils/DatabaseUtils.js`
- **Purpose**: Common utility functions
- **Responsibilities**:
  - Document ID generation
  - Reference number generation
  - Deep merging with array deduplication
  - Object key sorting
  - Parameter validation
  - Conflict resolution metadata creation

#### 3. CoreDatabaseOperations
- **File**: `operations/CoreDatabaseOperations.js`
- **Purpose**: Basic CRUD operations
- **Responsibilities**:
  - Document creation, reading, updating, deletion
  - Bulk operations
  - Find operations with Mango queries
  - Index creation
  - Attachment processing

#### 4. ConflictResolver
- **File**: `conflict/ConflictResolver.js`
- **Purpose**: Conflict resolution logic
- **Responsibilities**:
  - Simple and effective conflict resolution
  - Orphaned conflict cleanup
  - Batch conflict resolution
  - Conflict statistics tracking

#### 5. MemoryManager
- **File**: `memory/MemoryManager.js`
- **Purpose**: Memory optimization and cleanup
- **Responsibilities**:
  - Cache management
  - Timeout and interval management
  - Memory leak prevention
  - Periodic cleanup operations
  - Resource destruction

#### 6. SyncManager
- **File**: `sync/SyncManager.js`
- **Purpose**: Synchronization operations
- **Responsibilities**:
  - Database synchronization
  - Organization-based filtering
  - LAN authentication
  - Sync event handling
  - Sync status management

#### 7. AttachmentManager
- **File**: `attachment/AttachmentManager.js`
- **Purpose**: Attachment operations
- **Responsibilities**:
  - Attachment retrieval and storage
  - Blob to base64 conversion
  - Attachment metadata management
  - Error handling for attachments

## Benefits of the Modular Architecture

### KISS Principles Applied
1. **Single Responsibility**: Each module has one clear purpose
2. **Simplified Logic**: Complex operations are broken down into manageable pieces
3. **Clear Interfaces**: Each module has a well-defined API
4. **Reduced Complexity**: Main class is now focused on coordination only

### DRY Principles Applied
1. **Shared Utilities**: Common functions moved to DatabaseUtils
2. **Reusable Components**: Modules can be used independently
3. **Eliminated Duplication**: Removed redundant conflict resolution code
4. **Centralized Logic**: Similar operations grouped together

### Performance Improvements
1. **Memory Management**: Dedicated MemoryManager prevents leaks
2. **Efficient Conflict Resolution**: Simplified strategy reduces overhead
3. **Modular Loading**: Only necessary components are loaded
4. **Better Caching**: Centralized cache management

### Maintainability Improvements
1. **Easier Testing**: Each module can be tested independently
2. **Better Debugging**: Issues can be isolated to specific modules
3. **Simpler Updates**: Changes affect only relevant modules
4. **Clear Documentation**: Each module has focused documentation

## Usage Examples

### Basic Operations
```javascript
const handler = new DatabaseHandler();

// Initialize database
await handler.initializeDatabase('mydb', 'prefix_', lanDetails, 'branch');

// Save document
const result = await handler.saveDocument('prefix_mydb', { name: 'test' }, user);

// Get document
const doc = await handler.getDocument('prefix_mydb', 'docId');

// Delete document
await handler.deleteDocument('prefix_mydb', 'docId', user);
```

### Conflict Resolution
```javascript
// Manual conflict resolution
await handler.resolveDocumentConflicts('prefix_mydb', 'docId');

// Batch conflict resolution
await handler.resolveAllConflicts('prefix_mydb');

// Get conflict statistics
const stats = handler.getConflictStats();
```

### Sync Operations
```javascript
// Sync with remote database
await handler.syncThis('prefix_mydb', 'http://remote-db-url', options);

// Force sync
await handler.forceSync('prefix_mydb');

// Update LAN details
handler.updateLanDetails('prefix_mydb', lanDetails);
```

### Attachment Operations
```javascript
// Get attachment
const attachment = await handler.getAttachment('prefix_mydb', 'docId', 'attachmentName');

// Put attachment
await handler.putAttachment('prefix_mydb', 'docId', 'name', 'rev', data, 'image/png');

// Remove attachment
await handler.removeAttachment('prefix_mydb', 'docId', 'name', 'rev');
```

## Migration Guide

The refactored DatabaseHandler maintains backward compatibility with the original API. No changes are required for existing code that uses the DatabaseHandler.

### Internal Changes
- Conflict resolution is now handled by ConflictResolver
- Memory management is handled by MemoryManager
- Sync operations are handled by SyncManager
- Attachment operations are handled by AttachmentManager
- Utility functions are in DatabaseUtils

### Performance Considerations
- Memory usage is now actively managed
- Conflict resolution is more efficient
- Modular loading reduces initial overhead
- Better error isolation prevents cascading failures

## Testing

Each module should be tested independently:

```javascript
// Test CoreDatabaseOperations
const coreOps = new CoreDatabaseOperations(eventEmitter);
await coreOps.saveDocument(db, dbConfig, data, user);

// Test ConflictResolver
const resolver = new ConflictResolver(eventEmitter);
await resolver.handleConflicts(change, source, databases);

// Test MemoryManager
const memManager = new MemoryManager();
const stats = memManager.getMemoryStats();
```

## Future Enhancements

The modular architecture makes it easy to:
1. Add new database operations
2. Implement different conflict resolution strategies
3. Add new sync protocols
4. Enhance memory management
5. Add new attachment formats
6. Implement database sharding
7. Add performance monitoring

## Conclusion

The refactored DatabaseHandler provides a clean, maintainable, and efficient architecture that follows software engineering best practices while maintaining full backward compatibility.
