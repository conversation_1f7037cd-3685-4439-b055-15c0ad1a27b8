/**
 * Sync Manager for Database Operations
 * Handles all sync-related operations and organization filtering
 */

class SyncManager {
  constructor(eventEmitter) {
    this.eventEmitter = eventEmitter;
  }

  /**
   * Sync with a specific target URL
   */
  async syncThis(db, dbConfig, targetUrl, options = {}) {
    try {
      const { name, databasePrefix } = dbConfig;

      // Default sync options
      const syncOptions = { ...options };

      // Apply organization-based filtering for data isolation
      this.applyOrganizationFilter(syncOptions, name, databasePrefix);

      console.log(`[SyncManager] Starting sync for ${dbConfig.name} to ${targetUrl}`, syncOptions);

      // Perform the sync
      const syncResult = await db.sync(targetUrl, syncOptions);

      console.log(`[SyncManager] Sync completed for ${dbConfig.name}:`, {
        docs_read: syncResult.docs_read || 0,
        docs_written: syncResult.docs_written || 0,
        doc_write_failures: syncResult.doc_write_failures || 0
      });

      // Emit sync completion event
      this.eventEmitter.emit('syncCompleted', {
        dbKey: `${databasePrefix}${name}`,
        targetUrl,
        result: syncResult,
        manual: true
      });

      return {
        success: true,
        docs_read: syncResult.docs_read || 0,
        docs_written: syncResult.docs_written || 0,
        doc_write_failures: syncResult.doc_write_failures || 0
      };
    } catch (error) {
      console.error(`[SyncManager] Sync failed for ${dbConfig.name}:`, error);

      // Emit sync error event
      this.eventEmitter.emit('syncError', {
        dbKey: `${dbConfig.databasePrefix}${dbConfig.name}`,
        targetUrl,
        error: error.message
      });

      throw error;
    }
  }

  /**
   * Apply organization-based filtering for data isolation
   */
  applyOrganizationFilter(syncOptions, name, databasePrefix) {
    // Organizations database: Filter by organization ID
    if (name === "organizations" && !databasePrefix.includes('mission_control')) {
      const organizationID = this.extractOrganizationId(databasePrefix);
      console.log(`[SyncManager] Applying organization filter for ${name}: ${organizationID}`);

      syncOptions.filter = (doc) => {
        const matches = doc._id === organizationID;
        if (matches) {
          console.log(`[SyncManager] Organization filter: Including doc ${doc._id}`);
        }
        return matches;
      };
    }
    // Subscriptions database: Filter by organization.value
    else if (name === "subscriptions" && !databasePrefix.includes('mission_control')) {
      const organizationID = this.extractOrganizationId(databasePrefix);
      console.log(`[SyncManager] Applying subscription filter for ${name}: ${organizationID}`);

      syncOptions.filter = (doc) => {
        const matches = doc.organization 
          ? doc.organization.value === organizationID
          : doc.name 
            ? doc.name.value === organizationID
            : false;
        if (matches) {
          console.log(`[SyncManager] Subscription filter: Including doc ${doc._id} for org ${doc.organization?.value || doc.name?.value}`);
        }
        return matches;
      };
    }
    // Plans database: Usually no filtering needed as plans are shared
    else if (name === "plans" && !databasePrefix.includes('mission_control')) {
      console.log(`[SyncManager] Plans database: No filtering applied (plans are shared)`);
    }
    // For mission_control databases, no filtering needed
    else if (databasePrefix.includes('mission_control')) {
      console.log(`[SyncManager] Mission control database: No filtering applied`);
    }
    // For other organization-specific databases, no filtering needed
    else {
      console.log(`[SyncManager] Organization-specific database ${name}: No filtering needed`);
    }
  }

  /**
   * Extract organization ID from database prefix
   */
  extractOrganizationId(databasePrefix) {
    const pArray = databasePrefix.split('_');
    // For prefix like 'prosy_m9xlo8hv_', org ID is at index -2 (second to last)
    return (pArray.length >= 2 ? pArray[pArray.length - 2] : pArray[0]).toUpperCase();
  }

  /**
   * Trigger sync for a database with optional metadata
   */
  triggerSync(dbKey, operation, metadata = {}) {
    // Emit sync trigger event to be handled by SyncService
    this.eventEmitter.emit('syncTrigger', {
      dbKey,
      operation,
      ...metadata
    });
  }

  /**
   * Force sync for a specific database
   */
  async forceSync(dbKey) {
    this.triggerSync(dbKey, 'force_sync');
    return { success: true };
  }

  /**
   * Get database sync status
   */
  getSyncStatus(dbKey) {
    return { 
      isHealthy: true, 
      lastSyncTimes: {}, 
      pendingOperations: new Set() 
    };
  }

  /**
   * Update LAN details for a database
   */
  updateLanDetails(dbConfig, lan_details) {
    if (!dbConfig) {
      return { success: false, error: 'Database not found' };
    }

    dbConfig.lan_details = lan_details;
    dbConfig.lanString = lan_details
      ? `http://${lan_details.username}:${lan_details.password}@${lan_details.host}:${lan_details.port}/`
      : null;

    return { success: true };
  }

  /**
   * Authenticate with a database (for LAN authentication)
   */
  async authenticate(dbConfig, username, password) {
    if (!dbConfig) {
      throw new Error('Database not found');
    }

    if (!dbConfig.lanString) {
      throw new Error('No LAN configuration available for authentication');
    }

    try {
      const PouchDB = require('pouchdb');
      
      // For LAN authentication, try to connect to the remote database
      const remoteUrl = `${dbConfig.lanString}${dbConfig.databasePrefix}${dbConfig.name}`;

      // Create a temporary PouchDB instance to test authentication
      const testDb = new PouchDB(remoteUrl);

      // Try to get database info to verify authentication
      const info = await testDb.info();

      // Close the test connection
      await testDb.close();

      return {
        authenticated: true,
        database: dbConfig.name,
        info
      };
    } catch (error) {
      throw new Error(`Authentication failed: ${error.message}`);
    }
  }

  /**
   * Create sync options with common defaults
   */
  createSyncOptions(customOptions = {}) {
    return {
      live: false,
      retry: true,
      ...customOptions
    };
  }

  /**
   * Validate sync target URL
   */
  validateSyncTarget(targetUrl) {
    if (!targetUrl || typeof targetUrl !== 'string') {
      throw new Error('Invalid sync target URL');
    }

    try {
      new URL(targetUrl);
      return true;
    } catch (error) {
      throw new Error(`Invalid sync target URL format: ${error.message}`);
    }
  }

  /**
   * Handle sync events
   */
  handleSyncEvent(eventType, data) {
    switch (eventType) {
      case 'change':
        this.eventEmitter.emit('syncChange', data);
        break;
      case 'complete':
        this.eventEmitter.emit('syncComplete', data);
        break;
      case 'error':
        this.eventEmitter.emit('syncError', data);
        break;
      case 'denied':
        this.eventEmitter.emit('syncDenied', data);
        break;
      default:
        console.warn(`[SyncManager] Unknown sync event type: ${eventType}`);
    }
  }
}

module.exports = SyncManager;
