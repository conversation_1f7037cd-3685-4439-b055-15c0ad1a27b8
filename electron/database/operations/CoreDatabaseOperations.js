/**
 * Core Database Operations
 * Handles basic CRUD operations with simplified logic following KISS principles
 */

const DatabaseUtils = require('../utils/DatabaseUtils');

class CoreDatabaseOperations {
  constructor(eventEmitter) {
    this.eventEmitter = eventEmitter;
    this.currentUser = { name: "sys" };
  }

  /**
   * Create or update a document - simplified version of saveDocument
   */
  async saveDocument(db, dbConfig, data, user) {
    try {
      this.currentUser = user || this.currentUser;

      // Process attachments if present
      let processedData = { ...data };
      if (data._attachments) {
        processedData = await this.processAttachments(data, db);
      }

      // Determine if this is an update
      const isUpdate = !!processedData._id;
      const docId = processedData._id || DatabaseUtils.generateDocumentId();

      // Get existing document if updating
      let existingDoc = null;
      if (isUpdate) {
        try {
          existingDoc = await db.get(docId);
        } catch (err) {
          if (err.status !== 404) throw err;
        }
      }

      // Prepare document
      const preparedDoc = this.prepareDocument(processedData, docId, isUpdate, existingDoc);

      // Save document
      await db.put(preparedDoc);

      // Emit events
      this.emitDocumentEvents(isUpdate, preparedDoc);

      return { id: docId, doc: preparedDoc };
    } catch (error) {
      console.error(`[CoreDatabaseOperations] Save failed:`, error);
      throw error;
    }
  }

  /**
   * Get a document by ID
   */
  async getDocument(db, id) {
    try {
      const doc = await db.get(id);
      return doc;
    } catch (err) {
      return null; // SimplifiedDB returns null for not found
    }
  }

  /**
   * Get all documents with optional filtering
   */
  async getAllDocuments(db, options = {}) {
    try {
      const result = await db.allDocs({
        include_docs: true,
        conflicts: false, // DISABLED: Conflict detection disabled for maximum performance
        ...options
      });

      // Filter out design docs and deleted docs
      const docs = result.rows
        .filter(row => row.doc && !row.doc._id.startsWith('_'))
        .map(row => row.doc);

      return docs;
    } catch (err) {
      return [];
    }
  }

  /**
   * Delete a document
   */
  async deleteDocument(db, id) {
    const doc = await this.getDocument(db, id);
    if (!doc) throw new Error(`Document ${id} not found`);

    await db.remove(doc);
    
    // Emit events
    this.eventEmitter.emit('documentDeleted', doc);
    this.eventEmitter.emit('dbChange', {
      type: 'delete',
      id: doc._id,
      doc: doc
    });

    return doc;
  }

  /**
   * Bulk save documents
   */
  async bulkSave(db, docs, user) {
    try {
      const timestamp = new Date().toISOString();
      const baseTimestamp = Date.now();

      const preparedDocs = docs.map((doc, index) => {
        const isUpdate = !!doc._id;
        const docId = doc._id || (baseTimestamp + index).toString(36).toUpperCase();

        return {
          ...doc,
          _id: docId,
          updatedAt: timestamp,
          ...(isUpdate ? {} : {
            createdAt: timestamp,
            createdBy: user?.name || this.currentUser.name,
            referenceNumber: doc.referenceNumber || DatabaseUtils.generateReferenceNumber('', index + 1)
          })
        };
      });

      const result = await db.bulkDocs(preparedDocs);

      // Emit change event
      this.eventEmitter.emit('documentChanged', {
        type: 'bulk_create',
        docs: preparedDocs
      });

      return {
        success: true,
        results: result.map(res => ({ id: res.id, rev: res.rev }))
      };
    } catch (error) {
      console.error(`[CoreDatabaseOperations] Bulk save failed:`, error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Find documents using Mango queries
   */
  async findDocuments(db, selector, options = {}) {
    try {
      const result = await db.find({
        selector,
        ...options
      });
      return result.docs;
    } catch (error) {
      console.error(`[CoreDatabaseOperations] Find failed:`, error);
      throw error;
    }
  }

  /**
   * Create an index for faster queries
   */
  async createIndex(db, index) {
    return await db.createIndex(index);
  }

  /**
   * Process attachments from ElectronDB format to PouchDB format
   */
  async processAttachments(data, db) {
    const processedData = { ...data };
    const attachments = {};

    if (data._attachments) {
      for (const [key, attachment] of Object.entries(data._attachments)) {
        try {
          const buffer = Buffer.from(attachment.data);
          attachments[key] = {
            content_type: attachment.content_type,
            data: buffer
          };
        } catch (error) {
          console.error(`[CoreDatabaseOperations] Error processing attachment ${key}:`, error);
        }
      }

      if (Object.keys(attachments).length > 0) {
        processedData._attachments = attachments;
      } else {
        delete processedData._attachments;
      }
    }

    return processedData;
  }

  /**
   * Prepare document for saving
   */
  prepareDocument(data, docId, isUpdate, existingDoc) {
    const preparedDoc = {
      ...data,
      _id: docId,
      updatedAt: new Date().toISOString(),
      ...(isUpdate ? {} : {
        createdAt: new Date().toISOString(),
        createdBy: this.currentUser || 'system',
        referenceNumber: data.referenceNumber || DatabaseUtils.generateReferenceNumber()
      })
    };

    // Clean undefined values
    const cleanedDoc = DatabaseUtils.cleanUndefinedValues(preparedDoc);

    // Set revision if updating
    if (isUpdate && existingDoc) {
      cleanedDoc._rev = existingDoc._rev;
    }

    return cleanedDoc;
  }

  /**
   * Emit document events
   */
  emitDocumentEvents(isUpdate, doc) {
    this.eventEmitter.emit(isUpdate ? 'documentUpdated' : 'documentCreated', doc);
    this.eventEmitter.emit('dbChange', {
      type: isUpdate ? 'update' : 'create',
      id: doc._id,
      doc: doc
    });
  }
}

module.exports = CoreDatabaseOperations;
