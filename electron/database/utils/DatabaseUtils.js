/**
 * Database Utility Functions
 * Common utilities for database operations following DRY principles
 */

class DatabaseUtils {
  /**
   * Generate a unique document ID using the same pattern as SimplifiedDB
   */
  static generateDocumentId() {
    return Date.now().toString(36).toUpperCase();
  }

  /**
   * Generate a reference number using the same pattern as SimplifiedDB
   */
  static generateReferenceNumber(prefix = '', count = null) {
    const sequence = count !== null
      ? count.toString().padStart(6, '0')
      : Math.floor(Math.random() * 999999).toString().padStart(6, '0');
    const timestamp = Date.now().toString(36).slice(-4).toUpperCase();
    return `${prefix}${timestamp}-${sequence}`;
  }

  /**
   * Deep merge objects with array handling - ENHANCED DEDUPLICATION
   * Fixes duplication issues by improving array merging and object comparison
   */
  static deepMergeWithArrays(target, source) {
    if (!source) return target;
    if (!target) return source;

    const result = { ...target };

    for (const key in source) {
      if (source.hasOwnProperty(key)) {
        if (key === '_id' || key === '_rev' || key === '_conflicts') {
          // Skip PouchDB metadata fields during merge
          continue;
        }

        if (result[key] === undefined) {
          // If target doesn't have this field, take it from source
          result[key] = source[key];
        } else if (Array.isArray(result[key]) && Array.isArray(source[key])) {
          // ENHANCED ARRAY MERGING: Better deduplication logic
          result[key] = this.mergeArraysWithDeduplication(result[key], source[key]);
        } else if (typeof result[key] === 'object' && typeof source[key] === 'object' &&
                   result[key] !== null && source[key] !== null) {
          // Recursively merge objects
          result[key] = this.deepMergeWithArrays(result[key], source[key]);
        }
        // For primitive values, keep the target value (newest document wins)
      }
    }

    return result;
  }

  /**
   * Enhanced array merging with better deduplication
   * Handles objects with _id, id, or value properties for proper comparison
   */
  static mergeArraysWithDeduplication(targetArray, sourceArray) {
    const combined = [...targetArray, ...sourceArray];
    const uniqueItems = [];
    const seenKeys = new Set();

    for (const item of combined) {
      let uniqueKey;

      if (typeof item === 'object' && item !== null) {
        // For objects, create a unique key based on _id, id, value, or full content
        if (item._id) {
          uniqueKey = `obj_id_${item._id}`;
        } else if (item.id) {
          uniqueKey = `obj_id_${item.id}`;
        } else if (item.value !== undefined) {
          uniqueKey = `obj_value_${JSON.stringify(item.value)}`;
        } else {
          // For objects without clear identifiers, use sorted JSON
          uniqueKey = `obj_full_${JSON.stringify(this.sortObjectKeys(item))}`;
        }
      } else {
        // For primitives, use the value directly
        uniqueKey = `primitive_${item}`;
      }

      if (!seenKeys.has(uniqueKey)) {
        seenKeys.add(uniqueKey);
        uniqueItems.push(item);
      }
    }

    return uniqueItems;
  }

  /**
   * Sort object keys for consistent comparison
   */
  static sortObjectKeys(obj) {
    if (typeof obj !== 'object' || obj === null) return obj;
    if (Array.isArray(obj)) return obj.map(item => this.sortObjectKeys(item));

    const sorted = {};
    Object.keys(obj).sort().forEach(key => {
      sorted[key] = this.sortObjectKeys(obj[key]);
    });
    return sorted;
  }

  /**
   * Get fields that were preserved during merge (for enhanced metadata)
   */
  static getPreservedFields(newestDoc, mergedDoc) {
    const preserved = [];

    for (const key in mergedDoc) {
      if (mergedDoc.hasOwnProperty(key) && !key.startsWith('_') && key !== 'conflictResolution') {
        // Check if this field came from an older revision or was modified during merge
        if (!newestDoc.hasOwnProperty(key)) {
          preserved.push(`${key} (added from older revision)`);
        } else if (JSON.stringify(newestDoc[key]) !== JSON.stringify(mergedDoc[key])) {
          preserved.push(`${key} (merged from multiple revisions)`);
        }
      }
    }

    return preserved;
  }

  /**
   * Clean undefined values from an object (prevents PouchDB issues)
   */
  static cleanUndefinedValues(obj) {
    const cleaned = { ...obj };
    Object.keys(cleaned).forEach(key => {
      if (cleaned[key] === undefined) {
        delete cleaned[key];
      }
    });
    return cleaned;
  }

  /**
   * Validate document ID and name parameters
   */
  static validateParams(id, name) {
    return id && typeof id === 'string' && name && typeof name === 'string';
  }

  /**
   * Create conflict resolution metadata
   */
  static createConflictResolutionMetadata(strategy, conflictCount, additionalData = {}) {
    return {
      strategy,
      resolvedAt: new Date().toISOString(),
      conflictCount,
      resolvedBy: 'DatabaseHandler',
      ...additionalData
    };
  }

  /**
   * Check if a document ID is a design document
   */
  static isDesignDocument(docId) {
    return docId && docId.startsWith('_design/');
  }

  /**
   * Normalize branch value (kept for backward compatibility)
   */
  static normalizeBranch(branch) {
    return branch;
  }

  /**
   * Create database path
   */
  static createDatabasePath(basePath, name) {
    const path = require('path');
    return path.join(basePath, name);
  }

  /**
   * Create database key
   */
  static createDatabaseKey(databasePrefix, name) {
    return `${databasePrefix || ''}${name}`;
  }
}

module.exports = DatabaseUtils;
