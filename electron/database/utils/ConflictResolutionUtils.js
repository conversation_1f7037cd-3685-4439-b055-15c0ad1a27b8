/**
 * Conflict Resolution Utilities
 * Emergency tools for resolving stuck conflicts
 */

const DatabaseUtils = require('./DatabaseUtils');

class ConflictResolutionUtils {
  constructor(databaseHandler) {
    this.databaseHandler = databaseHandler;
  }

  /**
   * Diagnose conflicts in a specific database
   */
  async diagnoseConflicts(dbKey) {
    try {
      const dbConfig = this.databaseHandler.databases.get(dbKey);
      if (!dbConfig) {
        throw new Error(`Database ${dbKey} not found`);
      }

      const result = await dbConfig.db.allDocs({
        include_docs: true,
        conflicts: false // DISABLED: Conflict detection disabled for maximum performance
      });

      const conflictedDocs = result.rows
        .filter(row => row.doc && row.doc._conflicts && row.doc._conflicts.length > 0)
        .map(row => ({
          id: row.doc._id,
          rev: row.doc._rev,
          conflictCount: row.doc._conflicts.length,
          conflicts: row.doc._conflicts,
          lastUpdated: row.doc.updatedAt || row.doc.createdAt || 'unknown'
        }));

      return {
        database: dbKey,
        totalDocuments: result.total_rows,
        conflictedDocuments: conflictedDocs.length,
        conflicts: conflictedDocs,
        summary: {
          totalConflicts: conflictedDocs.reduce((sum, doc) => sum + doc.conflictCount, 0),
          oldestConflict: conflictedDocs.length > 0 ? 
            Math.min(...conflictedDocs.map(doc => new Date(doc.lastUpdated).getTime())) : null,
          newestConflict: conflictedDocs.length > 0 ? 
            Math.max(...conflictedDocs.map(doc => new Date(doc.lastUpdated).getTime())) : null
        }
      };
    } catch (error) {
      console.error(`[ConflictResolutionUtils] Error diagnosing conflicts for ${dbKey}:`, error);
      throw error;
    }
  }

  /**
   * Force resolve a specific document's conflicts
   * DISABLED: Conflict resolution disabled for performance
   */
  async forceResolveDocument(dbKey, docId) {
    console.log(`[ConflictResolutionUtils] Force resolution disabled for performance - document ${docId} conflicts will not be resolved`);
    return {
      success: true,
      message: 'Conflict resolution disabled for performance',
      disabled: true
    };
  }

  /**
   * Emergency: Delete all conflict revisions for a document (nuclear option)
   */
  async deleteAllConflictRevisions(dbKey, docId) {
    try {
      const dbConfig = this.databaseHandler.databases.get(dbKey);
      if (!dbConfig) {
        throw new Error(`Database ${dbKey} not found`);
      }

      // Get document with conflicts (disabled for performance)
      const doc = await dbConfig.db.get(docId, { conflicts: false });
      
      if (!doc._conflicts || doc._conflicts.length === 0) {
        return { success: true, message: 'No conflicts found' };
      }

      console.log(`[ConflictResolutionUtils] EMERGENCY: Deleting ${doc._conflicts.length} conflict revisions for ${docId}`);

      let deletedCount = 0;
      for (const conflictRev of doc._conflicts) {
        try {
          await dbConfig.db.remove(docId, conflictRev);
          deletedCount++;
        } catch (error) {
          if (error.status === 404 || error.status === 409) {
            deletedCount++; // Count as deleted
          } else {
            console.warn(`[ConflictResolutionUtils] Failed to delete conflict revision ${conflictRev}:`, error);
          }
        }
      }

      return {
        success: true,
        message: `Deleted ${deletedCount}/${doc._conflicts.length} conflict revisions`,
        deletedCount,
        totalConflicts: doc._conflicts.length
      };
    } catch (error) {
      console.error(`[ConflictResolutionUtils] Error deleting conflict revisions for ${docId}:`, error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Get conflict resolution statistics for all databases
   */
  async getGlobalConflictStats() {
    const stats = {
      databases: {},
      totals: {
        totalDatabases: 0,
        totalDocuments: 0,
        totalConflictedDocuments: 0,
        totalConflicts: 0
      }
    };

    for (const [dbKey, dbConfig] of this.databaseHandler.databases) {
      try {
        const diagnosis = await this.diagnoseConflicts(dbKey);
        stats.databases[dbKey] = diagnosis;
        
        stats.totals.totalDatabases++;
        stats.totals.totalDocuments += diagnosis.totalDocuments;
        stats.totals.totalConflictedDocuments += diagnosis.conflictedDocuments;
        stats.totals.totalConflicts += diagnosis.summary.totalConflicts;
      } catch (error) {
        console.warn(`[ConflictResolutionUtils] Failed to get stats for ${dbKey}:`, error);
        stats.databases[dbKey] = { error: error.message };
      }
    }

    return stats;
  }

  /**
   * Auto-resolve all conflicts in all databases (use with caution)
   * DISABLED: Conflict resolution disabled for performance
   */
  async autoResolveAllConflicts() {
    console.log('[ConflictResolutionUtils] Auto-resolution disabled for performance - no conflicts will be resolved');

    return {
      success: true,
      totalResolved: 0,
      totalFailed: 0,
      databases: {},
      disabled: true,
      message: 'Conflict resolution disabled for performance'
    };
  }

  /**
   * Health check for conflict resolution system
   */
  async healthCheck() {
    const health = {
      status: 'healthy',
      issues: [],
      recommendations: [],
      stats: await this.getGlobalConflictStats()
    };

    // Check for excessive conflicts
    if (health.stats.totals.totalConflicts > 100) {
      health.status = 'warning';
      health.issues.push(`High number of conflicts detected: ${health.stats.totals.totalConflicts}`);
      health.recommendations.push('Consider running auto-resolution or investigating sync issues');
    }

    // Check for stuck conflicts (older than 1 hour)
    const oneHourAgo = Date.now() - (60 * 60 * 1000);
    for (const [dbKey, dbStats] of Object.entries(health.stats.databases)) {
      if (dbStats.summary && dbStats.summary.oldestConflict && dbStats.summary.oldestConflict < oneHourAgo) {
        health.status = 'critical';
        health.issues.push(`Old conflicts detected in ${dbKey} (older than 1 hour)`);
        health.recommendations.push(`Force resolve conflicts in ${dbKey}`);
      }
    }

    return health;
  }
}

module.exports = ConflictResolutionUtils;
