/**
 * Simplified Sync Service - KISS principle
 * Basic database synchronization with organization-based filtering for mission_control databases
 *
 * CRITICAL SECURITY FEATURE:
 * - Organization filtering ONLY applies to mission_control databases when accessed by other apps
 * - This ensures each organization only syncs its own data from shared mission_control databases
 * - Organization-specific databases are already isolated by their prefix and don't need filtering
 */

const { EventEmitter } = require('events');

const REMOTE_DATABASE_CONNECTION_STRING = "https://therick:<EMAIL>/";

class SyncService extends EventEmitter {
  constructor(databaseHandler) {
    super();

    // MEMORY LEAK FIX: Increase max listeners to prevent warnings
    this.setMaxListeners(100);

    this.databaseHandler = databaseHandler;
    this.activeSyncs = new Set();
    this.backgroundSyncIntervals = new Map(); // Track background sync intervals

    // CRUD OPERATION SYNC CONTROL: Track active CRUD operations
    this.activeCrudOperations = new Set(); // Track databases with active CRUD operations
    this.pausedSyncs = new Set(); // Track which databases have paused sync
    this.crudOperationTimeouts = new Map(); // Track CRUD operation timeouts

    // Optimized settings for 3-way sync
    this.settings = {
      timeout: 15000,        // Reasonable timeout for remote sync
      batchSize: 100,        // Optimized batch size for performance
      maxRetries: 3,         // Minimal retries for KISS
      backgroundSyncInterval: 45000,  // 45 seconds - balanced performance
      crudSyncPauseTimeout: 5000  // 5 seconds - pause sync after CRUD operations
    };
  }

  /**
   * Intelligent 3-way sync with organization-based filtering
   * Syncs: Local ↔ LAN ↔ Remote with performance optimization
   */
  async syncDatabase(dbKey) {
    if (this.activeSyncs.has(dbKey)) {
      return { success: false, error: 'Sync already in progress' };
    }

    this.activeSyncs.add(dbKey);

    try {
      const dbConfig = this.databaseHandler.databases.get(dbKey);
      if (!dbConfig) {
        throw new Error(`Database ${dbKey} not found`);
      }

      // Update last used timestamp
      dbConfig.lastUsed = Date.now();

      // Perform 3-way sync with organization filtering
      const syncResult = await this.perform3WaySync(dbKey, dbConfig);

      // Emit events for real-time updates
      this.emitSyncEvents(dbKey, syncResult);

      return { success: true, syncResult };
    } catch (error) {
      console.error(`[SyncService] Sync failed for ${dbKey}:`, error);
      this.emit('syncError', { dbKey, error: error.message });
      return { success: false, error: error.message };
    } finally {
      this.activeSyncs.delete(dbKey);
    }
  }

  /**
   * Core 3-way sync implementation with organization filtering
   * PERFORMANCE: Optimized sync order and batch sizes
   * SCALABILITY: Configurable timeouts and error handling
   */
  async perform3WaySync(dbKey, dbConfig) {
    const { localDb, lanDb, remoteUrl } = dbConfig;
    const syncResults = { local: null, lan: null, remote: null };

    // Create base sync options with organization filtering
    const baseSyncOptions = await this.createSyncOptions(dbKey);

    try {
      // STEP 1: Local ↔ LAN sync (fastest, if available)
      if (lanDb && localDb) {
        console.log(`[SyncService] Step 1: Local ↔ LAN sync for ${dbKey}`);
        syncResults.lan = await this.syncBetween(localDb, lanDb, {
          ...baseSyncOptions,
          timeout: 8000,  // Shorter timeout for LAN
          batch_size: 100 // Larger batches for LAN
        });
      }

      // STEP 2: Primary ↔ Remote sync (critical path)
      const primaryDb = this.getPrimaryDatabase(dbConfig);
      if (primaryDb) {
        // Construct remote URL if not provided in config
        const targetRemoteUrl = remoteUrl || `https://therick:<EMAIL>/${dbKey}`;
        console.log(`[SyncService] Step 2: Primary ↔ Remote sync for ${dbKey}`);
        syncResults.remote = await this.syncBetween(primaryDb, targetRemoteUrl, baseSyncOptions);
      }

      // STEP 3: Final local sync (if LAN was primary)
      if (dbConfig.isPrimaryLan && lanDb && localDb) {
        console.log(`[SyncService] Step 3: Final Local ↔ LAN sync for ${dbKey}`);
        syncResults.local = await this.syncBetween(localDb, lanDb, {
          ...baseSyncOptions,
          timeout: 5000,  // Quick final sync
          batch_size: 50  // Smaller batches for final sync
        });
      }

      return this.consolidateSyncResults(syncResults);
    } catch (error) {
      console.error(`[SyncService] 3-way sync failed for ${dbKey}:`, error);
      throw error;
    }
  }

  /**
   * Create optimized sync options with organization filtering
   * DRY: Single method for all sync option creation
   */
  async createSyncOptions(dbKey) {
    const databaseName = this.databaseHandler.extractDatabaseName(dbKey);
    const databasePrefix = this.databaseHandler.extractDatabasePrefix(dbKey);

    const syncOptions = {
      timeout: this.settings.timeout,
      batch_size: this.settings.batchSize,
      retry: false,
      live: false, // Never use live sync for performance
      attachments: true, // Include attachments in sync operations
      binary: true // Support binary attachments
    };

    // Apply organization filtering (async)
    await this.applyOrganizationFilter(syncOptions, databaseName, databasePrefix);
    return syncOptions;
  }

  /**
   * Generic sync method between two databases
   * DRY: Single method for all sync operations
   */
  async syncBetween(sourceDb, targetDb, options = {}) {
    let syncHandler = null;

    try {
      // Handle both database objects and URLs
      let target;
      if (typeof targetDb === 'string') {
        // It's a URL - use directly
        target = targetDb;
      } else {
        // It's a database object - use directly
        target = targetDb;
      }

      syncHandler = sourceDb.sync(target, options);

      // MEMORY LEAK FIX: Set max listeners on sync handler
      if (syncHandler && syncHandler.setMaxListeners) {
        syncHandler.setMaxListeners(50);
      }

      const result = await syncHandler;

      return {
        success: true,
        docs_read: result.pull?.docs_read || 0,
        docs_written: result.push?.docs_written || 0,
        doc_write_failures: result.push?.doc_write_failures || 0
      };
    } catch (error) {
      // Performance: Don't log common network errors
      if (!this.isCommonNetworkError(error)) {
        console.warn(`[SyncService] Sync error:`, error.message);
      }

      return {
        success: false,
        error: error.message,
        docs_read: 0,
        docs_written: 0,
        doc_write_failures: 0
      };
    } finally {
      // MEMORY LEAK FIX: Clean up sync handler
      if (syncHandler) {
        try {
          if (syncHandler.removeAllListeners) {
            syncHandler.removeAllListeners();
          }
          if (syncHandler.cancel) {
            syncHandler.cancel();
          }
        } catch (cleanupError) {
          // Ignore cleanup errors
        }
      }
    }
  }

  /**
   * Get the primary database for sync operations
   * PERFORMANCE: Avoid unnecessary database checks
   */
  getPrimaryDatabase(dbConfig) {
    return dbConfig.isPrimaryLan && dbConfig.lanDb ? dbConfig.lanDb : dbConfig.localDb;
  }

  /**
   * Consolidate sync results from all 3-way sync steps
   * SCALABILITY: Aggregate metrics for monitoring
   */
  consolidateSyncResults(syncResults) {
    const consolidated = {
      success: true,
      docs_read: 0,
      docs_written: 0,
      doc_write_failures: 0,
      steps: {}
    };

    // Aggregate results from all sync steps
    Object.entries(syncResults).forEach(([step, result]) => {
      if (result) {
        consolidated.steps[step] = result;
        consolidated.docs_read += result.docs_read || 0;
        consolidated.docs_written += result.docs_written || 0;
        consolidated.doc_write_failures += result.doc_write_failures || 0;

        if (!result.success) {
          consolidated.success = false;
        }
      }
    });

    return consolidated;
  }

  /**
   * Emit sync events for real-time updates
   * DRY: Single method for all event emission
   */
  emitSyncEvents(dbKey, syncResult) {
    console.log(`[SyncService] 3-way sync completed for ${dbKey}:`, {
      docs_read: syncResult.docs_read,
      docs_written: syncResult.docs_written,
      success: syncResult.success
    });

    // Emit to SyncService listeners
    this.emit('syncComplete', { dbKey, result: syncResult });

    // Emit through DatabaseHandler for real-time updates
    this.databaseHandler.emit('documentChanged', {
      type: 'sync_complete',
      dbKey: dbKey,
      syncResult: syncResult
    });
  }

  /**
   * Check if error is a common network error (for performance)
   * PERFORMANCE: Avoid logging spam from common network issues
   */
  isCommonNetworkError(error) {
    const commonErrors = ['timeout', 'ECONNREFUSED', 'ENOTFOUND', 'ETIMEDOUT'];
    return commonErrors.some(errorType =>
      error.message.toLowerCase().includes(errorType.toLowerCase())
    );
  }

  /**
   * Force sync a database (uses 3-way sync)
   */
  async forceSyncDatabase(dbKey) {
    return this.syncDatabase(dbKey);
  }

  /**
   * Perform instant LAN sync (uses 3-way sync)
   */
  async performInstantLanSync(dbKey) {
    return this.syncDatabase(dbKey);
  }

  /**
   * Perform instant remote sync (uses 3-way sync)
   */
  async performInstantRemoteSync(dbKey) {
    return this.syncDatabase(dbKey);
  }

  /**
   * Start background 3-way sync for a database
   * SCALABILITY: Individual database sync intervals for performance
   */
  startDatabaseBackgroundSync(dbKey) {
    // Don't start if already running
    if (this.backgroundSyncIntervals.has(dbKey)) {
      return;
    }

    const interval = setInterval(async () => {
      // Skip if manual sync is in progress
      if (this.activeSyncs.has(dbKey)) {
        return;
      }

      // CRUD OPERATION SYNC CONTROL: Skip if CRUD operations are active or sync is paused
      if (this.activeCrudOperations.has(dbKey) || this.pausedSyncs.has(dbKey)) {
        console.log(`[SyncService] Skipping background sync for ${dbKey} - CRUD operation active or sync paused`);
        return;
      }

      try {
        await this.syncDatabase(dbKey);
      } catch (error) {
        // Silent background sync errors to prevent spam
        if (!this.isCommonNetworkError(error)) {
          console.warn(`[SyncService] Background sync failed for ${dbKey}:`, error.message);
        }
      }
    }, this.settings.backgroundSyncInterval);

    this.backgroundSyncIntervals.set(dbKey, interval);
    console.log(`[SyncService] Started background sync for ${dbKey}`);
  }

  /**
   * Stop background sync for a database
   */
  stopDatabaseBackgroundSync(dbKey) {
    const interval = this.backgroundSyncIntervals.get(dbKey);
    if (interval) {
      clearInterval(interval);
      this.backgroundSyncIntervals.delete(dbKey);
      console.log(`[SyncService] Stopped background sync for ${dbKey}`);
    }
  }

  /**
   * Start sync service with background 3-way sync
   * PERFORMANCE: Only start background sync for active databases
   */
  async startSyncService(databases) {
    console.log(`[SyncService] Starting 3-way sync service for ${databases.length} databases`);

    // Start background sync for each database
    databases.forEach(dbKey => {
      this.startDatabaseBackgroundSync(dbKey);
    });

    return { success: true, backgroundSyncCount: databases.length };
  }

  /**
   * Stop sync service and cleanup
   */
  async stopSyncService() {
    console.log(`[SyncService] Stopping sync service...`);

    // Stop all background sync intervals
    this.backgroundSyncIntervals.forEach((interval, dbKey) => {
      clearInterval(interval);
      console.log(`[SyncService] Stopped background sync for ${dbKey}`);
    });

    this.backgroundSyncIntervals.clear();
    console.log(`[SyncService] Sync service stopped`);
    return { success: true };
  }

  /**
   * Force full sync for all managed databases
   * PERFORMANCE: Parallel sync with concurrency control
   */
  async forceFullSync() {
    const databases = Array.from(this.databaseHandler.databases.keys());
    console.log(`[SyncService] Force full sync for ${databases.length} databases`);

    // Sync databases in parallel with limited concurrency
    const concurrency = 3; // Limit concurrent syncs for performance
    const results = [];

    for (let i = 0; i < databases.length; i += concurrency) {
      const batch = databases.slice(i, i + concurrency);
      const batchPromises = batch.map(dbKey =>
        this.syncDatabase(dbKey).catch(error => ({
          success: false,
          dbKey,
          error: error.message
        }))
      );

      const batchResults = await Promise.all(batchPromises);
      results.push(...batchResults);
    }

    const successCount = results.filter(r => r.success).length;
    return {
      success: successCount === results.length,
      total: results.length,
      successful: successCount,
      failed: results.length - successCount,
      results
    };
  }

  /**
   * Perform specific LAN sync with 3-way sync approach
   * PERFORMANCE: Uses optimized 3-way sync even for specific LAN requests
   */
  async performSpecificLanSync(dbKey, lanUrl) {
    if (this.activeSyncs.has(dbKey)) {
      return { success: false, error: 'Sync already in progress' };
    }

    this.activeSyncs.add(dbKey);

    try {
      const dbConfig = this.databaseHandler.databases.get(dbKey);
      if (!dbConfig) {
        throw new Error(`Database ${dbKey} not found`);
      }

      // If specific LAN URL provided, temporarily update config
      const originalLanDb = dbConfig.lanDb;
      if (lanUrl && lanUrl !== dbConfig.lanString) {
        // Use the patched PouchDB from DatabaseHandler
        const PatchedPouchDB = require('./DatabaseHandler').PouchDB || require('pouchdb');
        dbConfig.lanDb = new PatchedPouchDB(lanUrl);

        // MEMORY LEAK FIX: Set max listeners on temporary LAN database
        if (dbConfig.lanDb.setMaxListeners) {
          dbConfig.lanDb.setMaxListeners(100);
        }

        console.log(`[SyncService] Using specific LAN URL: ${lanUrl}`);
      }

      try {
        // Use full 3-way sync for consistency and performance
        const syncResult = await this.perform3WaySync(dbKey, dbConfig);
        this.emitSyncEvents(dbKey, syncResult);
        return { success: true, syncResult };
      } finally {
        // Restore original LAN database
        if (originalLanDb !== dbConfig.lanDb) {
          dbConfig.lanDb = originalLanDb;
        }
      }

    } catch (error) {
      console.error(`[SyncService] Specific LAN sync failed for ${dbKey}:`, error);
      this.emit('syncError', { dbKey, error: error.message });
      return { success: false, error: error.message };
    } finally {
      this.activeSyncs.delete(dbKey);
    }
  }

  /**
   * Get sync status
   */
  getSyncStatus() {
    return {
      isRunning: true,
      activeSyncs: Array.from(this.activeSyncs),
      managedDatabases: [],
      settings: this.settings
    };
  }

  /**
   * Add database to background sync
   * SCALABILITY: Dynamic database management
   */
  async addDatabase(dbKey) {
    this.startDatabaseBackgroundSync(dbKey);
    console.log(`[SyncService] Added ${dbKey} to background sync`);
    return { success: true };
  }

  /**
   * Remove database from background sync
   */
  async removeDatabase(dbKey) {
    this.stopDatabaseBackgroundSync(dbKey);
    console.log(`[SyncService] Removed ${dbKey} from background sync`);
    return { success: true };
  }

  /**
   * Stop database sync (alias for removeDatabase)
   */
  stopDatabaseSync(dbKey) {
    this.stopDatabaseBackgroundSync(dbKey);
  }

  /**
   * Start database sync (alias for addDatabase)
   */
  startDatabaseSync(dbKey) {
    this.startDatabaseBackgroundSync(dbKey);
  }

  /**
   * CRUD OPERATION SYNC CONTROL: Pause sync operations during CRUD operations
   */

  /**
   * Mark the start of a CRUD operation for a database
   * This will pause all sync operations for the specified database
   */
  startCrudOperation(dbKey, operationType = 'unknown') {
    console.log(`[SyncService] Starting CRUD operation (${operationType}) for ${dbKey} - pausing sync`);

    // Add to active CRUD operations
    this.activeCrudOperations.add(dbKey);
    this.pausedSyncs.add(dbKey);

    // Clear any existing timeout for this database
    if (this.crudOperationTimeouts.has(dbKey)) {
      clearTimeout(this.crudOperationTimeouts.get(dbKey));
    }

    // Set a safety timeout to automatically resume sync if endCrudOperation is not called
    const timeoutId = setTimeout(() => {
      console.warn(`[SyncService] CRUD operation timeout for ${dbKey} - automatically resuming sync`);
      this.endCrudOperation(dbKey, 'timeout');
    }, this.settings.crudSyncPauseTimeout * 2); // 10 seconds safety timeout

    this.crudOperationTimeouts.set(dbKey, timeoutId);

    // Emit event for monitoring
    this.emit('crudOperationStarted', { dbKey, operationType });
  }

  /**
   * Mark the end of a CRUD operation for a database
   * This will resume sync operations after a brief delay
   */
  endCrudOperation(dbKey, operationType = 'unknown') {
    console.log(`[SyncService] Ending CRUD operation (${operationType}) for ${dbKey} - resuming sync after delay`);

    // Remove from active CRUD operations immediately
    this.activeCrudOperations.delete(dbKey);

    // Clear the safety timeout
    if (this.crudOperationTimeouts.has(dbKey)) {
      clearTimeout(this.crudOperationTimeouts.get(dbKey));
      this.crudOperationTimeouts.delete(dbKey);
    }

    // Resume sync after a brief delay to allow for multiple rapid operations
    setTimeout(() => {
      this.pausedSyncs.delete(dbKey);
      console.log(`[SyncService] Sync resumed for ${dbKey} after CRUD operation`);

      // Trigger an immediate sync to catch up on any changes
      this.syncDatabase(dbKey).catch(error => {
        if (!this.isCommonNetworkError(error)) {
          console.warn(`[SyncService] Post-CRUD sync failed for ${dbKey}:`, error.message);
        }
      });

      // Emit event for monitoring
      this.emit('crudOperationEnded', { dbKey, operationType });
    }, this.settings.crudSyncPauseTimeout);
  }

  /**
   * Manually pause sync for a database (useful for batch operations)
   */
  pauseSyncForDatabase(dbKey, reason = 'manual') {
    console.log(`[SyncService] Manually pausing sync for ${dbKey} - reason: ${reason}`);
    this.pausedSyncs.add(dbKey);
    this.emit('syncPaused', { dbKey, reason });
  }

  /**
   * Manually resume sync for a database
   */
  resumeSyncForDatabase(dbKey, reason = 'manual') {
    console.log(`[SyncService] Manually resuming sync for ${dbKey} - reason: ${reason}`);
    this.pausedSyncs.delete(dbKey);
    this.activeCrudOperations.delete(dbKey); // Also clear CRUD operations

    // Clear any pending timeouts
    if (this.crudOperationTimeouts.has(dbKey)) {
      clearTimeout(this.crudOperationTimeouts.get(dbKey));
      this.crudOperationTimeouts.delete(dbKey);
    }

    this.emit('syncResumed', { dbKey, reason });
  }

  /**
   * Check if sync is currently paused for a database
   */
  isSyncPaused(dbKey) {
    return this.pausedSyncs.has(dbKey) || this.activeCrudOperations.has(dbKey);
  }

  /**
   * Get CRUD operation status for all databases
   */
  getCrudOperationStatus() {
    return {
      activeCrudOperations: Array.from(this.activeCrudOperations),
      pausedSyncs: Array.from(this.pausedSyncs),
      pendingTimeouts: Array.from(this.crudOperationTimeouts.keys())
    };
  }

  /**
   * Set remote connection string (not needed in simplified version)
   */
  setRemoteConnectionString(url) {
    console.log(`[SyncService] Remote connection string update not supported in simplified version`);
  }

  /**
   * Apply organization-based filtering for data isolation
   * CRITICAL: Only applies to mission_control databases when accessed by other apps
   * This ensures each organization only syncs its own data from shared mission_control databases
   * @param {Object} syncOptions - Sync options to modify
   * @param {string} name - Database name
   * @param {string} databasePrefix - Database prefix
   */
  async applyOrganizationFilter(syncOptions, name, databasePrefix) {
    // Only apply filtering to mission_control databases when accessed by other apps
    // CRITICAL: Organization-specific databases (like zenwrench_ly5w828y_organizations) should NOT be filtered
    const isMissionControlDatabase = this.isMissionControlDatabase(name, databasePrefix);
    const isAccessedByOtherApp = !databasePrefix.includes('mission_control');

    // Special case: Plans database should never have filters applied (shared across all organizations)
    if (name === "plans") {
      console.log(`[SyncService] Plans database: No filtering applied (shared across all organizations)`);
      return;
    }

    // CRITICAL: Only apply filtering to actual mission_control databases
    if (isMissionControlDatabase && isAccessedByOtherApp) {
      console.log(`[SyncService] 🔒 This is a MISSION CONTROL database accessed by other app - applying organization filter`);
    } else if (!isMissionControlDatabase) {
      console.log(`[SyncService] 🏢 This is an ORGANIZATION-SPECIFIC database (${databasePrefix}${name}) - no filtering needed`);
      console.log(`[SyncService] 🔄 Will sync entire database as it belongs to one organization`);
      return;
    } else {
      console.log(`[SyncService] 🎯 Mission control database accessed by mission control app - no filtering needed`);
      return;
    }

    if (isMissionControlDatabase && isAccessedByOtherApp) {
      const organizationID = this.extractOrganizationId(databasePrefix);
      console.log(`[SyncService] 🔒 APPLYING ORGANIZATION FILTER for mission_control ${name}`);
      console.log(`[SyncService] 🔍 Organization ID: ${organizationID} (from prefix: ${databasePrefix})`);
      console.log(`[SyncService] 🎯 Filter will only include documents matching organization: ${organizationID}`);

      // CRITICAL: Ensure organization exists before applying filter
      if (name === "organizations") {
        await this.ensureOrganizationExists(organizationID, databasePrefix);
      }

      // Organizations database: Filter by organization ID
      if (name === "organizations") {
        syncOptions.filter = (doc) => {
          const matches = doc._id.toLowerCase() === organizationID.toLowerCase();
          console.log(`[SyncService] Organization filter: Checking doc ${doc._id} against ${organizationID} - Match: ${matches}`);
          if (matches) {
            console.log(`[SyncService] Organization filter: ✅ Including doc ${doc._id}`);
          } else {
            console.log(`[SyncService] Organization filter: ❌ Excluding doc ${doc._id} (looking for ${organizationID})`);
          }
          return matches;
        };
      }

      // Subscriptions database: Filter by organization.value
      else if (name === "subscriptions") {
        syncOptions.filter = (doc) => {
          const matches = doc.organization && doc.organization.value === organizationID;
          if (matches) {
            console.log(`[SyncService] Subscription filter: Including doc ${doc._id} for org ${doc.organization.value}`);
          }
          return matches;
        };
      }



      // Other mission_control databases: Apply organization filtering if needed
      else {
        console.log(`[SyncService] Mission control ${name}: No specific filter defined, allowing all data`);
      }
    }

    // For mission_control databases accessed by mission_control app itself, no filtering
    else if (isMissionControlDatabase && databasePrefix.includes('mission_control')) {
      console.log(`[SyncService] Mission control ${name}: No filtering (accessed by mission_control app)`);
    }

    // For organization-specific databases, no filtering needed (already isolated by prefix)
    else {
      console.log(`[SyncService] Organization-specific database ${name}: No filtering needed (isolated by prefix: ${databasePrefix})`);
    }
  }

  /**
   * Check if a database is a mission_control database
   * @param {string} databaseName - Database name
   * @param {string} databasePrefix - Database prefix
   * @returns {boolean} True if it's a mission_control database
   */
  isMissionControlDatabase(databaseName, databasePrefix) {
    // CRITICAL: Only databases with 'mission_control_' prefix are mission control databases
    // Organization-specific databases like 'zenwrench_ly5w828y_organizations' are NOT mission control
    return databasePrefix.includes('mission_control');
  }

  /**
   * Extract organization ID from database prefix
   * @param {string} databasePrefix - Database prefix like 'prosy_m9xlo8hv_'
   * @returns {string} Organization ID in uppercase
   */
  extractOrganizationId(databasePrefix) {
    const pArray = databasePrefix.split('_');
    // For prefix like 'prosy_m9xlo8hv_', org ID is at index -2 (second to last)
    return (pArray.length >= 2 ? pArray[pArray.length - 2] : pArray[0]).toUpperCase();
  }

  /**
   * Ensure organization exists in mission_control_organizations before syncing
   * This prevents sync issues when organization documents are missing
   */
  async ensureOrganizationExists(organizationId, databasePrefix) {
    try {
      console.log(`[SyncService] 🏗️ Ensuring organization ${organizationId} exists in mission_control_organizations`);

      const result = await this.databaseHandler.ensureOrganizationExists(organizationId, {
        name: `Organization ${organizationId}`,
        databasePrefix: databasePrefix,
        autoCreated: true,
        createdBy: 'SyncService'
      });

      if (result.success) {
        if (result.created) {
          console.log(`[SyncService] ✅ Created organization ${organizationId} for sync`);
        } else {
          console.log(`[SyncService] ✅ Organization ${organizationId} already exists`);
        }
      } else {
        console.error(`[SyncService] ❌ Failed to ensure organization ${organizationId}:`, result.error);
      }

      return result;
    } catch (error) {
      console.error(`[SyncService] ❌ Error ensuring organization ${organizationId}:`, error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Comprehensive cleanup with background sync management
   * PERFORMANCE: Proper resource cleanup
   */
  destroy() {
    console.log(`[SyncService] Destroying sync service...`);

    // Stop all background sync intervals
    this.backgroundSyncIntervals.forEach((interval, dbKey) => {
      clearInterval(interval);
      console.log(`[SyncService] Cleaned up background sync for ${dbKey}`);
    });

    // Clear all tracking data
    this.backgroundSyncIntervals.clear();
    this.activeSyncs.clear();
    this.removeAllListeners();

    console.log(`[SyncService] Sync service destroyed`);
  }
}

module.exports = SyncService;