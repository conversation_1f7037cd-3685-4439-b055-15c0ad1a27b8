const { autoUpdater } = require("electron-updater");
const log = require("electron-log");
const { dialog } = require("electron");
const https = require("https");
function checkInternetConnectivity() {
  return new Promise((resolve, reject) => {
    https
      .get("https://www.google.com", (res) => {
        if (res.statusCode === 200) {
          resolve(true); // Connected to the internet
        } else {
          resolve(false); // Connected, but server returned an error
        }
      })
      .on("error", (e) => {
        resolve(false); // No internet connectivity
      });
  });
}

class AppUpdater {
  constructor(win) {
    autoUpdater.logger = log;
    autoUpdater.logger.transports.file.level = "info";
    autoUpdater.autoDownload = false;

    autoUpdater.on("checking-for-update", () => {
      win.webContents.send("checking-for-update");
    });

    autoUpdater.on("update-available", (_event, releaseNotes, releaseName) => {
      const dialogOpts = {
        type: "info",
        buttons: ["Download"],
        title: "Update Available",
        message:
          "A new version is available. Do you want to download and install it?",
        detail: process.platform === "win32" ? releaseNotes : releaseName,
      };

      dialog.showMessageBox(dialogOpts).then((returnValue) => {
        if (returnValue.response === 0) {
          autoUpdater.downloadUpdate();
        }
      });

      clearInterval(this.updateInterval);
    });

    autoUpdater.on("update-not-available", () => {
      win.webContents.send("update-not-available");
    });

    autoUpdater.on("error", (error) => {
      log.error("Error while checking for updates", error);
      dialog.showErrorBox(
        "Update Error",
        error == null ? "unknown" : (error.stack || error).toString()
      );
    });

    autoUpdater.on("download-progress", (progressObj) => {
      const percentage = progressObj.percent.toFixed(2);
      log.info(`Download progress: ${percentage}%`);
      win.webContents.send("download-progress", percentage);
    });

    autoUpdater.on("update-downloaded", (_event, releaseNotes, releaseName) => {
      const dialogOpts = {
        type: "info",
        buttons: ["Restart", "Later"],
        title: "Application Update",
        message:
          "A new version has been downloaded. Restart the application to apply the updates.",
        detail: process.platform === "win32" ? releaseNotes : releaseName,
      };

      dialog.showMessageBox(dialogOpts).then((returnValue) => {
        if (returnValue.response === 0) autoUpdater.quitAndInstall();
      });
    });

    checkInternetConnectivity().then((isConnected) => {
      if (isConnected) {
        autoUpdater.checkForUpdatesAndNotify();
        console.log("Connected to the internet");
      } else {
        console.log("No internet connection");
      }
    });

    const updateCheckInterval = 60 * 60 * 1000; // 1 hour
    this.updateInterval = setInterval(() => {
      checkInternetConnectivity().then((isConnected) => {
        if (isConnected) {
          autoUpdater.checkForUpdatesAndNotify();
          console.log("Connected to the internet");
        } else {
          console.log("No internet connection");
        }
      });
    }, updateCheckInterval);
  }
}

module.exports = AppUpdater;
